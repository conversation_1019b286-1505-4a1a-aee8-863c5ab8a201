import { defineStore } from 'pinia';

export const useIdListStore = defineStore('idList', {
  state: () => ({
    idList: [] as any[]
  }),
  getters: {
    getIdList: (state) => state.idList
  },
  actions: {
    addId(id: any) {
      if (!this.idList.includes(id)) {
        this.idList.push(id);
      }
    },

    removeId(id: any) {
      this.idList = this.idList.filter((item) => item !== id);
    },

    setIdList(newList: any[]) {
      this.idList = newList;
    },

    clearIdList() {
      this.idList = [];
    }
  }
});
