<template>
  <div class="process-item">
    <div class="left">
      <div
        class="round"
        :class="processData.processStatus == ProcessStatus.finish ? '' : 'inactive'"
      ></div>
      <div
        class="ver-line"
        :class="processData.processStatus == ProcessStatus.finish ? '' : 'inactive'"
      ></div>
    </div>
    <div class="right-main">
      <div
        :style="processData.processStatus == ProcessStatus.finish ? '' : 'color:#999999'"
        class="prj-info"
      >
        <div class="left-info">
          <div
            style="
              font-size: 14px;
              width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
          >
            {{ processData.goodsName }}
          </div>
          <div
            style="
              margin-left: 20px;
              width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
          >
            {{ processData.processSectionTitle }}
          </div>
        </div>
        <div v-if="processData.processStatus == ProcessStatus.finish" class="right-info">
          <div style="width: 100px">学习时长：{{ processData.processTime }}</div>
          <div>已学习</div>
          <img src="@/assets/images/prjlearn/completed.svg" alt="已完成图标" />
        </div>
        <div v-else class="right-info">
          <div style="width: 100px"></div>
          <div>未开始</div>
          <img
            style="cursor: pointer"
            @click="playVideo"
            src="@/assets/images/prjlearn/play.svg"
            alt="点击播放图标"
          />
        </div>
      </div>
      <div class="prj-date">{{ processData.startProcessTime }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
enum ProcessStatus {
  nostart,
  finish,
  learning
}
const props = defineProps<{
  processData: {
    goodsName: string;
    processSectionTitle: string;
    processTime: number;
    processStatus: number;
    startProcessTime: string;
  };
}>();

const playVideo = () => {};
</script>

<style lang="less" scoped>
.process-item {
  width: 100%;
  height: 92px;
  display: flex;
  flex-direction: row;
  font-family: var(--text-family);

  .left {
    width: 2%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .round {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: var(--color-theme-project);
    }

    .ver-line {
      height: 80px;
      width: 2px;
      background-color: var(--color-theme-project);
    }

    .inactive {
      background-color: #f2f2f2;
    }
  }

  .right-main {
    margin-left: 10px;
    width: 98%;
    background-color: var(--color-second);
    height: 55px;
    border-radius: 5px;
    padding-left: 10px;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .prj-info {
      width: 100%;
      height: 20px;
      font-size: var(--fontsize-small-project);
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      color: #333;

      .left-info {
        width: 450px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .right-info {
        width: 200px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .play-icon {
          width: 20px;
          height: 100%;
        }
      }
    }

    .prj-date {
      font-size: var(--fontsize-small-project);
      color: #999999;
    }
  }
}
</style>
