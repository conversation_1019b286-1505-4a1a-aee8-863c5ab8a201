<template>
  <el-dialog
    @close="handleClose"
    class="question-dialog"
    v-model="isDialogShow"
    :close-on-click-modal="false"
    width="596"
  >
    <template #header="{ titleId }">
      <div :id="titleId" class="title">
        <!--TODO先写死 -->
        <!-- {{dialogTitle}} -->
        新增问题
      </div>
    </template>
    <el-form ref="ruleFormRef" :rules="rules">
      <!-- TODO不知道这两个是干什么的 -->
      <!-- :model="curQuestion"
             :rules="rules"> -->
      <div class="content-container">
        <div class="tip">问题内容</div>
        <div class="related-content-container">
          文本关联内容：
          <b v-html="curQuestion.relatedText"></b>
        </div>
        <div class="tip">问题关键字</div>
        <el-form-item>
          <!-- TODO 需要用ckeditor -->
          <!-- <editor ref="editorRef"> 321</editor> -->
          <el-input v-model="input" style="width: 240px" />
        </el-form-item>
        <div class="tip">问题类型</div>
        <el-form-item prop="qType">
          <el-select
            :disabled="curMode == 2"
            v-model="curQuestion.qType"
            @change="handleChangeQType"
            style="margin-bottom: 10px"
          >
            <el-option
              v-for="(value, key) in qTypeDict"
              :key="key"
              :label="key"
              :value="value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div class="tip" v-if="showQDescription">问题描述</div>
        <el-form-item v-if="showQDescription">
          <el-input v-model="description" />
        </el-form-item>
        <!-- <el-form-item prop="qMode">
          <el-select :disabled="curMode == 2" v-model="curQuestion.qMode" style="margin-bottom: 20px">
            <el-option v-for="(value, key) in qModeDict" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item> -->
        <div
          style="height: 1px; background-color: var(--color-boxborder); margin-bottom: 10px"
        ></div>

        <!-- <div style="margin-bottom: 10px">问题解答</div>
        <el-form-item prop="klg">
          <div class="klg-wrapper">
            <my-button v-show="curMode != 2" style="margin-right: 30px" @click="handleOpenTargetDialog">
              + 添加知识
            </my-button>
            <div class="selectedTList" id="bottom" v-if="curQuestion.klg.length > 0">
              <my-tag class="t" :deletable="curMode != 2" @delete="handleDelete" type="target" v-for="item in curQuestion.klg" :tag-id="item.id" :key="item.id">
                <el-tooltip
                    popper-class="tooltip-width"
                    :content="item.name"
                    raw-content
                >
                <span v-html="item.name"></span>
                </el-tooltip>
              </my-tag>
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="explanation">
          <el-input :disabled="curMode == 2" v-model="curQuestion.explanation" type="textarea" placeholder="请输入说明内容"></el-input>
        </el-form-item> -->
      </div>
    </el-form>
    <template #footer>
      <div class="footer">
        <div v-if="curMode != 2" class="btn-group">
          <CmpButton type="info" @click="handleClose" class="btn">关闭</CmpButton>
          <CmpButton type="primary" @click="handleSubmit" class="btn">确认</CmpButton>
        </div>

        <!-- TODO 好像是编辑问题 -->
        <!-- <div v-else class="pagination">
          <div class="btn" :class="curQuestion.qId == q.qId ? 'focused' : ''" v-for="(q,idx) in displayQuestionList" :key="q.qId" @click="handleChangeQuestion(q)"></div>
        </div> -->
      </div>
    </template>
  </el-dialog>
  <!-- <add-t-dialog ref="dialogRef" @submit="receiveTarget"></add-t-dialog> -->
</template>
<script setup lang="ts">
import { ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { QuestionType, qMode, qModeDict, qTypeDict } from '@/types/constant';
// 打包注释
// import editor from "@/views/common/inlineCKEditor.vue";
import CmpButton from '@/components/CmpButton.vue';
const input = ref();
const description = ref();
const editorRef = ref();
const ruleFormRef = ref<FormInstance>();
const curMode = ref<number>(); // 0新增|1编辑|2只读
const isDialogShow = ref(false);
const dialogTitle = ref();
const emits = defineEmits(['submit', 'open', 'close']);
const displayQuestionList = ref<[]>();
const showQDescription = ref(false);
const curQuestion = reactive({
  qId: -1,
  relatedText: '',
  qContent: '',
  qType: QuestionType.what, // 是什么 | 为什么
  qMode: qMode.necessary, // 必要 | 参考
  klg: [],
  explanation: '',
  questionDescription: ''
});
// TODO 表单校验有一点问题
const rules = reactive<FormRules>({
  //   qContent: [{required: true,message: '请输入关键字',trigger: 'blur'}],
  //   qType: [{required: true,message: '请选择问题类型',trigger: 'change'}],
  //   qMode: [{required: true,message: '请选择问题属性',trigger: 'change'}],
  //   klg: [{required: false,message: '请选择知识点',trigger: 'change'}],
  //   explanation: [{required: false,message: '请输入问题解析',trigger: 'blur'}],
});
const handleClose = () => {
  description.value = '';
  isDialogShow.value = false;
  //emits('close');
};

const handleChangeQType = (value: number) => {
  curQuestion.qType = value;
  if (value == QuestionType.what) {
    curQuestion.klg = [curQuestion.klg[0]];
  }
  if (value == QuestionType.open) {
    showQDescription.value = true;
  } else {
    showQDescription.value = false;
  }
};

const handleSubmit = () => {
  curQuestion.qContent = input.value;
  if (curQuestion.qType == QuestionType.open) {
    curQuestion.questionDescription = description.value;
  }
  emits('submit', curQuestion);
  description.value = '';
  showQDescription.value = false;
  handleClose();
};

const showDialog = (questionList, mode: number) => {
  console.log(curQuestion);
  displayQuestionList.value = questionList;
  curMode.value = mode; // 2:不可操作
  Object.assign(curQuestion, questionList[0]);
  console.log(curQuestion);
  switch (curMode.value) {
    case 0:
      dialogTitle.value = '添加问题';
      break;
    case 1:
      dialogTitle.value = '编辑问题';
      break;
    case 2:
      dialogTitle.value = '展示问题';
      break;
    default:
      console.log('问题弹窗mode异常');
      break;
  }
  isDialogShow.value = true;
  input.value = curQuestion.qContent;
  console.log(curQuestion);
  //   nextTick(() => {
  //     editorRef.value.setData(curQuestion.relatedText, curMode.value == 2);
  //   })
  // emits('open');
};
defineExpose({
  showDialog
});
</script>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.el-select-dropdown__item {
  color: var(--color-black);
  font-family: var(--font-family-text);
  &:hover * {
    font-weight: 600;
  }
  &.selected {
    color: var(--color-theme-project);
  }
}
.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}
.content-container {
  display: flex;
  flex-direction: column;
  :deep(.is-disabled .el-textarea__inner),
  :deep(.is-disabled .el-input__wrapper) {
    background-color: white;
    resize: none; /*禁止文本域拖拽事件*/
  }
  .el-select {
    width: 100%;
  }
  .tip {
    margin-bottom: 6px;
  }
  .related-content-container {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    height: 35px;
    padding: 0 15px;
    background-color: var(--color-light);
  }
  .klg-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
  }
  .selectedTList {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: auto;
    .t {
      margin: 0 10px 10px 0;
    }
  }
}
.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;

    .btn {
      width: 105px;
      height: 40px;
      font-size: var(--fontsize-middle-project);
    }
  }
  .pagination {
    display: flex;
    flex-direction: row;
    .btn {
      cursor: pointer;
      margin: 0 5px;
      display: flex;
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: var(--color-grey);
      &.focused,
      &:hover {
        background-color: var(--color-theme-project);
      }
    }
  }
}
</style>
