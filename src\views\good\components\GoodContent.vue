<template>
  <div class="container">
    <!-- 除会员商品以外显示项目目标 -->
    <div class="prjBox" v-if="props.info.value.goodsType == GoodsType.common">
      <span class="prjTitle">项目目标</span>
      <span class="prjDetail" v-html="processAllLatexEquations(props.info.value.purpose)"></span>
    </div>
    <div class="prjBox">
      <!-- 会员商品（领域商品）和普通商品显示描述的title不同 -->
      <span class="prjTitle" v-if="props.info.value.goodsType == GoodsType.vip">领域描述</span>
      <span class="prjTitle" v-else>项目概述</span>
      <span class="prjDetail" v-html="processAllLatexEquations(props.info.value.description)"></span>
    </div>
    <div class="prjBox">
      <!-- 只有项目商品显示章节 -->
      <template v-if="props.info.value.prjType == PrjType.case"
        ><span class="prjTitle" style="padding-bottom: 10px">项目章节</span>
        <div
          class="chaptBox"
          v-for="(item, index) in props.info.value.chapterList"
          @click="goDetailPage(item)"
        >
          <span class="prjDetail">{{ item.chapterName }}</span>
          <div v-if="item.preview == true">
            <el-icon><ArrowRight /></el-icon>
          </div>
          <div v-else>
            <el-icon><Lock /></el-icon>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useLearnStore } from '@/stores/learnintro';
import { useLearningStore } from '@/stores/learning';
import { userPrjInfoStore } from '@/stores/prjInfo';
import defaultPng from '@/assets/images/home/<USER>';

const prjInfoStore = userPrjInfoStore();
const { info } = storeToRefs(prjInfoStore);

import { PrjForm } from '@/types/project';
//import TitleandInfo from '../components/TitleandInfo.vue';
import { ref, onMounted, onUnmounted } from 'vue';
import { VideoPlay, Lock, ArrowRight } from '@element-plus/icons-vue';
import type { ElTable } from 'element-plus';
import { BuyStatus, GoodsType } from '@/types/goods';
import { PrjType } from '@/types/project';
import { processAllLatexEquations } from '@/utils/latexUtils';

const learnStore = useLearnStore();
const learningStore = useLearningStore();
const route = useRoute();
const spuId = route.query.spuId as string;
const router = useRouter();

interface Props {
  info: any;
}
const props = withDefaults(defineProps<Props>(), {});

// 定义emit事件
const emit = defineEmits<{
  showLoginDialog: []
}>();


//登录状态相关，用前两个,v-show="infoStore.getUserId()"
import { userInfoStore } from '@/stores/userInfo';
const infoStore = userInfoStore();
import checkLoginStatus from '@/utils/checkLoginStatus';
import LoginDialog from '@/components/LoginDialog.vue';
const isLogined = checkLoginStatus();

const goDetailPage = async (item: any) => {
  if (!infoStore.getUserId()) {
    // 向父组件传递打开登录弹窗的消息
    emit('showLoginDialog');
    return;
  }
  //可看即跳转
  if (item.preview) {
    const query: {
      spuId: string;
      chapterId?: number;
    } = {
      spuId: props.info.value.spuId,
      chapterId: item.chapterId
    };
    // if (props.info.value.latestChapterId) {
    //   query.chapterId = props.info.value.latestChapterId;
    // }
    //const { href } =
    router.push({
      path: '/learning',
      query: query
    });
    //window.open(href, '_blank');
  } else {
    //不能看就购买
    learningStore.clickLockAtGoodContent = true;
    //return;
  }
};
</script>

<style lang="less" scoped>
.container {
  //padding-top: 100px;
}
.prjBox {
  display: flex;
  flex-direction: column;
  margin: 20px;
}
.prjTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  font-family: var(--title-family);
}
.prjDetail {
  font-size: 14px;
  margin-top: 5px;
  color: #333;
  font-family: var(--text-family);
  word-break: break-all;
}
.chaptBox {
  display: flex;
  padding: 6px;
  padding-left: 10px;
  // margin: 2px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #f2f2f2;
  border-bottom: 1px solid #c5c3c3;
  cursor: pointer;
  height: 46px;
}
</style>
