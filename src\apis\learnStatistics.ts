import { http } from '@/apis';
// 统一分页类型
interface pagination {
  limit: number;
  current: number;
}
// 获取学习列表请求参数
interface learnListParams extends pagination {
  learned?: number;
  startTime?: string;
  endTime?: string;
}

// 获取学习信息列表
export function getLearnListApi(params: learnListParams) {
  return http.get('/home/<USER>', params);
}

// 取消学习接口
export function cancelLearnApi(params: { spuId: string }) {
  return http.get('/user-project/setState', params);
}

// 右侧用户信息卡片获取信息接口
export function getUserDetailApi() {
  return http.get('/learning-process/getUserDetail');
}

// 浏览记录接口参数
interface historyListParams extends pagination {
  title?: string;
  time?: number;
}

// 浏览记录接口
export function getHistoryListApi(data: historyListParams) {
  return http.post('/browsing-history/getList', data);
}

// 清除浏览记录接口 oid为空则删除全部历史记录  oid不为空则删除某一条历史记录
export function deleteHistoryApi(oid: string) {
  return http.get('/browsing-history/delete/' + oid);
}

interface learnProcessParams extends pagination {
  startTime: string;
  endTime: string;
}

// 获取学习进程接口
export function getLearnProcessApi(data: learnProcessParams) {
  return http.post('/browsing-history/getLearned', data);
}

// 获取正在学习 和 完成学习数量接口
export function getTotalApi() {
  return http.get('/user-project/getTotal');
}
