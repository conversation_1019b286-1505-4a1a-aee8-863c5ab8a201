<template>
  <div class="right-info">
    <div class="learn-paln">
      <div class="title">
        <img src="@/assets/images/prjlearn/u6261.svg" alt="" />
        学习计划
      </div>
      <div class="content">
        <div class="start-date">计划开始日期： {{ startTime }}</div>
        <div class="end-date">
          预计计划结束日期： {{ endTime }}
          <img src="@/assets/images/prjlearn/process_warn.svg" alt="" />
          <div class="continue">续费</div>
        </div>
      </div>
    </div>
    <div class="week-plan">
      <div class="title-wrapper">
        <div class="title">
          <img src="@/assets/images/prjlearn/u6255.svg" alt="" />
          我的每周安排
        </div>
        <CmpButton type="primary" class="btn" @click="showModal = true">编辑</CmpButton>
        <el-dialog v-model="showModal" width="20%" @open="handleOpen" @close="handleClose">
          <template #header>
            <header class="modal-header">
              <h2>编辑我的每周安排</h2>
            </header>
            <div class="line"></div>
          </template>
          <div class="modal-overlay">
            <div class="modal">
              <main class="modal-content">
                <div class="learn-days">
                  <h5>每周学习天数</h5>
                  <el-select
                    style="margin-top: 10px"
                    v-model="daysAWeek"
                    placeholder="请选择天数"
                    @change="handleDaysChange"
                  >
                    <el-option v-for="item in 7" :key="item" :label="item" :value="item" />
                  </el-select>
                </div>
                <div class="week-select">
                  <h5>每周学习安排</h5>
                  <div class="week">
                    <div
                      v-for="(day, index) in weekInfo"
                      :key="day.day"
                      :class="day.isSelected ? 'active' : ''"
                      class="item"
                      style="cursor: pointer"
                      @click="selectDay(index)"
                    >
                      {{ day.day }}
                    </div>
                  </div>
                </div>
              </main>
            </div>
          </div>
          <template #footer>
            <div class="btns">
              <button class="btn-back" @click="cancleEdit">返回</button>
              <CmpButton
                style="height: 40px; width: 100px; font-size: 14px"
                class="btn-commit"
                type="primary"
                @click="confirmDays"
                >确定</CmpButton
              >
            </div>
          </template>
        </el-dialog>
      </div>
      <div class="duration">
        {{ startTime }}-{{ endTime }} &nbsp;&nbsp;每周学习{{ daysAWeek }}天
      </div>
      <div class="week">
        <div
          v-for="day in weekInfo"
          :key="day.day"
          :class="day.isSelected ? 'active' : ''"
          class="item"
        >
          {{ day.day }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getScheduleApi, setScheduleApi } from '@/apis/case';
import { useLearnStore } from '@/stores/learnintro';
// 周信息
const weekInfo = reactive([
  {
    day: '一',
    isSelected: false
  },
  {
    day: '二',
    isSelected: false
  },
  {
    day: '三',
    isSelected: false
  },
  {
    day: '四',
    isSelected: false
  },
  {
    day: '五',
    isSelected: false
  },
  {
    day: '六',
    isSelected: false
  },
  {
    day: '日',
    isSelected: false
  }
]);
type DayInfo = {
  day: string;
  isSelected: boolean;
};
const oldWeekInfo: Array<DayInfo> = [];
// 定义响应式状态
const showModal = ref(false);
// 此处变量 接入接口时都需在挂载时获取
// 每周学习天数
const daysAWeek = ref(3);
let oldDaysAWeek = 0;
// 当前选中的天数
let selectedDaysCount = 3;
let oldSelectedDaysCount = 0;
// 点击修改学习日期
const selectDay = (index: number) => {
  // 如果原本选中 则点击后 不选 当前选中的天数就减1
  if (weekInfo[index].isSelected) {
    selectedDaysCount--;
  } else {
    if (selectedDaysCount + 1 > daysAWeek.value) {
      ElMessage({
        type: 'warning',
        message: '天数已经够了哦~'
      });
      return;
    }
    selectedDaysCount++;
  }
  weekInfo[index].isSelected = !weekInfo[index].isSelected;
};
const handleDaysChange = (value: any) => {
  // 修改每周学习天数
  daysAWeek.value = value;
  // 清空已选
  selectedDaysCount = 0;
  weekInfo.forEach((day) => {
    day.isSelected = false;
  });
};
// 打开对话框时 对周信息做备份
const handleOpen = () => {
  // 清空存储信息的数组
  oldWeekInfo.length = 0;
  oldDaysAWeek = daysAWeek.value;
  oldSelectedDaysCount = selectedDaysCount;
  // 对周信息做备份
  weekInfo.forEach((day) => {
    oldWeekInfo.push({
      day: day.day,
      isSelected: day.isSelected
    });
  });
};

// 关闭对话框时 触发的函数
let isEdit = false;
const handleClose = () => {
  if (isEdit) {
    return;
  }
  cancleEdit();
};

// 取消修改每周安排
const cancleEdit = () => {
  showModal.value = false;
  // 恢复数据
  weekInfo.length = 0;
  daysAWeek.value = oldDaysAWeek;
  selectedDaysCount = oldSelectedDaysCount;
  oldWeekInfo.forEach((day) => {
    weekInfo.push(day);
  });
};

// 确定
const confirmDays = () => {
  if (selectedDaysCount != daysAWeek.value) {
    ElMessage({
      type: 'warning',
      message: '请选择足够的天数~'
    });
    return;
  }
  showModal.value = false;
  isEdit = true;
  // 此处编写提交数据的api
  // 遍历周信息数组 拼接结果字符串
  const data = {
    spuId: learnStore.spuId,
    learnWeekPlan: '',
    learnDaysNum: daysAWeek.value
  };
  weekInfo.forEach((day, index) => {
    if (day.isSelected) {
      if (data.learnWeekPlan == '') {
        data.learnWeekPlan += String(index + 1);
      } else {
        data.learnWeekPlan = data.learnWeekPlan + '@@' + String(index + 1);
      }
    }
  });

  setScheduleApi(data)
    .then((res) => {
      console.log(res);
    })
    .catch((error) => {
      console.log(error);
    });
};

const learnStore = useLearnStore();
const startTime = ref('');
const endTime = ref('');
const route = useRoute();
const spuId = route.query.spuId as string;
// 获取学习周计划数据
const getWeeklyPlan = () => {
  getScheduleApi(spuId)
    .then((res) => {
      console.log(res);
      startTime.value = res.data.startTime;
      endTime.value = res.data.endTime;
      daysAWeek.value = res.data.learnDaysNum;
      selectedDaysCount = daysAWeek.value;
      const learnWeekPlan = res.data.learnWeekPlan.split('@@');
      learnWeekPlan.forEach((index: any) => {
        weekInfo[Number(index) - 1].isSelected = true;
      });
    })
    .catch((error) => {
      console.log(error);
    });
};
getWeeklyPlan();
</script>

<style lang="less" scoped>
.right-info {
  font-family: var(--text-family);
  width: 300px;
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 10px;
  margin-left: 20px;
}

.learn-paln {
  width: 100%;
  border-bottom-color: #f2f2f2;
  border-bottom-style: solid;
  border-bottom-width: 2px;

  .content {
    margin-top: 12px;
    width: 100%;
    padding-bottom: 18px;
    font-size: var(--fontsize-middle-project);
    color: #333333;

    .continue {
      color: var(--color-theme-project);
      text-decoration: underline;
      cursor: pointer;
    }

    .continue:hover {
      font-weight: 700;
    }
  }

  .end-date {
    margin-top: 14px;
    width: 100%;
    display: flex;
    flex-direction: row;

    img {
      margin-left: 5px;
      margin-right: 5px;
    }
  }
}

.title {
  height: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  font-size: var(--fontsize-middle-project);
  font-family: var(--title-family);
  font-weight: 700;

  img {
    margin-right: 10px;
  }
}

.week-plan {
  width: 100%;
  margin-top: 24px;
  border-bottom-color: #f2f2f2;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  padding-bottom: 4px;

  .title-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .title {
      width: 40%;
    }

    .btn {
      width: 60px;
      height: 26px;
      font-size: var(--fontsize-small-project);
    }
  }

  .duration {
    width: 100%;
    font-size: var(--fontsize-small-project);
    font-weight: 400;
    color: #999999;
    margin-top: 13px;
  }
}

.modal-overlay {
  width: 100%;
  display: flex;
  justify-content: center;
}

.line {
  margin-top: 17px;
  width: 100%;
  height: 2px;
  background-color: #f2f2f2;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    width: 144px;
    height: 18px;
    font-size: 18px;
    color: var(--color-theme-project);
    font-weight: 700;
  }
}

.modal-header h2 {
  margin: 0 0 10px;
}

.modal-header button {
  border: none;
  background-color: transparent;
  font-size: 16px;
  cursor: pointer;
}

.modal-content {
  margin: 10px 0;

  .week-select {
    margin-top: 20px;
  }
}

.week {
  width: 217px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;

  .item {
    height: 26px;
    width: 26px;
    border-radius: 50%;
    background-color: #f2f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 13px;
  }

  .active {
    background-color: var(--color-theme-project);
    color: white;
  }
}

.btns {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 30px;

  .btn-back,
  .btn-commit {
    width: 100px;
    height: 40px;
    font-weight: var(--fontsize-middle-project);
  }

  .btn-commit {
    margin-left: 21px;
  }

  .btn-back {
    background-color: white;
    border-radius: 5px;
    border: 1px solid #999;
    cursor: pointer;

    &:hover {
      border: 1px solid rgb(114, 161, 181);
      background-color: rgb(222, 233, 238);
      color: rgb(0, 85, 121);
      font-weight: 700;
    }
  }
}
</style>
