<script setup lang="ts">
import { getklgdetailApi, saveFeedBackApi, getPreklglistApi, getKlgRecommend } from '@/apis/klgdetail';
import AreaContent from './areaContent.vue'; // 知识内容组件
import MapContent from './MapContent.vue';
import ExplainContent from './ExplainContent.vue'; // 讲解测评组件
import ErrorDialog from '@/components/ErrorDialog.vue';
import {
  Document,
  EditPen,
  Opportunity,
  Expand,
  Fold,
  ArrowRightBold,
  Orange
} from '@element-plus/icons-vue';

const route = useRoute();
const klgCode = "K1907350033191395328";
console.log('klgcodedede', klgCode);
provide('klgCode', klgCode);

//7/9上午新加
const areaCode = route.query.areaCode;
provide('areaCode', areaCode);


const currentContent = ref('area'); // 默认显示知识内容
const isFolded = ref(false);
const ready = ref(false);
const NoPermisson = ref(false);
const errorInfo = ref('');

//知识点详情数据类型
const klgDetail = ref<KlgdetailData>({
  klgTitle: '知识点名称',
  status: '已掌握', // 可选
  tags: ['Verlog语言', '芯片设计'],
  mastered: 2,
  total: 20,
  masterPercent: 10,
  preLearned: 0,
  preTotal: 77,
  prePercent: 0,
  provider: '',
  createTime: '',
  field: '',
  sysTitles: '',
  notice: '',
  projectList: [{ userCoverPic: '', uniqueCode: '' }],
  reference: [{ refid: 0, indexPage: '', cntName: '' }]
});

interface areadetailData {
  title: string;
  areaCode: string;
  authorName: string;
  createTime: string;
  description: string;
}

const areaDetail = ref<areadetailData>({
  title: '',
  areaCode: '',
  authorName: '',
  createTime: '',
  description: ''
});
onMounted(async () => {
  try {

    const response1 = await getAreaDetailApi(areaCode);
    areaDetail.value.title = response1.data.title;
    areaDetail.value.createTime = response1.data.createTime;
    areaDetail.value.authorName = response1.data.authorName;
    areaDetail.value.description = response1.data.description;
    areaDetail.value.areaCode = response1.data.areaCode;




    const response = await getklgdetailApi(klgCode);
    console.log(klgDetail.value)
    klgDetail.value.klgTitle = response.data.klgDetail.klgTitle;
    klgDetail.value.provider = response.data.klgDetail.provider;
    klgDetail.value.createTime = response.data.klgDetail.creatTime;
    klgDetail.value.field = response.data.klgDetail.areaList[0].areaName;
    klgDetail.value.sysTitles = response.data.klgDetail.synonym;
    klgDetail.value.notice = response.data.klgDetail.cnt;
    klgDetail.value.reference = response.data.klgDetail.referenceList;
    klgDetail.value.mastered = response.data.klgDetail.exerciseProgress;
    klgDetail.value.total = response.data.klgDetail.exerciseGoal;
    klgDetail.value.preLearned = response.data.klgDetail.preKlgProgress;
    klgDetail.value.preTotal = response.data.klgDetail.preKlgCount;
    if (response.data.klgDetail.areaList && Array.isArray(response.data.klgDetail.areaList)) {
      klgDetail.value.tags = response.data.klgDetail.areaList.map((item: any) => item.areaName).filter(Boolean);
    }
    if (response.data.klgDetail.status === 0) {
      klgDetail.value.status = '未学习';
    } else if (response.data.klgDetail.status === 1) {
      klgDetail.value.status = '已掌握';
    } else {
      klgDetail.value.status = '全掌握';
    }

    ready.value = true;
  } catch (error) {
    console.error('获取数据失败:', error);
    errorInfo.value = '加载领域详情失败，请刷新重试';
    NoPermisson.value = true;
    ready.value = true;
  }
});

//6/26新加
const recommends = ref<string>([])

const currentActive = ref('area');
const setActive = (content: string) => {
  currentContent.value = content;
  currentActive.value = content;
};
const currentContentComponent = computed(() => {
  if (currentContent.value == 'area') {
    return {
      component: AreaContent,
      props: { areaDetail: areaDetail.value, areaCode: areaCode }
    };
  } else if (currentContent.value == 'explain') {
    return {
      component: ExplainContent,
      props: {recommends: recommends.value}
    };
  } else if (currentContent.value == 'card') {
    return {
      component: cardPage,
      props: {}
    };
  } else if (currentContent.value == 'map') {
    return {
      component: MapContent,
      props: { areaDetail: areaDetail.value, areaCode: areaCode }
    };
  }
  return null;
});

const toggleNavbar = () => {
  isFolded.value = !isFolded.value;
};

// 参考文献数据类型
interface referenceData {
  refid: number;
  cntName: string;
  indexPage: string;
}
// 相关项目数据类型
interface projectData {
  userCoverPic: string;
  uniqueCode: string;
}
// 知识详情数据类型
interface KlgdetailData {
  klgTitle: string;
  status: string;
  tags: string[];
  mastered: number;
  total: number;
  masterPercent: number;
  preLearned: number;
  preTotal: number;
  prePercent: number;
  provider: string;
  createTime: string;
  field: string;
  sysTitles: string;
  notice: string;
  projectList: Array<projectData>;
  reference: Array<referenceData>;
}

//===========================ksg-map==================================
import { getLevelData } from '@/apis/ksgmap';
import KsgMap from 'ksg-map';
import type { GlobalConfig, OriginalData, AreaData, PointData } from 'ksg-map/dist/types';
import cardPage from '@/views/area/cardPage.vue';
import { getAreaDetailApi } from '@/apis/area';

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: true
});
const data = ref<OriginalData>({
  topAreas: [],
  points: [],
  areas: [],
  focuses: []
});

async function fetchLevelData(ids: string[], type: 'area' | 'focus', level: number, limit: number) {
  return (await getLevelData([klgCode], level, limit)).data;
}
//===========================ksg-map==================================






</script>
<template>
  <div class="areadetail-container">
    <template v-if="ready">
      <template v-if="!NoPermisson">
    <div class="areadetail-innercontainer">
      <div class="navbar" :class="{ folded: isFolded }">
        <div class="nav-change" @click="toggleNavbar">
          <el-icon v-if="isFolded"><Expand /></el-icon>
          <el-icon v-else><Fold /></el-icon>
        </div>
        <div class="nav-items" :class="{ folded: isFolded }">
          <div
            @click="setActive('area')"
            :class="['nav-item', { active: currentActive == 'area' }]"
          >
            <el-icon><Document /></el-icon>
            <span v-if="!isFolded">领域描述</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
          <div @click="setActive('map')" :class="['nav-item', { active: currentActive == 'map' }]">
            <el-icon><Orange /></el-icon>
            <span v-if="!isFolded">知识源图</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
          <div
            @click="setActive('explain')"
            :class="['nav-item', { active: currentActive == 'explain' }]"
          >
            <el-icon><EditPen /></el-icon>
            <span v-if="!isFolded">领域讲解</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
          <div
            @click="setActive('card')"
            :class="['nav-item', { active: currentActive == 'card' }]"
          >
            <el-icon><Opportunity /></el-icon>
            <span v-if="!isFolded">成为会员</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
        </div>
      </div>
      <div class="content-container" v-if="areaDetail.areaCode">
        <div class="areadetail-header">
          <!-- 左侧：知识点名称和标签 -->
          <div class="areadetail-header-left">
            <div class="areadetail-title-row">
              <span class="areadetail-name" v-html="areaDetail.title"></span>
            </div>
            <div class="areadetail-tags">
              <span  class="areadetail-tag">{{ areaDetail.authorName }}</span>
            </div>
          </div>
        </div>

        <div class="areadetail-container-content">
          <!-- 动态内容区域 -->
          <component
            :is="currentContentComponent.component"
            v-bind="currentContentComponent.props"
          ></component>
        </div>
      </div>
    </div>
        </template>
        <ErrorDialog v-else :errorMessage="errorInfo"></ErrorDialog>
      </template>
  </div>
</template>

<style scoped lang="less">
.areadetail-container {
  margin-top: 20px;
  display: flex;
  //align-items: center;
  justify-content: center;
  //max-width: 1220px;

  .areadetail-innercontainer {
    width: 1400px;
    display: flex;

    .navbar {
      display: flex;
      flex-direction: column;
      //background-color: #f4f4f4;
      padding: 10px;
      flex: 0 0 160px;
      //justify-content: center;
      margin-top: 64px;

      .nav-change {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .el-icon {
          font-size: 25px;
          color: #333333;
        }
      }

      .nav-item {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        height: 60px;
        transition:
          background-color 0.3s,
          border-radius 0.3s;
        color: #333333;
        margin-bottom: 12px;

        .el-icon {
          margin-left: 5px;
          margin-right: 5px;

          &:hover {
            //transform: scale(1.2); // 鼠标悬停时放大
          }
        }
      }

      .nav-item:hover {
        background-color: #f2f2f2;
        border-radius: 5px;
      }

      .nav-item.active {
        background-color: #f2f2f2;
        border-radius: 5px;
      }
    }
    .navbar.folded {
      flex: 0 0 70px;
    }
    .nav-item span {
      display: inline; // 默认显示
    }

    .navbar.folded .nav-item span {
      display: none; // 折叠时隐藏文字
    }

    .content-container {
      display: flex;
      flex-direction: column;
      margin-left: 10px;

      .areadetail-header {
        min-width: 1200px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        //padding: 12px 16px;
        padding-bottom: 2px;
        //padding-top: 12px;
        padding-left: 16px;
        //padding-right: 0px;
        background: #fff;
        //border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        //maxWidth: 1220px;
        max-width: 1200px;

        border-width: 1px;
        border-style: solid;
        border-color: rgba(220, 223, 230, 1);
        border-top: none;
        border-left: none;
        border-right: none;
      }

      .areadetail-header-left {
        flex: 1;
      }

      .areadetail-title-row {
        display: flex;
        align-items: center;
      }

      .areadetail-name {
        font-size: 18px;
        font-weight: bold;
        margin-right: 12px;
      }

      .areadetail-status {
        background: #fff3e0;
        color: #ff9800;
        border-radius: 4px;
        padding: 2px 8px;
        font-size: 12px;
        margin-left: 4px;
      }

      .areadetail-tags {
        margin-top: 8px;
      }

      .areadetail-tag {
        display: inline-block;
        background: #f2f2f2;
        color: #888;
        border-radius: 12px;
        padding: 2px 12px;
        font-size: 12px;
        margin-right: 8px;
        margin-bottom: 4px;
      }

      .areadetail-header-right {
        min-width: 260px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }

      .area-progress-card {
        display: flex;
        align-items: center;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        background: #fff;
        //padding: 0 16px;
        //min-width: 420px;
        max-width: 400px;
        height: 54px; /* 扁平感 */
        font-size: 14px;
        box-sizing: border-box;
        gap: 0;
      }

      .area-progress-col {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 60px;
        height: 100%;
        text-align: center;
      }

      .icon-col {
        min-width: 32px;
        color: #bfcbd9;
        font-size: 20px;
      }

      .stage-col {
        min-width: 60px;
        color: #222;
        font-weight: bold;
        font-size: 15px;
      }

      .gray-col {
        color: #bbb;
        font-weight: normal;
      }

      .triple-col {
        min-width: 90px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 0 4px;
      }

      .triple-title {
        font-size: 11px;
        color: #888;
        margin-bottom: 0;
        line-height: 1.1;
      }

      .triple-bar {
        display: flex;
        align-items: center;
        width: 60px;
        margin: 2px 0;

        .el-progress--line {
          width: 60px;
          .el-progress__text {
            width: 25px;
          }
        }
      }

      .area-progress-dot {
        width: 7px;
        height: 7px;
        background: #409eff;
        border-radius: 50%;
        display: inline-block;
        margin-right: 2px;
      }

      .area-progress-line {
        flex: 1;
        height: 2px;
        background: #e4e7ed;
        border-radius: 1px;
      }

      .triple-value {
        font-size: 11px;
        color: #222;
        font-weight: 500;
        margin-top: 0;
        line-height: 1.1;
      }

      .areadetail-container-content {
        //display: flex;
      }
    }
  }
}

.pagination-block {
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: center;
  margin-top: 5px;
}
</style>
