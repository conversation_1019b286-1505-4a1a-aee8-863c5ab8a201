import { http } from '@/apis';
import axios from 'axios';
import type { OriginalData, AreaData, PointData, FocusData } from 'ksg-map/dist/types';

/** 获取 /knowledge 页面的所需的原始数据 */
export function getKnowledgeOriginData() {
  return http.get<{
    targetArea: string;
    originalData: OriginalData;
  }>('/ksgmap/knowledge', undefined, false);
}

export async function getAreaGraph() {
  return (await http.get<Omit<AreaData, 'points'>[]>('/klg/areaGraph', undefined, false)).data.map(
    (item) => {
      return {
        points: [],
        ...item
      } as AreaData;
    }
  );
}

export async function getPrjGraph(spuId: string) {
  return (await http.get<PointData[]>('/klg/prjGraph', { spuId }, false)).data;
}

export async function getChapterGraph(spuId: string, chapterId: string) {
  return (await http.get<PointData[]>('/klg/chapterGraph', { spuId, chapterId }, false)).data;
}

export async function getUserKlgGraph() {
  return (await http.get<PointData[]>('/klg/userKlgGraph', undefined, false)).data;
}
//项目知识图谱
export async function projectGraph(
  spuId: string,
  chapterId: string,
  current: number,
  limit: number
) {
  return (
    await http.post<PointData[]>('/klg/project/graph', {
      spuId,
      chapterId,
      current,
      limit
    })
  ).data;
}
export function getChapterOriginData(uniqueCode: string, chapterId: string) {
  return http.get<{
    targetArea: string;
    originalData: OriginalData;
  }>('/ksgmap/chapter', { uniqueCode, chapterId }, false);
}

/** 根据领域 id 获取子领域数据 */
export async function getAreaData(id: string) {
  return (await http.get<AreaData[]>('/ksgmap/area', { id }, false)).data;
}

/** 根据知识点 id 获取数据 */
export async function getFocusData(id: string) {
  return (await http.get<FocusData>('/ksgmap/point', { id }, false)).data;
}

export function getLevelData(klgCodes: string[], current: number, limit: number) {
  return http.post<PointData[]>(
    '/klg/graph',
    {
      klgCodes,
      current,
      limit
    },
    false
  );
}
