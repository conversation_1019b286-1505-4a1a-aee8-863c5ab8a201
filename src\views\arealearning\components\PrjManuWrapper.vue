<template>
  <div class="main-wrapper" :class="{ big: isBig, map: isMap }">
    <div class="main">
      <div class="prjtitle">
        <div class="titlefont" v-html="currentItemTitle"></div>

        <div class="klg-icon">
          <div class="klg-tooltip">目标知识</div>
          <span class="klgbtn klg-icon-circle" @click="toggleTargetKnowledge">
            <img src="@/assets/svgs/kl.svg" alt="" srcset="" />
          </span>
        </div>
        <div class="klg-icon">
          <div class="klg-tooltip">大纲内容</div>
          <span class="klgbtn klg-icon-circle" @click="toggleOutline">
            <img src="@/assets/svgs/questionList.svg" alt="" srcset="" />
          </span>
        </div>
        <div class="klg-icon">
          <div class="klg-tooltip">知识源图</div>
          <span class="klgbtn klg-icon-circle" @click="toggleMap">
            <img src="@/assets/svgs/kGraph.svg" alt="" srcset="" />
          </span>
        </div>
      </div>
      <el-divider class="divider" />

      <div>
        <template v-if="wordContent">
          <!-- {{ wordContent }} -->
          <PrjManuscript
            :renderContent="renderContent"
            :questionList="questionList ?? []"
            :ksgMapData="ksgMapData"
            :ksgMapLoading="ksgMapLoading"
            @refresh="handleQuestionList"
            ref="manuScript"
            @close-map="closeMap"
            class="lineWordContent manuscript-container"
          ></PrjManuscript>
        </template>
      </div>
    </div>

    <!-- 问号图标 -->
    <div
      v-if="questionIconVisible"
      ref="questionIconElement"
      class="question-icon"
      :style="{
        position: 'fixed',
        left: questionIconPosition.x + 'px',
        top: questionIconPosition.y + 'px',
        zIndex: 10000
      }"
      @click="handleQuestionIconClick"
    >
      <!-- 悬浮提示 -->
      <div class="question-tooltip">提问</div>
      <!-- 问号图标 -->
      <div class="question-icon-circle">
        <img :src="questionIcon" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getManuProjectSectionApi,
  getQuestionListApi,
  deleteQuestionApi,
  saveQuestionApi,
  getPrjMoreInfoApi,
  getPartProApi,
  getPrjDetailApi
} from '@/apis/learning';
import { getKsgMapMutileApi } from '@/apis/klgdetail';
import type { Chapter, QuestionData } from '@/types/learning';
import { STATE_FLAG } from '@/types/learning';
import PrjManuscript from './PrjManuscript.vue';
import { defineExpose } from 'vue';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';

import { emitter } from '@/utils/emitter';

import { Event } from '@/types/event';
import { Lock, CloseBold } from '@element-plus/icons-vue';
import { getPrjIntroduceApi } from '@/apis/case';
import { PrjType } from '@/types/project';
import { Render } from '@endlessorigin/select_to_ask';
import { useRenderManager } from '@/composables/useRenderManager';
import { HelpFilled, Close } from '@element-plus/icons-vue';
import questionIcon from '@/assets/svgs/question.svg';
import { useQuestionIcon } from '@/composables/useQuestionIcon';

const questionList = ref();

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon();

// 使用Render管理器
const renderContent = ref('');
const { reinitializeRender, addQuestion, removeQuestion } = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => wordContent.value,
  questionList,
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      showQuestionIcon(data);
    } else {
      console.log('❌ 选中文本为空或无效');
    }
  },
  onClick: (data: any) => {
    const event = new CustomEvent('showAnswerDrawer', {
      detail: { questionData: data.target }
    });
    window.dispatchEvent(event);
  },
  onFinish: (arg: any) => {
    const content = arg.content;
    // console.log('myonFinish', content);
    renderContent.value = content;
  }
  // enableDebugLog: true
});
const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
  const res = await getQuestionListApi(uniqueCode, chapterId);
  questionList.value = res.data;
};
const addQuestionFn = async (params: {
  associatedWords: string;
  keyword: string;
  questionType: QuestionType;
  questionDescription: string;
  uniqueCode: string;
  chapterId: string;
  contentId?: string;
}) => {
  const res = await saveQuestionApi(params);
  const data = res.data.data;
  if (res.success) {
    ElMessage.success('保存问题成功');
    console.log('保存的问题:', data);
    addQuestion(data.associatedWords, data.questionId);
  } else {
    const errorMsg = res.message || '保存问题失败，请重试';
    ElMessage.error(errorMsg);
  }
};

const removeQuestionFn = async ([questionId, associatedWords]: any) => {
  console.log('removeQuestionFn', questionId);
  const res = await deleteQuestionApi(questionId);
  // console.log("deleteQuestionApi",res.data)
  if (res.success) {
    ElMessage.success('删除成功');
    // 使用Render管理器处理问题删除
    removeQuestion(associatedWords, Number(questionId));
  } else {
    ElMessage.error('删除失败');
  }
};
const payDialogVisible = ref(false);

// 知识图谱相关状态
const ksgMapData = ref<any>(null);
const ksgMapLoading = ref<'loading' | 'loaded' | 'error'>('loading');

// 切换知识图谱显示状态
const toggleMap = async () => {
  isMap.value = !isMap.value;

  // 当显示知识图谱时，获取数据并初始化
  if (isMap.value) {
    console.log('PrjManuWrapper - 开始获取知识图谱数据');
    ksgMapLoading.value = 'loading';

    try {
      // 第一步：获取项目详情，获取所有klgCodes
      const prjInfoRes = await getPrjMoreInfoApi(spuId);
      let klgCodes: string[] = [];

      if (prjInfoRes && prjInfoRes.data && (prjInfoRes.data as any).targetKlgs) {
        klgCodes = (prjInfoRes.data as any).targetKlgs.map((klg: any) => klg.klgCode);
      }
      console.log('PrjManuWrapper - klgCodes:', klgCodes);

      // 第二步：获取知识图谱数据
      let pointsData: any;
      if (klgCodes.length > 0) {
        pointsData = await getKsgMapMutileApi(klgCodes, 1, 10);
      }

      if (pointsData && pointsData.data) {
        ksgMapData.value = pointsData.data;
        ksgMapLoading.value = 'loaded';
        console.log('PrjManuWrapper - 知识图谱数据获取成功:', pointsData.data);
      } else {
        ksgMapLoading.value = 'error';
        console.warn('PrjManuWrapper - 知识图谱数据为空');
      }
    } catch (error) {
      console.error('PrjManuWrapper - 获取知识图谱数据失败:', error);
      ksgMapLoading.value = 'error';
    }
  } else {
    // 关闭时清空数据
    ksgMapData.value = null;
    ksgMapLoading.value = 'loading';
  }
};

//关闭图标
const closeMap = () => {
  isBig.value = false;
  isMap.value = false;

  // 关闭地图后重新初始化Render实例，确保事件监听正常工作
  nextTick(async () => {
    await reinitializeRender();
  });
};

// 显示大纲 - 只能打开，会覆盖目标知识
const toggleOutline = () => {
  if (manuScript.value && manuScript.value.showOutline) {
    // 切换到大纲模式
    sidebarMode.value = 'outline';
    // 调用子组件显示大纲
    manuScript.value.showOutline();
    console.log('PrjManuWrapper - 显示大纲');
  } else {
    console.warn('PrjManuWrapper - manuScript 组件引用不可用');
  }
};

// 目标知识相关状态
const targetKnowledgeList = ref<Array<{ klgCode: string; klgTitle: string; choose: boolean }>>([]);
const targetKnowledgeLoading = ref(false);

// 侧边栏显示模式：'outline' | 'knowledge'
const sidebarMode = ref<'outline' | 'knowledge'>('outline');

// 显示目标知识 - 只能打开，会覆盖大纲
const toggleTargetKnowledge = async () => {
  // 切换到目标知识模式
  sidebarMode.value = 'knowledge';

  // 如果是首次显示且还没有数据，则获取目标知识列表
  if (targetKnowledgeList.value.length === 0) {
    await fetchTargetKnowledge();
  }

  // 调用子组件显示目标知识
  if (manuScript.value && manuScript.value.showTargetKnowledge) {
    manuScript.value.showTargetKnowledge(targetKnowledgeList.value);
  }

  console.log('PrjManuWrapper - 显示目标知识');
};

// 获取目标知识列表
const fetchTargetKnowledge = async () => {
  if (!spuId) {
    console.warn('PrjManuWrapper - spuId 不存在，无法获取目标知识');
    return;
  }

  targetKnowledgeLoading.value = true;
  try {
    const res = await getPrjMoreInfoApi(spuId);
    if (res && res.data && (res.data as any).targetKlgs) {
      targetKnowledgeList.value = (res.data as any).targetKlgs;
      console.log('PrjManuWrapper - 获取目标知识成功:', targetKnowledgeList.value);
    } else {
      targetKnowledgeList.value = [];
      console.log('PrjManuWrapper - 无目标知识数据');
    }
  } catch (error) {
    console.error('PrjManuWrapper - 获取目标知识失败:', error);
    targetKnowledgeList.value = [];
  } finally {
    targetKnowledgeLoading.value = false;
  }
};

// 获取当前选中项标题
const currentItemTitle = inject('currentItemTitle', ref(''));

const hasPermission = ref(0);
const prjType = ref<number | undefined>(0);

// 注入购买状态，用于响应购买成功后的权限变化
const buyStatus = inject('buyStatus') as Ref<boolean>;
const props = defineProps<{ payDialogVisible: boolean }>();

// 监听标题变化
watch(
  currentItemTitle,
  (newTitle) => {
    console.log('PrjManuWrapper - 标题变化:', newTitle);
  },
  { immediate: true }
);

// const changedContent=ref()

const isSectionMenuShow = ref(false);
const manuScript = ref<InstanceType<typeof PrjManuscript>>();

// 项目的spuId
const route = useRoute();
const spuId = route.query.spuId as string;

const wordContent = ref();
const isMap = ref<boolean>(false); // 是否展示地图
const isBig = ref<boolean>(true); // 大屏小屏
// 项目的prjId
const prjId = ref();
const uniqueCode = ref();
// 项目的章节id
const curChapterId = ref();
// 项目详情
const projectDetailData = shallowRef<Chapter>();
// 章节相关的数据
const chapterList = shallowRef<Chapter[]>();
const activeIndex = ref();
// questionList已在上面声明，避免重复声明
//内容id
const contentId = ref<string | number>();
// assessmentId
const assessmentId = ref();
watch(
  () => props.payDialogVisible,
  (newVal) => {
    if (newVal == false) {
      payDialogVisible.value = false;
    }
  }
);

// 监听isMap变化，当地图关闭后重新初始化Render
watch(
  () => isMap.value,
  async (newVal, oldVal) => {
    // 当从显示地图切换到隐藏地图时，重新初始化Render
    if (oldVal === true && newVal === false) {
      // 等待DOM更新完成后再重新初始化
      await nextTick();
      setTimeout(async () => {
        await reinitializeRender();
      }, 100); // 给一点延迟确保DOM完全更新
    }
  }
);

// 处理章节变化
//当我切换到当前章节时，我需要发送老的end请求和当前章节的start请求, 结束老章节的start请求和当前章节的end请求

const { initUserBehaviour, handleChapterChange } = userBehaviour(
  PrjForm.draft,
  activeIndex,
  curChapterId
);

import type { QuestionType } from '@/types/question';

const handleChangeSectionFn = async (
  spuIdOrChapterId?: string | number,
  isProjectChange = false
) => {
  try {
    if (isProjectChange) {
      // 项目切换逻辑 - 简化版，不依赖 learningStore
      console.log('🔄 项目切换开始 - 清空旧数据');
      questionList.value = [];
      wordContent.value = '';

      const currentSpuId = (spuIdOrChapterId as string) || (route.query.spuId as string);

      // 获取项目数据
      const res2 = await getPrjDetailApi(currentSpuId, '');

      // 直接从 API 响应中获取数据，不通过 learningStore
      const projectData = res2.data;
      const currentChapter = projectData.chapterList[projectData.validIndex || 0];

      // 设置组件状态
      projectDetailData.value = currentChapter;
      assessmentId.value = currentChapter?.assessmentId;
      chapterList.value = projectData.chapterList;
      curChapterId.value = currentChapter?.chapterId;
      activeIndex.value = currentChapter?.chapterId;
      wordContent.value = currentChapter?.wordContent;
      uniqueCode.value = projectData.uniqueCode;
      prjId.value = projectData.prjId;
      contentId.value = currentChapter?.contentId;

      initUserBehaviour(curChapterId.value);
      await handleQuestionList(uniqueCode.value, curChapterId.value);
      console.log('📄 项目内容已更新');
    } else {
      // 章节切换逻辑（如果需要的话）
      const chapterId = spuIdOrChapterId as number;
      if (activeIndex.value != chapterId) {
        console.log('🔄 章节切换开始 - 清空旧数据');
        questionList.value = [];
        wordContent.value = '';

        handleChapterChange(chapterId);
        activeIndex.value = chapterId;
        const currentSpuId = route.query.spuId as string;
        const res = await getManuProjectSectionApi(currentSpuId, String(chapterId));
        console.log('处理章节变化', res);
        projectDetailData.value = res.data as any; // 使用 as any 避免类型问题
        curChapterId.value = res.data.chapterId;
        activeIndex.value = res.data.chapterId;
        contentId.value = res.data.contentId;

        wordContent.value = res.data.wordContent;
        await handleQuestionList(uniqueCode.value, curChapterId.value);
        console.log('📄 章节内容已更新');
      }
    }

    // 等待DOM更新后重新初始化Render实例
    await nextTick();
    console.log('🚀 重新初始化Render实例');
    await reinitializeRender();
    console.log('✅ 切换完成');
  } catch (error) {
    console.error('切换失败:', error);
  }

  isSectionMenuShow.value = false;
};

// 监听路由变化 - 项目切换
watch(
  () => route.query.spuId,
  async (newSpuId, oldSpuId) => {
    if (newSpuId && newSpuId !== oldSpuId) {
      console.log('路由spuId变化，重新加载项目:', newSpuId);
      // 调用统一的切换函数，标记为项目切换
      await handleChangeSectionFn(newSpuId as string, true);
    }
  }
);
onMounted(async () => {
  // 直接从路由获取 spuId，重新加载项目数据
  console.log('onMounted - 直接加载项目数据');
  const currentSpuId = route.query.spuId as string;
  await handleChangeSectionFn(currentSpuId, true);
});

onMounted(() => {
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);

  // 添加全局点击事件监听 - 使用捕获阶段
  document.addEventListener('click', handleDocumentClick as EventListener, true);
});

onBeforeUnmount(() => {
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);

  // 移除全局点击事件监听
  document.removeEventListener('click', handleDocumentClick as EventListener, true);
});

// 监听购买状态变化，实时更新权限
watch(
  () => buyStatus?.value,
  (newBuyStatus) => {
    console.log('buyStatus', newBuyStatus);
    if (newBuyStatus !== undefined) {
      hasPermission.value = newBuyStatus ? 1 : 0;
    }
  },
  { immediate: true }
);

onMounted(async () => {
  const res = await getPrjIntroduceApi({
    spuId: route.query.spuId as string
  });
  hasPermission.value = res.data.hasPermission;
  prjType.value = res.data.prjType;

  // 如果buyStatus已经注入且为true，优先使用buyStatus
  if (buyStatus?.value) {
    hasPermission.value = 1;
  }
});

// 章节信息
provide(
  'prjSectionInfo',
  computed<{ chapterId: string; prjId: string; contentId: string; uniqueCode: string }>(() => ({
    chapterId: curChapterId.value ?? '',
    uniqueCode: uniqueCode.value ?? '',
    contentId: String(contentId.value ?? ''),
    prjId: prjId.value ?? ''
  }))
);
provide(
  'assessmentId',
  computed(() => assessmentId.value)
);

// 提供isMap状态给子组件
provide('isMap', isMap);

defineExpose({
  curChapterId: curChapterId,
  payDialogVisible,
  handleChangeSectionFn
});
</script>

<style scoped src="./css/PrjManuWrapper.less"></style>
