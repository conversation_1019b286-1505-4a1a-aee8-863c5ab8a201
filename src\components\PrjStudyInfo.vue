<template>
  <!-- 项目学习信息组件：知识点数，已掌握/目标知识点数 -->
  <div class="line">
    <div class="learn-footer-info percentage"></div>
    <div v-if="!targetKlgs" class="inline">
      <div class="learn-footer-info percentage" style="color: #666666">
        <span>已掌握/全掌握/目标知识点数：</span>
        <span>{{ masteredSum }} / {{fullyMasteredSum}} / {{ knowledgeSum }}</span>
      </div>
    </div>
    <div v-else class="inline">
      <div class="learn-footer-info target">
        <span>讲解目标:</span>
      </div>
      <div v-if="targetKlgs.length <= 3" class="tags">
        <el-tooltip
          v-for="tag in targetKlgs.slice(0, 3)"
          :key="tag.id"
          effect="customized"
          :content="tag.klgTitle"
        >
          <div class="tag">
            {{ tag.klgTitle }}
          </div>
        </el-tooltip>
      </div>
      <div v-else class="tags">
        <el-tooltip
          v-for="tag in targetKlgs.slice(0, 3)"
          :key="tag.id"
          effect="customized"
          :content="tag.klgTitle"
        >
          <div class="tag">
            {{ tag.klgTitle }}
          </div>
        </el-tooltip>
        <el-popover
          v-model:visible="expand"
          placement="bottom"
          trigger="click"
          width="300"
          class="custom-popover"
        >
          <div class="popover-content tags">
            <el-tooltip
              v-for="tag in targetKlgs.slice(3)"
              :key="tag.id"
              effect="customized"
              :content="tag.klgTitle"
            >
              <div class="tag">
                {{ tag.klgTitle }}
              </div>
            </el-tooltip>
          </div>
          <template #reference>
            <div ref="more">
              <el-icon class="more"><More /></el-icon>
            </div>
          </template>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { More } from '@element-plus/icons-vue';
import { allowedNodeEnvironmentFlags } from 'process';

const props = defineProps<{
  knowledgeSum: number; // 知识点数，绝对值
  studyPercent?: number; // 学习进度，百分比
  acquaintePercent?: number; // 掌握进度，百分比
  targetKlgs?: any[];
  masteredSum?: number;
  fullyMasteredSum?: number;
}>();

//计算全掌握知识点数
const allgraspKlgPct = ref(
  props.acquaintePercent && props.knowledgeSum
    ? Math.floor((props.acquaintePercent * props.knowledgeSum) / 100)
    : 0
);
// 计算已掌握知识点数
const graspKlgPct = ref(
  props.studyPercent && props.knowledgeSum
    ? Math.floor((props.studyPercent * props.knowledgeSum) / 100)
    : 0
);

const expand = ref(false);

function handleExpandTag() {
  expand.value = !expand.value;
}

function closeExpandTag() {
  expand.value = false;
}

defineExpose({
  closeExpandTag
});

watch(
  () => [props.acquaintePercent, props.knowledgeSum],
  ([newPercent, newSum]) => {
    graspKlgPct.value = newPercent && newSum ? Math.floor((newPercent * newSum) / 100) : 0;
  }
);
</script>

<style scoped lang="less">
.line {
  // 重写element变量
  --el-color-primary: var(--color-theme-project);
  // 进度条底色
  --el-border-color-lighter: var(--color-second);
  position: relative;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--color-black);
  font-family: var(--text-family);
}

.learn-footer-info {
  display: flex;
  align-items: center;
  white-space: no-wrap;

  .wjby {
    font-size: var(--fontsize-small-project);
    font-weight: 400;
    font-family: var(--text-family);
    color: var(--color-deep);
    margin-left: 10px;
  }

  .el-progress--line {
    margin-left: 5px;
    width: 135px;
  }

  // &:deep(.el-progress-bar__outer) {
  //   background-color: rgb(211, 222, 227);
  // }
}

.inline {
  display: flex;
  flex-direction: row;
}

.float {
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
  z-index: 999;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  background-color: white;
  box-shadow: 0px 8px 5px 4px white;
}

.tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 5px;
  .tag {
    max-width: 110px;
    padding: 0px 5px;
    height: 20px;
    margin: 5px 5px;
    color: var(--color-theme-project);
    background-color: white;
    border: 1px solid var(--color-theme-project);

    display: flex;
    align-items: center;
    justify-content: center;

    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
  }
  .more {
    width: 18px;
    height: 18px;
    margin: 5px 5px;
    border-radius: 10px;
    color: var(--color-theme-project);
    background-color: white;
    border: 1px solid var(--color-theme-project);
    &:hover {
      border: 1px solid var(--color-deep);
    }
  }
}
.custom-popover {
  .el-popper {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1); /* 修改阴影 */
    border: 1px solid #dcdfe6; /* 增加边框 */
    padding: 10px; /* 增加内边距 */
  }

  .el-popper__arrow {
    border-color: transparent transparent #f4f4f9 transparent; /* 修改箭头颜色 */
  }
}

.knowledge_sum {
  display: flex;
  align-items: center;
  margin-left: 5px;
  color: var(--color-theme-project);
}

.percentage {
  margin-left: 15px;
}

.percentage {
  font-size: var(--fontsize-small-project);
  font-weight: 400;
  color: var(--color-theme-project);
  white-space: nowrap;
}

.target {
  font-size: var(--fontsize-small-project);
  font-weight: 400;
  color: var(--color-theme-project);
  margin-left: 15px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
