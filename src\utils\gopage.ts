import router from '@/router';

//跳转到登录页
export function toLogin(path: string, from?: string, type?: string) {
  console.log("tologin")
  const route = router.currentRoute.value;
  let blank = false; //是否打开的新标签
  if (path == '') path = route.fullPath;
  if (from == '/' || from == undefined) {
    blank = true;
  }
  console.log("path", path)
  setTimeout(() => {
    window.open(
      `${import.meta.env.VITE_APP_HTTP}://${
        import.meta.env.VITE_APP_USERCENTER_URL
      }/login?redirect=${import.meta.env.VITE_APP_HOST_URL}${path}&blank=${blank}&protocol=${import.meta.env.VITE_APP_HTTP}&type=${type}`,
      '_self'
    );
  }, 0);
}

//跳转到klg
export function toKlg() {
  sessionStorage.clear();
  localStorage.clear();
  const url = `${import.meta.env.VITE_APP_HTTP}://${import.meta.env.VITE_APP_KLG_URL}`;
  setTimeout(() => {
    window.open(url, '_self');
  }, 0);
}

//跳转到PRJ
export function toPRJ() {
  sessionStorage.clear();
  localStorage.clear();
  const url = `${import.meta.env.VITE_APP_HTTP}://${import.meta.env.VITE_APP_PRJ_URL}`;
  setTimeout(() => {
    window.open(url, '_self');
  }, 0);
}

//跳转到EXER
export function toExer() {
  sessionStorage.clear();
  localStorage.clear();
  const url = `${import.meta.env.VITE_APP_HTTP}://${import.meta.env.VITE_APP_EXER_URL}`;
  setTimeout(() => {
    window.open(url, '_self');
  }, 0);
}

//跳转到用户信息页
export function toUserInfo() {
  sessionStorage.clear();
  localStorage.clear();
  setTimeout(() => {
    window.open(
      `${import.meta.env.VITE_APP_HTTP}://${import.meta.env.VITE_APP_USERCENTER_URL}/user/information`,
      '_self'
    );
  }, 0);
}

//跳转到admain
export function toADMIN() {
  sessionStorage.clear();
  localStorage.clear();
  const url = `${import.meta.env.VITE_APP_HTTP}://${import.meta.env.VITE_APP_ADMIN_URL}`;
  console.log(url);
  setTimeout(() => {
    window.open(url, '_self');
  }, 0);
}
