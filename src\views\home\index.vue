<template>
  <div v-if="isLogined">
    <!-- 消息推送 -->
    <MessageTip></MessageTip>
    <!-- 学习之旅-->
    <PersonalProfile></PersonalProfile>
    <!-- 正在学习 -->
    <LearningCard></LearningCard>
  </div>
  <!--<div class="picture" v-else>
     //<img style="width:1400px" src="@/assets/images/knowledge/knowledgeindex.gif" />
    <ksg_window></ksg_window>
  </div>-->
  <!-- 案例讲解 -->
  <CaseLearn></CaseLearn>
  <!-- 知识讲解 -->
  <KlgLearn></KlgLearn>
  <!-- 知识测评 todo 接口还没写-->
  <!-- <KlgTest></KlgTest> -->

  <BackTop></BackTop>
</template>

<script setup lang="ts">
import BackTop from '@/components/Backtop.vue';
import LearningCard from './components/LearningCard.vue';
import CaseLearn from './components/CaseLearn.vue';
import MessageTip from './components/MessageTip.vue';
import PersonalProfile from './components/PersonalProfile.vue';
import KlgLearn from './components/KlgLearn.vue';
import KlgTest from './components/KlgTest.vue';
import ksg_window from '@/components/ksg_window.vue';
import checkLoginStatus from '@/utils/checkLoginStatus';
const isLogined = checkLoginStatus();
</script>

<style scoped lang="less">
.picture {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
