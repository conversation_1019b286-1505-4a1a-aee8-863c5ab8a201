<!-- 是否变蓝，有锁没锁的状态全部在这个页面进行判断 -->
<!-- 变蓝是已学，用islearned 判断
是否有锁——>未购买非试学有锁， -->

<template>
  <div
    class="item"
    :class="lessonInfo.isLearned ? 'learned' : ''"
    :style="!lessonInfo.isTry && isBuy == BuyStatus.nobuy ? 'cursor:default' : ''"
  >
    <!-- :style="lessonInfo.isTry || lessonInfo.isTry == undefined ? '' : 'cursor:default'"> -->
    <div class="title">{{ lessonInfoIndex }}&nbsp;{{ lessonInfo.chapterName }}</div>
    <div class="play-icon">
      <!-- 此处可优化 可以将路径改为变量传入 不过可能存在一些问题 -->
      <!-- 根据是否购买进行分块 -->
      <!-- 已经买了 -->
      <div v-if="isBuy == BuyStatus.bought">
        <!-- 文稿类型的内容 -->
        <div v-if="type == PrjForm.draft">
          <el-icon><Document /></el-icon>
        </div>
        <!-- 视频类型的内容 -->
        <div v-else>
          <el-icon><VideoPlay /></el-icon>
        </div>
      </div>
      <!-- 未购买部分 -->
      <div v-else>
        <!-- 文稿类型 -->
        <div v-if="type == PrjForm.draft">
          <!-- istry 0代表不可看，1代表可看 -->
          <!-- 不可试看的情况 -->
          <!-- <img src="@/assets/images/prjlearn/lock.svg" alt="" /> -->
          <el-icon v-if="!lessonInfo.isTry"><Lock /></el-icon>
          <!-- 可试看 -->
          <el-icon v-else><Document /></el-icon>
        </div>
        <!-- 视频类型 -->
        <div v-else>
          <!-- 不可试看的情况 -->
          <!-- <img v-if="!lessonInfo.isTry" src="@/assets/images/prjlearn/lock.svg" alt="" /> -->
          <el-icon v-if="!lessonInfo.isTry"><Lock /></el-icon>
          <!-- 可试看 -->
          <el-icon v-else><VideoPlay /></el-icon>
          <!-- <img v-if="lessonInfo.isLearned" src="@/assets/images/prjlearn/played.svg" alt="" />
          <img v-else src="@/assets/images/prjlearn/play.svg" alt="" /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PrjForm } from '@/types/project';
import { BuyStatus } from '@/types/goods';
import type { LessonInfoType } from '@/types/case';
// 抽离学习小组件 方便样式调整和跳转处理
const props = defineProps<{
  lessonInfo: LessonInfoType;
  isBuy: BuyStatus;
  type: PrjForm;
  lessonInfoIndex?: number;
}>();

console.log('items info', props.lessonInfo);
</script>
<style lang="less" scoped>
.item {
  height: 30px;
  width: 170px;
  color: var(--color-black);
  background-color: #f2f2f2;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-left: 5px;
  padding-right: 5px;
  font-size: var(--fontsize-middle-project);
  font-family: var(--text-family);
  font-weight: 400;

  .select-icon {
    width: 12px;
    height: 12px;
  }

  .title {
    width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.learned {
  background-color: var(--color-theme-project);
  color: white;
}
</style>
