import { ElementType, parseDocument } from 'htmlparser2';
import { Document, type ChildNode, type Text } from 'domhandler';
import { render } from 'dom-serializer';
import { trim } from 'lodash-es';

export function filterHtmlString(htmlString: string) {
  const dom = parseDocument(htmlString, {
    decodeEntities: false
  });
  let visibleLength = 0;
  const renderList: Array<{
    content: string;
    isTag: boolean;
  }> = [];
  const dfs = (node: ChildNode) => {
    if (node.type == ElementType.Text) {
      const textContent = trim(node.data);
      for (let i = 0; i < textContent.length; ++i) {
        if (textContent[i] == '&') {
          const end = textContent.indexOf(';', i);
          if (end == -1) {
            throw new Error('转义字符异常');
          }
          renderList.push({
            content: textContent.slice(i, end + 1),
            isTag: false
          });
          visibleLength++;
          i = end;
        } else {
          renderList.push({
            content: textContent[i],
            isTag: false
          });
          visibleLength++;
        }
      }
    } else if (node.type == ElementType.Script) {
      renderList.push({
        content: katex.renderToString(node.children[0].data),
        isTag: false
      });
      visibleLength++;
    } else if (node.type == ElementType.Tag) {
      if (node.tagName == 'img') {
        renderList.push({
          content: `<img src="${node.attribs.src}" />`,
          isTag: false
        });
        visibleLength++;
      } else {
        renderList.push({
          content: `<${node.tagName}>`,
          isTag: true
        });
        for (const child of node.children) {
          dfs(child);
        }
        if (!['br', 'hr'].includes(node.tagName)) {
          renderList.push({
            content: `</${node.tagName}>`,
            isTag: true
          });
        }
      }
    } else {
      for (const child of (node as Document).children) {
        dfs(child);
      }
    }
  };
  dfs(dom);
  return { renderList, visibleLength };
}

/**
 * 根据传入的 html 字符串和最大可见字符数，返回截断后的渲染字符串。
 * 保证 html 标签结构不被破坏，超出部分以 ... 结尾。
 * @param htmlString 需要处理的 html 字符串
 * @param maxLength 最大可见字符数（不包括 html 标签），默认 20
 * @returns 截断后的 html 字符串
 */
export function getRenderString(htmlString: string, maxLength: number = 20) {
  // 通过 filterHtmlString 解析 html 字符串，得到渲染列表和可见字符数
  const { renderList, visibleLength } = filterHtmlString(htmlString);
  console.log(renderList); // 调试用，输出渲染列表

  // 如果可见字符数小于等于最大长度，直接拼接所有内容返回
  if (visibleLength <= maxLength) {
    // 将渲染列表中的内容拼接成字符串
    const res = renderList.map((item) => item.content).join('');
    console.log(res); // 调试用，输出结果
    return res;
  } else {
    // 需要截断，拼接前 maxLength 个可见字符（跳过标签）
    let count = 0; // 当前已拼接的可见字符数
    let result = ''; // 最终结果字符串
    for (let i = 0; i < renderList.length && count < maxLength; i++) {
      result += renderList[i].content; // 拼接内容（包括标签和文本）
      if (!renderList[i].isTag) {
        // 只有非标签内容才计数
        count++;
      }
    }
    // 拼接省略号，表示内容被截断
    const res = result + '...';
    console.log(res); // 调试用，输出结果
    return res;
  }
}
