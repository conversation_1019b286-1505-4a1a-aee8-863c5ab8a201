import { http } from '@/apis';

// 获取会员订单或者资料订单列表
export function getShoppinglistApi(
  current?: number,
  limit?: number,
  order_type?: number,
  orderNumberOrTitle?: string
) {
  return http.request<{
    records: Array<{}>;
    total: number;
    size: number;
    current: number;
    orders: any[];
    optimizeCountSql: boolean;
    hitCount: boolean;
    countId: string;
    maxLimit: number;
    searchCount: boolean;
    pages: number;
  }>({
    method: 'post',
    url: '/orderList/shoppinglist',
    data: {
      current,
      limit,
      orderType: order_type,
      orderNumberOrTitle
    }
  });
}
