import { BuyStatus, CardType, GoodsType } from './goods';
import { type Klg } from './knowledge';
import type { LessonInfoType } from './case';
export enum PrjForm {
  video = 1,
  draft = 2,
  domain = 3 //会员用 不用不用
}

// 学习类型
export enum PrjType {
  klgExplain = 1, //知识讲解
  case = 2, // 案例学习
  exam = 3, // 知识测评
  area = 4, // 领域学习
  hotArea = 5, //热门领域
}

export interface CaseInfo {
  studyInstructions: string;
  klgCount: number;
  latestChapterId: number;
  prjType: PrjType;
  studyTime: number;
  chapterList: LessonInfoType[];
  learned: number;
  cardType: CardType;
  description: string;
  title: string;
  userName: string;

  goodsType: GoodsType;
  prjForm: PrjForm;
  price: string;
  buyStatus: BuyStatus;
  spuId: string;
  coverPic: string;
  userCoverPic: string;
  graspKlg: number;
  skuId: string;
}
export interface KlgExplainInfo {
  studyInstructions: string;
  klgCount: number;
  prjType: PrjType;
  targetKlgs: Klg[];
  learned: number;
  validDate: number;
  description: string;
  title: string;
  userName: string;
  goodsType: GoodsType;
  prjForm: PrjForm;
  price: string;
  buyStatus: BuyStatus;
  spuId: string;
  coverPic: string;
  userCoverPic: string;
  graspKlg: number;
  skuId: string;
}
export interface ExamInfo {
  studyInstructions: string;
  klgCount: number;
  prjType: PrjType;
  targetKlgs: Klg[];
  learned: number;
  validDate: number;
  description: string;
  title: string;
  userName: string;
  goodsType: GoodsType;
  total: number;
  prjForm: PrjForm;
  currentCount: number;
  buyStatus: BuyStatus;
  spuId: string;
  coverPic: string;
  userCoverPic: string;
  graspKlg: number;
  skuId: string;
  price: string;
}

//todo:缺领域讲解的数据类型

export type PrjInfo = CaseInfo | KlgExplainInfo | ExamInfo;
