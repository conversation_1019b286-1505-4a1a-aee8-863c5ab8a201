import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import { visualizer } from 'rollup-plugin-visualizer';
import type { PluginOption } from 'vite';
import mkcert from 'vite-plugin-mkcert';
import TurboConsole from 'unplugin-turbo-console/vite';
import viteCompression from 'vite-plugin-compression';
import vueSetupExtend from 'vite-plugin-vue-setup-extend';
import { codeInspectorPlugin } from 'code-inspector-plugin';
// import cdn from 'vite-plugin-cdn-import';
// import postCssPxToRem from 'postcss-pxtorem';

// https://vitejs.dev/config/
export default defineConfig({
  base: '/', // 设置公共路径
  plugins: [
    vue(),
    codeInspectorPlugin({
      bundler: 'vite',
      editor: 'code'
    }),
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      resolvers: [ElementPlusResolver()]
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    }),
    visualizer({
      gzipSize: true, // 搜集gzip压缩包的大小到图表
      brotliSize: true, // 搜索brotli压缩包的大小到图表
      emitFile: false, // 设置为true，分析文件在dist目录下
      filename: 'bundle.html', //分析图生成的文件名
      open: true //如果存在本地服务端口，将在打包后自动展示
    }) as PluginOption,
    mkcert(),
    viteCompression({
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz',
      deleteOriginFile: true
    }),
    vueSetupExtend()
    // TurboConsole({
    //   extendedPathFileNames: ['index']
    // })
    // cdn({
    //   modules: [
    //     {
    //       name: 'vue',
    //       var: 'Vue',
    //       path: 'https://cdn.bootcdn.net/ajax/libs/vue/3.4.33/vue.global.min.js'
    //     },
    //     {
    //       name: 'vue-router',
    //       var: 'VueRouter',
    //       path: 'https://cdn.bootcdn.net/ajax/libs/vue-router/4.4.0/vue-router.global.min.js'
    //     },
    //     {
    //       name: 'jquery',
    //       var: '$',
    //       path: 'https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js'
    //     },
    //     {
    //       name: 'luxon',
    //       var: 'luxon',
    //       path: 'https://cdn.bootcdn.net/ajax/libs/luxon/3.4.4/luxon.min.js'
    //     },
    //     {
    //       name: 'axios',
    //       var: 'axios',
    //       path: 'https://cdn.bootcdn.net/ajax/libs/axios/1.7.2/axios.min.js'
    //     },
    //     {
    //       name: 'element-plus',
    //       var: 'ElementPlus',
    //       path: 'https://cdn.bootcdn.net/ajax/libs/element-plus/2.7.7/index.full.min.js'
    //     }
    //   ],
    //   enableInDevMode: true
    // })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    proxy: {
      '/champaign-service': {
        target: 'http://**************:8001', // 测试环境调试
        secure: false,
        changeOrigin: true
      },
      '/auth-service': {
        target: 'http://**************:8001', // 测试环境调试
        secure: false,
        changeOrigin: true
      }
    },
    https: true,
    allowedHosts: ['xxx.endlessorigin.com', 'test.endlessorigin.com']
  },

  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        keep_infinity: true,
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {}
  }
  // css: {
  //   postcss: {
  //     plugins: [
  //       postCssPxToRem({
  //         rootValue: 192.0,
  //         propList: ['*'],
  //         unitPrecision: 3,
  //         mediaQuery: false
  //       })
  //     ]
  //   }
  // }
});
