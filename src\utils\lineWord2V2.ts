// 不带span的版本，可以实现table局部划词，大小写匹配, will be used in the future
import { clone, throttle } from 'lodash-es';
import { ElMessage } from 'element-plus';
import { escape } from 'lodash-es';

const throwErr = throttle((msg: string) => {
  ElMessage.error(msg);
}, 5000);

function handleLineWord(): null | string | undefined {
  const selection = window.getSelection();
  if (!selection || selection.isCollapsed) {
    return null;
  }
  const range = selection.getRangeAt(0);
  const htmlContent = document.getElementById('htmlContent');
  const lineWordHtmlContents = document.getElementsByClassName('lineWordContent');

  let cloneContents;
  let node: Node | HTMLElement | null = range.commonAncestorContainer;
  let str;
  if (node.nodeType == Node.TEXT_NODE) {
    node = node.parentElement;
  }
  console.dir(node);
  if ((node as HTMLElement)?.closest("[class^='inline-equation']")) {
    cloneContents = (node as HTMLElement).closest("[class^='inline-equation']");
    const latexCode = (cloneContents as HTMLLegendElement).getAttribute('latexCode');
    str = `<script type="math/tex">${latexCode}</script>`;
  } else if ((node as HTMLElement)?.closest("[class^='equation']")) {
    cloneContents = (node as HTMLElement).closest("[class^='equation']");
    const latexCode = (cloneContents as HTMLLegendElement).getAttribute('latexCode');
    str = `<script type="math/tex; mode=display">${latexCode}</script>`;
  } else {
    cloneContents = range.cloneContents();
    str = buildLineWord(cloneContents);
    // console.log(str, str.length);
  }
  console.log(str);
  if (str.trim().length == 0) {
    return null;
  }

  let flag = false;
  for (let i = 0; i < lineWordHtmlContents.length; i++) {
    if (lineWordHtmlContents[i]?.contains(range.commonAncestorContainer)) {
      flag = true;
    }
  }
  console.log('2V2_lineWordHtmlContents', lineWordHtmlContents);
  if (!flag) {
    throwErr('请在内容区域内划词');
    return null;
  }
  // if (!htmlContent?.contains(range.commonAncestorContainer)) {
  //   throwErr("请在内容区域内划词")
  //   return null
  // }
  return str;
}

/**
 * 构建行内单词的HTML字符串
 * @param node - 要处理的DOM节点
 * @returns 处理后的HTML字符串
 */
function buildLineWord(node: Node): string {
  let str = ''; // 用于存储最终的HTML字符串

  /**
   * 深度优先搜索遍历DOM树
   * @param node - 当前处理的节点
   */
  const dfs = (node: Node) => {
    if (node.nodeType == Node.TEXT_NODE) {
      // 处理文本节点
      const textContent = node.textContent as string;
      // 对文本内容进行转义，保留原始格式（包括换行符等）
      str += escape(textContent);
    } else if (node.nodeType == Node.ELEMENT_NODE) {
      // 处理元素节点
      if ((node as HTMLElement).tagName == 'IMG') {
        // 对于图片元素，直接添加其outerHTML
        str += (node as HTMLElement).outerHTML;
        console.log('imgstr', str);
      } else {
        // 处理数学公式
        if ((node as HTMLElement).closest("[class^='inline-equation']")) {
          // 处理行内公式
          const element = (node as HTMLElement).closest("[class^='inline-equation']");
          const latexCode = element?.getAttribute('latexCode') as string;
          str += `<script type="math/tex">${latexCode}</script>`;
        } else if ((node as HTMLElement).closest("[class^='equation']")) {
          // 处理块级公式
          const element = (node as HTMLElement).closest("[class^='equation']");
          const latexCode = element?.getAttribute('latexCode') as string;
          str += `<script type="math/tex; mode=display">${latexCode}</script>`;
        } else {
          // 对于其他元素，递归处理其子节点
          for (const child of (node as HTMLElement).childNodes) {
            dfs(child);
          }
        }
      }
    }
  };

  // 遍历输入节点的所有子节点
  for (const childNode of node.childNodes) {
    dfs(childNode);
  }

  return str; // 返回处理后的HTML字符串
}

export { handleLineWord };
