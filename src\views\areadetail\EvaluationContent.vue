<template>
  <div class="content-section">
    <div class="card">
      <div class="left">
        <div class="top-controls">
          <div class="radio-group">
            <el-radio-group v-model="mode" @change="handleModeChange(mode)">
              <el-radio :label="1">边学边测</el-radio>
              <el-radio :label="2">只看讲解</el-radio>
              <el-radio :label="3">只做测评</el-radio>
            </el-radio-group>
          </div>
          <div class="header-arrows">
            <div class="label-wrapper" style="margin-right: 15px">
              <el-icon :class="{ disabled: isFirstItem }" @click="changeProject('left')">
                <ArrowLeft />
              </el-icon>
              <span
                class="nav-label"
                :class="{ disabled: isFirstItem }"
                @click="changeProject('left')"
                >上一个</span
              >
            </div>
            <div class="label-wrapper">
              <span
                class="nav-label"
                :class="{ disabled: isLastItem }"
                @click="changeProject('right')"
                >下一个</span
              >
              <el-icon :class="{ disabled: isLastItem }" @click="changeProject('right')">
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </div>

        <template v-if="ready">
          <template v-if="isPrj">
            <PrjManuWrapper
              v-if="prjForm == PrjForm.draft"
              :prjInfo="prjInfo"
              :targetKlgs="targetKlgs"
              :prjTargetObj="prjTargetObj" />
            <PrjVideoWrapperNew
              v-else
              @move="mve"
              :prjInfo="prjInfo"
              :targetKlgs="targetKlgs"
              :prjTargetObj="prjTargetObj"
          /></template>
          <template v-else>
            <!-- <DraftExam v-if="showType == ShowType.draft" :state="state"></DraftExam> -->
            <ExerciseWrapper
              v-if="!isPrj"
              :exerciseInfo="exerciseInfo"
              :isLastItem="isLastItem"
              @toNext="changeProject('right')"
            ></ExerciseWrapper>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PrjType } from '@/types/project';
import { PrjForm } from '@/types/project';
import type { PrjinfoItf } from '@/types/learning';
import type { ExerciseType, exerciseItem } from '@/types/exercise';
import PrjManuWrapper from '@/views/areadetail/PrjManuWrapper.vue';
import PrjVideoWrapperNew from '@/views/areadetail/PrjVideoWrapperNew.vue';
import DraftExam from '@/views/exam/components/DraftExam.vue';
import ExerciseWrapper from '@/views/areadetail/ExerciseWrapper.vue';
import anime from 'animejs/lib/anime.es.js';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { useLearningStore } from '@/stores/learning';
import { getPartProApi, getPrjDetailApi, getPrjMoreInfoApi } from '@/apis/learning';
import { getIdListApi } from '@/apis/area';
import { getExerciseApi } from '@/apis/exercise';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

const ready = ref(false);
const spuId = ref('0');
const exerciseId = ref('0');
const chapterId = ref('0');
const learningStore = useLearningStore();

const prjInfo = ref<PrjinfoItf>({});
const prjForm = ref<PrjForm>();
const prjType = ref<PrjType>();
const exerciseInfo = ref<exerciseItem>({
  exerciseId: '',
  stem: '',
  explanation: '',
  content: [],
  answer: '',
  type: 0
});

const ids = ref<string[]>([]); // 用于存储获取到的所有项目ID
const currentIndex = ref(0); // 当前选中的项目索引
const isFirstItem = computed(() => currentIndex.value == 0); // 判断是否是第一个项目
const isLastItem = computed(() => currentIndex.value == ids.value.length - 1); // 判断是否是最后一个项目

//模式单选框
const mode = ref(1);
function handleModeChange(value: number) {
  currentIndex.value = 0;
  loadProjects(value);
}

const tomove = ref(false);

// const descriptionVisible = ref(false);
// const tagsVisible = ref(false);
const drawerControllerStore = useDrawerControllerStore();

const router = useRouter();
const route = useRoute();
const klgCode = inject('klgCode') as string;
const areaCode = inject('areaCode') as string;

provide('prjInfo', prjInfo);
provide('prjForm', prjForm);
provide('prjType', prjType);

const useWideScreen = inject('useWideScreen') as Ref<boolean>;

const prjTargetObj = ref<{
  description: string;
  purpose: string;
}>({ description: '', purpose: '' }); // 项目描述和项目目标

const klg = ref<{
  klgNumbers: number;
  learned: number;
  graspKlg: number;
}>({ klgNumbers: 0, learned: 0, graspKlg: 0 });
provide('klg', klg);

const targetKlgs = ref<
  [
    {
      klgCode: string;
      klgTitle: string;
      choose: boolean;
    }
  ]
>([{ klgCode: '', klgTitle: '', choose: false }]); // 目标知识点

async function mve(mode: boolean) {
  if (mode) {
    tomove.value = true;
  } else {
    tomove.value = false;
  }
}

//做判断区分项目、测评
const isPrj = ref<boolean>(true);
function separateIds(id: string) {
  if (/^\d+$/.test(id)) {
    isPrj.value = false; //测评
  } else if (/^P\d+$/.test(id)) {
    isPrj.value = true; //讲解
  }
}

async function handlePrj(spuId: string) {
  const res = await getPrjDetailApi(spuId);
  learningStore.setInfo(res.data);
  chapterId.value = res.data.chapterList[0].chapterId;
  learningStore.chapterId = learningStore.chapterList[0].chapterId;

  //获取知识讲解（视频/文稿）基本信息
  const res1 = await getPartProApi(spuId);
  prjInfo.value = res1.data.list[0];
  prjForm.value = prjInfo.value.prjForm;
  prjType.value = prjInfo.value.prjType;
  const res2 = await getPrjMoreInfoApi(spuId); //获取小header
  prjTargetObj.value = res2.data.prj;
  klg.value = res2.data.klg;
  targetKlgs.value = res2.data.targetKlgs;

  //讲解类有章节列表
  if (true) {
    router.replace({
      query: {
        ...route.query,
        chapterId: chapterId.value,
        spuId: spuId
      }
    });
  }
}

async function handleExercise(exerciseId: string) {
  const res = await getExerciseApi(exerciseId);
  exerciseInfo.value = res.data;
}

async function loadProjects(tagId: number) {
  //todo:接口需要改 改为areaCode
  const resList = await getIdListApi(areaCode, tagId); // 获取项目ID列表
  ids.value = resList.data;

  if (ids.value.length > 0) {
    const id = ids.value[currentIndex.value];
    separateIds(id);
    if (isPrj.value == true) {
      //讲解项目
      exerciseId.value = '';
      spuId.value = id;
      await handlePrj(spuId.value);
    } else {
      //测评
      spuId.value = '';
      exerciseId.value = id;

      handleExercise(exerciseId.value);
    }
  }
}

function changeProject(direction: 'left' | 'right') {
  if (direction == 'left' && currentIndex.value > 0) {
    currentIndex.value--; // 切换到上一个项目
  } else if (direction == 'right' && currentIndex.value < ids.value.length - 1) {
    currentIndex.value++; // 切换到下一个项目
  }
  const id = ids.value[currentIndex.value];
  separateIds(id);

  if (isPrj.value == true) {
    //讲解项目
    // console.log('prj!!!', isPrj);
    exerciseId.value = '';
    spuId.value = id;
    handlePrj(spuId.value);
  } else {
    //测评
    // console.log('exercise!!!', isPrj);
    spuId.value = '';
    exerciseId.value = id;
    handleExercise(exerciseId.value); //todo:没写
  }
}

onMounted(async () => {
  loadProjects(mode.value);

  ready.value = true;
  useWideScreen.value = true;
});

onBeforeUnmount(() => {
  useWideScreen.value = false;
});

//const header = ref();

// function handleClose(event: MouseEvent) {
//   if (header.value.contains(event.target as HTMLElement)) {
//     return;
//   }
//   descriptionVisible.value = false;
//   tagsVisible.value = false;
// }

// onMounted(() => {
//   document.addEventListener('click', handleClose);
//   watch(
//     () => descriptionVisible.value || tagsVisible.value,
//     () => {
//       if (descriptionVisible.value || tagsVisible.value) {
//         anime({
//           targets: header.value,
//           backgroundColor: {
//             value: '#F2F2F2',
//             duration: 300,
//             easing: 'linear'
//           }
//         });
//       } else {
//         anime({
//           targets: header.value,
//           backgroundColor: {
//             value: '#FFFFFF',
//             duration: 300,
//             easing: 'linear'
//           }
//         });
//       }
//     }
//   );
// });
// onBeforeUnmount(() => {
//   document.removeEventListener('click', handleClose);
// });
</script>

<style scoped>
.content-section {
  padding: 10px;
  .card {
    /* width: var(--width-fixed--project); */
    width: 100%;
    height: 620px;
    /* padding-left: var(--padding-box);
    padding-right: var(--padding-box);
    padding-top: 10px; */
    margin: 0 auto;
    background-color: #f2f2f2;
    .left {
      .top-controls {
        display: flex;
        background-color: #ffffff;
      }
      .radio-group {
        background-color: #ffffff;
      }
      .left-header {
        background-color: #f2f2f2;
        margin-left: 10px;
        width: 668px;
        /* width: calc(61.8vw - 10px); */
        &.big {
          margin: 0 auto;
        }
        .title {
          font-size: 18px;
          font-weight: 700;
          margin: 10px 0px 10px 0;
        }
        .base-info {
          font-size: 12px;
          display: flex;
          align-items: center;
          color: var(--color-deep);
          .creater {
            display: flex;
            align-items: center;
            .avatar {
              width: 15px;
              height: 15px;
              border-radius: 50%;
              margin-right: 5px;
            }
            .name {
            }
          }
          .time {
            margin: 0 10px;
          }
          .function-tag {
            margin: 0 10px;
            padding: 0 10px;
            border-radius: 10px;
            border: 1px solid var(--color-deep);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: break-all;

            &:hover {
              background-color: var(--color-inactive-project);
              cursor: pointer;
            }
          }
        }
      }

      .left-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        width: calc(61.8vw - 10px);
        display: flex;
        &.big {
          height: calc(100vh - 160px);
        }

        .video-title {
          padding-left: 10px;
          padding-right: 10px;
          position: relative;
          bottom: 0px;
          background-color: #f2f2f2;
          height: 63px;
          font-size: 24px;
          font-weight: 400;
          color: var(--color-black);
          font-family: var(--title-family);
          display: flex;
          justify-content: space-between;
          align-items: center;
          .btn {
            background-color: white;
            border-radius: 14px;
            height: 28px;
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 10px;
            border: 1px solid var(--color-theme-project);
            color: var(--color-theme-project);
            font-family: var(--text-family);
            &:hover {
              background-color: var(--color-theme-project);
              color: white;
              cursor: pointer;
            }
          }
          .icon {
            cursor: pointer;
          }

          .icon-wrapper {
            width: 16px;
            height: 12px;
            margin: 0 5px;
            background: url('@/assets/svgs/u2985.svg') no-repeat center/contain;
            cursor: pointer;

            &:hover {
              background-image: url('@/assets/svgs/u4176.svg');
            }
          }
        }

        .video {
          /* background-color: rgb(242, 242, 242); */
          flex: 1;
          position: relative;

          .coverPic-wrapper {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--color-black);
            background-repeat: no-repeat;
            background-size: cover;
            /* background-size: 100% auto; */
          }

          .video-btn-wrapper {
            width: 50px;
            height: 50px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
          }

          .expand-logo {
            position: absolute;
            right: 10px;
            /* bottom: 10px; */
            cursor: pointer;

            &:hover {
              font-weight: 500;
            }
          }
        }

        .video-footer-info {
          height: 40px;
          display: flex;
          align-items: center;
          background-color: #ffffff;
          box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
          border: 1px solid rgb(242, 242, 242);
          padding-left: 10px;
          width: 100%;
          position: relative;

          .video-footer {
            vertical-align: middle;
            transform: translateY(-3px);
          }

          .footer-logo-wrapper {
            width: 90%;
            display: flex;
            align-items: center;
            position: absolute;
          }

          .footer-title {
            font-size: 18px;
            font-weight: 300;
            color: var(--color-black);
            margin-left: 17px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .header-arrows {
        background-color: #ffffff;
        display: flex;
        align-items: center;
        flex: 1;
        margin-left: 735px;
        .el-icon {
          margin-left: 10px;
          cursor: pointer;
          align-items: center;
        }
        .el-icon svg {
          align-items: center;
        }

        .nav-label {
          font-size: 14px;
          margin: 0 5px;
          border-radius: 5px;
          align-items: center;
        }
        .label-wrapper {
          display: flex;
          align-items: center;
          height: 32px;
          text-align: center;
        }
        .label-wrapper:hover {
          background-color: var(--color-second);
          border-radius: 3px;
        }

        .disabled {
          pointer-events: none;
          color: #ccc;
        }
      }
    }

    .info {
      /* transition: height 0.2s ease-in-out; */
      height: 70px;
      width: 100%;
      background-color: white;
      display: flex;
      margin-bottom: 10px;
      /* justify-content: space-between; */
    }
  }

  @media screen and (max-width: 1200px) {
    .card {
      width: 100%;
      padding-left: 20px;
      padding-right: 20px;
    }

    .tags-wrapper {
      max-width: 50% !important;
    }
  }
}
</style>
