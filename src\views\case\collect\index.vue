<template>
  <div class="collect">
    <div class="collectup">
      <div class="backbuttom" @click="back">返回</div>
      <div class="collecttitle">收藏夹</div>
    </div>
    <div class="collectdown">
      <div class="collectdown_title">
        <div class="projectname" v-html="collectdetail.projectTitle"></div>
        <div class="left_operate">
          <span class="hover_style" @click="removewindow()">移出收藏夹</span>
          <!-- 这里需要用v-if判断是否显式的给出上一个 -->
          <span class="hover_style" v-if="preId != null" @click="last()">上一个</span>
          <span class="hover_style" v-if="nextId != null" @click="next()">下一个</span>
        </div>
      </div>
      <div class="collectdown_detail">
        <div class="problemtitle" v-html="collectdetail.sectionTitle"></div>
        <div class="problem">
          <CollectCard :questionObj="collectdetail"></CollectCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CollectCard from '@/components/CollectCard.vue';
import type { documentData, collectData } from '@/types/data';
import { getCollectDetailDataApi, removeCollectDetailDataApi } from '@/apis/collect';
import { useLearningStore } from '@/stores/learning';

const learningStore = useLearningStore();
const router = useRouter();
const route = useRoute();
//这里后续可能需要传递参数！！！回退都上一页
const back = () => {
  router.push({
    path: '/case/exam',
    query: {
      spuId: route.query.spuId
    }
  });
};
//必须规范一下数据接口，显示的写出来，否则会出现无法渲染的问题
const nextId = ref();
const preId = ref();
const collectdetail = ref<documentData>({
  projectId: '',
  examId: 0, //测试题目id
  sectionId: 0, //小节id
  order: 0, //题目序号
  sectionTitle: '', //小节名称
  examType: 0, //1.填空 2选择 3判断 4问答
  examTitle: '', //题目
  examChoices: [''], //选项
  examAnswer: '', //答案
  myAnswer: '',
  examExplanation: '', //解释说明
  questionList: [
    {
      questionId: 0, //问题自增id
      associatedWords: '', //关联文本内容
      keyword: '', //关键字内容
      questionType: '', //问题类型，是什么、为什么、怎么做
      questionNecessity: 0, //问题必要性 1必须问题，2参考问题
      creatorId: '', //创建者id
      creatorName: '', //创建者名字
      createTime: '', //创建时间
      explanation: '',
      answerList: [
        {
          answerId: 0, //回答自增id
          answerKlgs: [
            {
              klgId: 0,
              klgTitle: ''
            }
          ], //知识点名称列表
          answerExplanation: '', //解释
          createTime: '', //回答时间
          creatorName: '', //回答者名称
          modifiedTime: '' //修改时间
        }
      ]
    }
  ]
});

//获取题目的详细数据
const getCollectData = async (favoriteId: string) => {
  const res = await getCollectDetailDataApi({
    favoriteId
  });
  collectdetail.value = res.data.documentData;
  nextId.value = res.data.nextId;
  preId.value = res.data.preId;
  learningStore.uniqueCode = res.data.documentData.uniqueCode;
  learningStore.chapterId = res.data.documentData.chapterId;
  learningStore.contentId = res.data.documentData.contentId;
};
onMounted(async () => {
  await getCollectData(route.query.favoriteId as string);
});
const next = async () => {
  router.replace({
    query: {
      ...route.query,
      favoriteId: nextId.value
    }
  });
  await getCollectData(nextId.value);
};
const last = async () => {
  router.replace({
    query: {
      ...route.query,
      favoriteId: preId.value
    }
  });
  await getCollectData(preId.value);
};
//移除题目
//移除以后需要进行判断，如果有下一个就展示下一个页面，如果没有下一个就展示上一个页面，同时也要切换localstorage
const remove = async () => {
  await removeCollectDetailDataApi({
    examId: route.query.favoriteId as string
  });
  if (nextId.value != null) {
    //如果有下一个则直接跳转到下一个页面，如果没有下一个则跳转到上一个页面
    router.replace({
      query: {
        ...route.query,
        favoriteId: nextId.value
      }
    });
    await getCollectData(nextId.value);
  } else {
    router.replace({
      query: {
        ...route.query,
        favoriteId: preId.value
      }
    });
    await getCollectData(preId.value);
  }
};
//弹窗
const removewindow = () => {
  ElMessageBox.confirm('是否将当前题目移出收藏夹', '移出收藏夹', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await remove();
      ElMessage({
        type: 'success',
        message: '已成功移出收藏夹'
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消当前操作'
      });
    });
};
</script>

<style lang="less" scoped>
.collect {
  width: 1100px;
  // height: 600px;
  margin-left: 10px;
  padding-left: 10px;
  padding-right: 18px;
  display: flex;
  flex-direction: column;

  .collectup {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-top: 8px;

    .backbuttom {
      width: 80px;
      height: 56px;
      background-color: #005579;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.996);
      line-height: normal;
      font-feature-settings: 'kern';
      border-width: 0.8px;
      border-style: solid;
      padding-top: 20px;
      padding-left: 26px;
      // font-size: var(--fontsize-large-project);
      cursor: pointer;
    }

    .backbuttom:hover {
      border-color: var(--color-theme-project);
      background-color: var(--color-second);
      color: var(--color-theme-project);
    }

    .collecttitle {
      font-size: 18px;
      font-weight: 700;
      color: #333333;
      line-height: normal;
      font-feature-settings: 'kern';
      margin-left: 16px;
    }
  }

  .collectdown {
    margin-left: 50px;
    margin-top: 22px;

    .collectdown_title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .projectname {
        font-size: 18px;
        font-weight: 700;
        color: #333333;
        line-height: normal;
        font-feature-settings: 'kern';
      }

      .hover_style {
        margin-left: 28px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: normal;
        font-feature-settings: 'kern';
      }

      .hover_style:hover {
        color: #005579;
      }
    }

    .collectdown_detail {
      margin-top: 8px;
      display: flex;
      flex-direction: column;

      .problemtitle {
        height: 33px;
        padding-top: 9px;
        padding-left: 2px;
        background-color: #f2f2f2;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: normal;
        font-feature-settings: 'kern';
      }

      .problem {
        display: flex;
        flex-direction: row;
        margin-top: 15px;
        justify-content: space-between;
      }
    }
  }
}
</style>
