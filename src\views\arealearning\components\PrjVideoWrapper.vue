<template>
  <div class="main-wrapper" :class="{ map: isMap, 'script-mode': isScriptMode }">
    <div class="main">
      <!-- 标题栏 -->
      <div class="prjtitle">
        <div class="titlefont" v-html="currentItemTitle"></div>

        <div class="klg-icon">
          <div class="klg-tooltip">目标知识</div>
          <span class="klgbtn klg-icon-circle" @click="showTargetKnowledge">
            <img src="@/assets/svgs/kl.svg" alt="" srcset="" />
          </span>
        </div>
        <div class="klg-icon">
          <div class="klg-tooltip">问题列表</div>
          <span class="klgbtn klg-icon-circle" @click="showQuestionList">
            <img src="@/assets/svgs/questionList.svg" alt="" srcset="" />
          </span>
        </div>
        <div class="klg-icon">
          <div class="klg-tooltip">知识源图</div>
          <span class="klgbtn klg-icon-circle" @click="toggleMap">
            <img src="@/assets/svgs/kGraph.svg" alt="" srcset="" />
          </span>
        </div>
      </div>
      <el-divider class="divider" />

      <!-- 主内容区域 - 对应 PrjManuWrapper 的内容结构 -->
      <div class="content-wrapper textfont">
        <!-- 主内容卡片 - 对应 PrjManuWrapper 的 content-card -->
        <div class="content-card">
          <!-- 视频播放器区域 -->
          <div class="video-container" v-show="!isMap && !isScriptMode">
            <template v-if="projectDetailData?.videoUrl">
              <XgPlayer
                :videoSrc="projectDetailData?.videoUrl"
                :canBeWiden="prjType == PrjType.exam ? false : true"
                :questionTimelineMatches="questionTimelineMatches"
                @timeupdate="handleTimeupdateFn"
                @closePip="switchScreen(0)"
                ref="player"
              />
            </template>
          </div>
          <!-- 视频控制栏 -->
          <div class="video-control-bar" v-show="!isScriptMode && !isMap">
            <div class="question-stats">
              <span class="stats-text">共包含{{ questionList?.length || 0 }}个问题</span>
            </div>

            <el-button @click="toggleScriptMode" class="script-toggle-button textfont">
              <span>点这里划词提问</span>
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <!-- 知识地图区域 -->
          <div class="map-container" v-show="isMap">
            <!-- ksgMap覆盖层 -->
            <div class="ksg-map-overlay">
              <KsgMap
                ref="ksgRef"
                :config="config"
                @load-more="handleLoadMore"
                :loading="loading"
                @click-label="handleClickLabel"
                class="ksgmap"
              />
              <el-icon class="close-icon" size="2em" @click="closeMap">
                <Close />
              </el-icon>
            </div>
          </div>

          <!-- 视频脚本区域 - 在脚本模式下显示 -->
          <div class="script-main-container" v-show="isScriptMode && !isMap">
            <PrjVideoScript
              ref="prjVideoScript"
              :big="isBig ? true : false"
              :videoCaptionList="videoCaptionList"
              :renderedContent="renderedContent"
              :questionList="questionList"
              @returnInit="handleReturnInitFn"
              @refresh="handleQuestionList"
              @delete-question="handleDeleteQuestion"
              :isMap="isMap"
              v-if="ready"
              @wheel="handleVideoWheel"
              @search="handleSearch"
              @toggle-video="toggleScriptMode"
            />
          </div>
          <!-- 画中画视频窗口 -->
          <div class="pip-video-container" v-show="isScriptMode && !isMap">
            <div class="pip-video-wrapper">
              <!-- 画中画模式下显示主播放器的克隆 -->
              <div class="pip-video-placeholder">
                <div class="pip-video-content">
                  <!-- 这里会通过JavaScript将主播放器移动到此处 -->
                </div>
              </div>
              <div class="pip-controls">
                <el-button size="small" @click="toggleScriptMode"> 返回视频 </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧边栏 -  -->
        <div class="question-sidebar" v-show="!isBig">
          <div
            class="sidebar-header"
            @click="toggleQuestionList"
            :class="{ expanded: isQuestionListExpanded }"
          >
            <div class="sidebar-title-wrapper">
              <div class="sidebar-title">
                {{ sidebarMode === 'questions' ? '问题列表' : '目标知识' }}
              </div>
              <div class="question-toggle" v-if="sidebarMode === 'questions'">
                <el-icon class="toggle-icon" :size="22">
                  <ArrowDown v-if="isQuestionListExpanded" />
                  <ArrowUp v-else />
                </el-icon>
              </div>
            </div>
          </div>
          <!-- 问题列表内容 -->
          <div
            class="question-list hover-scrollbar"
            v-show="isQuestionListExpanded && sidebarMode === 'questions'"
          >
            <div
              v-for="question in questionTimelineMatches"
              :key="question.questionId"
              class="question-item"
              @click="handleQuestionClick(question)"
            >
              <div
                class="question-time clickable-time"
                @click.stop="jumpToQuestionTime(question.startTime)"
                :title="'点击跳转到 ' + getDisplayTime(question.startTime)"
              >
                {{ getDisplayTime(question.startTime) }}
              </div>
              <span class="description">
                【

                <span class="key-words ellipsis textfont" v-html="question?.keyword || ''"></span>
                】
                <span v-if="question?.questionType != '开放性问题'" class="question-type textfont"
                  >{{ question?.questionType }}?</span
                >
              </span>
            </div>
            <div
              v-if="!questionTimelineMatches || questionTimelineMatches.length === 0"
              class="empty-state"
            >
              <div v-if="!questionList || questionList.length === 0">
                暂无问题，开始划词提问吧！
              </div>
            </div>
          </div>

          <!-- 目标知识内容 -->
          <div
            class="question-list hover-scrollbar"
            v-show="isQuestionListExpanded && sidebarMode === 'knowledge'"
          >
            <div
              v-for="knowledge in targetKnowledgeList"
              :key="knowledge.klgCode"
              class="question-item knowledge-item"
              @click="handleKnowledgeClick(knowledge)"
            >
              <span class="knowledge-title textfont">{{ knowledge.klgTitle }}</span>
            </div>
            <div
              v-if="!targetKnowledgeList || targetKnowledgeList.length === 0"
              class="empty-state"
            >
              <div v-if="targetKnowledgeLoading">加载目标知识中...</div>
              <div v-else>暂无目标知识</div>
            </div>
          </div>

          <!-- QuestionDrawer组件 -->
          <QuestionDrawer
            :visible="showQuestionDrawer"
            :selectedText="selectedText"
            :zIndex="componentZIndex.question"
            :buyStatus="true"
            @close="handleCloseQuestionDrawer"
            @submit="handleCloseQuestionDrawer"
          />

          <!-- AnswerDrawerSidebar组件 -->
          <AnswerDrawerSidebar
            :visible="showAnswerDrawer"
            :questionData="currentQuestionData"
            :projectAuthor="projectAuthor"
            :zIndex="componentZIndex.answer"
            @close="handleCloseAnswerDrawer"
            @show-question="handleShowQuestionFromFloating"
          />
        </div>
      </div>
    </div>

    <!-- 问题图标 -->
    <div
      v-if="questionIconVisible"
      ref="questionIconElement"
      class="question-icon"
      :style="{
        position: 'fixed',
        left: questionIconPosition.x + 'px',
        top: questionIconPosition.y + 'px',
        zIndex: 10000
      }"
      @click="handleQuestionIconClick"
    >
      <!-- 悬浮提示 -->
      <div class="question-tooltip">提问</div>
      <!-- 问号图标 -->
      <div class="question-icon-circle">
        <img :src="questionIcon" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getPrjDetailApi,
  getPrjSectionApi,
  saveQuestionApi,
  getQuestionDetailApi,
  getPrjMoreInfoApi
} from '@/apis/learning';
import type { Chapter, VideoCaptionListObj, QuestionData } from '@/types/learning';
import { STATE_FLAG } from '@/types/learning';
import XgPlayer from '@/components/XgPlayer.vue';
import { defineExpose } from 'vue';
import { getQuestionListApi, deleteQuestionApi } from '@/apis/learning';
import { ElMessage } from 'element-plus';
import PrjVideoScript from './PrjVideoScript.vue';
import { PrjType } from '@/types/project';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { emitter } from '@/utils/emitter';
import {
  ArrowUpBold,
  ArrowDownBold,
  HelpFilled,
  Lock,
  Close,
  ArrowDown,
  ArrowUp,
  ArrowRight
} from '@element-plus/icons-vue';
import { getPrjIntroduceApi } from '@/apis/case';
import { useRenderManager } from '@/composables/useRenderManager';
import QuestionDrawer from '@/components/QuestionDrawer.vue';
import AnswerDrawerSidebar from '@/components/AnswerDrawerSidebar.vue';
import questionIcon from '@/assets/svgs/question.svg';
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import { useDrawerManager } from '@/composables/useDrawerManager';
import {
  matchQuestionsWithTimeline,
  getDisplayTime,
  getSeconds,
  type QuestionTimelineMatch
} from '@/utils/Video';

// ksgMap相关导入
import { getKsgMapMutileApi } from '@/apis/klgdetail';
import { KsgMap, MODE, type Options } from '@endlessorigin/KsgMap'; //直接导入组件，局部使用方式
import { Event as EventType } from '@/types/event';
import type { QuestionType } from '@/types/question';

const router = useRouter();
const route = useRoute();
const hasPermission = ref(0);

/**
 * 显示视频功能
 */
const isBig = ref(false); // 大屏小屏
const bigScreen = ref(0); // 0：大屏视频|1：大屏字幕
const isSectionMenuShow = ref(false); //控制多节菜单的显示
const player = ref<InstanceType<typeof XgPlayer> | null>(null);
const prjVideoScript = ref<InstanceType<typeof PrjVideoScript>>();
// 提供播放器实例给子组件
provide('player', player);
const spuId = route.query.spuId as string;
const prjType = inject('prjType') as Ref; // 1讲解 2案例 3测评
// 获取当前选中项标题
const currentItemTitle = inject('currentItemTitle', ref(''));

// 监听标题变化
watch(
  currentItemTitle,
  (newTitle) => {
    console.log('PrjVideoWrapper - 标题变化:', newTitle);
  },
  { immediate: true }
);

// 先声明所有需要的响应式变量，确保在函数中使用前已经初始化
// 项目的prjId
const prjId = ref<string>('');
const uniqueCode = ref<string>('');
// 项目的章节id
const curChapterId = ref();
// 项目详情
const projectDetailData = ref<Chapter>();
// 章节相关的数据
const chapterList = ref<Chapter[]>();
const activeIndex = ref();
// 拿到章节信息
const videoCaptionList = ref<[VideoCaptionListObj[]]>([[]]);
const questionList = shallowRef<QuestionData[]>();
const playerTime = ref<number>(0);

// 问题与视频文稿匹配结果
const questionTimelineMatches = ref<QuestionTimelineMatch[]>([]);

// 渲染后的内容 - 用于传递给子组件
const renderedContent = ref<any>(null);

// 初始化用户行为追踪函数
const { initUserBehaviour, handleChapterChange } = userBehaviour(
  PrjForm.video,
  activeIndex,
  curChapterId,
  playerTime
);

// 移除对learningStore的依赖，统一使用直接API调用的方式
// 这些函数已被 handleProjectChangeFn 替代，不再需要

const props = defineProps<{ payDialogVisible: boolean }>();

const payDialogVisible = ref(false);
watch(
  () => props.payDialogVisible,
  (newVal) => {
    if (newVal == false) {
      payDialogVisible.value = false;
    }
  }
);

// 提供isBig数据
provide(
  'isBig',
  computed(() => toValue(isBig))
);
// const isBig = inject('isBig') as Ref;
const isMap = ref(false); // 是否展示地图
const isScriptMode = ref(false); // 是否为脚本模式

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  currentSelectedText,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon();

// 使用Render管理器 - 针对视频字幕的特殊配置
const {
  handleSearch,
  addQuestion,
  removeQuestion,
  destroyRenderInstance,
  initializeRender
} = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => convertVideoCaptionListToTextArray(), // 直接返回字符串数组给render库处理
  questionList,
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      showQuestionIcon(data);
    }
  },
  onClick: (data: any) => {
    const event = new CustomEvent('showAnswerDrawer', {
      detail: { questionData: data.target }
    });
    window.dispatchEvent(event);
  },
  onFinish: (arg: any) => {
    const content = arg.content;
    // console.log('myonFinish', content);
    // 将渲染后的内容存储到响应式变量中，传递给子组件
    renderedContent.value = content;
  }
  // enableDebugLog: true
});
const addQuestionFn = async (params: {
  associatedWords: string;
  keyword: string;
  questionType: QuestionType;
  questionDescription: string;
  uniqueCode: string;
  chapterId: string;
  contentId?: string;
}) => {
  const res = await saveQuestionApi(params);
  const data = res.data.data;
  if (res.success) {
    ElMessage.success('保存问题成功');
    console.log('保存的问题:', data);
    addQuestion(data.associatedWords, data.questionId);

    // 添加到本地问题列表
    const rawQuestionList = toRaw(questionList.value);
    if (rawQuestionList) {
      rawQuestionList.push(data);
      triggerRef(questionList);
    }

    // 重新匹配问题与视频文稿时间轴
    questionTimelineMatches.value = matchQuestionsWithTimeline(
      questionList.value || [],
      videoCaptionList.value
    );
  } else {
    const errorMsg = res.message || '保存问题失败，请重试';
    ElMessage.error(errorMsg);
  }
};

const removeQuestionFn = async ([questionId, associatedWords]: any) => {
  console.log('removeQuestionFn', questionId);
  const res = await deleteQuestionApi(questionId);
  // console.log("deleteQuestionApi",res.data)
  if (res.success) {
    ElMessage.success('删除成功');
    // 使用Render管理器处理问题删除
    removeQuestion(associatedWords, Number(questionId));

    // 从问题列表中移除
    const rawQuestionList = toRaw(questionList.value);
    const questionIndex = rawQuestionList?.findIndex(
      (item) => item.questionId === Number(questionId)
    );

    if (questionIndex !== undefined && questionIndex >= 0) {
      rawQuestionList?.splice(questionIndex, 1);
      triggerRef(questionList);
    }

    // 重新匹配问题与视频文稿时间轴
    questionTimelineMatches.value = matchQuestionsWithTimeline(
      questionList.value || [],
      videoCaptionList.value
    );
  } else {
    ElMessage.error('删除失败');
  }
};

// 使用抽屉管理 composable
const {
  showQuestionDrawer,
  showAnswerDrawer,
  selectedText,
  currentQuestionData,
  componentZIndex,
  projectAuthor,
  updateComponentLayer,
  handleCloseQuestionDrawer,
  handleCloseAnswerDrawer: originalHandleCloseAnswerDrawer,
  handleShowQuestionFromFloating,
  initializeEventListeners,
  cleanupEventListeners
} = useDrawerManager();

const lastProcessedElement = ref<HTMLElement | null>(null); // 记录上次处理的DOM元素

// 问题列表展开/收起状态
const isQuestionListExpanded = ref(true);

// 目标知识相关状态
const targetKnowledgeList = ref<Array<{ klgCode: string; klgTitle: string; choose: boolean }>>([]);
const targetKnowledgeLoading = ref(false);

// 侧边栏显示模式：'questions' | 'knowledge'
const sidebarMode = ref<'questions' | 'knowledge'>('questions');

// 移除弹幕管理器，由XgPlayer组件内部处理

// 提供isMap数据
provide(
  'isMap',
  computed(() => toValue(isMap.value))
);

// 切换脚本模式
const toggleScriptMode = () => {
  isScriptMode.value = !isScriptMode.value;

  // 切换模式时隐藏问号图标和关闭弹窗
  questionIconVisible.value = false;
  showQuestionDrawer.value = false;
  showAnswerDrawer.value = false;
  currentSelectedText.value = '';
  selectedText.value = '';
  currentQuestionData.value = null;

  nextTick(async () => {
    if (isScriptMode.value) {
      // 进入脚本模式 - 将主播放器移动到画中画容器
      movePlayerToPip();
      console.log('进入脚本模式，显示画中画');

      await nextTick();
      await updateData();
    } else {
      // 退出脚本模式，将播放器移回主容器
      destroyRenderInstance();
      movePlayerToMain();
      console.log('退出脚本模式，回到视频');
    }
  });
};

// 将播放器移动到画中画容器
const movePlayerToPip = () => {
  const playerElement = player.value?.$el;
  const pipContainer = document.querySelector('.pip-video-content');

  if (playerElement && pipContainer) {
    // 保存原始父容器的引用
    const originalParent = playerElement.parentNode;
    if (originalParent) {
      originalParent.setAttribute('data-original-player', 'true');
    }

    // 移动播放器到画中画容器
    pipContainer.appendChild(playerElement);

    // 调整播放器尺寸以适应画中画容器
    playerElement.style.width = '100%';
    playerElement.style.height = '100%';
  }
};

// 将播放器移回主容器
const movePlayerToMain = () => {
  const playerElement = player.value?.$el;
  const originalParent = document.querySelector('[data-original-player="true"]');

  if (playerElement && originalParent) {
    // 移回原始容器
    originalParent.appendChild(playerElement);

    // 恢复原始样式
    playerElement.style.width = '';
    playerElement.style.height = '';

    // 清除标记
    originalParent.removeAttribute('data-original-player');
  }
};

// 处理问题列表中的问题点击（直接显示完整答案）
const handleQuestionClick = async (question: QuestionData) => {
  // 调用API获取问题详情
  const res = await getQuestionDetailApi(question.questionId.toString());

  if (res.success && res.data && res.data.length > 0) {
    // 强制触发props变化：先清空再设置，确保Vue能检测到变化
    currentQuestionData.value = null;

    // 使用nextTick确保Vue响应式系统处理了null值
    nextTick(() => {
      // 直接传递问题数据对象，跳过floating-content，直接显示完整答案
      currentQuestionData.value = res.data[0];
      showAnswerDrawer.value = true;

      // 更新组件层级
      updateComponentLayer('answer');
    });
  } else {
    console.error('❌ 获取问题详情失败:', res);
  }
};

// 切换问题列表展开/收起状态 - 修改为只能打开，不能关闭
const toggleQuestionList = () => {
  isQuestionListExpanded.value = !isQuestionListExpanded.value;
};

// 显示问题列表 - 只能打开，会覆盖目标知识
const showQuestionList = async () => {
  // 切换到问题列表模式
  sidebarMode.value = 'questions';
  // 显示侧边栏
  isQuestionListExpanded.value = true;
};

// 显示目标知识 - 只能打开，会覆盖问题列表
const showTargetKnowledge = async () => {
  // 切换到目标知识模式
  sidebarMode.value = 'knowledge';
  // 显示侧边栏
  isQuestionListExpanded.value = true;

  // 如果是首次显示且还没有数据，则获取目标知识列表
  if (targetKnowledgeList.value.length === 0) {
    await fetchTargetKnowledge();
  }
};

// 获取目标知识列表
const fetchTargetKnowledge = async () => {
  const spuId = route.query.spuId as string;
  if (!spuId) {
    return;
  }

  targetKnowledgeLoading.value = true;
  try {
    const res = await getPrjMoreInfoApi(spuId);
    if (res && res.data && (res.data as any).targetKlgs) {
      targetKnowledgeList.value = (res.data as any).targetKlgs;
    }
  } catch (error) {
    console.error('PrjVideoWrapper - 获取目标知识失败:', error);
    targetKnowledgeList.value = [];
  } finally {
    targetKnowledgeLoading.value = false;
  }
};

// 处理目标知识点击事件
const handleKnowledgeClick = (knowledge: {
  klgCode: string;
  klgTitle: string;
  choose: boolean;
}) => {
  // 跳转到知识详情页面
  const { href } = router.resolve({
    path: '/klgdetail',
    query: {
      klgCode: knowledge.klgCode
    }
  });
  window.open(href, '_blank');
};

// 问题图标相关逻辑已移至 useQuestionIcon composable

// 抽屉管理相关逻辑已移至 useDrawerManager composable
const handleCloseAnswerDrawer = () => {
  originalHandleCloseAnswerDrawer();
  lastProcessedElement.value = null; // 重置处理记录
};

// 旧的函数已被useRenderManager替代

// 生成包含完整HTML结构的数据数组 - 保留时间戳信息用于句子级高亮
const convertVideoCaptionListToTextArray = () => {
  if (!videoCaptionList.value || !Array.isArray(videoCaptionList.value)) {
    return [];
  }

  const htmlArray: string[] = [];
  videoCaptionList.value.forEach((paragraphList, paragraphIndex) => {
    if (Array.isArray(paragraphList)) {
      // 构建包含时间戳信息的HTML结构
      let paragraphHtml = `<div class="paragraph-wrapper" data-paragraph="${paragraphIndex}">`;
      paragraphHtml += `<div class="text">`;

      // 为每个句子添加oid、data-start、data-end属性
      paragraphList.forEach((item) => {
        paragraphHtml += `<span oid="${item.oid}" data-start="${item.startTime}" data-end="${item.endTime}">${item.caption}</span>`;
      });

      paragraphHtml += `</div></div>`;

      if (paragraphHtml.trim()) {
        htmlArray.push(paragraphHtml);
      }
    }
  });

  return htmlArray;
};
/**
 * 点击问题时间跳转到对应的视频时间点
 * @param startTime 开始时间字符串（如 "00:01:30"）
 */
const jumpToQuestionTime = (startTime: string) => {
  if (startTime && player.value) {
    const seconds = getSeconds(startTime);
    player.value.setTime(seconds);
  }
};

// 监听问答组件状态变化，确保组件间状态同步
watch(
  () => [showQuestionDrawer.value, showAnswerDrawer.value],
  ([questionVisible, answerVisible]) => {
    // 当有弹窗显示时，通知其他组件
    const stateChangeEvent = new CustomEvent('drawerStateChange', {
      detail: {
        questionVisible,
        answerVisible,
        hasActiveDrawer: questionVisible || answerVisible
      }
    });
    window.dispatchEvent(stateChangeEvent);
  }
);

const switchScreen = (direction: number) => {
  if (bigScreen.value == direction) {
    return;
  }
  bigScreen.value = direction;
  if (direction == 0) {
    //isMap.value = true;
    player.value?.exitPIP();
  } else {
    isMap.value = false;
    player.value?.requestPIP();
  }
};

//================================ksg-map================================
// ksgMap相关变量
const ksgRef = ref<any>(null);
const loading = ref<'loading' | 'loaded' | 'error'>('loading');
// 场景配置项
const config: Options = {
  model: MODE.MULTIPLE_ROOT, //Single_ROOT单根节点模,多根知识点场景(MODE.MULTIPLE_ROOT)，
  // 配置加载更多参数
  pointsLevelPager: {
    current: 1, //当前层级（从1层开始，默认第一层）
    levelSize: 2 //每次加载层数
  }
};

// 关闭知识图谱
const closeMap = () => {
  isMap.value = false;
  if (player.value) {
    player.value.exitPIP();
  }
};

const toggleMap = async () => {
  isMap.value = !isMap.value;

  if (isMap.value) {
    // 显示知识图谱时，视频进入画中画模式
    player.value?.requestPIP();

    // 获取知识图谱数据并初始化
    loading.value = 'loading'; // 设置加载状态

    // 第一步：获取项目详情，获取所有klgCodes
    const prjInfoRes = await getPrjMoreInfoApi(spuId);
    let klgCodes: string[] = [];

    if (prjInfoRes && prjInfoRes.data && (prjInfoRes.data as any).targetKlgs) {
      klgCodes = (prjInfoRes.data as any).targetKlgs.map((klg: any) => klg.klgCode);
    }
    // console.log('klgCodes:', klgCodes);

    let pointsData: any;
    if (klgCodes.length > 0) {
      pointsData = await getKsgMapMutileApi(klgCodes, 1, 10);
    }
    pointsData = pointsData.data;
    console.log('pointsData', pointsData);
    const dataList = pointsData.records;
    const total = pointsData.total;
    const rootid = '0';
    ksgRef.value?.firstLoadPointsData(dataList, total, rootid);
    loading.value = 'loaded'; // 设置加载完成状态
  }
};

const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
  // 直接调用API获取最新数据，不使用缓存
  const res = await getQuestionListApi(uniqueCode, chapterId);
  questionList.value = res.data as any; // 使用 as any 避免类型问题
  // 匹配问题与视频文稿时间轴
  questionTimelineMatches.value = matchQuestionsWithTimeline(
    questionList.value || [],
    videoCaptionList.value
  );
};
const handleDeleteQuestion = (questionId: string) => {
  emitter.emit(EventType.REMOVE_QUESTION, questionId);
};

// TODO: 后期优化，增加节流
let scrollBarAble = true;
let scrollBarTimer: any;
const handleVideoWheel = () => {
  scrollBarAble = false;
  if (scrollBarTimer) {
    clearTimeout(scrollBarTimer);
  }
  scrollBarTimer = setTimeout(() => {
    scrollBarAble = true;
  }, 2000);
};
const tempOid = ref(-1);
const handleTimeupdateFn = (currentTime: number | string) => {
  // myPlayer的获取有问题，waq注释了这一句 todo需要修改
  // if (!myPlayer.value) return;
  playerTime.value = currentTime as number;
  // console.log('currentTime1111', currentTime)
  // FIXME: hoverPList没了，可能得换一个东西看
  // const hoverPList = prjManuscript.value?.hoverPList as VideoCaptionListObj[];
  // const hoverPList=videoCaptionList.value
  // console.log((e.target as HTMLVideoElement).currentTime.toFixed(2), typeof currentTime);
  // console.log(currentTime, typeof currentTime);
  // console.log('hoverPList', prjManuscript.value?.hoverPList);
  // console.count();
  let manuIndex = 0; //当前字幕段
  let idx = -1;
  let curoid = -1;
  manuIndex = videoCaptionList.value!.findIndex((hoverPList) => {
    idx = hoverPList?.findIndex((hoverParam) => {
      // console.log(hoverParam)
      if (
        getSeconds(hoverParam.startTime) <= (currentTime as number) &&
        getSeconds(hoverParam.endTime || '0') >= (currentTime as number)
      ) {
        // console.log(hoverParam.caption)
        curoid = hoverParam.oid;
        return true;
      }
    });
    // console.log('idx', idx)
    if (idx != -1) return true;
  });
  if (idx != -1 && curoid != tempOid.value) {
    // console.log('idx',idx)
    // console.log('index',index)
    tempOid.value = curoid;
    prjVideoScript.value!.hoverPList(curoid); //字幕高亮
    if (scrollBarAble) {
      prjVideoScript.value!.scrollBar(manuIndex); // 滑动滚动条
    }
  }
};

provide(
  'prjSectionInfo',
  computed(() => ({
    chapterId: curChapterId.value,
    prjId: prjId.value,
    uniqueCode: uniqueCode.value
  }))
);

// 处理项目切换 - 参考PrjManuWrapper的实现
const handleProjectChangeFn = async (newSpuId: string) => {
  // 清空相关状态
  questionList.value = [];
  videoCaptionList.value = [[]];
  questionTimelineMatches.value = [];
  renderedContent.value = null; // 清空渲染后的内容

  // 获取项目数据
  console.log('🔄 PrjVideoWrapper - 调用 getPrjDetailApi，spuId:', newSpuId);
  const res2 = await getPrjDetailApi(newSpuId, '');
  console.log('🔄 PrjVideoWrapper - API响应:', res2);

  // 直接从 API 响应中获取数据，不通过 learningStore
  const projectData = res2.data;
  const currentChapter = projectData.chapterList[projectData.validIndex || 0];
  console.log('🔄 PrjVideoWrapper - 当前章节:', currentChapter);

  // 设置组件状态
  projectDetailData.value = currentChapter;
  chapterList.value = projectData.chapterList;
  curChapterId.value = currentChapter?.chapterId;
  activeIndex.value = currentChapter?.chapterId;
  videoCaptionList.value = currentChapter?.videoCaptionList || [[]];
  uniqueCode.value = projectData.uniqueCode;
  prjId.value = String(projectData.prjId);

  initUserBehaviour(curChapterId.value);
  await handleQuestionList(uniqueCode.value, curChapterId.value);

  console.log('📄 视频项目内容已更新');
};

// 处理章节变化

//当我从一个章节切到另一个章节时，我需要发送现在章节的end请求和新章节的start请求, 结束现在章节的start请求和新章节的end请求

const handleChangeSectionFn = async (
  chapterId: number,
  preview: boolean,
  skipRouteUpdate = false
) => {
  if (!preview && !hasPermission.value) {
    /*learningStore.autoPay = true
    router.push({
      path: '/goodIntroduce',
      query: {
        spuId: spuId
      }
  })*/
    payDialogVisible.value = true;
  } else {
    if (activeIndex.value != chapterId) {
      // 只有在不跳过路由更新时才更新路由
      if (!skipRouteUpdate) {
        router.replace({
          query: {
            ...route.query,
            chapterId: chapterId
          }
        });
      }
      handleChapterChange(chapterId);
      activeIndex.value = chapterId;
      // console.log(uniqueCode.value, chapterId);
      const res = await getPrjSectionApi(spuId, String(chapterId));
      // console.log('处理章节变化', res);
      // 拿到了新的video资源
      projectDetailData.value = res.data as any; // 使用 as any 避免类型问题
      //   changeVideoFn(prjDetailData.value?.videoUrl as string);
      curChapterId.value = projectDetailData.value?.chapterId;
      videoCaptionList.value = res.data.videoCaptionList as any; // 使用 as any 避免类型问题

      // 🔥 章节切换时也要清理渲染后的内容，避免显示错误的内容
      renderedContent.value = null;

      await handleQuestionList(uniqueCode.value, chapterId.toString());
    }
  }
};

// 小屏转大屏的时候操控状态STATE_FLAG
// 切换状态
const handleReturnInitFn = () => {
  prjVideoScript.value!.changeStateFn(STATE_FLAG.init);
};

const ready = ref(false);
// 🔥 关键修复：合并所有onMounted钩子，避免重复初始化
onMounted(async () => {
  console.log('🚀 PrjVideoWrapper onMounted - 开始初始化');

  // 1. 设置组件就绪状态
  ready.value = true;

  // 2. 注册事件监听器
  emitter.on(EventType.ADD_QUESTION, addQuestionFn);
  emitter.on(EventType.REMOVE_QUESTION, removeQuestionFn);
  document.addEventListener('click', handleDocumentClick);
  initializeEventListeners();

  // 3. 从父组件注入的prjInfo中获取数据
  const prjInfo = inject('prjInfo', ref({}));
  if (prjInfo.value && Object.keys(prjInfo.value).length > 0) {
    const info = prjInfo.value as any;
    hasPermission.value = info.hasPermission;
    projectAuthor.value = info.editorName || '';
    console.log('📋 从父组件获取到项目信息');
  }

  // 4. 直接从路由获取 spuId，加载项目数据
  const currentSpuId = route.query.spuId as string;
  if (currentSpuId) {
    console.log('📂 开始加载项目数据，spuId:', currentSpuId);
    await handleProjectChangeFn(currentSpuId);
    console.log('✅ 项目数据加载完成');
  }
});

onBeforeUnmount(() => {
  console.log('🧹 PrjVideoWrapper onBeforeUnmount - 开始清理');

  // 清理事件监听器
  emitter.off(EventType.ADD_QUESTION, addQuestionFn);
  emitter.off(EventType.REMOVE_QUESTION, removeQuestionFn);
  document.removeEventListener('click', handleDocumentClick);
  cleanupEventListeners();
});

// 监听路由变化 - 项目切换
watch(
  () => route.query.spuId,
  async (newSpuId, oldSpuId) => {
    if (newSpuId && newSpuId !== oldSpuId) {
      console.log('视频组件路由spuId变化，重新加载项目:', newSpuId);
      // 调用统一的切换函数，标记为项目切换
      await handleProjectChangeFn(newSpuId as string);
    }
  }
);

defineExpose({
  curChapterId,
  handleChangeSectionFn
});
</script>

<style scoped src="./css/PrjVideoWrapper.less"></style>
