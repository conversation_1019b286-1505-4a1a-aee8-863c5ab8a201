.script-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.paragraph-wrapper {
  margin-bottom: 10px;
}
.paragraph-wrapper .select_to_ask_time {
  width: 70px;
  font-weight: 600;
  color: #333333;
  font-size: 12px;
  margin-bottom: 5px;
  user-select: none;
}
.paragraph-wrapper .select_to_ask_time:hover {
  cursor: pointer;
  color: var(--color-theme-project);
}
.paragraph-wrapper .text:deep(.highlight2) {
  background-color: yellow;
  cursor: pointer;
  font-weight: 700;
}
.paragraph-wrapper .text:deep(.text-hover) {
  color: var(--color-theme-project);
  cursor: pointer;
}
.paragraph-wrapper .text:deep(.text-hover:hover) {
  font-weight: 700;
}
.content-card {
  color: #333333;
  max-width: 100%;
  max-height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
}
.content-card .tool {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 37px;
  padding-left: 10px;
  margin-bottom: 10px;
}
.content-card .tool .remind {
  font-size: 12px;
}
.content-card .tool .remind img {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}
.content-card .tool .mode {
  margin-left: 10px;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  display: flex;
  align-items: center;
}
.content-card .tool .mode .el-switch {
  margin-right: 10px;
}
.content-card .tool .input {
  margin-top: 5px;
  margin-right: 10px;
}
.content-card .tool .input .custom-el-input :deep(.el-input__inner)::placeholder {
  font-size: 12px;
}
.content-card .tool .input .custom-el-input {
  height: 25px;
  width: 230px;
}
.content-card .tool .back-to-video {
  margin-right: 10px;
}
.content-card .tool .back-to-video .el-button {
  background: var(--color-theme-project);
  border-color: var(--color-theme-project);
  color: white;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}
.content-card .tool .back-to-video .el-button:hover {
  background: var(--color-theme-project-hover, #409eff);
  border-color: var(--color-theme-project-hover, #409eff);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}
.content-card .content-text-wrapper {
  width: 100%;
  overflow-y: auto;
  word-wrap: break-word;
  padding: 0 10px;
  margin-top: 2px;
  height: calc(100vh - 66px - 70px - 60px);
  position: relative;
}
.content-card .content-text-wrapper.learning {
  min-height: calc(100vh - 66px - 70px - 100px);
  max-height: calc(100vh - 60px - 70px);
}
.content-card:deep(.search-keys) {
  color: red;
  cursor: pointer;
  font-weight: 700;
}
.content-card:deep(.highlight2) {
  background-color: v-bind(matchingColor);
  cursor: pointer;
  font-weight: v-bind(matchingHeight);
}
.content-card:deep(.activeElement) {
  background-color: #ff9632;
}
.floating-content {
  padding: 6px 12px;
  background: var(--color-theme-project);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 14px;
  font-weight: 400;
}
.floating-content .floating-content-item:hover {
  font-weight: 700;
  cursor: pointer;
}
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
  transform-origin: top;
  /* 设置动画起点为左侧 */
}
.scale-enter-from,
.scale-leave-to {
  transform: scaleY(0);
  /* 从左侧开始水平缩放 */
  opacity: 0;
}
