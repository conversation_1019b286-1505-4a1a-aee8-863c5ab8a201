<template>
  <!-- 
    右侧个人信息卡片组件
    这个组件在展示想学习的技能时还存在一些问题
    后续可做调整
   -->
  <div class="up-right">
    <div class="avatar-name">
      <img
        style="width: 60px; height: 60px; border-radius: 5px"
        :src="userInfo!.coverPic"
        alt=""
        @click="router.push('/userspace')"
        class="img-name"
      />
      <div class="username" @click="router.push('/userspace')">{{ userInfo!.username }}</div>
    </div>
    <div class="learn-target">
      <span>学习目标：{{ userInfo!.goal }}</span>
    </div>
    <div class="user-job">
      <div>
        <span>当前职业：{{ userInfo!.career }}</span>
      </div>
      <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
        <dinv style="display: flex"
          ><span>想学的技能：</span>
          <div class="title">{{ firstlineskill }}</div></dinv
        >
        <div style="display: flex; flex-wrap: wrap">
          <div class="row1" v-for="(item, index) in secondlineskill" :key="index">
            <div class="title">{{ item }}</div>
          </div>
          <span
            class="more"
            @mouseenter="expandMore = true"
            @mouseleave="expandMore = false"
            v-if="afterskill.length != 0"
            >...</span
          >
        </div>
      </div>
    </div>
    <div class="learn-info">
      <div class="today">
        <span
          >今日学习 <span class="bolder-num">{{ userInfo?.todayTime }}</span> 分钟</span
        >
      </div>
      <div class="line"></div>
      <div class="continue">
        <span
          >连续学习 <span class="bolder-num">{{ userInfo?.days }}</span
          >天</span
        >
      </div>
    </div>
    <div class="tohome">
      <div class="btn" @click="goBack">回到首页</div>
    </div>
    <el-collapse-transition>
      <div class="more-info" v-show="expandMore">
        <div class="left-info">
          <div class="row" v-for="(item, index) in afterskill" :key="index">
            <div class="title">{{ item }}</div>
          </div>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { formToJSON } from 'axios';
// import { sk } from 'element-plus/es/locale';
import { useRouter } from 'vue-router';
import { getUserDetailApi } from '@/apis/learnStatistics';
interface Skill {
  oid: Number;
  title: string;
}
// import router from '@/router';
const router = useRouter();
const expandMore = ref(false);
const goBack = () => {
  router.push('/home');
};
const userInfo = ref<UserInfo>();
const getUserInfo = () => {
  getUserDetailApi()
    .then((res) => {
      userInfo.value = res.data;
      const skills = res.data.skills;
      calSkills(skills);
    })
    .catch((error) => {
      console.log(error);
    });
};

// 获取用户信息
onMounted(() => {
  getUserInfo();
});

// 此处避免格式化产生的空格对样式造成影响 只是展示 无其他用途
type UserInfo = {
  career: string;
  coverPic: string;
  days: number;
  goal: string;
  industry: string;
  skills: Skill[];
  todayTime: number;
  uniqueCode: string;
  username: string;
};
//默认值只用作样式调整
userInfo.value = {
  career: '暂无',
  coverPic: '',
  days: 365,
  goal: '暂无',
  industry: '暂无',
  skills: [
    {
      oid: 1,
      title: '暂无'
    }
  ],
  todayTime: 0,
  uniqueCode: '',
  username: ''
};

// 字符串的显示是按照个数来显示的所以需要按照传过来的技能进行拆分
let firstlineskill = ref<string>();
let secondlineskill = ref<string[]>([]);
let afterskill = ref<string[]>([]);

const calSkills = (skills: Skill[]) => {
  if (skills.length == 0) {
    firstlineskill.value = '暂无';
  } else {
    //需要对skills0进行字符串拆分
    firstlineskill.value = skills[0].title;
    if (skills.length > 1 && skills.length <= 3) {
      for (let i = 1; i < skills.length; i++) {
        secondlineskill.value.push(skills[i].title);
      }
    } else {
      for (let i = 1; i < 3; i++) {
        secondlineskill.value.push(skills[i].title);
      }
      for (let i = 3; i < skills.length; i++) {
        afterskill.value.push(skills[i].title);
      }
    }
  }
};
</script>
<style scoped lang="less">
.up-right {
  width: 254px;
  height: 383px;
  border-radius: 5px;
  // margin-left: 40px;
  background-color: var(--color-second);
  color: var(--color-black);
  font-family: var(--text-family);
  display: flex;
  align-items: center;
  padding-top: 21px;
  flex-direction: column;

  .avatar-name {
    height: 60px;
    width: 90%;
    display: flex;
    margin-left: 20px;
    align-items: center;

    .img-name {
      &:hover {
        cursor: pointer;
      }
    }
    .username {
      margin-left: 10px;
      font-weight: 600;
      font-style: normal;
      font-size: 16px;
      font-family: 'Alimama FangYuanTi VF SemiBold', 'Alimama FangYuanTi VF Regular',
        'Alimama FangYuanTi VF', sans-serif;

      &:hover {
        cursor: pointer;
      }
    }
  }

  .learn-target {
    width: 90%;
    border-radius: 5px;
    background-color: white;
    height: 32px;
    margin-top: 7px;
    padding-left: 10px;
    line-height: 32px;
    font-size: var(--fontsize-middle-project);
    //对文本样式进行控制，分别是防止其换行，溢出时隐藏并且用省略号代替
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .user-job {
    height: 102px;
    width: 90%;
    font-size: var(--fontsize-middle-project);
    background-color: white;
    margin-top: 7px;
    border-radius: 5px;
    line-height: 25px;
    padding-left: 10px;
    padding-top: 5px;
    padding-right: 5px;
  }
  .title {
    width: 60px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .learn-info {
    height: 89px;
    width: 90%;
    font-size: var(--fontsize-middle-project);
    background-color: white;
    margin-top: 7px;
    border-radius: 5px;
    padding-left: 10px;
    padding-top: 5px;
    padding-right: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .line {
      height: 2px;
      width: 124px;
      background-color: #f2f2f2;
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }

  .tohome {
    margin-top: 10px;

    .btn {
      width: 160px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      background-color: var(--color-theme-project);
      color: white;
      border: solid var(--color-theme-project) 1px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        color: var(--color-theme-project);
        background-color: var(--color-second);
      }
    }
  }

  .more-info {
    width: 230px;
    min-height: 100px;
    //也可以根据需求将高度设置为auto
    position: absolute;
    background-color: white;
    top: 317px;
    border-radius: 5px;
    // line-height: 25px;
    // padding-top: 5px;
    // padding-left: 10px;
    // padding-top: 5px;
    // padding-bottom: 5px;
  }
}

.more {
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
  border-radius: 5px;
}

.left-info {
  width: 90%;
  padding-top: 5px;
  padding-left: 10px;
  padding-bottom: 5px;
  // align-items: center;
  // justify-content: center;
  // white-space: pre-wrap;
  font-size: var(--fontsize-middle-project); // display: grid;
  // gap: 5px 5px;
  // grid-template-columns: repeat(4, 170px);
  // grid-template-rows: repeat(auto-fill, 30px);
  display: flex;
  //使其可以实现换行的属性
  //表示在容器不足时允许元素换行
  flex-wrap: wrap;
}

.row {
  margin-bottom: 5px;
  // 设置确保每行包含两个元素,因为一个元素占百分之50，需要和flex-warp搭配使用
  flex: 0 0 50%;
  box-sizing: border-box;
}

.row1 {
  margin-right: 8px;
  // 设置确保每行包含两个元素，因为这个数组最多有两个元素，还要给...留下位置，所以每每一块占40%
  // flex: 0 0 40%;
  box-sizing: border-box;
  // 控制溢出文本
  overflow: hidden;
  text-overflow: ellipsis;
}

.bolder-num {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-theme-project);
}
</style>
