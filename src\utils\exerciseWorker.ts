import escapeStringRegexp from '@/utils/escapeStringRegexp';
import URLParse from 'url-parse';
import { html, parseFragment, serializeOuter, type DefaultTreeAdapterTypes } from 'parse5';
import { cloneDeep } from 'lodash-es';
import { ExerciseType } from '@/types/exercise';

const singleCloseTag = [
  'IMG',
  'BR',
  'HR',
  'INPUT',
  'AREA',
  'HR',
  'LINK',
  'META',
  'BASE',
  'BASEFONT',
  'PARAM',
  'COL',
  'FRAME',
  'EMBED'
];

type ChildNode = DefaultTreeAdapterTypes.ChildNode;
type Element = DefaultTreeAdapterTypes.Element;
type TextNode = DefaultTreeAdapterTypes.TextNode;
type Parse5DocumentFragment = DefaultTreeAdapterTypes.DocumentFragment;

const uncommonWordMap = new Map<string, string>();
let exerciseStringList: Array<{ list: Array<string>; start: number }> = [];
let originalExerciseStringList: Array<{ list: Array<string>; start: number }> = [];
let regString = '';
const unitList: any[] = [];
onmessage = function (
  e: MessageEvent<{
    questionList: any[];
    exercise: any;
  }>
) {
  // const start = Date.now();
  // while (Date.now() - start < 2000) {}
  const questionList = e.data.questionList;
  const exercise = e.data.exercise;
  const htmlStringList: string[] = [];
  htmlStringList.push(`<span etype="stem" type="${exercise.type}">${exercise.stem}</span>`);
  if (exercise.type == ExerciseType.single || exercise.type == ExerciseType.multi)
    exercise.option.forEach((item) => {
      htmlStringList.push(`<span etype="option" optionid="${item.optionId}">${item.text}</span>`);
    });
  htmlStringList.push(`<span etype="answer">${exercise.answer}</span>`);
  htmlStringList.push(`<span etype="explanation">${exercise.explanation}</span>`);
  // 建匹配串
  buildRegString(htmlStringList);

  //console.log('exerWorker:', questionList);

  questionList?.forEach((question: any) => {
    handleQuestion(question);
  });
  // console.log("打印regString",JSON.stringify(regString, null, 2));
  // console.log("打印exerciseStringList",JSON.stringify(exerciseStringList, null, 2));
  // console.log("打印unitList", JSON.stringify(unitList, null, 2));
  // console.log("打印originalVideoStringList", JSON.stringify(originalExerciseStringList,null,2));
  postMessage({
    exerciseStringList,
    originalExerciseStringList,
    regString,
    unitList,
    uncommonWordMap
  });
};

function buildRegString(htmlStringList: string[]) {
  let contentIndex = 0;
  let uncommonWordIndex = 0;
  let stringIndex = 0;
  let start = 0;
  const uncommonWords = getRemainingRandomChars(htmlStringList.join(''), 100);
  htmlStringList.forEach((htmlString: string) => {
    const dom = {
      tagName: '',
      children: [],
      index: -1,
      qids: [],
      highlight: false,
      stringIndex: -1
    };
    const regCharList: string[] = [];
    const tempDiv = parseFragment(htmlString) as Parse5DocumentFragment;
    let stringList: string[] = [];
    const dfs = (node: ChildNode, parent: any) => {
      if (node.nodeName == '#text') {
        const textContent = ((node as TextNode).value as string).trim();
        if (textContent?.length == 0) {
          return;
        }
        const newDom: {
          tagName: string;
          children: any[];
          index: number;
          qids: number[];
          highlight: boolean;
          stringIndex: number;
        } = {
          tagName: 'TEXT',
          children: [],
          index: -1,
          qids: [],
          highlight: false,
          stringIndex: -1
        };
        for (const char of textContent) {
          if (char != '\n') {
            const unit = {
              tagName: 'unit',
              children: [char],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            newDom.children.push(unit);
            unitList.push(unit);
            contentIndex += char.length;
            stringList.push(char);
            regCharList.push(char);
          }
        }
        parent.children.push(newDom);
      } else if ((node as Element).tagName) {
        if (node.nodeName == 'img') {
          const src = node.attrs.find((attr) => attr.name == 'src')?.value;
          const outerHTML = serializeOuter(node);
          const obj = URLParse(src as string);
          const key = obj.pathname.split('/').pop() as string;
          if (!uncommonWordMap.has(key)) {
            uncommonWordMap.set(key, uncommonWords[uncommonWordIndex]);
            stringList.push(outerHTML);
            regCharList.push(uncommonWords[uncommonWordIndex]);
            const unit = {
              tagName: 'unit',
              children: [outerHTML],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            parent.children.push(unit);
            unitList.push(unit);
            contentIndex += uncommonWords[uncommonWordIndex].length;
            uncommonWordIndex++;
          } else {
            const unit = {
              tagName: 'unit',
              children: [outerHTML],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            parent.children.push(unit);
            unitList.push(unit);
            const str = uncommonWordMap.get(key)!;
            contentIndex += str.length;
            stringList.push(outerHTML);
            regCharList.push(str);
          }
        } else if ((node as Element).nodeName == 'script') {
          const key = ((node as Element).childNodes[0] as TextNode).value;
          const outerHTML = serializeOuter(node);
          if (!uncommonWordMap.has(key)) {
            uncommonWordMap.set(key, uncommonWords[uncommonWordIndex]);
            stringList.push(outerHTML);
            regCharList.push(uncommonWords[uncommonWordIndex]);
            const unit = {
              tagName: 'unit',
              children: [outerHTML],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            parent.children.push(unit);
            unitList.push(unit);
            contentIndex += uncommonWords[uncommonWordIndex].length;
            uncommonWordIndex++;
          } else {
            const unit = {
              tagName: 'unit',
              children: [outerHTML],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            parent.children.push(unit);
            unitList.push(unit);
            const str = uncommonWordMap.get(key)!;
            contentIndex += str.length;
            stringList.push(outerHTML);
            regCharList.push(str);
          }
        } else {
          const newDom: {
            tagName: string;
            children: any[];
            index: number;
            qids: number[];
            highlight: boolean;
            stringIndex: number;
          } = {
            tagName: (node as Element).nodeName,
            children: [],
            index: -1,
            qids: [],
            highlight: false,
            stringIndex: -1
          };
          parent.children.push(newDom);
          const etype = (node as Element).attrs.find((attr) => attr.name == 'etype')?.value;
          if (etype) {
            const optionid = (node as Element).attrs.find((attr) => attr.name == 'optionid')?.value;
            const type = (node as Element).attrs.find((attr) => attr.name == 'type')?.value;
            let pushFlag = false;
            if (optionid && !type) {
              stringList.push(
                `<${(node as Element).nodeName} etype="${etype}" optionid="${optionid}">`
              );
              pushFlag = true;
            }
            if (type && !optionid) {
              stringList.push(`<${(node as Element).nodeName} etype="${etype}" type="${type}">`);
              pushFlag = true;
            }
            if (!pushFlag) {
              stringList.push(`<${(node as Element).nodeName} etype="${etype}">`);
            }
          } else {
            stringList.push(`<${(node as Element).nodeName}>`);
          }
          stringIndex++;
          for (const child of (node as Element).childNodes) {
            dfs(child, newDom);
          }
          if (!singleCloseTag.includes((node as Element).tagName.toUpperCase())) {
            stringList.push(`</${(node as Element).tagName}>`);
            stringIndex++;
          }
        }
      }
    };

    for (const child of tempDiv.childNodes) {
      dfs(child, dom);
    }
    regString += regCharList.join('');
    exerciseStringList.push({
      list: stringList,
      start: start
    });
    start = stringIndex;
  });
  originalExerciseStringList = cloneDeep(exerciseStringList);
  // console.log("original",JSON.stringify(originalExerciseStringList, null, 2))
}
function buildRegSubString(htmlString: string): string {
  const tempDiv = parseFragment(htmlString) as Parse5DocumentFragment;
  let regSubString = '';
  const dfs = (node: ChildNode) => {
    if (node.nodeName == '#text') {
      const textContent = ((node as TextNode).value as string).trim();
      if (textContent?.length == 0) {
        return;
      }
      for (const char of textContent) {
        if (char != '\n') {
          regSubString += char;
        }
      }
    } else if ((node as Element).tagName) {
      if ((node as Element).nodeName == 'img') {
        const src = (node as Element).attrs.find((attr) => attr.name == 'src')?.value;
        const obj = URLParse(src as string);
        const key = obj.pathname.split('/').pop() as string;
        regSubString += uncommonWordMap.get(key);
      } else if ((node as Element).nodeName == 'script') {
        const key = ((node as Element).childNodes[0] as TextNode).value;
        regSubString += uncommonWordMap.get(key);
      } else {
        for (const child of (node as Element).childNodes) {
          dfs(child);
        }
      }
    }
  };
  for (const childNode of tempDiv.childNodes) {
    dfs(childNode);
  }
  return regSubString;
}

function handleQuestion(question: any): void {
  const str = buildRegSubString(question.associatedWords as string);
  if (str.trim().length == 0) {
    return;
  }
  const positions = findAllOccurrences(regString, str.replace(/\n/g, ''));
  for (const position of positions) {
    find(position, str.length, question.questionId);
  }
}
function find(postion: number, offset: number, questionId: number) {
  for (let i = postion; i < postion + offset; i++) {
    const unit = unitList[i];
    if (unit) {
      unit.qids.push(questionId);
      handleUnit(unit);
    }
  }
}
function handleUnit(node: {
  tagName: string;
  children: any[];
  index: number;
  qids: number[];
  highlight: boolean;
  stringIndex: number;
}) {
  if (node.qids.length == 0) {
    if (!node.highlight) {
      exerciseStringList.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] = node.children[0];
        }
      });
    } else {
      exerciseStringList.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span class="highlight2">${node.children[0]}</span>)`;
        }
      });
    }
  } else {
    const qids = node.qids.join(',');
    if (!node.highlight) {
      exerciseStringList.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span data-qid="${qids}" class="highlight">${node.children[0]}</span>`;
        }
      });
    } else {
      exerciseStringList.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span data-qid="${qids}" class="highlight highlight2">${node.children[0]}</span>`;
        }
      });
    }
  }
}

function findAllOccurrences(str: string, subStr: string) {
  const matchStr = str.toLowerCase();
  const matchSubStr = subStr.toLowerCase();
  const regex = new RegExp(escapeStringRegexp(matchSubStr), 'g');
  const matches = [...matchStr.matchAll(regex)];
  const positions = matches.map((match) => match.index);
  return positions;
}

function getRemainingRandomChars(input: string, length: number): string[] {
  // 定义Unicode字符的范围（只取常见的 BMP 字符，U+0000 到 U+FFFF）
  const MAX_CODE_POINT = 0xffff;

  // 将输入字符串的字符存储到一个 Set 中（去重）
  const usedChars = new Set(input);

  // 创建一个数组，保存所有未使用的字符
  const remainingChars = [];

  // 遍历 BMP 范围内的所有字符，过滤掉已使用的字符
  for (let i = 1; i <= MAX_CODE_POINT; i++) {
    const char = String.fromCharCode(i);
    if (!usedChars.has(char)) {
      remainingChars.push(char);
    }
    if (remainingChars.length >= length) {
      break;
    }
  }

  return remainingChars;
}
