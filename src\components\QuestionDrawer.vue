<template>
  <transition name="drawer" appear>
    <div
      class="question-drawer-wrapper hover-scrollbar"
      v-if="visible"
      :style="{ zIndex: zIndex }"
      :class="{ 'local-panel': local }"
    >
      <div class="header">
        <span class="iconfont icon-icon_close" @click="handleClose"></span>
        <div class="title">划词提问</div>
      </div>
      <div class="body">
        <div class="info">
          <span class="name"> {{ userInfo.username }} </span>
          的提问
        </div>

        <!-- 根据问题类型决定编辑器和类型选择的顺序 -->
        <template v-if="curQuestion.questionType === QuestionType.open">
          <!-- 开放问题：type在上，编辑器在下 -->
          <div class="type">
            <!-- 第一行：基本问题类型 -->
            <div class="button-container">
              <CmpButton
                v-for="(value, key) in basicTypeDict"
                :key="key"
                type="info"
                class="type-btn"
                :class="{ hightlight: curQuestion.questionType == value }"
                @click="handleChangeQType(value)"
              >
                {{ key }}
              </CmpButton>
            </div>
            <!-- 第二行：开放性问题 -->
            <div class="open-question-container">
              <CmpButton
                type="info"
                class="open-question-btn"
                :class="{ hightlight: curQuestion.questionType == QuestionType.open }"
                @click="handleChangeQType(QuestionType.open)"
              >
                开放性问题
              </CmpButton>
            </div>
          </div>
          <div class="question-container">
            <div class="extra-editor">
              <IinlineClassicEditor
                v-model="curQuestion.keyword"
                placeholder="请输入您的问题"
                ref="editorRef"
              ></IinlineClassicEditor>
            </div>
          </div>
        </template>

        <template v-else>
          <!-- 其他问题类型：编辑器在上，type在下 -->
          <div class="question-container">
            <div class="extra-editor">
              <IinlineClassicEditor
                v-model="curQuestion.keyword"
                placeholder="请输入您的问题"
                ref="editorRef"
              ></IinlineClassicEditor>
            </div>
          </div>
          <div class="type">
            <!-- 第一行：基本问题类型 -->
            <div class="button-container">
              <CmpButton
                v-for="(value, key) in basicTypeDict"
                :key="key"
                type="info"
                class="type-btn"
                :class="{ hightlight: curQuestion.questionType == value }"
                @click="handleChangeQType(value)"
              >
                {{ key }}
              </CmpButton>
            </div>
            <!-- 第二行：开放性问题 -->
            <div class="open-question-container">
              <CmpButton
                type="info"
                class="open-question-btn"
                :class="{ hightlight: curQuestion.questionType == QuestionType.open }"
                @click="handleChangeQType(QuestionType.open)"
              >
                开放性问题
              </CmpButton>
            </div>
          </div>
        </template>

        <div class="submit">
          <CmpButton class="submit-btn" type="primary" @click="handleSubmit">提交</CmpButton>
          <CmpButton class="cancel-btn" @click="handleClose">取消</CmpButton>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { QuestionType, qMode, qModeDict, qTypeDict, qTypeMap } from '@/types/constant';
import IinlineClassicEditor from '@/components/editors/VeditorInline.vue';
import CmpButton from '@/components/CmpButton.vue';
import { saveQuestionApi } from '@/apis/learning';
import { saveExerciseQuestionApi } from '@/apis/exercise';
import { useUserStore } from '@/stores/user';
import { useLearningStore } from '@/stores/learning';
import { emitter } from '@/utils/emitter';
import { useDrawerControllerStore } from '@/stores/drawerController';
import {
  convertMathTagsToMDLatex,
  convertLanguageMathToScript,
  convertImgTagLongUrls
} from '@/utils/latexUtils';
const saveType = inject('saveType') as Ref;
// 注入章节信息，包含 contentId
const prjSectionInfo = inject('prjSectionInfo') as ComputedRef<{
  chapterId: string;
  prjId: string;
  contentId: string;
  uniqueCode?: string;
}>;

const route = useRoute();
const userStore = useUserStore();
const learningStore = useLearningStore();
const drawerControllerStore = useDrawerControllerStore();

const { userInfo } = storeToRefs(userStore);
const { mode } = storeToRefs(drawerControllerStore);
const editorRef = ref();

// 定义props
interface Props {
  visible?: boolean;
  selectedText?: string;
  zIndex?: number;
  local?: boolean;
  buyStatus?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selectedText: '',
  zIndex: 1001,
  local: false,
  buyStatus: false
});

const emits = defineEmits(['close', 'submit', 'showPayDialog']);
const curQuestion = reactive({
  associatedWords: '',
  keyword: '',
  questionType: QuestionType.what,
  questionDescription: ''
});

// 基本问题类型（不包括开放性问题）
const basicTypeDict: { [key: string]: number } = {
  是什么: QuestionType.what,
  为什么: QuestionType.why,
  怎么办: QuestionType.how
};

// 监听问题类型的变化
watch(
  () => curQuestion.questionType,
  (newType) => {
    if (newType !== QuestionType.open) {
      curQuestion.questionDescription = ''; // 如果不是开放问题类型，清空描述
    } else {
      curQuestion.questionDescription = curQuestion.keyword;
    }
  }
);

const handleChangeQType = (questionType: QuestionType) => {
  curQuestion.questionType = questionType;
};
import { useExerciseStore } from '@/stores/exercise';
import { Mode } from '@/types/word';
import { Event } from '@/types/event';
const exerStore = useExerciseStore();
const handleSubmit = async () => {
  // 检查购买状态
  if (!props.buyStatus) {
    // 未购买，触发事件通知父组件打开PayDialog
    emits('showPayDialog');
    return;
  }

  const params: {
    associatedWords: string;
    keyword: string;
    questionType: QuestionType;
    questionDescription: string;
    uniqueCode: string;
    chapterId: string;
    contentId?: string;
  } = {
    associatedWords: curQuestion.associatedWords,
    keyword: convertLanguageMathToScript(convertImgTagLongUrls(editorRef.value.getHtml())),
    questionType: curQuestion.questionType,
    questionDescription: curQuestion.questionDescription,
    uniqueCode: prjSectionInfo?.value?.uniqueCode || learningStore.uniqueCode,
    chapterId:
      prjSectionInfo?.value?.chapterId ||
      ((route.query.chapterId || learningStore.chapterId) as string),
    contentId: prjSectionInfo?.value?.contentId || learningStore.contentId || ''
  };

  //测评的参数
  const paramsExer: {
    associatedWords: string;
    keyword: string;
    questionType: QuestionType;
    exerciseId: string;
  } = {
    associatedWords: curQuestion.associatedWords,
    keyword: curQuestion.keyword,
    questionType: curQuestion.questionType,
    exerciseId: exerStore.exercise.exerciseId as string
  };
  if (saveType.value == 0) {
    emitter.emit(Event.ADD_QUESTION, params);
  } else {
    const res = await saveExerciseQuestionApi(paramsExer);
    if (res.success) {
      ElMessage.success('保存问题成功');
    } else {
      ElMessage.error('保存问题失败');
    }
    emitter.emit('refresh');
    //saveType.value = 0;
  }

  // 通知父组件关闭
  emits('close');
  // 重置临时内容，确保下次划词能正常触发
  tempContent.value = null;
  mode.value = mode.value == Mode.ask ? Mode.read : Mode.ask;
  //saveType.value = 0; //默认为项目
};

// 用于临时存储上一次的划词内容,用来判断是否是新的划词
const tempContent = ref<string | null>(null);

// 处理关闭弹窗
const handleClose = () => {
  // 通知父组件关闭
  emits('close');
  // 重置临时内容，确保下次划词能正常触发
  tempContent.value = null;
};

// 监听selectedText的变化
watch(
  () => props.selectedText,
  (newVal) => {
    // newVal是用户划词选中的文本内容
    if (newVal && newVal.trim()) {
      // 将划词内容同步到问题的关联词和关键词字段,关联词要转成md格式出现在编辑器中；
      curQuestion.associatedWords = newVal;
      curQuestion.keyword = convertMathTagsToMDLatex(newVal);
      console.log('curQuestion.keyword', curQuestion.keyword);

      // 如果临时内容和新值不同,说明是新的划词操作
      if (tempContent.value != newVal) {
        // 更新临时内容
        tempContent.value = newVal;
        // 清空问题描述,因为是新问题
        curQuestion.questionDescription = '';
        // 设置默认问题类型为"是什么"
        curQuestion.questionType = QuestionType.what;
      }
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
// 弹窗动画效果
.drawer-enter-active,
.drawer-leave-active {
  transition: all 0.3s ease;
}

.drawer-enter-from,
.drawer-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.drawer-enter-to,
.drawer-leave-from {
  opacity: 1;
  transform: translateX(0);
}

.question-drawer-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: calc(100vh - 110px);
  background-color: white;
  border-left: 1px solid #e8e8e8;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
  box-sizing: border-box;
  color: #333333;
  font-family:
    '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 16px;
  font-weight: 400;

  .header {
    position: relative;
    margin-bottom: 10px;

    .icon-icon_close {
      position: absolute;
      right: 0;
      top: 0;
      cursor: pointer;
      font-size: 16px;
      color: #666;

      &:hover {
        color: #333;
      }
    }

    .title {
      font-weight: 700;
      font-size: 16px;
    }

    .divider {
      margin: 10px 0;
    }
  }

  .body {
    width: 260px;
    height: 340px;
    padding: 10px;
    margin: 0 auto;
    border-width: 1px;
    border-style: solid;
    background-color: rgba(255, 255, 255, 1);
    box-sizing: border-box;
    border-color: rgba(228, 228, 228, 1);
    box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.0980392156862745);
    border-radius: 4px;
    font-weight: 400;
    margin-top: 100px;
    .info {
      font-size: 12px;
      .name {
        font-weight: 700;
        margin-right: 10px;
      }
    }

    .question {
      margin: 10px 0;
      padding: 5px 10px;
      font-size: 14px;
      color: var(--color-theme-project);
      background-color: var(--color-second);
    }

    .keyword {
      margin: 10px 0px;
    }

    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 10px;
      .qtype {
        display: flex;
        justify-content: flex-end;
        font-weight: bold;
      }
    }

    .extra-editor {
      width: 100%;
      margin: 10px 0;
      box-sizing: border-box;
      border-color: rgba(220, 223, 230, 1);
    }

    .type {
      margin: 15px 0;

      .button-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0;
        border: 1px solid #d9d9d9;
        overflow: hidden;
      }

      .type-btn {
        text-align: center;
        flex: 1;
        height: 32px;
        font-size: 14px;
        border: none;
        border-right: 1px solid #d9d9d9;
        background-color: #fff;
        color: #666;
        width: 80px;
        height: 35px;
        box-sizing: border-box;
        &:last-child {
          border-right: none;
        }

        &:hover {
          color: #333333;
          background-color: rgba(220, 223, 230, 1);
        }
      }

      .type-btn.hightlight {
        color: #333333;
        background-color: rgba(220, 223, 230, 1);
        box-sizing: border-box;
        border-width: 1px;
        border-style: solid;
        border-color: rgba(220, 223, 230, 1);
      }

      .open-question-container {
        // margin-top: 8px;
        display: flex;
        justify-content: flex-start;
      }

      .open-question-btn {
        text-align: center;
        width: 100%;
        height: 35px;
        font-size: 14px;
        border: 1px solid #d9d9d9;
        background-color: #fff;
        color: #666;
        transition: all 0.3s ease;

        &:hover {
          color: #333333;
          background-color: rgba(220, 223, 230, 1);
        }
      }

      .open-question-btn.hightlight {
        color: #333333;
        background-color: rgba(220, 223, 230, 1);
        box-sizing: border-box;
        border-width: 1px;
        border-style: solid;
        border-color: rgba(220, 223, 230, 1);
      }
    }

    .submit {
      display: flex;
      justify-content: center;
      margin-top: 10px;
      gap: 10px;

      .submit-btn {
        width: 80px;
        height: 36px;
        font-size: 14px;
        background-color: #1973cb;
        border-color: #1973cb;
        border-radius: 4px;
        color: #fff;

        &:hover {
          background-color: #40a9ff;
          border-color: #40a9ff;
        }
      }

      .cancel-btn {
        width: 80px;
        height: 36px;
        font-size: 14px;
        background-color: #fff;
        color: #666;
        border: 1px solid #d9d9d9;
        border-radius: 4px;

        &:hover {
          // color: #1890ff;
          border-color: #1973cb;
        }
      }
    }
  }
}

.question-drawer-wrapper :deep(.ck-editor__editable) {
  height: 120px;
}

.question-drawer-wrapper .question-container {
  display: block;
  width: 240px;
  height: 147px;
  margin: 10px 0;
  .extra-editor {
    width: 100%;
    display: block;
  }
}

.question-drawer-wrapper.local-panel {
  position: static !important;
  width: 100% !important;
  height: 100% !important;
  min-width: 0;
  min-height: 0;
  box-shadow: none;
  margin-top: 0;
}
</style>
