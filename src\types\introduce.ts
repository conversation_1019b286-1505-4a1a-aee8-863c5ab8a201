/**
 * 项目介绍页面相关类型定义
 */

/**
 * 章节项目接口
 */
export interface ChapterItem {
  chapterId: number;
  chapterName: string;
  preview: boolean;
}

/**
 * 问题项目接口
 */
export interface QuestionItem {
  questionId: number;
  keyword: string;
  questionType: string;
  questionWeight: string;
  questionNecessity: string;
  learned: boolean;
  questionDescription: string;
}

/**
 * 根据问题类型返回对应的文本
 * @param type 问题类型
 * @returns 问题类型文本
 */
export const questionTypeText = (type: string): string => {
  switch (type) {
    case '1':
      return '是什么';
    case '2':
      return '为什么';
    case '3':
      return '怎么做';
    case '4':
      return '开放型问题';
    default:
      return '未知类型';
  }
};

/**
 * 根据问题必要性返回对应的文本
 * @param necessity 问题必要性
 * @returns 问题必要性文本
 */
export const questionNecessityText = (necessity: string): string => {
  switch (necessity) {
    case '1':
      return '必要';
    case '2':
      return '参考';
    default:
      return '未知';
  }
};

/**
 * 根据问题权重返回对应的文本
 * @param weight 问题权重
 * @returns 问题权重文本
 */
export const questionWeightText = (weight: string): string => {
  switch (weight) {
    case '2':
      return '公开';
    case '1':
      return '私人';
    default:
      return '未知';
  }
};