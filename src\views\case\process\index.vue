<template>
  <div class="learn-process">
    <div class="main-content">
      <TitleandInfo :istest="isTest"></TitleandInfo>
      <div class="content">
        <ProcessItem
          v-for="(processInfo, i) in learnProcessData"
          :key="i"
          :process-data="processInfo"
        ></ProcessItem>
      </div>
    </div>
    <LearnPlan></LearnPlan>
  </div>
</template>

<script setup lang="ts">
import ProcessItem from './ProcessItem.vue';
import LearnPlan from './LearnPlan.vue';
import { getStudyProcess } from '@/apis/case';
import TitleandInfo from '../components/TitleandInfo.vue';
//表明当前不是测试页面，显示相关按钮
const isTest = ref(false);
// 周信息
const learnProcessData = ref([]);
const route = useRoute();
const spuId = route.query.spuId as string;
const getProcess = () => {
  const params = {
    spuId: spuId
  };
  getStudyProcess(params)
    .then((res) => {
      learnProcessData.value = res.data.list;
    })
    .catch((error) => {
      console.log(error);
    });
};
getProcess();
</script>

<style lang="less" scoped>
.learn-process {
  width: 1100px;
  padding-left: 10px;
  display: flex;
  flex-direction: row;

  .main-content {
    width: 754px;
    height: 700px;
    border: 0.8px solid rgb(242, 242, 242);
    border-radius: 5px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
    // background-image: linear-gradient(to bottom, #f2f2f2, #ffffff);
    padding-right: 10px;
    padding-top: 20px;
    padding-bottom: 10px;
    padding-left: 10px;
    margin-bottom: 40px;

    .info {
      width: 100%;
      height: 50px;

      margin-top: 10px;
    }

    .content {
      width: 100%;
      margin-top: 30px;
      height: 520px;
      overflow: auto;
      height: 520px;
      overflow: auto;
    }
  }
}
</style>
