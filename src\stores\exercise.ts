import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useExerciseStore = defineStore('exerciseStore', () => {
  const exercise = ref({
    type: 0,
    stem: '',
    option: [],
    answer: '',
    explanation: '',
    exerciseId: ''
  });
  const headerMode = ref(0);
  const setExerciseId = (exerciseId: string) => {
    exercise.value.exerciseId = exerciseId;
  };
  const setExercise = (exer: any) => {
    exercise.value.type = exer.type;
    exercise.value.stem = exer.stem;
    exercise.value.option = exer.content ? exer.content : exer.option;
    exercise.value.answer = exer.answer;
    exercise.value.explanation = exer.explanation;
    exercise.value.exerciseId = exer.exerciseId;
    //console.log('inside:', exercise.value.exerciseId);
  };
  const setHeadrMode = (mode: number) => {
    headerMode.value = mode;
  };
  return { exercise, headerMode, setExercise, setHeadrMode, setExerciseId };
});
