// 知识测评相关接口
import { http } from '@/apis';
import type { ExamType } from '@/types/exam';
interface getDocumentListParams {
  current: number;
  limit: number;
  spuId: string;
}
// 通过项目id或者节id获取文稿测评
export function getDocumentListApi(data: getDocumentListParams) {
  return http.request<{
    total: number;
    currentCount: number;
    map: {
      examTitle: string;
      examExplanation: string;
      sectionTitle: string;
      examId: number;
      examType: ExamType;
      spuId: string;
      examAnswer: string;
      sectionId: number;
      myAnswer: string;
      prjId: number;
      questionList: any[];
      order: number;
    };
  }>({
    method: 'post',
    url: '/knowledgeExam/getDocumentList',
    data
  });
}

// 保存每一道题目的答案
interface answerParams {
  order: number;
  examId: number;
  myAnswer: string;
  spuId: string;
  examAnswer: string;
}
export function saveAnswerApi(data: answerParams) {
  return http.request({
    method: 'post',
    url: '/knowledgeExam/saveAnswer',
    data
  });
}

// 完成测评
interface finishParams {
  spuId: string;
}

export function finishTestApi(data: finishParams) {
  return http.request({
    method: 'post',
    url: '/knowledgeExam/finishTest',
    data
  });
}

// 加入收藏夹
//接口参数需要调整修改
interface addFavoritesParams {
  examId: number;
  spuId: string;
}
export function addFavoritesApi(data: addFavoritesParams) {
  return http.request({
    method: 'post',
    url: '/favorites-file/create',
    data
  });
}

// 视频测评
export function getVideoExamApi(projectId: string) {
  return http.request({
    method: 'post',
    url: '/knowledgeExam/getVideoResult',
    data: {
      projectId
    }
  });
}

// 视频测评上传附件
export function uploadVideoFileApi(data: any) {
  return http.request({
    method: 'post',
    url: '/knowledgeExam/uploadTest',
    data
  });
}
