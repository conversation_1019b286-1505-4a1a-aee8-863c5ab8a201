<template>
  <div class="card-page">
    <!-- 左侧区域 -->
    <div class="left-section">
      <div class="card" v-for="(card, index) in cards" :key="index">
        <div class="card-info">
          <h3>{{ title }}</h3>
          <p>{{ card.type }}</p>
          <p>有效期：{{ card.validity }}</p>
        </div>
        <img :src="card.imgSrc" :alt="card.type" />
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="right-section">
      <h2>{{ title }}</h2>
      <div id="chart" class="chart"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts';
import { onMounted, ref, PropType } from 'vue';

// 父组件传来的 title
const props = defineProps({
  title: {
    type: String,
    required: true
  }
});

// 图片及卡片信息
const cards = ref([
  {
    type: '年卡',
    validity: '365天',
    imgSrc: '/path/to/annual-card.jpg'
  },
  {
    type: '季卡',
    validity: '120天',
    imgSrc: '/path/to/season-card.jpg'
  },
  {
    type: '月卡',
    validity: '30天',
    imgSrc: '/path/to/month-card.jpg'
  }
]);

// ECharts 图表初始化
const initChart = () => {
  const chartDom = document.getElementById('chart') as HTMLElement;
  const myChart = echarts.init(chartDom);

  const option = {
    title: {
      text: 'ECharts '
    },
    tooltip: {},
    xAxis: {
      data: ['一月', '二月', '三月', '四月', '五月', '六月']
    },
    yAxis: {},
    series: [
      {
        name: '销量',
        type: 'bar',
        data: [5, 20, 36, 10, 10, 20]
      }
    ]
  };

  myChart.setOption(option);
};

onMounted(() => {
  initChart();
});
</script>

<style scoped>
.card-page {
  display: flex;
  justify-content: space-between;
  padding: 20px;
}

/* 左侧区域样式 */
.left-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card {
  position: relative;
  width: 330px;
  height: 185px;
  /* border-radius: 3px; */
}

.card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.card-info {
  position: absolute;
  top: 10px;
  left: 10px;
  color: white;
  background: rgba(0, 0, 0, 0.6);
  padding: 10px;
  border-radius: 5px;
}

/* 右侧区域样式 */
.right-section {
  flex: 1;
  margin-left: 20px;
}

.right-section h2 {
  text-align: center;
  margin-bottom: 20px;
}

.chart {
  width: 100%;
  height: 400px;
}
</style>
