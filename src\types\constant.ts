export const cardTypeMap = new Map([
  [3, '月卡'],
  [2, '季卡'],
  [1, '年卡']
]);

//划词
export enum qMode {
  necessary = 1,
  refer = 2
}

export enum QuestionType {
  what = 1,
  why = 2,
  how = 3,
  open = 4
}

export const qTypeMap = new Map([
  [QuestionType.what, '是什么'],
  [QuestionType.why, '为什么'],
  [QuestionType.how, '怎么做'],
  [QuestionType.open, '开放性问题']
]);

export const qTypeDict: { [key: string]: number } = {
  是什么: QuestionType.what,
  为什么: QuestionType.why,
  怎么做: QuestionType.how,
  开放性问题: QuestionType.open
};

export const qModeDict: { [key: string]: number } = {
  必要问题: qMode.necessary,
  参考问题: qMode.refer
};
