<template>
  <div class="layout" :class="{ 'no-scroll': $route.name == 'questionDetail' }">
    <!-- <div v-html="`<script type='math/tex'>x^2</script>`"></div> -->
    <!-- <div v-html="tryhtml"></div> -->

    <div
      :class="{
        'content-with-footer':
          $route.name != 'learning' &&
          $route.name != 'questionDetail' &&
          $route.name != 'arealearning-prj',
        'good-content-wrapper': $route.name == 'goodIntroduce'
      }"
    >
      <RouterView />
    </div>
    <!-- footer -->

    <Footer
      v-if="
        $route.name != 'learning' &&
        $route.name != 'questionDetail' &&
        $route.name != 'arealearning-prj'
      "
      class="footer-fixed"
    />
    <AnswerDrawer v-model="element"></AnswerDrawer>
    <ThumbNail v-model="thumbNailElement"></ThumbNail>
  </div>
</template>

<script setup lang="ts">
import Header from '@/layout/Header.vue';
import Footer from '@/components/Footer.vue';
import AnswerDrawer from './components/AnswerDrawer.vue';
import ThumbNail from './components/ThumbNail.vue';
import { useDrawerControllerStore } from './stores/drawerController';
import PicIcon from './assets/imgs/pic_icon.jpg';
import TableIcon from './assets/imgs/table_icon.jpg';
import katex from 'katex';
import { useRoute } from 'vue-router';
import { bindKeyWordsHover } from '@/composables/useKeyWordsHover';
import { decodeHTML } from 'entities';

const thumbNailElement = ref<HTMLElement | null>(null);
const imgs: Array<HTMLElement> = [];
const observer = new MutationObserver(() => {
  let scripts = Array.prototype.slice.call(document.body.getElementsByTagName('script'));
  scripts.forEach(function (script) {
    if (!script.type || !script.type.match(/math\/tex/i)) {
      return -1;
    }
    // 检查是否在 id=underline 元素内(可划词内容由划词包内容渲染），如果是则跳过渲染
    // let parentElement = script.parentElement;
    // while (parentElement) {
    //   if (parentElement.id && parentElement.id == 'underline') {
    //     return -1; // 跳过渲染
    //   }
    //   parentElement = parentElement.parentElement;
    // }

    // const stringIndex = script.getAttribute('stringIndex');
    const display = script.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;

    const katexElement = document.createElement(display ? 'div' : 'span');
    katexElement.setAttribute('class', display ? 'equation' : 'inline-equation');

    // 使用安全的entities库进行HTML实体解码,避免xss攻击
    const decodedText = decodeHTML(script.text);
    katexElement.setAttribute('latexCode', script.text);

    try {
      // 预处理公式文本，将 align 环境替换为 align* 以禁用自动编号
      let processedText = decodedText.replace(/\s+/g, ' ');
      processedText = processedText.replace(/\\begin\{align\}/g, '\\begin{align*}');
      processedText = processedText.replace(/\\end\{align\}/g, '\\end{align*}');

      const htmlString = katex.renderToString(processedText, {
        displayMode: display,
        throwOnError: false,
        output: 'html'
      });

      katexElement.innerHTML = htmlString;
    } catch (err) {
      //console.error(err); linter doesn't like this
      katexElement.textContent = decodedText;
    }
    script.parentNode.replaceChild(katexElement, script);
  });

  const images = document.querySelectorAll('img');

  images.forEach((img) => {
    if (imgs.includes(img)) {
      return;
    }
    img.addEventListener(
      'dblclick',
      function () {
        const range = document.createRange();
        range.selectNode(this);

        const selection = window.getSelection();
        selection!.removeAllRanges();
        selection!.addRange(range);
      },
      true
    );
    imgs.push(img);
  });
  const questionLists = document.querySelectorAll('.questionList, .questionPop, .quesCollapse');
  questionLists.forEach((questionList) => {
    const imgElements = questionList.querySelectorAll('img:not([thumbnail])');
    imgElements?.forEach((item) => {
      const newImg = document.createElement('img');
      newImg.src = PicIcon;
      newImg.style.height = '14px';
      newImg.setAttribute('thumbnail', item.outerHTML);
      newImg.addEventListener('mouseover', (event) => {
        thumbNailElement.value = event.target as HTMLElement;
      });
      item.replaceWith(newImg);
    });
    const tableElements = questionList.querySelectorAll('table:not([thumbnail])');
    tableElements?.forEach((item) => {
      const newImg = document.createElement('img');
      newImg.src = TableIcon;
      newImg.style.height = '14px';
      newImg.setAttribute('thumbnail', item.outerHTML);
      newImg.addEventListener('mouseover', (event) => {
        thumbNailElement.value = event.target as HTMLElement;
      });
      item.replaceWith(newImg);
    });
  });

  // 处理 keyWords 区域的悬浮放大功能
  const keyWordsContainers = document.querySelectorAll('.keyWords');
  keyWordsContainers.forEach((container) => {
    const elements = container.querySelectorAll('.equation, img');
    elements.forEach((element) => {
      bindKeyWordsHover(element as HTMLElement);
    });
  });
});

const element = ref<HTMLElement | null>(null);

const lineWordContent = ref('');
// 用于跟踪QuestionDrawer是否打开
const isQuestionDrawerOpen = ref(false);

watch(
  () => lineWordContent.value,
  (newVal) => {
    if (newVal) {
      isQuestionDrawerOpen.value = true;
    } else {
      // 当lineWordContent被清空时，可能是弹窗关闭了
      // 添加一个小延迟确保状态同步
      setTimeout(() => {
        isQuestionDrawerOpen.value = false;
      }, 100);
    }
  }
);

//划词提交问题的种类 0:项目 1:测评
const saveType = ref(0);
provide('saveType', saveType);

onMounted(() => {
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 监听QuestionDrawer关闭事件
  window.addEventListener('questionDrawerClosed', () => {
    isQuestionDrawerOpen.value = false;
  });
});

onUnmounted(() => {
  observer.disconnect();
  window.removeEventListener('questionDrawerClosed', () => {
    isQuestionDrawerOpen.value = false;
  });
});

const useWideScreen = ref(false);
provide('useWideScreen', useWideScreen);

// 监听路由变化，动态添加或移除body的class
const route = useRoute();
watch(
  () => route.name,
  (newRouteName) => {
    if (newRouteName === 'questionDetail') {
      document.body.classList.add('question-detail');
    } else {
      document.body.classList.remove('question-detail');
    }
  },
  { immediate: true }
);
</script>

<style lang="less">
[data-qid]:not([data-qid='']) img {
  &:hover {
    border: 2px solid var(--color-theme-project); /* 2像素宽的黑色实线边框 */
    border-radius: 5px; /* 可选：圆角边框 */
  }
}

.no-scroll {
  height: 100vh;
  overflow: hidden;
}

.content-with-footer {
  min-height: calc(100vh - 200px); /* 减去footer高度 */
  padding-bottom: 20px;
}

/* 为 good/index.vue 页面添加特殊样式，确保内容可以完全展示 */
.good-content-wrapper {
  min-height: auto;
  padding-bottom: 0;
}
</style>
