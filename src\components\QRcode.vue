<template>
  <div class="dialog-container-qrcode">
    <div class="dialog-container-qrcode-content">
      <div class="qrcode" v-loading="loading">
        <div v-if="fetchError" class="error-overlay" @click="getPaymentcode(payType)">
          <el-icon size="18" class="error-content"><Refresh /></el-icon>
          <span class="error-content">点击刷新</span>
        </div>
        <template v-else>
          <div v-if="!checked" style="height: 200px">
            <span class="message">请先勾选下方协议</span>
          </div>
          <canvas v-else-if="payType == 2 && checked" ref="qrcode"></canvas>
          <template v-else>
            <iframe
              v-if="url != '' && checked"
              style="height: 200px"
              :srcdoc="url"
              @load="loading = false"
            ></iframe>
          </template>
        </template>
      </div>
      <div class="payfor">
        <span class="btn" :class="payType == 1 ? 'activeBtn' : ''" @click="getPaymentcode(1)">
          <span
            class="iconfont icon-zhifubao"
            :style="payType != 1 ? 'color: rgb(24,119,255);' : ''"
          ></span>
          支付宝扫码
        </span>
        <span class="btn" :class="payType == 2 ? 'activeBtn' : ''" @click="getPaymentcode(2)">
          <span
            class="iconfont icon-weixin"
            :style="payType != 2 ? 'color: rgb(50,179,44);' : ''"
          ></span>
          微信扫码
        </span>
        <!-- <span>请使用微信或支付宝扫码支付</span> -->
      </div>
      <div class="price">价格:{{ orderInfo.actualPaidAmount }}元</div>
      <div class="agreement">
        <input type="checkbox" v-model="checked" style="margin-right: 10px" />我已登录并同意
        <span @click="gotoAgreement" class="agreement-content">《无尽本源支付协议》</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getPaymentcodeApi, getPollResultApi, type QueryOrderResult } from '@/apis/payment';
//import QrcodeVue, { Level, RenderAs } from 'qrcode.vue';
import type { Level, RenderAs } from 'qrcode.vue';
import { useRouter } from 'vue-router';
import QRCode from 'qrcode';
import { useProjectStore } from '@/stores/project';
import { BuyStatus, GoodsType } from '@/types/goods';

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);

const router = useRouter();

const props = defineProps<{
  skuId: string;
  orderNo?: string;

  subtotal: number;
  discount: number;
  actualPaidAmount: number;
}>();

const payType = ref<1 | 2>(1);
const loading = ref(false);
const fetchError = ref(false);

//注意：默认使用info.priceList[0]初始化三个属性（在购买没有推荐界面的商品时后续不需要更改）
//todo: 使用props获取subtotal discount actualPaidAmount初始化数据
const orderInfo = ref<QueryOrderResult>({
  orderNo: '',
  subtotal: info.value.priceList[0].subtotal,
  discount: info.value.priceList[0].discount,
  actualPaidAmount: info.value.priceList[0].actualPaidAmount,
  orderStatus: 0,
  createTime: ''
});
const qrcode = ref<HTMLCanvasElement | null>(null);
const url = ref('');

onMounted(() => {
  orderInfo.value.subtotal = props.subtotal;
  orderInfo.value.actualPaidAmount = props.actualPaidAmount;
  orderInfo.value.discount = props.discount;
});
watch(
  () => props.skuId,
  (newVal) => {
    orderInfo.value.subtotal = props.subtotal;
    orderInfo.value.actualPaidAmount = props.actualPaidAmount;
    orderInfo.value.discount = props.discount;
  }
);
// 获取二维码地址
const getPaymentcode = (dealMethod: 1 | 2) => {
  if (!checked.value) {
    return;
  }
  url.value = '';
  if (!props.skuId) return;
  payType.value = dealMethod;
  const params: {
    skuId: string;
    channel: 1 | 2;
    orderNo?: string;
  } = {
    skuId: props.skuId,
    channel: payType.value
  };
  if (props.orderNo) {
    params.orderNo = props.orderNo;
  }
  loading.value = true;
  fetchError.value = false;
  getPaymentcodeApi(params)
    .then((res) => {
      if (payType.value == 2) {
        QRCode.toCanvas(
          qrcode.value!,
          res.data.qrCode,
          {
            margin: 0,
            width: 200
          },
          (error) => {
            if (error) throw error;
          }
        );
      }
      url.value = res.data.qrCode;
      loopOrderState(res.data.orderNo);
      if (payType.value == 2) {
        loading.value = false;
      }
    })
    .catch((err) => {
      console.log(err);
      ElMessage({
        message: '获取二维码失败',
        type: 'error'
      });
      fetchError.value = true;
      QRCode.toCanvas(qrcode.value!, 'placeholder', {
        margin: 0,
        width: 200
      });
      loading.value = false;
    });
};
// 轮询查询订单状态
const emit = defineEmits(['paySuccess']);
let timer: string | number | NodeJS.Timeout | undefined | null;
const loopOrderState = (orderNo: string) => {
  clearInterval(timer);
  timer = setInterval(() => {
    getPollResultApi(orderNo).then((res) => {
      // 支付成功消除定时器
      orderInfo.value = res.data;
      if (orderInfo.value.orderStatus == 20) {
        clearInterval(timer);
        timer = null;
        emit('paySuccess');
      }
    });
  }, 2000);
};

// 默认同意协议，不能修改
const checked = ref(false);
// 跳转到同意协议
const gotoAgreement = () => {
  const { href } = router.resolve({
    path: '/agreement'
  });
  window.open(href, '_blank');
};
watch(
  () => checked.value,
  (newVal) => {
    if (newVal) {
      getPaymentcode(payType.value); // 默认支付宝支付
    } else {
      if (timer) {
        clearInterval(timer);
        timer = null;
      }
    }
  }
);
// 关闭定时器
onUnmounted(() => {
  clearInterval(timer);
  timer = null;
});
defineExpose({
  orderInfo
});
</script>

<style scoped lang="less">
.dialog-container-qrcode {
  background-color: #fff9eb;
  width: 352px;
  height: 543px;

  .dialog-container-qrcode-content {
    margin-top: 95px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .qrcode {
      margin-bottom: 20px;
      width: 200px;
      height: 200px;
      overflow: hidden;

      .error-overlay {
        position: absolute;
        // top: 50%;
        // left: 50%;
        // transform: translate(-50%, -50%);
        background-color: #ccccccc9;
        width: 200px;
        height: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
        .error-content {
          font-size: 20px;
          font-weight: 400;
          color: var(--color-theme-project);
          &:hover {
            cursor: pointer;
            font-weight: 700;
          }
        }
      }
    }

    .payfor {
      margin-bottom: 20px;
      display: flex;

      span {
        margin-right: 5px;
      }
      .btn {
        width: 130px;
        height: 35px;
        color: var(--color-theme-project);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        cursor: pointer;
        border: 1px solid var(--color-theme-project);
        background-color: white;
      }
      .activeBtn {
        background-color: var(--color-theme-project);
        color: white;
      }
    }

    .price {
      margin-bottom: 20px;
      width: 271px;
      height: 35px;
      background-color: #ffd37a;
      border-radius: 5px;
      color: #4d0819;
      font-weight: 700;
      font-size: 16px;
      text-align: right;
      line-height: 35px;
      padding-right: 24px;
    }

    .agreement {
      font-size: 14px;
      font-weight: 400;
      display: flex;
      align-items: center;

      .agreement-content:hover {
        cursor: pointer;
      }
    }
    .message {
      padding-top: 180px;
      text-align: center;
      color: #4d0819;
      background-image: url('@/assets/images/project/code.png');
      font-size: 12px;
      font-weight: 600;
      height: 200px;
      width: 200px !important;
      float: left !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: normal !important;
    }
  }
}
</style>
