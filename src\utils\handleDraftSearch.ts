import { useDraftWordStoreV2 } from '@/stores/draftWordV2';
import { findAllOccurrences } from './regUtils';
import { type RenderInfo } from '@/types/word';
import { trim } from 'lodash-es';
// import { useLatexStore } from '@/stores/latex';

function handleDraftSearch(
  str: string,
  regString: string,
  renderInfoIndexes: number[],
  renderInfoList: RenderInfo[]
): void {
  const positions = findAllOccurrences(regString, str);
  for (const position of positions) {
    find(position, str.length, renderInfoIndexes, renderInfoList);
  }
}

function find(
  postion: number,
  offset: number,
  renderInfoIndexes: number[],
  renderInfoList: RenderInfo[]
) {
  for (let i = postion; i < postion + offset; i++) {
    const index = renderInfoIndexes[i];
    renderInfoList[index].searchCount++;
  }
}

export { handleDraftSearch };
