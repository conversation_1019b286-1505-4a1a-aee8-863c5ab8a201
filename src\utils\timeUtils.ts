export const timestampToTime = (timestamp: number) => {
  // timestamp = timestamp ? timestamp : null;
  let date = new Date(timestamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear() + '-';
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
  let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
  let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  return Y + M + D + h + m + s;
};

//  秒准换为时分秒
export const formatTime = (seconds?: number) => {
  if (seconds == null || seconds == undefined) return null;
  let hours: string | number = Math.floor(seconds / 3600); // 计算小时数
  let minutes: string | number = Math.floor((seconds % 3600) / 60); // 计算分钟数
  let sec: string | number = Math.floor(seconds % 60); // 剩余的秒数，去掉小数点

  // 将小时、分钟和秒数格式化为两位数字
  hours = hours.toString().padStart(2, '0');
  minutes = minutes.toString().padStart(2, '0');
  sec = sec.toString().padStart(2, '0');

  return `${hours}:${minutes}:${sec}`;
};
