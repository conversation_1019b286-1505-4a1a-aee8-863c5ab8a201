<template>
  <div class="kl-map">
    <div class="main-content">
      <TitleandInfo :istest="isTest"></TitleandInfo>
      <!-- 是领域的显示 -->
      <div class="part" v-if="isArea == GoodsType.vip">
        <div
          v-if="info.buyStatus == BuyStatus.bought && isObjectEmpty(lessonData)"
          style="display: flex; flex-direction: row; align-items: center"
        >
          <div>当前学习:&nbsp;&nbsp;&nbsp;&nbsp;</div>
          <LessonItem
            v-if="lessonData"
            :lessonInfo="lessonData[0]"
            :isBuy="info.buyStatus"
            :type="contentType!"
          ></LessonItem>
        </div>
      </div>
      <!-- 非领域的显示 -->
      <div class="part" v-else>
        <LessonInfo></LessonInfo>
      </div>
      <div class="map">
        <!-- 占位置 -->
        <!-- <img src="@/assets/images/knowledge/knowledgeindex.gif" /> -->
        <KsgMap ref="ksgMap"
          v-model:config="config"
          :data="data"
          :fetchAreaData="fetchAreaData"
          :fetchFocusData="fetchFocusData"
        />
      </div>
    </div>
    <div class="right-info">
      <div class="title">
        <img src="@/assets/images/prjlearn/u4108.svg" alt="" />
        学习说明
      </div>
      <div class="content">
        {{ studyInstructions }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import LessonInfo from './LessonInfo.vue';
import LessonItem from './LessonItem.vue';
import { PrjForm } from '@/types/project';
import { BuyStatus, GoodsType } from '@/types/goods';
import type { LessonInfoType } from '@/types/case';
import TitleandInfo from '../components/TitleandInfo.vue';
import { useRoute } from 'vue-router';
import { useProjectStore } from '@/stores/project';
import { getAreaData, getFocusData, getPrjGraph } from '@/apis/ksgmap';
import KsgMap from 'ksg-map'
import type { GlobalConfig, OriginalData, AreaData, PointData, FocusData } from 'ksg-map/dist/types';

const projectStore = useProjectStore();

const { info } = storeToRefs(projectStore);

//表明当前不是测试页面，显示相关按钮
const isTest = ref(false);
// 是否已经购买 控制样式

// 将路由传过来的是否可试看传给TitleandInfo做按钮显示的判断
const route = useRoute();

const isArea = inject('goodsType') as GoodsType;
const contentType = inject('prjForm') as PrjForm;

// let lessonData: LessonInfoType[] = [{
//   chapterName: '',
//   isLearned: false,
//   isTry: false,
//   chapterNum: 0,
//   sectionId: 0
// }]
const lessonData = inject('lessonData') as LessonInfoType[];
// 项目介绍
const studyInstructions = inject('studyInstructions');
const isObjectEmpty = (obj: any) => {
  return Object.keys(obj).length == 0 && obj.constructor == Object;
};

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null)
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: false,
})
const data= ref<OriginalData>({
  topAreas: [],
  areas: [],
  focuses: [],
  points: [],
})
async function fetchAreaData(id: string): Promise<AreaData[]> {
  return await getAreaData(id);
}
async function fetchFocusData(id: string): Promise<FocusData> {
  return await getFocusData(id);
}
onMounted(async () => {
  const unwatch = watch(() => info.value.spuId, async () => {
    if (info.value.spuId) {
      unwatch()
      const pointsData = (await getPrjGraph(info.value.spuId))
      await ksgMap.value?.ready;
      data.value.topAreas = []
      data.value.points = pointsData
      
      ksgMap.value?.reloadData();
    }
  })
  
})

</script>

<style lang="less" scoped>
.kl-map {
  width: 1100px;
  padding-left: 10px;
  display: flex;
  flex-direction: row;

  .main-content {
    width: 754px;
    border: 0.8px solid rgb(242, 242, 242);
    border-radius: 5px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
    // background-image: linear-gradient(to bottom, #f2f2f2, #ffffff);
    background-color: #fff;
    padding-right: 10px;
    padding-top: 20px;
    padding-bottom: 10px;
    padding-left: 10px;
    margin-bottom: 40px;

    display: flex;
    flex-direction: column; /* 垂直方向排列 */

    .info {
      width: 100%;
      height: 50px;

      margin-top: 10px;
      display: flex;
      align-items: center;
    }

    .part {
      width: 100%;
      margin-top: 20px;
    }

    .map {
      width: 100%;
      // height: 530px;
      // background-color: rgba(0, 85, 121, 0.376);
      border-radius: 5px;
      margin-top: 10px;
      flex-grow: 1;
    }
  }

  .right-info {
    width: 300px;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 10px;
    margin-left: 20px;
    height: 700px;

    .title {
      height: 20px;
      width: 100%;
      display: flex;
      align-items: center;
      font-size: var(--fontsize-middle-project);
      font-weight: 700;

      img {
        margin-right: 10px;
      }
    }

    .content {
      font-family: var(--text-family);
      margin-top: 12px;
      margin-bottom: 10px;
      width: 100%;
      /* 1.设置旧版弹性盒 */
      display: -webkit-box;
      /* 2.设置子元素的排列方式  垂直排列*/
      -webkit-box-orient: vertical;
      /* 3.溢出隐藏 */
      overflow: hidden;
      text-overflow: ellipsis;
      /* 4. 控制行数*/
      -webkit-line-clamp: 25;
      /* 5. 换行*/
      white-space: pre-wrap;
      font-weight: 400;
      font-size: var(--fontsize-middle-project);
      line-height: normal;
    }
  }
}
</style>
@/stores/learnintro
