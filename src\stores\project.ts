import { defineStore } from 'pinia';
import { type PrjInfo, PrjForm, PrjType } from '@/types/project';
import { BuyStatus, GoodsType } from '@/types/goods';

export const useProjectStore = defineStore('project', {
  state: () => ({
    info: {
      buyStatus: BuyStatus.nobuy,
      coverPic: '',
      description: '',
      purpose: '',
      goodsType: GoodsType.common,
      graspKlg: 0,
      klgCount: 0, //知识点总数
      prjType: PrjType.klgExplain,
      learned: 0, //
      price: '0', //todo
      prjForm: PrjForm.video,
      spuId: '', //
      studyInstructions: '',
      skuId: '', //todo
      chapterList: [],
      skuList: [],
      targetKlgs: [],
      studyTime: 0,
      title: '',
      userCoverPic: '', //todo
      userName: '', //todo
      editorName: '',
      editorPic: '',

      validDate: 0, //?有效期

      priceList: [
        {
          skuId: '',
          cardType: 0,
          subtotal: 0,
          discount: 0,
          actualPaidAmount: 0,
          studyTime: 0
        }
      ],
      startTime: '2024-11-20',
      expirationTime: '2024-12-21',
      latestChapterId: 0,
      hasPermission: BuyStatus.nobuy,
      validDays: 0,
      learnedDays: 0,
      learnedQuestionCount: 0,
      learnedKlgCount: 0,
      masteredKlgCount: 0,
      questionCount: 0,
      targetKnowledgeCount: 0
    },

    createdOrder: {
      actualPaidAmount: 0,
      creatTime: '',
      discountAmount: 0,
      orderNo: '',
      orderStatus: 0,
      subtotal: 0
    }
  }),
  getters: {},
  actions: {
    setPrjInfo(info: any) {
      this.info = info;
      this.info.buyStatus = this.info.hasPermission;
      this.info.validDate = this.info.validDays;
      if ('priceList' in info) {
        this.info.skuList = this.info.priceList;
      }
    },
    setSpuId(spuId: string) {
      this.info.spuId = spuId;
    },
    setSkuId(skuId: string) {
      this.info.skuId = skuId;
    },
    setPrice(price: string) {
      this.info.price = price;
    },
    setStudyTime(studyTime: number) {
      //判断this.info的类型
      if ('studyTime' in this.info) {
        this.info.studyTime = studyTime;
      }
    }
  }
});
