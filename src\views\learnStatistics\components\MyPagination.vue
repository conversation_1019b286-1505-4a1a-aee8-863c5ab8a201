<template>
  <!-- 
    根据原型实现的分页器
   -->
  <div class="pagination">
    <div
      :class="['pre', currentPage == 1 ? 'inactive' : 'active', isHori ? '' : 'vertical']"
      @click="prevclick"
    >
      <el-icon color="white" :size="11">
        <ArrowLeftBold />
      </el-icon>
    </div>
    <div class="page">{{ currentPage }}</div>
    <div
      :class="[
        'next',
        currentPage == total || props.total == 0 ? 'inactive' : 'active',
        isHori ? '' : 'vertical'
      ]"
      @click="nextclick"
    >
      <el-icon color="white" :size="11">
        <ArrowRightBold />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue';

let props = defineProps({
  total: {
    type: Number,
    require: true,
    default: 1
  },
  currentPage: {
    type: Number,
    require: true,
    default: 1
  },
  isHori: {
    type: Boolean,
    require: false,
    default: true
  }
});
// 当前页
let currentPage = toRef(props.currentPage);

/**
 * 向前一页函数
 */
function prevclick() {
  if (props.total == 0) return;
  if (currentPage.value == 1) {
    ElMessage({
      type: 'warning',
      message: '已经是第一页啦~'
    });
    return;
  }
  currentPage.value--;
}
/**
 * 向后一页函数
 */
function nextclick() {
  if (props.total == 0) return;
  if (currentPage.value == props.total) {
    ElMessage({
      type: 'warning',
      message: '已经是最后一页啦~'
    });
    return;
  }
  currentPage.value++;
}

let emits = defineEmits(['pageChange']);
watch(currentPage, (newValue, oldVale) => {
  emits('pageChange', newValue);
});
defineExpose({ currentPage });
</script>

<style lang="less" scoped>
.wrapper {
  height: 15px;
  width: 15px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.inactive {
  background-color: var(--color-inactive-project);
}

.active {
  background-color: var(--color-theme-project);
}

.pagination {
  width: 66px;
  height: 20px;
  display: flex;
  flex-direction: row;
  //justify-content: space-between;
  justify-content: center;

  .pre {
    margin-top: 7px;
    .wrapper();
  }

  .page {
    height: 15px;
    width: 15px;
    border-radius: 3px;
    background-color: var(--color-theme-project);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: white;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 7px;
  }

  .next {
    margin-top: 7px;
    .wrapper();
  }

  .vertical {
    transform: rotate(90deg);
  }
}
</style>
