import escapeStringRegexp from '@/utils/escapeStringRegexp';
import { useWordStore } from '@/stores/word';
import { PrjForm } from '@/types';
import { useDraftWordStoreV2 } from '@/stores/draftWordV2';
let globalPrjForm: null | PrjForm = null;

const oldMatchUnits: any[] = [];
function handleSearch(str: string, prjForm: PrjForm): void {
  globalPrjForm = prjForm;
  while (oldMatchUnits.length > 0) {
    const unit = oldMatchUnits.pop();
    unit.highlight = false;
    if (globalPrjForm == PrjForm.video) {
      handleVideoUnit(unit);
    } else if (globalPrjForm == PrjForm.draft) {
      handleDraftUnit(unit);
    }
  }
  if (str.trim().length == 0) {
    return;
  }
  const draftWordStore = useDraftWordStoreV2();
  const wordStore = useWordStore();
  const regString = draftWordStore.regString;
  const positions = findAllOccurrences(regString, str.replace(/\n/g, ''));
  for (const position of positions) {
    find(position, str.length);
  }
}

function find(postion: number, offset: number) {
  const wordStore = useWordStore();
  const { unitList } = storeToRefs(wordStore);

  for (let i = postion; i < postion + offset; i++) {
    const unit = unitList.value[i];
    if (unit) {
      oldMatchUnits.push(unit);
      unit.highlight = true;

      if (globalPrjForm == PrjForm.video) {
        handleVideoUnit(unit);
      } else if (globalPrjForm == PrjForm.draft) {
        handleDraftUnit(unit);
      }
    }
  }
}

function handleDraftUnit(node: {
  tagName: string;
  children: any[];
  index: number;
  qids: number[];
  highlight: boolean;
  stringIndex: number;
}) {
  const wordStore = useWordStore();
  const { draftStringList } = storeToRefs(wordStore);
  if (node.qids.length == 0) {
    if (!node.highlight) {
      draftStringList.value[node.stringIndex] = node.children[0];
    } else {
      draftStringList.value[node.stringIndex] =
        `<span class="highlight2">${node.children[0]}</span>`;
    }
  } else {
    const qids = node.qids.join(',');
    if (!node.highlight) {
      draftStringList.value[node.stringIndex] =
        `<span data-qid="${qids}" class="highlight">${node.children[0]}</span>`;
    } else {
      draftStringList.value[node.stringIndex] =
        `<span data-qid="${qids}" class="highlight highlight2">${node.children[0]}</span>`;
    }
  }
}
function handleVideoUnit(node: {
  tagName: string;
  children: any[];
  index: number;
  qids: number[];
  highlight: boolean;
  stringIndex: number;
}) {
  const wordStore = useWordStore();
  const { videoStringList } = storeToRefs(wordStore);
  if (node.qids.length == 0) {
    if (!node.highlight) {
      videoStringList.value.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] = node.children[0];
        }
      });
    } else {
      videoStringList.value.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span class="highlight2">${node.children[0]}</span>`;
        }
      });
    }
  } else {
    const qids = node.qids.join(',');
    if (!node.highlight) {
      videoStringList.value.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span class="highlight" data-qid="${qids}">${node.children[0]}</span>`;
        }
      });
    } else {
      videoStringList.value.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span class="highlight highlight2" data-qid="${qids}">${node.children[0]}</span>`;
        }
      });
    }
  }
}

function findAllOccurrences(str: string, subStr: string) {
  const matchStr = str.toLowerCase();
  const matchSubStr = subStr.toLowerCase();
  const regex = new RegExp(escapeStringRegexp(matchSubStr), 'g');
  const matches = [...matchStr.matchAll(regex)];
  const positions = matches.map((match) => match.index);
  return positions;
}

export { handleSearch };
