<template>
  <div class="content">
    <div class="error-top-content">
      <div class="content-question">
        <span v-html="data.examTitle"></span>
      </div>
      <el-skeleton :loading="!data.hasAccess">
        <template #default>
          <div v-if="data.examType == ExamType.choice">
            <el-form-item label="">
              <el-radio-group v-model="data.myAnswer" class="selection-style">
                <el-radio
                  :label="indexIntoAlpha(index)"
                  v-for="(selection, index) in data.examChoices"
                  :key="index"
                  disabled
                >
                  <span>{{ indexIntoAlpha(index) }}</span>
                  <span v-html="selection"></span>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div v-if="data.examType == ExamType.judgment">
            <el-form-item label="">
              <el-radio-group v-model="data.myAnswer" class="selection-style">
                <el-radio label="true" disabled
                  ><span class="iconfont icon-duigou1"></span
                ></el-radio>
                <el-radio label="false" disabled
                  ><span class="iconfont icon-cuowu"></span
                ></el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div v-if="data.examType == ExamType.fillBlank" class="replycontent-style">
            <p class="replycontent-title">回答:</p>
            <!-- <ckeditor :editor="editor" v-model="item.myAnswer"></ckeditor> -->
            <div>{{ data.myAnswer }}</div>
          </div>
          <div v-if="data.examType == ExamType.shortAnswer" class="replycontent-style">
            <p class="replycontent-title">回答:</p>
            <!-- <ckeditor :editor="editor" v-model="item.myAnswer"></ckeditor> -->
            <div>{{ data.myAnswer }}</div>
          </div>
          <div class="explanation-wrap">
            <div class="detail">
              <div class="choice">
                <div>
                  答案：
                  <span v-html="data.examAnswer"></span>
                </div>
              </div>
              <div class="description">
                说明：
                <span
                  v-html="renderMarkdown(data.examExplanation)"
                  @mouseup="DelineateWords"
                ></span>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <div class="error-top-list">
      <p>问题列表</p>
      <div
        v-for="questionItem in data.questionList"
        :key="questionItem.questionId"
        class="questionliststyle"
        @click="questionDetail(questionItem)"
      >
        <span v-html="questionItem.keyword + ' ' + questionItem.questionType"></span>
      </div>
    </div>
  </div>
  <!-- 问题详情 -->
  <el-dialog v-model="dialogDetailVisible" width="572px">
    <template #header>
      <div class="my-header">
        <span>问题</span>
      </div>
    </template>
    <div class="dialog-content">
      <div class="dialog-content-top" v-html="currentQuestion.associatedWords"></div>
      <p class="dialog-content-middle">其中</p>
      <div class="dialog-content-bottom">
        <div class="section1">
          <div class="section-item">
            <span class="section-name">{{ currentQuestion.userName }}</span
            ><span style="font-size: 12px">的提问</span>
          </div>
          <div class="section-item">
            "<span v-html="currentQuestion.keyword"></span>" {{ currentQuestion.questionType }}
          </div>
          <div class="section-time">{{ currentQuestion.createTime }}</div>
        </div>
        <div class="line"></div>
        <div class="section2" v-for="item in currentQuestion.answers" :key="item.answerId">
          <div class="section-item">
            <span class="section-name">{{ item.creatorName }}</span
            ><span style="font-size: 12px">的回答</span>
          </div>
          <div class="section-item section-klg">
            <span v-for="(klg, index) in item.answerKlgs" :key="index">#{{ klg.klgTitle }}#</span>
          </div>
          <div class="section-item">
            <span>解释说明</span>
            <p>
              {{ item.answerExplanation }}
            </p>
          </div>
          <div class="section-time">{{ item.createTime }}</div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <!-- <el-button @click="dialogDetailVisible = false">Cancel</el-button> -->
        <CmpButton class="btn" type="info" @click="dialogDetailVisible = false">关闭问题</CmpButton>
      </span>
    </template>
  </el-dialog>

  <!-- 提出问题 -->
  <el-dialog v-model="buildupQuestionVisible" width="572px">
    <template #header>
      <div class="my-header">
        <span>问题</span>
      </div>
    </template>
    <div class="dialog-content">
      <div class="dialog-content-top">测验内容中提到:{{ form.referenceContent }}</div>
      <el-form :model="form" label-width="96px" :rules="rules" ref="ruleFormRef">
        <el-form-item label="关键字" prop="keyword">
          <ClassicEditor v-model="form.keyword"></ClassicEditor>
        </el-form-item>
        <el-form-item label="问题类型" prop="type">
          <el-select v-model="form.type" placeholder="选择问题类型">
            <el-option label="是什么" value="是什么" />
            <el-option label="为什么" value="为什么" />
            <el-option label="怎么做" value="怎么做" />
            <el-option label="自定义类型" value="自定义类型" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <CmpButton class="btn" type="info" @click="buildupQuestionVisible = false"
          >取消问题</CmpButton
        >
        <CmpButton class="btn" type="primary" @click="submitQuestion()">提交问题</CmpButton>
      </span>
    </template>
  </el-dialog>
  <!-- 没有查看问题的权限 -->
  <el-dialog v-model="showAccessDialog" width="30%">
    <div>没有查看问题的权限</div>
  </el-dialog>
</template>
<script setup lang="ts">
import { getQuestionDetailApi } from '@/apis/learning';
import ClassicEditor from '@/components/editors/Veditor.vue';
import { addQuestionApi } from '@/apis/learning';
import type { documentData, questionData } from '@/types/data';
import indexIntoAlpha from '@/utils/indexIntoAlpha';
import { handleLineWord } from '@/utils/lineWord';
import { ExamType } from '@/types/exam';
import type { FormRules } from 'element-plus';
import { renderMarkdown } from '@/utils/markdown';
// 从父组件获取题目具体信息
const props = defineProps(['questionObj']);
console.log('从父组件传递过来的参数', props.questionObj);
// let data: documentData = props.tiwenObj;
let data = ref<documentData>({
  spuId: '',
  examId: 0, //测试题目id
  sectionId: 0, //小节id
  order: 0, //题目序号
  sectionTitle: '', //小节名称
  examType: ExamType.fillBlank, //1.填空 2选择 3判断 4问答
  examTitle: '', //题目
  examChoices: [''], //选项
  examAnswer: '', //答案
  myAnswer: '',
  examExplanation: '', //解释说明
  hasAccess: true,
  questionList: [
    {
      questionId: 0, //问题自增id
      associatedWords: '', //关联文本内容
      keyword: '', //关键字内容
      questionType: '', //问题类型，是什么、为什么、怎么做
      questionNecessity: 0, //问题必要性 1必须问题，2参考问题
      creatorId: '', //创建者id
      creatorName: '', //创建者名字
      createTime: '', //创建时间
      explanation: '',
      answerList: [
        {
          answerId: 0, //回答自增id
          answerKlgs: [
            {
              klgId: 0,
              klgTitle: ''
            }
          ], //知识点名称列表
          answerExplanation: '', //解释
          createTime: '', //回答时间
          creatorName: '', //回答者名称
          modifiedTime: '' //修改时间
        }
      ]
    }
  ]
});

const skeletonLoading = ref(true);
const showAccessDialog = ref(false);
watch(
  () => props.questionObj,
  (newVal) => {
    data.value = newVal;
    skeletonLoading.value = false;
    if (newVal.hasAccess == false) {
      showAccessDialog.value = true;
    }
  },
  { deep: true, immediate: true }
);

// 查看问题详情
const dialogDetailVisible = ref(false);
// 转换问题类型
const questionTypeMap = new Map([
  [1, '是什么?'],
  [2, '为什么？'],
  [3, '怎么做？'],
  [4, '自定义类型']
]);
// 当前点开的问题
const currentQuestion = ref<questionData>({
  questionId: 0, //问题自增id
  associatedWords: '', //关联文本内容
  keyword: '', //关键字内容
  questionType: '', //问题类型，是什么、为什么、怎么做,自定义
  questionNecessity: 0, //问题必要性 1必须问题，2参考问题
  creatorId: '', //创建者id
  creatorName: '', //创建者名字
  createTime: '', //创建时间
  explanation: '',
  answerList: [
    {
      answerId: 0, //回答自增id
      answerKlgs: [
        {
          klgId: 0,
          klgTitle: ''
        }
      ], //知识点名称列表
      answerExplanation: '', //解释
      createTime: '', //回答时间
      creatorName: '', //回答者名称
      modifiedTime: '' //修改时间
    }
  ]
});

const questionDetail = async (item: questionData) => {
  dialogDetailVisible.value = true;
  console.log('当前问题详情', currentQuestion.value.associatedWords);
  const res = await getQuestionDetailApi(item.questionId);
  currentQuestion.value = res.data[0];
};

// 划词弹窗
const buildupQuestionVisible = ref(false); //提问弹窗
interface RuleForm {
  referenceContent: string;
  keyword: string;
  type: string;
}
const form = ref<RuleForm>({
  referenceContent: '',
  keyword: '',
  type: '是什么'
});
const ruleFormRef = ref<any>();
const rules = ref<FormRules<RuleForm>>({
  keyword: [
    {
      required: true,
      message: '请输入关键字'
    }
  ],
  type: [
    {
      required: true,
      message: '请选择问题类型'
    }
  ]
});

// 高亮
const lightword = ref('');
const showlightWord = ref('');
watch(lightword, (val) => {
  showlightWord.value = val
    .replace(/<p>/g, '')
    .replace(/<\/p>/g, '')
    .replace(/[.[*?+;-^$|(){}/]|\]|\\/g, '\\$&');
  const pattern = new RegExp(`(${showlightWord.value})`, 'gi');
  data.value.examExplanation = data.value.examExplanation.replace(
    pattern,
    '<span class="highlight">$1</span>'
  );
  let showlightWord2;
  if (lightword.value.indexOf('math/tex') != -1) {
    showlightWord2 = val
      .replace('math/tex', 'math/tex; mode=display')
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .replace(/[.[*?+;-^$|(){}/]|\]|\\/g, '\\$&');
    const pattern2 = new RegExp(`(${showlightWord2})`, 'gi');
    data.value.examExplanation = data.value.examExplanation.replace(
      pattern2,
      '<span class="highlight">$1</span>'
    );
  }
});

// 划词
const DelineateWords = async () => {
  const result = handleLineWord();
  if (result != null) {
    form.value.referenceContent = result;
    form.value.keyword = result;
    buildupQuestionVisible.value = true;
  }
};
// 提交问题
const submitQuestion = () => {
  ruleFormRef.value.validate((valid: any) => {
    if (valid) {
      const params = {
        sectionId: data.value.sectionId,
        assessmentId: data.value.examId,
        // FIXME: 还没对好
        spuId: data.value.spuId,
        associatedWords: form.value.referenceContent,
        questionType: form.value.type,
        keyword: form.value.keyword
      };
      addQuestionApi(params)
        .then((res) => {
          // @ts-ignore
          if (res.code == 20000) {
            buildupQuestionVisible.value = false;
            lightword.value = form.value.keyword;
          } else {
            ElMessage({
              // @ts-ignore
              message: res.message,
              type: 'warning'
            });
          }
        })
        .catch((error) => {
          ElMessage({
            message: error,
            type: 'warning'
          });
        });
    } else {
      ElMessage({
        message: '验证失败',
        type: 'warning'
      });
    }
  });
};
</script>

<style lang="less" scoped>
.content {
  display: flex;

  .error-top-content {
    padding-right: 20px;
    width: 527px;
    margin-bottom: 20px;
    // height: auto;
    // margin-bottom: 100px;

    .content-question {
      font-size: 14px;
      font-weight: 700;
      color: #333333;
      margin-left: 74px;
      display: flex;
      flex-wrap: wrap;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .selection-style {
      display: flex;
      flex-direction: column;
      margin-left: 30px;
      align-items: flex-start;

      .el-radio {
        margin-top: 18px;

        span {
          margin-left: 32px;
          margin-right: 5px;
        }
      }
    }

    .explanation-wrap {
      width: 447px;
      min-height: 134px;
      border-radius: 5px;
      margin-left: 30px;
      padding: 11px 0px;
      font-size: 14px;
      white-space: pre-wrap;

      .detail {
        .choice {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        }

        .description {
          margin-top: 20px;
          // overflow: auto
        }
      }
    }
  }

  .error-top-list {
    width: 422px;
    background-color: rgba(242, 242, 242, 0.376);
    border-radius: 5px;
    padding: 15px 22px;

    // 这里使用固定高度后续可能出现问题
    // overflow-y: auto;
    min-height: 355px;

    p {
      font-size: 14px;
      font-weight: 700;
    }

    .questionliststyle {
      margin-top: 17px;
      font-size: 14px;
    }

    .questionliststyle:hover {
      cursor: pointer;
      color: #005539;
      font-weight: 700;
    }
  }
}

.my-header {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-theme-project);
}

.dialog-content {
  white-space: pre-wrap;

  .dialog-content-top {
    width: 540px;
    min-height: 40px;
    background-color: #f2f2f2;
    border-radius: 2px;
    display: flex;
    align-items: center;
    padding-left: 15px;
    margin-bottom: 5px;
  }

  .dialog-content-middle {
    font-size: 12px;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .dialog-content-bottom {
    width: 540px;
    min-height: 222px;
    background-color: #f2f2f2;
    border-radius: 2px;
    padding: 12px 10px;

    .line {
      width: 517px;
      height: 2px;
      background-color: #ffff;
      margin: 11px auto;
    }

    .section-name {
      font-size: 12px;
      font-weight: 700;
      margin-right: 18px;
    }

    .section-item {
      margin-bottom: 8px;
      white-space: pre-wrap;
    }

    .section-time {
      font-size: 12px;
    }

    .section2 {
      padding-left: 25px;
      margin-bottom: 10px;
    }

    .section-klg {
      font-size: 14px;
      font-weight: 400;
      color: #005579;

      span {
        margin-right: 37px;
      }

      span:hover {
        font-weight: 700;
        pointer: cursor;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;

  .btn {
    width: 160px;
    height: 43px;
    margin-right: 21px;
  }
}
</style>
