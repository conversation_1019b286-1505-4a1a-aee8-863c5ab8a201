<template>
  <div class="message-container">
    <div class="message-menu">
      <div class="message-menu-prgitem">系统信息</div>
    </div>
    <div class="message-content">
      <el-skeleton style="width: 1000px" :rows="5" :loading="!ready" :throttle="100" animated>
        <template #default>
          <div v-if="ready">
            <div v-if="messageList.length != 0" class="message-list">
              <div class="message-list-item" v-for="(item, index) in messageList" :key="index">
                <div class="message-list-item-header">
                  <span class="message-list-itemtitle">{{ item.title }}</span>
                  <span class="message-list-time">{{ item.createTime }}</span>
                </div>
                <p class="message-list-content">{{ item.content }}</p>
              </div>
              <div class="message-list-bottom">
                <div class="down-more" @click="getMore" v-if="shouldMore">加载更多</div>
                <div class="no-more" style="cursor: default" v-if="!shouldMore && current != 1">已经是最底部啦</div>
                <Backtop></Backtop>
              </div>
            </div>
            <div v-else class="lc-empty"><el-empty description="暂无消息记录" /></div>
          </div>
        </template>
        <template #template>
          <div class="message-list">
            <div v-for="i in 5" :key="i" class="message-list-item">
              <div class="message-list-item-header">
                <el-skeleton-item variant="text" style="width: 60%; height: 18px; margin-bottom: 8px" />
                <el-skeleton-item variant="text" style="width: 25%; height: 14px" />
              </div>
              <div style="margin-top: 8px">
                <el-skeleton-item variant="p" style="width: 100%; height: 14px; margin-bottom: 4px" />
                <el-skeleton-item variant="p" style="width: 85%; height: 14px; margin-bottom: 4px" />
                <el-skeleton-item variant="p" style="width: 70%; height: 14px" />
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getMessagelistApi } from '@/apis/message';
import Backtop from '@/components/Backtop.vue';
// 消息列表
// 获取消息列表
interface messageData {
  oid: number;
  title: string;
  sort: string;
  messageTitle: string;
  publicTime: string;
  messageContent: string;
  content?: string;
  createTime?: string;
}
const limit = 10;
const current = ref(1);
const messageList = ref<messageData[]>([]); // 订单列表
const messageListTotal = ref(0); // 列表总数
let currentTotal = ref(0);
let shouldMore = ref(true);
const ready = ref(false);

// const getMessagelist = async (current: number, limit: number) => {
//   try {
//     const res = await getMessagelistApi(current, limit);
//     // console.log('getMessagelistres:', res);
//     if ((res as any).data.records != undefined) {    
//       messageListTotal.value = (res as any).data.total;
//       messageList.value = (res as any).data.records;
//       currentTotal.value = (current - 1) * limit + (res as any).data.records.length;
//       shouldMore.value = currentTotal.value < messageListTotal.value ? true : false;
//       // 只有在数据加载成功后才设置 ready 为 true
//       ready.value = true;
//     } 
//   } catch (error) {
//     console.error('获取消息列表失败:', error);
//     // 即使出错，也要设置 ready 为 true，避免一直显示骨架屏
//     ready.value = true;
//   }
// };

onMounted(async () => {
  current.value = 1;
  try {
    const res = await getMessagelistApi(current.value, limit);
    // console.log('getMessagelistres:', res);
    if ((res as any).data.records != undefined) {    
      messageListTotal.value = (res as any).data.total;
      messageList.value = (res as any).data.records;
      currentTotal.value = (current.value - 1) * limit + (res as any).data.records.length;
      shouldMore.value = currentTotal.value < messageListTotal.value ? true : false;
    } 
  } catch (error) {
    console.error('获取消息列表失败:', error);
    // 即使出错，也要设置 ready 为 true，避免一直显示骨架屏
  }
  // 调试时使用
  // setTimeout(() => {
  //   ready.value = true;
  // }, 100000);
  ready.value = true;
});

onBeforeMount(() => {
  ready.value = false;
});

// 加载更多
const getMore = () => {
  current.value += 1;
  getMessagelistApi(current.value, limit).then((res: any) => {
    messageListTotal.value = res.data.total;
    messageList.value = messageList.value.concat(res.data.records || res.data.systemMessage?.records || []);
    currentTotal.value = (current.value - 1) * limit + res.data.records.length;
    shouldMore.value = currentTotal.value < messageListTotal.value ? true : false;
  });
};
</script>

<style scoped lang="less">
@width: 1400px;
@menuwidth: 300px;
@menuheight: 50px;
@fontfamily: var(--title-family);
@fontsize: 16px;
@bgdeepcolor: var(--color-theme-project);
@bglightcolor: #fff;
@bghovercolor: var(--color-second);
@margin: 10px;

.router-link-active {
  text-decoration: none;
  color: #fff;
}

a {
  text-decoration: none;
  color: #fff;
}

.hoverstyle {
  background-color: @bghovercolor;
  color: @bgdeepcolor;
  border: 1px solid @bgdeepcolor;
}

.message-container {
  color: var(--color-black);
  font-family: var(--title-family);
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-top: 2 * @margin;

  .message-menu {
    margin-left: -80px;

    .message-menu-item {
      width: @menuwidth;
      height: @menuheight;
      font-family: @fontfamily;
      // border: 1px solid #333;
      font-size: @fontsize;
      margin-bottom: @margin;
      display: flex;
      align-items: center;
      padding-left: @margin;
      cursor: pointer;
    }

    .message-menu-prgitem {
      background-color: @bgdeepcolor;
      color: #fff;
      .message-menu-item();
    }

    .message-menu-prgitem:hover {
      .hoverstyle();
    }

    .message-menu-vipitem {
      background-color: @bglightcolor;
      color: black;
      .message-menu-item();
    }

    .message-menu-vipitem:hover {
      .hoverstyle();
    }
  }

  .message-content {
    width: 1000px;
  }

  .lc-empty {
    width: 1000px;
    height: 670px;
  }

  .message-list {
    display: flex;
    flex-direction: column;
    width: 1000px;

    .message-list-item {
      margin-left: @margin;
      margin-right: @margin;
      min-height: 86px;
      border: 1px solid #eee;
      box-shadow: 0px 5px 5px #eee;
      margin-bottom: @margin;
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      padding-left: 15px;
      padding-right: 15px;
      padding-top: 5px;

      .message-list-item-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .message-list-itemtitle {
          // min-width: 30%;
          // margin-right: 100px;
          font-size: @fontsize;
          font-weight: 600;
        }

        .message-list-time {
          font-size: 12px;
          font-weight: 400;
          margin-right: 15px;
        }
      }

      .message-list-content {
        margin-top: 5px;
        font-size: 14px;
      }
    }

    .message-list-bottom {
      display: flex;
      flex-direction: row;
      margin-top: 53px;
      margin-bottom: 53px;

      .no-more {
        width: 100%;
        display: flex;
        justify-content: center;
        color: var(--color-theme-project);
        text-align: center;
        font-family: var(--text-family);
        font-weight: 400;
        font-size: 14px;
        height: 20px;
        line-height: 20px;
        margin: 10px 0;
      }
      .down-more {
        width: 100%;
        display: flex;
        justify-content: center;
        color: var(--color-theme-project);
        cursor: pointer;
      }

      .down-more:hover {
        font-weight: bolder;
      }
    }
  }
}
</style>
