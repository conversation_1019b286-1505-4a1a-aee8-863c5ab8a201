<template>
    <span class="tag">{{ prefix ? prefix : '' }}{{ label }}{{ prefix ? prefix : '' }}</span>
  </template>
  
  <script setup lang="ts">
  // withDefaults();
  defineProps<{
    label?: string;
    prefix?: string | boolean;
  }>();
  </script>
  
  <style scoped lang="less">
  .tag {
    width: 80px;
    height: 20px;
    font-size: 12px;
    font-weight: 400;
    color: #005579;
    line-height: 20px;
    //border: 1px solid rgb(0, 85, 121);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
  }
  </style>
  