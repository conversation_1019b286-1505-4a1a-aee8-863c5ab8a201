import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: {
      goal: '',
      industry: '',
      career: '',
      skills: [],
      uniqueCode: '',
      username: '',
      coverPic: '',
      selfIntroduction: '',
      phone: '',
      days: 0,
      todayTime: 0
    }
  }),
  actions: {
    setUserInfo(userInfo: any) {
      this.userInfo = userInfo;
    }
  }
});
