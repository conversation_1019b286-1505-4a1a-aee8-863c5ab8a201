<template>
  <!-- 项目文稿 -->
  <div class="layout">
    <div
      class="content-card"
      v-if="prjForm != PrjForm.video || (props.videoCaptionList && !props.isMap)"
      :class="{ active: mode }"
    >
      <div>返回项目学习</div>
      <div
        style="font-size: 16px; color: #333; font-family: var(--title-family); font-weight: 500"
        v-for="item in chapterList"
      >
        <span v-if="item.chapterId == route.query.chapterId">{{ item.chapterName }}</span>
      </div>
      <div id="header" class="tool">
        <div class="mode">
          <el-switch v-model="mode" />
          <span>{{ mode ? '提问模式' : '阅读模式' }}</span>
        </div>
        <div class="input">
          <el-input
            placeholder="请输入要查找的关键字"
            v-show="!mode"
            v-model="searchKey"
            @keyup.enter="handleSearchFn"
            clearable
          >
            <template #append>
              <el-button :icon="Search" @click="handleSearchFn" />
            </template>
          </el-input>
        </div>
      </div>
      <el-watermark :font="font" :content="userInfo.username + '@' + userInfo.phone">
        <div
          class="content-text-wrapper"
          :class="{ learning: $route.name == 'learning' }"
          ref="contentWrapperRef"
          @wheel="handleWheelFn"
          id="script"
          @click="handleWord"
          @mouseover="handleHover"
          @mouseout="cancelHover"
        >
          <!-- 视频项目的文稿 -->
          <template v-if="prjForm == PrjForm.video">
            <!--        mode: false=>read true=>question -->
            <div id="htmlContent">
              <PrjParagraph4Read
                ref="prjParagraph4ReadList"
                @showQuestion="handleShowQuestion"
                v-for="(paragraphList, idx) in myWordContent"
                :key="idx"
                :paraIndex="idx"
                :questionList="questionList"
                :paragraphList="paragraphList"
                :searchKey="searchKey"
                :curoid="curoid"
              >
                <!-- <template v-if="true">
                <PrjAskCard
                  v-for="(question, idx) in filterQuestionList"
                  :key="'_' + idx"
                  v-bind="question"
                  @returnInit="handleReturnInitFn"
                ></PrjAskCard>
              </template> -->
              </PrjParagraph4Read>
            </div>
          </template>
          <template v-else>
            <!-- 文稿项目文稿 -->
            <!--        FIXME: 没-->
            <template v-if="myWordContent">
              <!-- <PrjParagraph :wordcontent="wordContent" @ask-question="handleAskQuestionFn" >
            <template v-if="curhighlightQuestion && curhighlightQuestion.length > 0">
              <el-dialog v-model="curAskingDialog" draggable width="30%">
                <PrjAskCard
                  v-for="(question, idx) in curhighlightQuestion"
                  :key="'_' + idx"
                  v-bind="question"
                  @returnInit="handleReturnInitFn"
                ></PrjAskCard>
              </el-dialog>
            </template>
          </PrjParagraph> -->
              <div v-html="myWordContent" id="htmlContent"></div>
            </template>
          </template>
        </div>
      </el-watermark>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, type Events } from 'vue';
import { Search } from '@element-plus/icons-vue';
import PrjParagraph4Read from './PrjParagraph4Read.vue';
import { STATE_FLAG } from '@/types/learning';
import type { VideoCaptionListObj, QuestionData } from '@/types/learning';
import { getQuestionDetailApi, getPrjDetailApi } from '@/apis/learning';
import { PrjForm } from '@/types/project';
// import { QuestionType, qMode } from '@/types/constant';
// import PrjQuestionDrawer from './PrjQuestionDrawer.vue';
// import PrjAnswerDrawer from './PrjAnswerDrawer.vue';
import { handleLineWord, getQuestionList, updataDom, markWord, unmarkWord } from '@/utils/lineWord';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { useUserStore } from '@/stores/user';
import { useWordStore } from '@/stores/word';
import { handleSearch } from '@/utils/handleSearch';
import { intersection } from 'lodash-es';

const chapterList = ref({});
const userStore = useUserStore();
const drawerControllerStore = useDrawerControllerStore();
const wordStore = useWordStore();
const { userInfo } = storeToRefs(userStore);
const prjParagraph4ReadList = ref<Array<InstanceType<typeof PrjParagraph4Read> | null>>([]);

const handleWord = inject('handleWord') as (e: Event) => void;
const handleHover = (e: Event) => {
  let element = e.target as HTMLElement;
  if ((e.target as HTMLElement).closest("[class^='inline-equation'], [class^='equation']")) {
    element = (e.target as HTMLElement).closest(
      "[class^='inline-equation'], [class^='equation']"
    ) as HTMLElement;
  }
  element = element.closest('[data-qid]') as HTMLElement;
  if (element && element.classList.contains('highlight')) {
    element.classList.add('highlightHover');
    const index = parseInt(element.getAttribute('data-index') as string);
    const rootQids = (element.getAttribute('data-qid') as string).split(',');
    for (let i = index - 1; ; i--) {
      const ele = document.querySelector(`span[data-index="${i}"]`) as HTMLElement;
      if (!ele) {
        break;
      }
      const qidString = ele.getAttribute('data-qid');
      if (qidString) {
        const qids = qidString.split(',');
        if (intersection(rootQids, qids).length > 0) {
          ele.classList.add('highlightHover');
        } else {
          break;
        }
      } else {
        break;
      }
    }
    for (let i = index + 1; ; ++i) {
      const ele = document.querySelector(`span[data-index="${i}"]`) as HTMLElement;
      if (!ele) {
        break;
      }
      const qidString = ele.getAttribute('data-qid');
      if (qidString) {
        const qids = qidString.split(',');
        if (intersection(rootQids, qids).length > 0) {
          ele.classList.add('highlightHover');
        } else {
          break;
        }
      } else {
        break;
      }
    }
  }
};
const cancelHover = (e: Event) => {
  const elements = document.querySelectorAll('.highlightHover');
  elements.forEach((element) => {
    element.classList.remove('highlightHover');
  });
};
// 打包注释
// import { tr } from 'element-plus/es/locale';
// markWord()
interface Props {
  videoCaptionList?: [VideoCaptionListObj[]]; // 讲稿段落
  questionList?: QuestionData[]; // 问题列表
  wordContent?: ''; // 文本项目文本
  // big?: false;
  isMap?: Boolean;
}
const emits = defineEmits([
  'returnInit',
  'scrollInTop',
  'refresh',
  'refreshContent',
  'deleteQuestion',
  'search'
]);

const font = reactive({
  color: 'rgba(0, 0, 0, .07)'
});
// emit事件
const props = defineProps<Props>();
// const showContentWord=ref(false)

const prjForm = inject('prjForm') as Ref;
// const prjType = ref(1);
const isBig = inject('isBig') as Ref;
// console.log('isBig', isBig);
const { mode } = storeToRefs(drawerControllerStore); // false为read模式 或者true为 question 模式
const matchingColor = computed(() => {
  return mode.value ? '' : '#FFDD6C';
});
const matchingHeight = computed(() => {
  return mode.value ? '' : '700';
});
const searchKey = ref('');
const lightWords = computed(() => props.questionList?.map((question) => question.keyword) ?? []); // 高亮词语
const qList = ref<QuestionData[]>([]);

const myWordContent = ref();
const router = useRouter();
const route = useRoute();
let questionId = route.query.questionId as string;
let chapterId = route.query.chapterId as string;
let spuId = route.query.spuId as string;

watch(
  () => route.query.questionId,
  async (newVal) => {
    const res = await getQuestionDetailApi(newVal);
    const tempKeyWords = res.data[0].keyword;
    searchKey.value = tempKeyWords.replace(/<[^>]*>/g, '');
    handleSearchFn();
  }
);

watch(
  () => props.wordContent,
  async (newVal) => {
    myWordContent.value = newVal;
  }
);
watch(
  () => props.videoCaptionList,
  async (newVal) => {
    myWordContent.value = newVal;
  }
);
myWordContent.value = prjForm.value == PrjForm.video ? props.videoCaptionList : props.wordContent;
// TODO: 这个地方有点风险，在其他地方\\b\\b会起作用，会匹配上<符号
// TODO: 而不是完全不匹配内容，如果这个地方出bug，可以重点看一下\\b\\b是否匹配上什么东西了
const pattern2 = ref(
  lightWords.value.length > 0 ? new RegExp(lightWords.value.join('|'), 'g') : new RegExp('\\b\\b')
); // 正则

const prjSectionInfo = inject('prjSectionInfo') as Ref;

// video文稿原始段落

// 文稿项目的文稿
// const firstWordContent = computed(
//   () =>
//     props.wordContent?.replace(
//       pattern2.value,
//       (match: string) => `<span class="${'text-hover'}" data-id="">${match}</span>`
//     ) ?? ''
// ); // 第一次渲染的时候的文本
// const hoverWordContent = ref(toValue(firstWordContent.value));
// 为划词的内容绑定点击事件 控制弹窗

// 👇因为字幕数据结构变化，废弃此版，改为直接把列表传进组件，在组件内处理标签
// // TODO: hoverPList和highlightPList可以合并成一个，通过模式来控制一个类，再继续控制高亮
// // 高亮的文本根据两部分来确定
// // 1. 传递的属性值的问题
// // 2. 输入框的值
// const initHoverPlist = getPlist(props.videoCaptionList!, 'text-hover');
// const hoverPList = ref(initHoverPlist); // 悬浮段落
// // console.log('hoverPList', hoverPList)
// const highlightPList = computed(() => getPlist(props.videoCaptionList!, 'highlight')); // 高亮段落
// 👆因为字幕数据结构变化，废弃此版，改为直接把列表传进组件，在组件内处理标签

const stateFlag = ref(STATE_FLAG.init);
// TODO：还得继续优化，多次点击的时候直接返回，还可以加一个缓存函数
// const wordsSet = new Set();
// 文稿项目的搜索已经实现了，视频项目的搜索还没实现
const handleChangeMode = () => {
  if (!mode.value) {
    markWord(); // 阅读模式
  } else {
    unmarkWord(); // 提问模式
  }
};

const tempSearchKey = ref('');
let matchingElements: HTMLElement[] = [];
let matchingIndex = 0;
const matchingElementList = ref<HTMLElement[]>([]);
console.log('===matchingElementList', matchingElementList);
// watch(
//   () => matchingElementList.value,
//   (newVal, oldVal) => {
//     if (newVal) {
//       // console.log("=====newVal",newVal)
//       newVal.forEach((element) => {
//         element.classList.add('activeElement');
//       });
//     }
//     if (oldVal) {
//       oldVal.forEach((element) => {
//         element.classList.remove('activeElement');
//       });
//     }
//   }
// );

const handleSearchFn = async () => {
  if (tempSearchKey.value == searchKey.value) {
    console.log('===tempSearchKey,11111111', tempSearchKey);
    let index = 0;
    matchingElementList.value = [];
    while (index < searchKey.value.length) {
      matchingElementList.value.push(matchingElements[matchingIndex % matchingElements.length]);
      // console.log("=====matchingElements[matchingIndex % matchingElements.length",matchingElements[matchingIndex % matchingElements.length])
      matchingIndex++;
      index++;
    }
    contentWrapperRef.value.scrollTo({
      top: matchingElementList.value![0].offsetTop - 100,
      behavior: 'smooth'
    });
  } else {
    // console.log("===tempSearchKey,222222",tempSearchKey)
    matchingIndex = 0;
    tempSearchKey.value = searchKey.value;
    let replaceKey = searchKey.value;
    wordStore.uncommonWordMap.forEach((value, key) => {
      replaceKey = replaceKey.replace(value, key);
    });
    // console.log("=====wordStore",wordStore.uncommonWordMap)
    handleSearch(searchKey.value, prjForm.value);
    emits('search');
    await nextTick();
    const elements = document.querySelectorAll('.highlight2');
    console.log('=====elements', elements);
    matchingElements = [];
    elements.forEach((element: HTMLElement) => {
      matchingElements.push(element);
    });
    let index = 0;
    matchingElementList.value = [];
    while (index < searchKey.value.length) {
      matchingElementList.value.push(matchingElements[matchingIndex % matchingElements.length]);
      matchingIndex++;
      index++;
    }
    contentWrapperRef.value.scrollTo({
      top: matchingElementList.value![0].offsetTop - 100,
      behavior: 'smooth'
    });
  }
  // if (tempSearchKey.value == searchKey.value) {
  //   matchingElement.value = matchingElements.get(matchingIndex++);
  //   if (!matchingElement.value) {
  //     matchingIndex = 0;
  //     matchingElement.value = matchingElements.get(matchingIndex++);
  //   }
  //   contentWrapperRef.value.scrollTo({
  //     top: matchingElement.value!.offsetTop - 100,
  //     behavior: 'smooth'
  //   });
  // } else {
  //   matchingIndex = 0;
  //   tempSearchKey.value = searchKey.value;
  //   if (prjForm.value != PrjForm.video) {
  //     await manuSearch(searchKey.value);
  //   } else {
  //     for await (const prjParagraph4Read of prjParagraph4ReadList.value) {
  //       await prjParagraph4Read?.handleSearch(searchKey.value);
  //     }
  //   }
  //   const elements = document.querySelectorAll('.highlight2');
  //   matchingElements = cyclist(elements.length);
  //   elements.forEach((element, index) => {
  //     matchingElements.put(index, element);
  //   });
  //   matchingElement.value = matchingElements.get(matchingIndex++);
  //   if (!matchingElement.value) {
  //     matchingIndex = 0;
  //     matchingElement.value = matchingElements.get(matchingIndex++);
  //   }
  //   contentWrapperRef.value.scrollTo({
  //     top: matchingElement.value!.offsetTop - 100,
  //     behavior: 'smooth'
  //   });
  // }
};

const manuSearch = async (searchKey: string) => {
  if (prjForm.value != PrjForm.video) {
    myWordContent.value = props.wordContent;
  }
  if (!searchKey) {
    await nextTick();
    if (!mode.value) {
      markWord(); // 阅读模式
    } else {
      unmarkWord(); // 提问模式
    }
    return;
  }
  let str = '';
  for (let char of searchKey) {
    str += `<span data-index=\"[^<]*\" data-qid=\"[^<]*\" onclick=\"handleWord\\(this\\)\">${char}</span>`;
  }
  const pattern3 = new RegExp(str, 'g');

  myWordContent.value = getPlist(myWordContent.value!, 'highlight2', pattern3);
  await nextTick();
  if (!mode.value) {
    markWord(); // 阅读模式
  } else {
    unmarkWord(); // 提问模式
  }
};
const getPlist = (originPList: string, myClassName: string, pattern: RegExp = pattern2.value) => {
  if (!originPList || originPList.length == 0) return [];
  const sentense = originPList;
  let newSentense = JSON.parse(JSON.stringify(sentense));

  newSentense = newSentense?.replace(pattern, (match: string) => {
    return `<span class="${myClassName}" ">${match}</span>`;
  });
  return newSentense;
  // console.log('返回的结果res：', res, pattern2.value, lightWords.value, props.questionList);
};
// 过滤的问题
const filterQuestionList = ref();
// 大屏情况下显示的问题的段落ID
const paragraphId = ref();

const refreshTextContent = (newTextContent) => {
  myWordContent.value = newTextContent;
  nextTick(() => {
    // console.log(myWordContent.value)
    // console.log(mode.value)
    // mode.value=false
    // if (!mode.value) {
    //   console.log(11111111)
    //   markWord();
    // }else{
    //   console.log(222222222)
    //   unmarkWord();
    // }
    handleChangeMode();
  });
};

const handleQuestionList = (id) => {
  getQuestionList(id).then((res) => {
    // questionDialogRef.value.showDialog(questionList, 2);
    qList.value = res;
    open.value = false;
  });
};
const isShowQuestionInBig = (id: number) => {
  return (
    isBig &&
    filterQuestionList.value &&
    filterQuestionList.value.length > 0 &&
    paragraphId.value == id
  );
};

// 文稿项目的问题
const curAskingDialog = ref(false);
const curhighlightWord = ref('');
const curhighlightQuestion = computed(() =>
  props.questionList.filter((question) => question.keyword == curhighlightWord.value)
);
const handleShowQuestion = (paramId?: number) => {
  // alert('paragraphId:   ' + paragraphId.value)
  paragraphId.value = paramId;
};
// 请求问题
const handleAskQuestionFn = (highlightWords: string, paramId?: number) => {
  // debugger;
  // 拿到高亮词语，去匹配 questionList
  filterQuestionList.value = props.questionList.filter((question) =>
    highlightWords.includes(question.keyword)
  );
  if (prjForm.value == PrjForm.video) {
    // 视频项目
    // @ts-ignore
    if (!isBig.value) {
      // 小屏情况
      stateFlag.value = STATE_FLAG.asking;
    } else {
      // 大屏情况
      stateFlag.value = STATE_FLAG.init;
      paragraphId.value = paramId;
    }
  } else {
    curAskingDialog.value = true;
    stateFlag.value = STATE_FLAG.init;
    curhighlightWord.value = highlightWords;
  }
};
/**
const returnFn = () => {
  stateFlag.value = STATE_FLAG.init;
};

const submitAnswerFn = () => {
  stateFlag.value = STATE_FLAG.init;
};
*/
//小屏： 问题 -> 答案
const handleGetAnswerFn = () => {
  stateFlag.value = STATE_FLAG.answering;
};
const changeStateFn = (state: STATE_FLAG) => {
  stateFlag.value = state;
};
const contentWrapperRef = ref();
// 阅读时滑动滚
// 阅读模式，双向绑定，提问模式单项绑定。而且滚动条在最中间
const scrollElement = ref<HTMLElement>();
watch(
  () => scrollElement.value,
  (newVal, oldVal) => {
    if (newVal) {
      newVal.style.backgroundColor = '#a6d0ea';
      newVal.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    if (oldVal) {
      oldVal.style.backgroundColor = '';
    }
  }
);

const scrollBar = async (idx: number) => {
  const containerHeight = contentWrapperRef.value.offsetHeight;
  // 找到元素并且滑动
  const allParagraph = Array.from(document.querySelectorAll('.paragraph-wrapper')) as HTMLElement[];
  const activeParagraph = allParagraph?.[idx];
  sethighlightColor(idx);
  // 小屏可以使用这个api
  await nextTick();
  scrollElement.value = document.querySelector(`[oid="${curoid.value}"]`) as HTMLElement;
};
const activeIdx = ref(0);
// 设置高亮色
const sethighlightColor = (idx: number) => {
  activeIdx.value = idx;
};
// 阅读模式滑动时候，实时更新视频进度

// 用户滚动滑动触发的事件（wheel），与scroll最大的不同就是 js触发的滚动事件不会触发wheel

// 宽屏模式在顶端向上滑动滚轮则切换上层
const handleWheelFn = (e) => {
  if (props.big) {
    let top = document.querySelector('#header').offsetTop;
    let now = document.querySelector('#script').scrollTop;
    setTimeout(() => {
      if (now <= top && e.deltaY < 0) {
        emits('scrollInTop');
      }
    }, 100);
  }
  if (mode.value) {
    // true为 question 模式
    return;
  } else {
  }
};

const handleReturnInitFn = () => {
  // emits('returnInit');
  stateFlag.value = STATE_FLAG.init;
};

const afterDeleteQuestion = (newcontent) => {
  myWordContent.value = newcontent;
  nextTick(() => {
    if (!mode.value) {
      markWord();
    } else {
      unmarkWord();
    }
  });
};
const curoid = ref();
const hoverPList = (_idx: Number) => {
  curoid.value = _idx;
};

// ==============================
const open = ref(false);

const curQuestions = ref<QuestionData[]>([]);
// ==============================
onMounted(() => {
  const res = getPrjDetailApi(route.query.spuId as string, route.query.chapterId as string).then(
    (res) => {
      chapterList.value = res.data.chapterList;
    },
    (err) => {}
  );
  // console.log("====res",res)
  // chapterNameList.value = res.
  nextTick(() => {
    markWord();
  });
});
// 停止监听
defineExpose({
  changeStateFn,
  scrollBar,
  sethighlightColor,
  handleQuestionList,
  afterDeleteQuestion,
  hoverPList
});
</script>

<style scoped lang="less">
// :deep(.highlight) {
//   color: var(--color-theme-project);
//   cursor: pointer;
// }
// :deep(.highlight:hover) {
//   // font-weight: 700;
// }
:deep(.highlightHover) {
  font-weight: 700;
  * {
    font-weight: 700;
  }
}
.content-card {
  max-width: 100%;
  max-height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  // overflow-y: scroll;
  .tool {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 37px;

    .mode {
      margin-left: 10px;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      display: flex;
      align-items: center;

      .el-switch {
        margin-right: 10px;
      }
    }

    .input {
      margin-top: 5px;
      margin-right: 10px;
      max-width: 230px;
    }
  }

  .content-text-wrapper {
    // height: 420px;
    // height: 100%;
    width: 100%;
    overflow-y: auto;
    word-wrap: break-word;
    padding: 0 10px;
    margin-top: 2px;
    height: calc(100vh - 66px - 70px - 100px);
    position: relative;

    &.learning {
      min-height: calc(100vh - 66px - 70px - 100px);
      max-height: calc(
        100vh - 60px - 70px - 40px
      ); // 里面的高度要比calc(100vh - 60px - 70px - 40px)小
    }

    // 轨道相关
    &::-webkit-scrollbar {
      width: 6px;
      //   background-color: rgb(242, 242, 242);
      border-radius: 5px;
      transform: translateX(-10px);
    }

    // 整个地方
    &::-webkit-scrollbar-thumb {
      width: 6px;
      background-color: #ccc;
      border-radius: 2px;
    }

    // 动得地方
    &::-webkit-scrollbar-track {
      border-radius: 10px; // 轨道圆角
      background-color: #ffffff; // 轨道颜色;
    }

    // 剩下不动的地方
  }

  &:deep(.search-keys) {
    color: red;
    cursor: pointer;
    font-weight: 700;
  }
  &:deep(.highlight2) {
    // color: var(--color-theme-project);
    background-color: v-bind(matchingColor);
    cursor: pointer;
    font-weight: v-bind(matchingHeight);
  }
  &:deep(.activeElement) {
    background-color: #ff9632;
  }
}
.floating-content {
  padding: 6px 12px;
  background: var(--color-theme-project);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family:
    '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 14px;
  font-weight: 400;
  .floating-content-item {
    &:hover {
      // background-color: #C5D5EA;
      font-weight: 700;
      cursor: pointer;
    }
  }
}
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
  transform-origin: top; /* 设置动画起点为左侧 */
}

.scale-enter-from,
.scale-leave-to {
  transform: scaleY(0); /* 从左侧开始水平缩放 */
  opacity: 0;
}

//.active:deep(.text-hover) {
//color: red;
//font-weight: 700;
//cursor: pointer;
//}
</style>
