<template>
  <div class="lesson-info" ref="lessonInfo">
    <div class="base-info">
      <div class="left-info">
        <LessonItem
          v-for="(item, i) in lessonData.slice(0, 4)"
          :key="'_' + i"
          :lesson-info="item"
          :is-buy="info.buyStatus"
          :lesson-info-index="i + 1"
          :type="type!"
          @click="selectSection(i, item)"
        ></LessonItem>
      </div>
      <div class="more" @click="expandMore = !expandMore" v-if="lessonData.length > 4">...</div>
    </div>
    <el-collapse-transition>
      <div class="more-info" v-show="expandMore">
        <div class="left-info">
          <LessonItem
            v-for="(item, i) in lessonData.slice(4)"
            :key="'_' + i"
            :lesson-info="item"
            :lesson-info-index="i + 5"
            :is-buy="info.buyStatus"
            :type="type!"
            @click="selectSection(i + 4, item)"
          ></LessonItem>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import LessonItem from './LessonItem.vue';
import { useLearnStore } from '@/stores/learnintro';
import { storeToRefs } from 'pinia';
import type { LessonInfoType } from '@/types/case';
import { PrjForm } from '@/types/project';
import { BuyStatus } from '@/types/goods';
import { useProjectStore } from '@/stores/project';
const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const router = useRouter();
const route = useRoute();
const learnStore = useLearnStore();
// const learnStoreRef = storeToRefs(learnStore);
const lessonInfo = ref<any>(null);
const expandMore = ref(false);
// 共享状态变量 当前课程是否已经购买  共享状态暂未接入

const type = inject('prjForm') as PrjForm;

const lessonData = inject('lessonData') as LessonInfoType[];
// console.log('读取过来的数据', lessonData);
const finalData = ref([]);
//@ts-ignore
finalData.value = lessonData;

// 小节选择  item组件只负责信息展示和播放页面跳转  选择功能由父组件控制
const selectSection = (index: number, lessonitem: LessonInfoType) => {
  //未购买且非试学的不能点击
  // console.log('BuyStatus', isBuy, 'lessonitem.isTry', lessonitem.isTry)
  // @ts-ignore
  if (info.value.buyStatus == BuyStatus.nobuy && lessonitem.isTry == 0) {
    return;
  }
  // waqtodo 此处应该带着小结参数跳转
  // 此处不需要按照其他跳转设置learnstore持久化处理，是因为小结的详情页spuId与介绍页的spuId一致
  else {
    try {
      const { href } = router.resolve({
        path: '/learning',
        query: {
          spuId: route.query.spuId,
          chapterId: lessonitem.sectionId
        }
      });
      window.open(href, '_blank');
    } catch (error) {
      console.error('Error setting intro data:', error);
    }
  }
};

onMounted(() => {
  // 获取小节信息
});
</script>

<style lang="less" scoped>
.lesson-info {
  width: 732px;
  display: flex;
  position: relative;

  .base-info {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .more {
      width: 32px;
      height: 30px;
      display: flex;
      justify-content: center;
      font-weight: 700;
      font-size: 14px;
      cursor: pointer;
      border: 1px solid var(--color-theme-project);
      color: var(--color-theme-project);
      border-radius: 5px;

      &:hover {
        background-color: #9fbfcd;
      }
    }
  }

  .more-info {
    width: 100%;
    position: absolute;
    background-color: #f3f3f3;
    top: 35px;
    padding-bottom: 5px;
  }

  .left-info {
    width: 700px;
    display: grid;
    gap: 5px 5px;
    grid-template-columns: repeat(4, 170px);
    grid-template-rows: repeat(auto-fill, 30px);
  }
}
</style>
