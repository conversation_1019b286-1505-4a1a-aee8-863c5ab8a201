<template>
  <Transition name="fade">
    <div v-if="model" class="overlay">
      <div class="success-animation">
        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
          <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
          <path class="checkmark__check" fill="none" d="M14 26l7 7 16-16" />
        </svg>
        <p class="success-message">支付成功</p>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import anime from 'animejs/lib/anime.es.js';

const model = defineModel();

function playAnimation() {
  anime({
    targets: '.checkmark__circle',
    strokeDashoffset: [anime.setDashoffset, 0],
    easing: 'easeInOutSine',
    duration: 1000
  });

  anime({
    targets: '.checkmark__check',
    strokeDashoffset: [anime.setDashoffset, 0],
    easing: 'easeInOutSine',
    duration: 1000
  });

  anime({
    targets: '.success-message',
    opacity: [0, 1],
    easing: 'easeInOutSine',
    duration: 1000
  });
  setTimeout(() => {
    model.value = false;
  }, 2500);
}

watch(
  () => model.value,
  (newValue) => {
    if (newValue) {
      nextTick(() => {
        playAnimation();
      });
    }
  }
);
</script>

<style scoped>
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

.success-animation {
  background: white;
  padding: 20px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.checkmark {
  width: 56px;
  height: 56px;
  stroke-width: 2;
  stroke: #4caf50;
  fill: none;
  /* stroke-miterlimit: 10; */
}

.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #4caf50;
  fill: none;
}

.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
}

.success-message {
  color: #4caf50;
  margin-top: 10px;
  opacity: 0;
  font-size: 20px;
  font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
    '阿里巴巴普惠体 3.0';
  font-weight: 400;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
