<template>
  <div class="kl-map">
    <div class="main-content">
      <!-- title 和 info可以封装成一个组件 具体如何实现看情况 -->
      <h1 class="title">{{ info.title }}</h1>
      <div class="info">
        <div class="study-info">
          <div class="learn-footer-info">
            <img style="width: 20px; height: 20px" :src="info.userCoverPic" class="logo" />
            <span class="wjby">{{ info.userName }}</span>
          </div>
          <PrjStudyInfo :knowledgeSum="info.klgNumbers"></PrjStudyInfo>
          <div>有效期：{{ info.validDate }}天</div>
          <div v-if="info.buyStatus == BuyStatus.bought" style="width: 120px"></div>
          <div v-else style="width: 120px">购买费用：{{ info.price }}元</div>
          <div class="btn-wrapper">
            <CmpButton
              v-if="info.buyStatus == BuyStatus.bought"
              @click="start"
              class="btn"
              type="primary"
              >马上开始
            </CmpButton>
            <CmpButton
              v-else
              class="btn"
              type="primary"
              @click="payDialogVisible = !payDialogVisible"
              style="width: 100px"
              >购买</CmpButton
            >
          </div>
        </div>
      </div>
      <div class="part">
        <!-- 样式待优化 -->
        <div class="partwapper">
          <div style="display: flex; width: 75px; height: 30px; align-items: center">
            测评目标：
          </div>
          <div class="target-items">
            <div class="item" v-for="item in info.targetKlgs!.slice(0, 4)" :key="item.klgCode">
              {{ item.klgTitle }}
            </div>

            <div class="more" @click="expandMore = !expandMore" v-if="info.targetKlgs!.length! > 4">
              ...
            </div>
          </div>
        </div>
        <el-collapse-transition>
          <div class="more-info" v-show="expandMore">
            <div
              class="item"
              style="margin-top: 5px"
              v-for="item in info.targetKlgs!.slice(4)"
              :key="item.klgCode"
            >
              {{ item.klgTitle }}
            </div>
          </div>
        </el-collapse-transition>
      </div>
      <div class="map">
        <KsgMap ref="ksgMap"
          v-model:config="config"
          :data="data"
          :fetchLevelData="fetchLevelData"
        />
        <!-- 占位置 -->
      </div>
    </div>
    <div class="right-info">
      <div class="title">
        <img src="@/assets/images/prjlearn/u4108.svg" alt="" />
        学习说明
      </div>
      <div class="content">
        {{ info.studyInstructions }}
      </div>
    </div>
  </div>
  <!-- <div v-if="dialogPayVisible"> -->
  <!-- 此处没有支付类型选择，只有一个价格，不需要DialogPayment -->
  <!-- <dialogPayment :paymentObj="paymentObj" @closeDialog="closeDialog"></dialogPayment> -->
  <!-- <el-dialog class="dg" v-model="dialogPayVisible" :show-close="false" width="526px" top="40vh">
      <template #header>
        <div class="dg-header">
          <h1>资料购买</h1>
          <img
            @click="dialogPayVisible = !dialogPayVisible"
            style="width: 16px; height: 16px; cursor: pointer"
            src="@/assets/images/prjlearn/close.svg"
            alt=""
          />
        </div>
      </template>
      <div class="content1">
        购买{{prjInfo.title}}学习资料，学习周期为{{prjInfo.validDate}}天，支付价格0元，购买后，学习资料会在我的“正在学”页面找到。
      </div>
      <template #footer>
        <div class="foot-btns">
          <CmpButton class="btn" type="info" @click="dialogPayVisible = !dialogPayVisible">我知道了</CmpButton>
          <CmpButton
            class="btn"
            type="primary"
            @click="emitBuy"
          >
            确认购买
          </CmpButton>
        </div>
      </template>
    </el-dialog> -->
  <!-- </div> -->
</template>

<script setup lang="ts">
import { BuyStatus } from '@/types/goods';
import CmpButton from '@/components/CmpButton.vue';
import { useProjectStore } from '@/stores/project';
import PrjStudyInfo from '@/components/PrjStudyInfo.vue';
import { ShowType } from '@/types/exam';
import { PrjForm } from '@/types/project';

const expandMore = ref(false);
const showType = inject('showType') as Ref<ShowType>;

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const payDialogVisible = inject('payDialogVisible') as Ref<boolean>;

const start = () => {
  if (info.value.prjForm == PrjForm.draft) {
    showType.value = ShowType.draft;
  } else {
    showType.value = ShowType.video;
  }
};

//===========================ksg-map==================================
import { getLevelData, getPrjGraph } from '@/apis/ksgmap';
import KsgMap from 'ksg-map'
import type { GlobalConfig, OriginalData, AreaData, PointData } from 'ksg-map/dist/types';

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null)
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: true,
})
const data = ref<OriginalData>({
  topAreas: [],
  points: [],
  areas: [],
  focuses: [],
})
const points: string[] = []
onMounted(async () => {
  const unwatch = watch(() => info.value.spuId, async () => {
    if (info.value.spuId) {
      unwatch()
      const pointsData = (await getPrjGraph(info.value.spuId))
      pointsData.forEach((item) => {
        points.push(item.pointId)
      })
      await ksgMap.value?.ready;
      data.value.topAreas = []
      data.value.points = pointsData
      
      ksgMap.value?.reloadData();
    }
  })
})
async function fetchLevelData(ids: string[], type: 'area' | 'focus', level: number, limit: number) {
  return (await getLevelData(points, level, limit)).data
}
//===========================ksg-map==================================
</script>
<style lang="less" scoped>
.kl-map {
  // 重写element变量
  --el-color-primary: var(--color-theme-project);
  // 进度条底色
  --el-border-color-lighter: var(--color-second);

  color: var(--color-black);
  font-family: var(--text-family);
  width: 1100px;
  padding-left: 10px;
  display: flex;
  flex-direction: row;

  .main-content {
    width: 754px;
    border: 0.8px solid rgb(242, 242, 242);
    border-radius: 5px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
    // background-image: linear-gradient(to bottom, #f2f2f2, #ffffff);
    background-color: white;
    padding-right: 10px;
    padding-top: 20px;
    padding-bottom: 10px;
    padding-left: 10px;
    margin-bottom: 40px;

    h1 {
      // width: 100%;
      font-size: 20px;
      font-weight: 700;
      font-family: var(--title-family);
    }

    .info {
      // width: 100%;
      height: 50px;
      margin-top: 10px;
      display: flex;
      align-items: center;

      .study-info {
        width: 754px;
        height: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .learn-footer-info {
          display: flex;
          align-items: center;

          .wjby {
            font-size: var(--fontsize-small-project);
            font-weight: 400;
            font-family: var(--text-family);
            color: var(--color-deep);
            margin-left: 10px;
          }

          .el-progress--line {
            margin-left: 5px;
            width: 135px;
          }

          // &:deep(.el-progress-bar__outer) {
          //   background-color: rgb(211, 222, 227);
          // }

          // &:deep(.el-progress__text) {
          //   color: rgb(0, 85, 121);
          // }
        }

        .btn-wrapper {
          width: 105px;
          height: 40px;

          .btn {
            width: 105px;
            height: 40px;
            font-size: var(--fontsize-middle-project);
          }
        }
      }
    }

    .part {
      // width: 100%;
      width: 754px;
      margin-top: 10px;
      // display: flex;
      // flex-direction: column;
      // flex-wrap: wrap;
      font-size: 14px;
      // align-items: center;

      .partwapper {
        display: flex;
        flex-direction: row;
        // align-items: space-between;

        .target-items {
          display: flex;
          flex-wrap: wrap;
          flex-direction: row;
        }

        .more {
          width: 32px;
          height: 30px;
          display: flex;
          justify-content: center;
          font-weight: 700;
          font-size: 14px;
          cursor: pointer;
          border: 1px solid var(--color-theme-project);
          color: var(--color-theme-project);
          border-radius: 5px;
        }
      }

      .more-info {
        width: 745px;
        position: absolute;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        background-color: #f3f3f3;
        top: 245px;
        padding: 0 0 5px 75px;
      }
    }

    .item {
      background-color: white;
      color: var(--color-theme-project);
      width: 146px;
      height: 30px;
      border-radius: 2px;
      display: inline;
      line-height: 30px;
      text-align: center;
      // justify-content: center;
      // align-items: center;
      margin-right: 10px;
      // padding: 15px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      box-sizing: border-box;
      border: 1px solid var(--color-theme-project);
      word-break: break-all;
      padding: 0 12px 0 12px;
    }

    .map {
      // width: 100%;
      height: 530px;
      // background-color: rgba(0, 85, 121, 0.376);
      border-radius: 5px;
      margin-top: 10px;
    }
  }

  .right-info {
    width: 300px;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 10px;
    margin-left: 20px;

    .title {
      height: 20px;
      width: 100%;
      display: flex;
      align-items: center;
      font-size: var(--fontsize-middle-project);
      font-weight: 700;

      img {
        margin-right: 10px;
      }
    }

    .content {
      margin-top: 12px;
      width: 100%;
      white-space: pre-wrap;
      font-weight: 400;
      font-size: var(--fontsize-middle-project);
      line-height: normal;
    }
  }
}
.dg {
  .dg-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #f2f2f2;
    padding-bottom: 15px;

    h1 {
      font-size: 18px;
      font-weight: 700;
      color: var(--color-theme-project);
    }
  }

  .content1 {
    width: 100%;
    white-space: pre-wrap;
  }

  .foot-btns {
    width: 100%;
    display: flex;
    // flex-direction: row;
    justify-content: center;

    .btn {
      width: 160px;
      height: 43px;

      &:nth-child(2) {
        margin-left: 20px;
      }
    }
  }
}
</style>
