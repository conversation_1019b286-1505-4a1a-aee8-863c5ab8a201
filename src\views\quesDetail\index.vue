<template>
  <div class="card-wrapper">
    <div class="card">
      <div id="infoBack" class="info">
        <img class="backBtn" @click="handleBack()" src="@/assets/images/prjlearn/u4508.svg" />
        <!-- 项目信息 -->
        <!-- <PrjDetailInfo :curHeight="curHeight" @expandTag="handleTagExpand"></PrjDetailInfo> -->
        <!-- 更多信息 -->
        <!-- <PrjMoreInfo
          :curHeight="curHeight"
          @expandInfo="handleInfoExpand"
          @expandMap="expandMapFn"
        ></PrjMoreInfo> -->
        <PrjInfo v-if="ready" />
      </div>
      <template v-if="ready">
        <!-- <PrjManuWrapper v-if="prjForm == PrjForm.draft" />
        <PrjVideoWrapper v-else /> -->
        <!-- <queListWrapper /> -->
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PrjForm, PrjType } from '@/types/project';
import PrjInfo from '../learning/components/PrjInfo.vue';
import type { PrjinfoItf } from '@/types/learning';
import { getPartProApi, getPrjDetailApi } from '@/apis/learning';
import PrjManuWrapper from '../learning/components/PrjManuWrapper.vue';
import PrjVideoWrapper from '../learning/components/PrjVideoWrapper.vue';
import queListWrapper from '../learning/components/queListWrapper.vue';
import { useLearningStore } from '@/stores/learning';
const learningStore = useLearningStore();

const ready = ref(false);

const route = useRoute();
const router = useRouter();
const spuId = route.query.spuId as string;
const chapterId = route.query.chapterId as string ;
const handleBack = () => {
  switch (prjType.value) {
    // 讲解
    case PrjType.klgExplain:
      router.push({
        path: '/klgexplain',
        query: route.query
      });
      break;
    // 案例
    case PrjType.case:
      router.push({
        path: '/case',
        query: route.query
      });
      break;
    // 测评
    case PrjType.exam:
      router.push({
        path: '/exam',
        query: route.query
      });
      break;
    // 回到主页
    default:
      router.push('/home');
      break;
  }
};

const prjInfo = ref<PrjinfoItf>({});
const prjForm = ref<PrjForm>();
const prjType = ref<PrjType>();

provide('prjInfo', prjInfo);
provide('prjForm', prjForm);
provide('prjType', prjType);
const useWideScreen = inject('useWideScreen') as Ref<boolean>;
onMounted(async () => {
  // console.log('aaa'+spuId)
  const res = await getPartProApi(spuId);
  // const res = await getPartProApi(spuId);
  // console.log('hhhhh')
  // console.log(res)
  prjInfo.value = res.data.list[0];
  prjForm.value = prjInfo.value.prjForm;
  prjType.value = prjInfo.value.prjType;
  const res2 = await getPrjDetailApi(spuId, chapterId)
  learningStore.setInfo(res2.data);
  router.replace({
    query: {
      ...route.query
      // chapterId: res2.data.chapterList[res2.data.validIndex].chapterId
    }
  });
  ready.value = true;
  useWideScreen.value = true;
});
onBeforeUnmount(() => {
  useWideScreen.value = false;
});
// // waqtodo 此处还需要传递 learnType
// const expandMapFn = () => {
//   // TODO:
//   // isMap.value = true;
// };
// const isTagExpand = ref(false);
// const isInfoExpand = ref(false);
// const curHeight = ref(70);
// const handleTagExpand = (expandHeight: string) => {
//   let newHeight = parseInt(expandHeight.replace('px', '')) + 38;
//   isTagExpand.value = !isTagExpand.value;
//   if (isTagExpand.value) {
//     if (isInfoExpand && curHeight.value >= newHeight) {
//       return;
//     }
//     (document.getElementById('infoBack') as HTMLElement).style.height = newHeight + 'px';
//     curHeight.value = newHeight;
//   } else {
//     (document.getElementById('infoBack') as HTMLElement).style.height = '70px';
//     (document.getElementById('infoBack') as HTMLElement).style.backgroundColor = 'white';
//     isInfoExpand.value = false;
//     curHeight.value = 70;
//   }
// };
// const handleInfoExpand = (expandHeight: string) => {
//   let newHeight = parseInt(expandHeight.replace('px', '')) + 38;
//   isInfoExpand.value = !isInfoExpand.value;
//   if (isInfoExpand.value) {
//     if (isTagExpand && curHeight.value >= newHeight) return;
//     (document.getElementById('infoBack') as HTMLElement).style.height = newHeight + 'px';
//     curHeight.value = newHeight;
//   } else {
//     (document.getElementById('infoBack') as HTMLElement).style.height = '70px';
//     (document.getElementById('infoBack') as HTMLElement).style.backgroundColor = 'white';
//     isTagExpand.value = false;
//     curHeight.value = 70;
//   }
// };
</script>

<style scoped lang="less">
@middle-width: 1440px;
.backBtn {
  cursor: pointer;
  margin: 12px 16px 12px 0px; // 因为要中点对齐18px的标题，所以上下各加2px
  height: 14px;
  width: 15px;
}
.card {
  // width: var(--width-fixed--project);
  width: 100%;
  padding-left: var(--padding-box);
  padding-right: var(--padding-box);
  padding-top: 10px;
  margin: 0 auto;
  background-color: white;

  .info {
    //transition: height 0.2s ease-in-out;
    height: 70px;
    width: 100%;
    background-color: white;
    display: flex;
    margin-bottom: 10px;
    //justify-content: space-between;
  }
}

@media screen and (max-width: 1440px) {
  .card {
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
  }

  .tags-wrapper {
    max-width: 50% !important;
  }
}
</style>
