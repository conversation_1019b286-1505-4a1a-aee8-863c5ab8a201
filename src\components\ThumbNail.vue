<template>
  <el-popover
    :visible="floatingVisible"
    :virtual-ref="referenceElement"
    placement="top"
    virtual-triggering
  >
    <div v-html="content" @mouseleave="handler" v-click-outside="handler"></div>
  </el-popover>
</template>

<script setup lang="ts">
const referenceElement = ref<HTMLElement | null>(null);

const floatingVisible = ref(false);

const model = defineModel<HTMLElement | null>();
const content = ref('');

watch(
  () => model.value,
  (newVal) => {
    if (newVal) {
      content.value = newVal?.getAttribute('thumbnail') as string;
      referenceElement.value = newVal;
      floatingVisible.value = true;
    }
  }
);

const handler = (event: Event) => {
  floatingVisible.value = false;
  model.value = null;
};
</script>

<style lang="less" scoped>
// .content {
//   position: relative;
//   width: 150px;
//   //   overflow: hidden;
//   background: var(--el-bg-color-overlay);
//   border: 1px solid var(--el-border-color-light);
//   box-shadow: var(--el-box-shadow-light);
//   border-radius: var(--el-popover-border-radius);
//   &::before {
//     content: '';
//     position: absolute;
//     z-index: -1;
//     top: -15px;
//     left: calc(50% - 15px);
//     width: 30px;
//     height: 30px;
//     transform: rotateZ(45deg);
//     // background-color: red;
//     // border: 1px solid var(--el-border-color-light);
//     box-shadow: var(--el-box-shadow-light);
//     border-radius: var(--el-popover-border-radius);
//   }
// }
</style>
