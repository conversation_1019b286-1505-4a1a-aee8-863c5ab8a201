<template>
  <div class="big-video" @click="goIntroPage">
    <div v-if="!isFree" class="vip" :class="isFree == 1 ? 'free' : ''">
      <i>{{ isFree == 1 ? '免费' : 'VIP' }}</i>
    </div>
    <div class="right-layer">
      <h3 class="title"><span v-html="title"></span></h3>
      <p class="abstract"><span v-html="description"></span></p>
      <div class="logo-info">
        <img :src="userCoverPic" class="logo" />
        <span class="wjby">{{ userName }}</span>
      </div>
      <div class="btn-group">
        <CmpButton type="info" class="w130" @click.stop="goIntroPage">更多信息</CmpButton>
        <CmpButton type="primary" class="w130" @click.stop="goDetailPage">开始学习</CmpButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLearnStore } from '@/stores/learnintro';
import { PrjForm, PrjType } from '@/types/project';
import { GoodsType, BuyStatus } from '@/types/goods';
const router = useRouter();
const learnStore = useLearnStore();
type Props = {
  //跳转介绍页需要用到的参数
  goodsType: number; //表示商品是否是只有会员才能看的商品
  prjType: PrjType; //表示类型，案例，讲解，测评
  spuId: string;
  //跳转详情页需要用到的参数
  //视频形式还是文稿形式
  prjForm: PrjForm; // 表示内容类型, 文稿还是视频
  coverPic?: string;
  description?: string;
  title?: string;
  prjStudyFrequence?: number;
  prjQuestionNumbers?: number;
  studyTime?: number;
  userCoverPic?: string;
  userName?: string;
  latestChapterId?: number;
  /**
   * 1表示免费商品，0表示付费商品
   */
  isFree: number;
  buyStatus: BuyStatus;
};

const props = defineProps<Props>();
const parseUrl = computed(() => `url(${props.coverPic})`);
const goIntroPage = async () => {
  const { href } = router.resolve({
    path: '/goodIntroduce',
    query: {
      spuId: props.spuId
    }
  });
  window.open(href, '_blank');
  // try {
  //   if (props.goodsType == GoodsType.vip) {
  //     const { href } = router.resolve({
  //       path: '/case',
  //       query: {
  //         spuId: props.spuId
  //       }
  //     });
  //     window.open(href, '_blank');
  //   } else {
  //     if (props.prjType == PrjType.case) {
  //       const { href } = router.resolve({
  //         path: '/case',
  //         query: {
  //           spuId: props.spuId
  //         }
  //       });
  //       window.open(href, '_blank');
  //     } else if (props.prjType == PrjType.klgExplain) {
  //       const { href } = router.resolve({
  //         path: '/case',
  //         query: {
  //           spuId: props.spuId
  //         }
  //       });
  //       window.open(href, '_blank');
  //     } else if (props.prjType == PrjType.exam) {
  //       const { href } = router.resolve({
  //         path: '/case',
  //         query: {
  //           spuId: props.spuId
  //         }
  //       });
  //       window.open(href, '_blank');
  //     }
  //   }
  // } catch (error) {
  //   console.error('Error setting intro data:', error);
  // }
};
// 免费的和已购买的跳转详情页
// 付费的跳转到介绍页
const goDetailPage = async () => {
  try {
    learnStore.setintroData(props.spuId);
    // istry 0代表不可看，1代表可看
    if (props.buyStatus == BuyStatus.bought) {
      const query: {
        spuId: string;
        chapterId?: number;
      } = {
        spuId: props.spuId
      };
      if (props.latestChapterId) {
        query.chapterId = props.latestChapterId;
      }
      const { href } = router.resolve({
        path: '/learning',
        query: query
      });
      window.open(href, '_blank');
    } else {
      // 跳转介绍页
      goIntroPage();
    }
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
</script>

<style scoped lang="less">
.w130 {
  width: 130px;
  height: 25px;
  font-size: 12px;
}

.big-video {
  // no-repeat left/cover
  background-image: v-bind(parseUrl);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 630px;
  height: 355px;
  font-family: var(--text-family);
  //   font-family: '华文细黑', 'STXihei', sans-serif;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(242, 242, 242, 1);
  border-radius: 5px;
  -moz-box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.376470588235294);
  -webkit-box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.376470588235294);
  box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.376470588235294);
  position: relative;

  /* 添加过渡动画 */
  transition: all 0.3s ease;

  &:hover {
    cursor: pointer;
    transform: translateY(-5px);
    box-shadow: 0px 12px 20px rgba(170, 170, 170, 0.5);
  }

  .vip {
    position: absolute;
    left: 5px;
    top: 5px;
    display: inline-block;
    width: 36px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    background-color: #ffd37a;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.996);
    z-index: 1;

    &.free {
      background-color: rgb(18, 170, 156);
    }
  }

  .right-layer {
    width: 440px;
    height: 354px;
    margin-left: auto;
    border-radius: 5px;
    background: linear-gradient(
      to left,
      rgba(255, 255, 255, 1) 50%,
      rgba(255, 255, 255, 0.95) 60%,
      rgba(255, 255, 255, 0.9) 70%,
      rgba(255, 255, 255, 0.8) 80%,
      rgba(255, 255, 255, 0.4) 90%,
      rgba(255, 255, 255, 0) 100%
    );
    // box-shadow: inset 0px 0px TODO:细分
    //   linear-gradient(to left, rgba(255, 255, 255, 1), rgba(179, 179, 180, 0.3));
    padding: 83px 23px 82px 100px;

    .title {
      font-family: '黑体', 'Simhei', sans-serif;
      font-size: var(--fontsize-middle-project);
      font-weight: 700;
      font-size: 24px;
      color: #333;
    }

    .abstract {
      font-size: var(--fontsize-large-project);
      font-weight: 400;
      color: #333;
      display: -webkit-box;
      margin-top: 15px;
      overflow: hidden;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      line-height: 1.5;
      font-family: '黑体', 'Simhei', sans-serif;
    }

    .logo-info {
      display: flex;
      align-items: center;
      height: 20px;
      line-height: 20px;
      margin: 35px 0 25px;

      .logo {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        overflow: hidden;
      }

      .wjby {
        font-size: var(--fontsize-small-project);
        font-weight: 400;
        color: #797979;
        margin-left: 10px;
      }
    }

    .btn-group {
      display: flex;
      // align-items: center;
      justify-content: space-between;
      padding-left: 17px;
      padding-right: 17px;
    }
  }
}
</style>
