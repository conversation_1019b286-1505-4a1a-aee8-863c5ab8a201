export interface BoughtInfoType {
  userCoverPic: string;
  userName: string;
  klgNumbers: number;
  learned: number;
  graspKlg: number;
}

export interface NobuyInfoType {
  userCoverPic: string;
  userName: string;
  klgNumbers: number;
  cardType: number;
  price: string;
  priceArr: Array<{
    label: string;
    value: number;
    price: number;
  }>;
  studyTime: number;
}

export interface LessonInfoType {
  chapterName: string;
  isLearned?: boolean;
  isTry?: boolean | number;
  chapterNum: number;
  sectionId: number;
  uniqueCode?: string; //小结的uniqueCode
}
