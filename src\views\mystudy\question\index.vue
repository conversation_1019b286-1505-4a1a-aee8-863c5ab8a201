<!-- waqtodo 此处可以跳转详情页，等后端接口改好后联调 -->
<template>
  <div class="collect-container">
    <div class="collect-header">
      <span class="question-title" @click="goBack">
        <img style="height: 10px; width: 10px" src="@/assets/images/myStudy/back.svg" />
        返回个人空间
      </span>
      <div class="collect">
        <el-input
          v-model="searchInput"
          class="w-50 m-2"
          placeholder="请输入关键字/材料名称"
          :prefix-icon="Search"
          @keyup.enter="searchFn"
          clearable
        />
      </div>
    </div>
    <div v-if="questionlist.length != 0">
      <div class="question-middle">
        学习中我提了{{ myQuestions }}个问题，已经有{{ repliedQuestions }}个问题被回复
      </div>
      <div class="question-content">
        <div
          class="question-content-item"
          v-for="(item, index) in questionlist"
          :key="index"
          @click="goToQuestionDetail(item)"
        >
          <div class="question-content-item-left">
            <div class="question-content-item-top">
              <span class="question-content-item-top-left">
                <span class="quote-content-wrapper-custom">
                  <span class="quote-left-custom">"</span>
                  <el-tooltip
                    placement="top"
                    :content="(item.associatedWords)"
                    :raw-content="true"
                    :show-after="200"
                    effect="customized"
                  >
                    <span
                      class="ellipsis-text-custom main-title-custom"
                      v-html="(item.associatedWords)"
                    ></span>
                  </el-tooltip>
                  <span class="quote-right-custom">"</span>
                </span>
              </span>
              <span class="question-content-item-top-right" v-html="item.createTime"></span>
            </div>
            <div class="question-content-item-bottom">
              <span class="keyword-custom">
                <span class="quote-content-wrapper-custom">
                  <span class="quote-left-custom">[</span>
                  <el-tooltip
                    placement="top"
                    :content="
                      item.questionType === '开放性问题'
                        ? (item.questionDescription)
                        : (item.keyword)
                    "
                    :raw-content="true"
                    :show-after="200"
                    effect="customized"
                  >
                    <span
                      class="ellipsis-text-custom secondary-text-custom"
                      v-html="
                        item.questionType === '开放性问题'
                          ? (item.questionDescription)
                          : (item.keyword)
                      "
                    ></span>
                  </el-tooltip>
                  <span class="quote-right-custom">]</span>
                </span>
              </span>

              <span class="requesType-custom">
                <span
                  v-if="item.questionType !== '开放性问题'"
                  v-html="item.questionType + '?'"
                ></span>
                <span v-else>开放性问题</span>
              </span>
              <span class="requestNum-custom">
                <span>(</span>
                <span v-html="item.repliedNumber"></span>
                <span>)</span>
              </span>
            </div>
          </div>
          <div class="question-content-item-right">
            <div class="question-content-item-img">
              <img :src="item.coverPic" />
            </div>
            <el-tooltip
              placement="top"
              :content="(item.title)"
              :raw-content="true"
              :show-after="200"
            >
              <div
                class="fontstyle4 ellipsis-text-custom title-custom"
                v-html="(item.title)"
              ></div>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="question-bottom">
        <div class="down-more" @click="getMore" v-if="shouldMore">加载更多</div>
        <div class="no-more" v-if="!shouldMore && current > 1">已经是最底部啦</div>
        <!-- TODO：这个组件是无效的，或许是backtop? -->
        <FindTop></FindTop>
      </div>
    </div>
    <div v-else class="lc-empty"><el-empty description="暂无提问记录" /></div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { getMyquestionlistApi } from '@/apis/mystudy';
import type { ResponseData } from '@/apis/index';
import { ref } from 'vue';

// 定义问题列表数据类型
interface QuestionListData {
  questionCount: number;
  repliedCount: number;
  total: string;
  records: Array<{
    associatedWords: string;
    createTime: string;
    keyword: string;
    questionDescription: string;
    questionType: string;
    repliedNumber: number;
    title: string;
    coverPic: string;
    spuId: string;
    chapterId: string;
    questionId: string;
  }>;
}

const router = useRouter();
function goBack() {
  router.push('/userspace');
}
// 使用从API导入的类型，不需要重复定义
type questionData = QuestionListData['records'][0];
let searchInput = ref('');
// 获取我的学习提问
const limit = 10;
const current = ref(1);
let keywordOrTitle = ref('');
const questionlist = ref<questionData[]>([]); // 订单列表
const myQuestions = ref(0); // 我提问的总个数
const repliedQuestions = ref(0); // 被回复的总个数
let shouldMore = ref(true);
const getQuestionlist = (current: number, limit: number, keywordOrTitle: string) => {
  getMyquestionlistApi(current, limit, keywordOrTitle).then(
    (res: any) => {
      myQuestions.value = res.data.questionCount;
      repliedQuestions.value = res.data.repliedCount;
      questionlist.value = res.data.records;
      shouldMore.value = current < parseInt(res.data.total) / 10 ? true : false;
    }
  );
};

// 初始化
const InitData = () => {
  current.value = 1;
  keywordOrTitle.value = '';
  getQuestionlist(current.value, limit, keywordOrTitle.value);
};

InitData();

// 加载更多
const getMore = () => {
  current.value += 1;
  getMyquestionlistApi(current.value, limit, keywordOrTitle.value).then(
    (res: any) => {
      myQuestions.value = res.data.questionCount;
      repliedQuestions.value = res.data.repliedCount;
      questionlist.value = questionlist.value.concat(res.data.records);
      shouldMore.value = current.value < parseInt(res.data.total) / 10 + 1;
    }
  );
};

// 根据订单编号等搜索
const searchFn = () => {
  current.value = 1;
  keywordOrTitle.value = searchInput.value;
  getQuestionlist(current.value, limit, keywordOrTitle.value);
};

// 跳转到问题详情页（在新窗口中打开）
const goToQuestionDetail = (item: questionData) => {
  const url = router.resolve({
    path: '/questionDetail',
    query: {
      spuId: item.spuId,
      chapterId: item.chapterId,
      questionId: item.questionId
    }
  });
  window.open(url.href, '_blank');
};
</script>

<style lang="less" scoped>
@titlefontsize: 18px;
@titlefontfamily: '黑体';
@headerfontfamily: '华文细黑';
@headerfontsize: 14px;
@margin: 10px;
@bgclor: #eee;

.fontstyle1 {
  font-size: 14px;
}

.fontstyle2 {
  font-size: 12px;
}

.fontstyle3 {
  font-size: 16px;
  font-weight: 700;
}

.fontstyle4 {
  font-size: 14px;
  font-weight: 700;
}

.collect-container {
  // 重写element样式
  --el-color-primary: var(--color-theme-project);

  display: flex;
  flex-direction: column;
  width: 1090px;
  padding-left: 2 * @margin;
  padding-top: 13px;

  .collect-header {
    display: flex;
    justify-content: space-between;

    .question-title {
      font-family: @titlefontfamily;
      font-size: @headerfontsize;
      color: var(--color-theme-project);
    }

    .question-title:hover {
      font-weight: 700;
      cursor: pointer;
    }

    .collect {
      width: 225px;
    }
  }

  .question-middle {
    font-size: 12px;
  }

  .question-content {
    margin-top: 18px;
    margin-bottom: 44px;
    width: 100%;

    .question-content-item {
      width: 100%;
      height: 69px; /* 调整高度更接近图片效果 */
      box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.08); /* 更轻微的阴影 */
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px; /* 减小间距 */
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 4px; /* 更小的圆角 */
      background: white;
      border: 1px solid #f0f0f0; /* 添加边框 */

      &:hover {
        background-color: #fafafa;
        transform: translateY(-1px);
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.12);
      }

      .question-content-item-left {
        padding: 5px 16px; /* 减小内边距 */
        width: 70%; /* 增加左侧宽度 */
        display: flex;
        flex-direction: column;
        //justify-content: space-between;
        height: 100%;

        .question-content-item-top {
          display: flex;
          flex-direction: row;
          align-items: center; /* 改为居中对齐 */
          margin-bottom: 4px; /* 减小间距 */

          .question-content-item-top-left {
            display: flex;
            flex-direction: row;
            max-width: 85%;
            min-height: 28px; /* 减小高度 */
            line-height: 1.2;
            align-items: center;
            color: #333;
            font-size: 14px; /* 调整字体大小 */
            font-weight: 500;
          }

          .question-content-item-top-right {
            margin-left: 50px;
            //flex: 1;
            height: 28px;
            line-height: 1.2;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 12px;
            color: #666666;
          }
        }

        .question-content-item-bottom {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 8px; /* 添加元素间距 */
          
          .keyword-custom {
            display: flex;
            flex-direction: row;
            height: 28px;
            line-height: 1.2;
            align-items: center;
            color: #333333;
            flex-shrink: 0; /* 防止压缩 */
            font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
            font-weight: 600;
            font-style: normal;
            font-size: 16px;
          }

          .requesType-custom {
            height: 28px;
            //line-height: 1.2;
            display: flex;
            align-items: center;
            color: #333333;
            flex-shrink: 0; /* 防止压缩 */
            font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
            font-weight: 600;
            font-style: normal;
            font-size: 16px;
          }
          
          .requestNum-custom {
            height: 28px;
            line-height: 1.2;
            display: flex;
            align-items: center;
            color: #333333;
            flex-shrink: 0; /* 防止压缩 */
            font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
            font-weight: 600;
            font-style: normal;
            font-size: 16px;
          }
        }
      }

      .question-content-item-right {
        display: flex;
        align-items: center;
        flex: 1;
        height: 100%;
        width: 100%;
        padding-right: 12px;
        //justify-content: flex-end; /* 右对齐 */

        .question-content-item-img {
          margin-right: 8px;

          img {
            width: 60px; /* 进一步调小图片 */
            height: 40px;
            border-radius: 3px;
            object-fit: cover;
          }
        }
        

      
      }
    }
  }
}

.question-bottom {
  display: flex;
  flex-direction: row;
  margin-top: 53px;
  margin-bottom: 53px;
}

.lc-empty {
  height: 670px;
}

.no-more {
  width: 100%;
  display: flex;
  text-align: center;
  justify-content: center;
  color: var(--color-theme-project);
  font-family: var(--text-family);
  font-weight: 400;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  margin: 10px 0;
}

.down-more {
  width: 100%;
  display: flex;
  justify-content: center;
  color: var(--color-theme-project);
  cursor: pointer;
}

.down-more:hover {
  font-weight: bolder;
}

/* 引号内容包装器 */
.quote-content-wrapper-custom {
  display: flex;
  width: 100%;
  position: relative;
  white-space: nowrap;
  align-items: center;
  height: 28px; /* 调整高度匹配新布局 */
  overflow: hidden;
}

/* 左引号样式 */
.quote-left-custom {
  flex-shrink: 0;
  margin-right: 2px;
  font-size: 13px; /* 调小引号 */
  line-height: 1;
  display: flex;
  align-items: center;
  color: #999;
}

/* 右引号样式 */
.quote-right-custom {
  flex-shrink: 0;
  margin-left: 2px;
  position: relative;
  z-index: 2;
  font-size: 13px; /* 调小引号 */
  line-height: 1;
  display: flex;
  align-items: center;
  color: #999;
}

/* 省略号文本样式 */
.ellipsis-text-custom {
  display: flex;
  max-width: calc(100% - 12px);
  color: inherit;
  position: relative;
  align-items: center;
  overflow: hidden;
  height: 100%;
}

/* 主要标题样式 */
.main-title-custom {
    font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
    font-weight: 400;
    font-style: normal;
}

/* 次要文本样式 */
.secondary-text-custom {
    font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
    font-weight: 600;
    font-style: normal;
    font-size: 16px;
}

/* 标题样式 */
.title-custom {
    font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
    font-weight: 600;
    font-style: normal;
    font-size: 16px;
}

/* 对不包含公式的内容应用省略号效果以及公式的垂直居中 */
:deep(.ellipsis-text p) {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-height: 100%;
}
</style>
