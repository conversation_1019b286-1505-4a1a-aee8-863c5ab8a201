<template>
  <div class="help-center-wrapper">
    <!-- 1 帮助中心页面 -->
    <div class="container" v-if="pageId == '1'">
      <!--TODO unfix 这块有个问题，如果帮助中心中只有一条数据，这段代码就会报错，后续需要检查一下是为什么-->
      <div class="menu">
        <div
          v-for="(menuitem, index) in menuInfo"
          :key="index"
          class="menu1"
          :class="{ highlight: selectedIndex == index }"
          @click="changecurrentpage(index)"
        >
          {{ menuitem.title }}
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="line"></div>
      <div class="content">
        <div class="title">{{ menuInfo[currentpage].title }}</div>
        <div class="detail" v-html="renderMarkdown(menuInfo[currentpage].content)"></div>
      </div>
    </div>
    <!-- 根据不同的参数目录进行改变 -->
    <!-- 2用户协议页面 -->
    <!-- 3隐私政策页面 -->
    <!-- 4知识产权声明 -->
    <div class="container" v-else>
      <div class="menu">
        <!-- <div class="menu1" style="margin-top: 50px">
          
          {{ title[Number(pageId) - 2] }}
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div> -->
        <div
          v-for="(value, index) in title"
          :key="index"
          class="menu1"
          :class="{ highlight: selectedIndex == index }"
          @click="changeinnerpage(index)"
        >
          {{ value }}
        </div>
      </div>
      <div class="line"></div>
      <div class="content">
        <div class="title">{{ menuInfo2.title }}</div>
        <div class="detail" v-html="renderMarkdown(menuInfo2.content)"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getHelpCenterApi,
  getPrivacyApi,
  getStatementApi,
  getUserAgreementApi
} from '@/apis/helpcenter';
import router from '@/router';
import { renderMarkdown } from '@/utils/markdown';

const currentpage = ref(0);
const route = useRoute();
const pageId = ref<string | null>(null);
const changecurrentpage = (id: number) => {
  currentpage.value = id;
  selectedIndex.value = id;
};
const selectedIndex = ref(0); // 给一个初始值
// selectedIndex.value=Number(route.query.pageId)-2;
type menu = {
  title: string;
  content: string;
};

//
let title = ref(['用户协议', '隐私政策', '知识产权声明']);
//按文本原有格式输入即可
let detail1 = '';

// 需要探索一下如何能够保持文字的段落结构
let menuInfo = ref<menu[]>([
  {
    title: '',
    content: detail1
  }
]);
let menuInfo2 = ref<menu>({
  title: '',
  content: ''
});

// 现在，变量content包含了你提供的文字，并保留了原有的格式。
onMounted(() => {
  // 获取路由中的 pageId 参数
  let pageIdd = route.query.pageId as string;
  pageId.value = pageIdd || '1'; // 如果没有 pageId，默认为 '1'
  getcontent(); // 总是调用 getcontent
});
//如果跳转有问题，可以将上面的赋值调整到watch里
watch(
  () => route.query,
  (nv, ov) => {
    pageId.value = nv.pageId as string;
    getcontent();
  }
);
const changeinnerpage = (id: number) => {
  router.push({ path: '/helpcenter', query: { pageId: id + 2 } });
  selectedIndex.value = id;
};
const getcontent = () => {
  if (pageId.value == '1') {
    selectedIndex.value = 0;
    getHelpingCenter();
  } else if (pageId.value == '2') {
    selectedIndex.value = 0;
    getUserAgreement();
  } else if (pageId.value == '3') {
    selectedIndex.value = 1;
    getPrivacy();
  } else {
    selectedIndex.value = 2;
    getStatement();
  }
};
const getHelpingCenter = () => {
  getHelpCenterApi()
    .then((res) => {
      // console.log('获取帮助中心数据', res.list);
      menuInfo.value = res.data.list;
    })
    .catch(() => {});
};

const getPrivacy = () => {
  getPrivacyApi()
    .then((res) => {
      console.log('获取隐私政策', res);
      menuInfo2.value = res.data;
    })
    .catch(() => {});
};
const getStatement = () => {
  getStatementApi()
    .then((res) => {
      console.log('获取知识产权', res);
      menuInfo2.value = res.data;
    })
    .catch(() => {});
};
const getUserAgreement = () => {
  getUserAgreementApi()
    .then((res) => {
      console.log('获取用户协议', res);
      menuInfo2.value = res.data;
    })
    .catch(() => {});
};
</script>

<style lang="less" scoped>
.help-center-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;

  .container {
    display: flex;
    flex-direction: row;
    width: 1400px;
    // height: 720px;

    .menu {
      display: flex;
      flex-direction: column;
      width: 327px;
      align-items: center;
      padding-left: 17px;

      .menu1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 300px;
        height: 60px;
        background-color: #ffffff;
        border-radius: 5px;
        margin-top: 10px;
        justify-content: space-between;
        padding-right: 35px;
        padding-left: 15px;
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        line-height: normal;
        font-feature-settings: 'kern';
        font-family: '阿里巴巴普惠体 3.0 55  L3', '阿里巴巴普惠体 3.0 55 Regular',
          '阿里巴巴普惠体 3.0', sans-serif;
      }

      .menu1:hover {
        //background-color: rgb(240, 240, 240);
        cursor: pointer;
      }

      .highlight {
        background-color: rgb(240, 240, 240) !important;
        cursor: pointer;
      }
    }

    .line {
      // display: flex;
      border-width: 0px;
      left: 0px;
      top: 0px;
      width: 2px;
      background-color: rgb(240, 240, 240);
    }
    .content {
      display: flex;
      width: 1071;
      flex-direction: column;
      padding-top: 52px;
      padding-left: 101px;

      .title {
        font-size: 36px;
        font-weight: 700;
        color: var(--color-theme-project);
        line-height: normal;
        font-feature-settings: 'kern';
        font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
          '阿里巴巴普惠体 3.0', sans-serif;
      }

      .detail {
        margin-top: 23px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: normal;
        font-feature-settings: 'kern';
        width: 885px;
        // white-space: phre-wrap; //用于控制文本格式

        white-space: pre-line;
        font-family: '阿里巴巴普惠体 3.0 55  L3', '阿里巴巴普惠体 3.0 55 Regular',
          '阿里巴巴普惠体 3.0', sans-serif;
      }
    }
  }
}
</style>
