/**
 * keyWords区域悬浮放大功能的核心逻辑
 * 现在由 App.vue 的 MutationObserver 统一调用
 */
let tooltipElement: HTMLElement | null = null;

export const showKeyWordsTooltip = (element: HTMLElement) => {
  hideKeyWordsTooltip();

  tooltipElement = document.createElement('div');
  const cloned = element.cloneNode(true) as HTMLElement;

  // 重置样式
  if (element.classList.contains('equation')) {
    cloned.style.cssText =
      'height: auto !important; margin: 0 !important; font-size: initial !important;';
  } else {
    cloned.style.cssText = 'height: auto !important; max-height: 200px; max-width: 300px;';
  }

  tooltipElement.appendChild(cloned);

  // 定位和样式
  const rect = element.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;
  
  // 计算浮窗尺寸（临时添加到DOM中测量）
  tooltipElement.style.visibility = 'hidden';
  tooltipElement.style.position = 'fixed';
  tooltipElement.style.top = '0px';
  tooltipElement.style.left = '0px';
  document.body.appendChild(tooltipElement);
  const tooltipRect = tooltipElement.getBoundingClientRect();
  const tooltipHeight = tooltipRect.height;
  const tooltipWidth = tooltipRect.width;
  
  // 计算最佳位置
  let top = rect.top - 10;
  let left = rect.left + rect.width / 2;
  let transform = 'translateX(-50%) translateY(-100%)';
  
  // 检查上边界
  if (top - tooltipHeight < 10) {
    // 如果上方空间不够，显示在下方
    top = rect.bottom + 10;
    transform = 'translateX(-50%) translateY(0)';
  }
  
  // 检查下边界
  if (top + tooltipHeight > viewportHeight - 10) {
    // 如果下方空间不够，显示在上方
    top = rect.top - tooltipHeight - 10;
    transform = 'translateX(-50%) translateY(0)';
  }
  
  // 检查左边界
  if (left - tooltipWidth / 2 < 10) {
    left = tooltipWidth / 2 + 10;
    transform = 'translateX(-50%) translateY(-100%)';
  }
  
  // 检查右边界
  if (left + tooltipWidth / 2 > viewportWidth - 10) {
    left = viewportWidth - tooltipWidth / 2 - 10;
    transform = 'translateX(-50%) translateY(-100%)';
  }
  
  // 确保最小边距
  top = Math.max(10, top);
  left = Math.max(tooltipWidth / 2 + 10, Math.min(left, viewportWidth - tooltipWidth / 2 - 10));
  
  Object.assign(tooltipElement.style, {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    transform: transform,
    background: 'white',
    border: '1px solid #ddd',
    borderRadius: '4px',
    padding: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    zIndex: '99999',
    pointerEvents: 'none',
    visibility: 'visible'
  });

  document.body.appendChild(tooltipElement);
};

export const hideKeyWordsTooltip = () => {
  if (tooltipElement && tooltipElement.parentNode) {
    tooltipElement.parentNode.removeChild(tooltipElement);
    tooltipElement = null;
  }
};

// 为单个元素绑定悬浮事件
export const bindKeyWordsHover = (element: HTMLElement) => {
  // 避免重复绑定
  if (element.hasAttribute('data-keywords-hover-bound')) {
    return;
  }

  element.addEventListener('mouseenter', () => {
    showKeyWordsTooltip(element);
  });

  element.addEventListener('mouseleave', () => {
    hideKeyWordsTooltip();
  });

  element.setAttribute('data-keywords-hover-bound', 'true');
};
