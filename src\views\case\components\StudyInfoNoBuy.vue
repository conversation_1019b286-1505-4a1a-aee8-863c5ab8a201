<template>
  <!-- 未购买状态封装-->
  <div class="study-info">
    <div class="learn-footer-info">
      <img style="width: 20px; height: 20px" :src="info.userCoverPic" class="logo" />
      <span class="wjby">{{ info.userName }}</span>
    </div>
    <div class="learn-footer-info percentage">
      <img src="@/assets/svgs/u2938.svg" class="logo" />
      <span class="knowledge_sum">知识点总数:&nbsp;{{ info.klgCount }}</span>
    </div>
    <div
      v-if="info.goodsType == GoodsType.vip"
      style="display: flex; flex-direction: row; align-items: center"
    >
      <div>有效期：</div>
      <div>
        <el-select v-model="validity" style="width: 100px">
          <el-option
            v-for="(option, index) in info.skuList!"
            :key="index"
            :label="cardTypeName[option.cardType]"
            :value="index"
          >
          </el-option>
        </el-select>
      </div>
    </div>
    <div v-else>有效期：{{ info.studyTime }}天</div>
    <div>购买费用：{{ info.price }}元</div>
    <div class="btn-wrapper" v-if="info.goodsType != GoodsType.vip">
      <CmpButton class="btn" type="primary" @click="payDialogVisible = true">购买</CmpButton>
      <!-- TODO: 没有试学就隐藏 -->
      <CmpButton class="btn" type="info" @click="tryWatch" v-show="withTryChpater">试学</CmpButton>
    </div>
    <div class="btn-wrapper" v-else>
      <CmpButton class="btn" type="primary" @click="payDialogVisible = true" style="width: 100px"
        >购买</CmpButton
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import { GoodsType } from '@/types/goods';
import { ref } from 'vue';
import type { NobuyInfoType } from '@/types/case';
import { useLearnStore } from '@/stores/learnintro';
import { useProjectStore } from '@/stores/project';
import type { Sku } from '@/types';
//数据的统一管理
const learnStore = useLearnStore();
const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const payDialogVisible = inject('payDialogVisible') as Ref<boolean>;

const router = useRouter();
const route = useRoute();
const spuId = route.query.spuId as string;

const validity = ref<number>(0);
// 值待确定
const validityOptions = ref([
  {
    label: '',
    value: 0,
    price: ''
  }
]);
// 定义触发事件
const emits = defineEmits(['changeday']);

const withTryChpater = ref(false);
const chapterId = ref<string>();

const tryWatch = () => {
  // 触发试看事件
  if (chapterId.value) {
    router.push({
      path: '/learning',
      query: {
        spuId,
        chapterId: chapterId.value
      }
    });
  }
};
// const handleSelectChange = (value: any) => {
//   price.value = info.value.price[value];
//   emits('changeday',validity.value)
//   console.log(validity.value)

// };
// 项目信息
const cardDays = [0, 365, 90, 30];
enum cardTypes {
  yearCard = 1,
  monthCard = 2,
  dayCard = 3
}
const cardTypeName = ['', '年卡', '季卡', '月卡'];

const sku = computed(() => {
  return info.value.skuList![validity.value];
});

onMounted(() => {
  watch(validity, () => {
    if (info.value.goodsType == GoodsType.vip) {
      projectStore.setSkuId(sku.value.skuId);
      projectStore.setPrice(sku.value.price);
      projectStore.setStudyTime(sku.value.studyTime);
    }
  });
  // 监听 info 变化, 在第一次变化时(getPrjIntroduce 返回数据时)执行初始化数据
  const unwatch = watch(info, () => {
    const chapterList = info.value.chapterList;
    const tryChapterId = chapterList.find((item) => item.isTry == 1)?.sectionId;
    if (tryChapterId) {
      withTryChpater.value = true;
      chapterId.value = tryChapterId;
    }
    if (info.value.goodsType == GoodsType.vip) {
      projectStore.setSkuId(sku.value.skuId);
      projectStore.setPrice(sku.value.price);
      projectStore.setStudyTime(sku.value.studyTime);
    }
    unwatch();
  });
  const chapterList = info.value.chapterList;
  const tryChapterId = chapterList.find((item) => item.isTry == 1)?.sectionId;
  if (tryChapterId) {
    withTryChpater.value = true;
    chapterId.value = tryChapterId;
  }
});
const initData = () => {
  // if (isArea == LearnType.area) {
  //   // 说明当前是领域学习
  //   // console.log(info.price[0]);
  //   // 操作传入的价格 使其符合下拉框选项
  //   validityOptions.value.length = 0;
  //   const priceArr = info.priceArr[0];
  //   for (let i = 1; i < 4; i++) {
  //     // console.log(priceArr[1]);
  //     validityOptions.value.push({
  //       label: cardTypeName[i],
  //       value: i,
  //       price: priceArr.price ,
  //     });
  //   }
  //   console.log(validityOptions);
  // }
};
onBeforeMount(() => {});
</script>

<style lang="less" scoped>
.study-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .percentage {
    margin-left: 15px;
    font-size: var(--fontsize-small-project);
    font-weight: 400;
    color: rgb(0, 85, 121);
  }

  .knowledge_sum {
    display: flex;
    align-items: center;
    margin-left: 15px;
  }

  .learn-footer-info {
    display: flex;
    align-items: center;

    .wjby {
      font-family: var(--text-family);
      font-size: var(--fontsize-small-project);
      font-weight: 400;
      font-family: var(--text-family);
      color: var(--color-deep);
      margin-left: 10px;
    }
  }

  .btn-wrapper {
    height: 40px;

    .btn {
      width: 70px;
      height: 35px;
      font-size: var(--fontsize-middle-project);
    }

    .btn:nth-child(2) {
      margin-left: 10px;
    }
  }
}
</style>
