<template>
  <div class="learn-wrapper">
    <div class="learn">
      <div class="learn-up">
        <div class="learn-up-content">
          <div class="up-left">
            <div class="header-info">
              <div class="left-info">
                <div class="icon-info" style="margin-left: 10%; margin-top: 10px" @click="changeLearn(learnedType.learning)">
                  <img
                    v-if="isLearning == learnedType.learning"
                    src="@/assets/images/learn/u3571.svg"
                    alt="当前学习内容图标"
                  />
                  <img v-else src="@/assets/images/learn/u3570.svg" alt="当前学习内容图标" />
                  <span :class="isLearning == learnedType.learning ? '' : 'inactive'"
                    >正在学({{ learningTotal }})</span
                  >
                </div>
                <div
                  class="icon-info"
                  style="margin-left: 35%; margin-top: 10px"
                  @click="changeLearn(learnedType.learned)"
                >
                  <img
                    v-if="isLearning == learnedType.learned"
                    src="@/assets/images/learn/u3571.svg"
                    alt="当前学习内容图标"
                  />
                  <img v-else src="@/assets/images/learn/u3570.svg" alt="当前学习内容图标" />
                  <span :class="isLearning == learnedType.learning ? 'inactive' : ''"
                    >已学过({{ learnedTotal }})</span
                  >
                </div>
              </div>
              <div class="right-icons">
                <img
                  @click="changeContent(1)"
                  style="margin-right: 8px"
                  src="@/assets/images/learn/u3568.svg"
                  alt="当前学习内容图标"
                  v-if="currentCard == 1"
                />
                <img
                  @click="changeContent(1)"
                  style="margin-right: 8px"
                  src="@/assets/images/learn/u6.svg"
                  alt="当前学习内容图标"
                  v-else
                />
                <img
                  @click="changeContent(2)"
                  src="@/assets/images/learn/u3569.svg"
                  alt="当前学习内容图标"
                  v-if="currentCard == 2"
                />
                <img
                  @click="changeContent(2)"
                  src="@/assets/images/learn/u7.svg"
                  alt="当前学习内容图标"
                  v-else
                />
              </div>
            </div>
            <!-- 后续使用v-for渲染
            -->
            <div class="body-info">
              <!-- 空数据处理 -->
              <div class="lc-wrapper" v-if="currentCard == 1">
                <!-- 骨架屏 -->
                <div class="lc" v-if="loading">
                  <div 
                    v-for="index in skeletonCount" 
                    :key="'skeleton_' + index"
                    class="skeleton-card"
                  >
                    <div class="skeleton-left-img"></div>
                    <div class="skeleton-mid-info">
                      <div class="skeleton-start-time"></div>
                      <div class="skeleton-title"></div>
                      <div class="skeleton-learn-process">
                        <div class="skeleton-process-text"></div>
                      </div>
                      <div class="skeleton-current-learn">
                        <div class="skeleton-current-label"></div>
                        <div class="skeleton-current-content">
                          <div class="skeleton-icon"></div>
                          <div class="skeleton-chapter-title"></div>
                          <div class="skeleton-time-tag"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 实际内容 -->
                <div class="lc" v-else-if="learnList.length != 0">
                  <LearningCard
                    v-for="(card, index) in learnList"
                    :key="index + '_'"
                    :LCinfo="card"
                    @cancel="getList"
                  >
                  </LearningCard>
                </div>
                <div v-else class="lc-empty"><el-empty description="暂无学习记录" /></div>
                <!-- 分页器 -->
                <div class="footer-pagination">
                  <MyPagination
                    ref="mypagination"
                    :total="cardTotalPages"
                    :currentPage="cardCurrentPage"
                    @pageChange="handlerCardPageChange"
                  ></MyPagination>
                </div>
              </div>
              <!-- 组件化 -->
              <div v-if="currentCard == 2" class="an-wrapper">
                <CalendarCard :learned="isLearning"></CalendarCard>
              </div>
            </div>
          </div>
          <RightIntroduction></RightIntroduction>
        </div>
      </div>
      <div class="learn-down">
        <DownList></DownList>
      </div>
    </div>
  </div>
  <BackTop></BackTop>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import RightIntroduction from './components/RightIntroduction.vue';
import LearningCard from './components/LearningCard.vue';
import blankCard from './components/blankCard.vue';
import MyPagination from './components/MyPagination.vue';
import DownList from './components/DownList.vue';
import CalendarCard from './components/CalendarCard.vue';
import { getLearnListApi, getHistoryListApi, getTotalApi } from '@/apis/learnStatistics';
import BackTop from '@/components/Backtop.vue';

// const goBack = () => {
//   router.push('/home');
// };

// 正在学习 和 已学过类型
enum learnedType {
  learning = 1,
  learned = 2
}
// watch(() => isLearning, (nv, ov) => {

// })
// watch(
//   () => route.query,
//   (nv, ov) => {
//     pageId.value = nv.pageId as string;
//     getcontent();
//   }
// );

// 正在学和已学过内容控制  默认是正在学
const isLearning = ref(learnedType.learning);
const changeLearn = (index: learnedType) => {
  if (isLearning.value == index) {
    return;
  }
  // 切换操作后 重置分页变量
  cardCurrentPage.value = 1;

  if (mypagination && mypagination.value) {
    mypagination.value.currentPage = 1;
  }
  isLearning.value = index;
  getList();
};

// 学习记录内容切换 1-卡片展示  2-日历展示 默认值为1 后续可做优化
const currentCard = ref(1);
const changeContent = (index: number) => {
  if (index == currentCard.value) {
    return;
  }
  currentCard.value = index;
};

// 卡片部分分页控制
let cardTotalPages = ref(10);
let cardCurrentPage = ref(1);
const handlerCardPageChange = (newPages: number) => {
  // 此处对cardCurrentPages进行处理 方便后续请求
  cardCurrentPage.value = newPages;
  getList();
};


const learnList = ref([]);
const loading = ref(true);

// 计算骨架屏数量
const skeletonCount = computed(() => {
  const total = isLearning.value === learnedType.learning ? learningTotal.value : learnedTotal.value;
  return Math.min(total, 4);
});

// 获取学习列表函数
// 获取学习列表函数
const getList = () => {
  loading.value = true;
  const params = {
    current: cardCurrentPage.value,
    limit: 4,
    // 残留问题 后续可优化
    learned: isLearning.value
  };
  getLearnListApi(params)
    .then((res) => {
      learnList.value = res.data.list;
      cardTotalPages.value = res.data.totalPage;
      console.log(res.data);
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};
// type UserInfo = {
//   career: string;
//   coverPic: string;
//   days: number;
//   goal: string;
//   industry: string;
//   skills: Array<string>[];
//   todayTime: number;
//   uniqueCode: string;
//   username: string;
// };
// const userInfo = ref<UserInfo>();
// // 获取个人信息

// const historyInfo = ref([]);
// const historyLimit = ref(10);
// 获取历史记录
// const getHistory = () => {
//   const params = {
//     current: historyCurPage.value,
//     limit: historyLimit.value,
//     title: historyKey.value,
//     time: currentIndex.value
//   };
//   getHistoryList(params)
//     .then((res) => {
//       // 此处对类型进行判断
//       historyInfo.value = res.data.list;
//       historyTotal.value = res.data.total;
//       console.log(res);
//     })
//     .catch((error) => {
//       console.log(error);
//     });
// };
const mypagination = ref(null);
// 挂载时执行
onMounted(() => {
  getList();
  // getHistory();
});

// 计算用户想要学习的技能
// const calSkills = (skills: Array<string>[]) => {
//   let result = '';
//   if (skills.length == 0) {
//     result = '暂无';
//   } else {
//     skills.forEach((skl) => {
//       result = result + skl + ' ';
//     });
//   }
//   return result;
// };

// const historyCurPage = ref(1);
// const historyTotal = ref(3);
// 获取
// const getMore = () => {
//   // 使用增加limit的方法 实现加载更多的展示
//   if (historyLimit.value + 10 > historyTotal.value) {
//     historyLimit.value = historyTotal.value;
//   } else {
//     historyLimit.value += 10;
//   }
//   getHistory();
// };

const learningTotal = ref(0);
const learnedTotal = ref(0);
// 获取总数接口
const getLearnTotal = () => {
  getTotalApi()
    .then((res) => {
      learningTotal.value = res.data.learningTotal;
      learnedTotal.value = res.data.learnedTotal;
    })
    .catch((error) => {
      console.log(error);
    });
};

getLearnTotal();
</script>

<style scoped lang="less">
@cardWidth: 520px;
@cardHeight: 124px;
.learn-wrapper {
  // 骨架屏样式 - 与真实卡片完全一致
  .skeleton-card {
    background-color: white;
    margin-top: 5px;
    width: 530px;
    height: 134px;
    display: flex;
    border-radius: 6px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 15px;
    padding-right: 17px;
    
    // 左侧图片骨架
    .skeleton-left-img {
      width: 170px;
      //height: 124px;
      border-radius: 5px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
    }
    
    // 中间信息骨架
    .skeleton-mid-info {
      width: 350px;
      margin-left: 5px;
      
      // 学习启动时间
      .skeleton-start-time {
        height: 13px;
        width: 120px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 2px;
        margin-bottom: 7px;
      }
      
      // 标题
      .skeleton-title {
        height: 20px;
        width: 280px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 2px;
        margin-bottom: 10px;
      }
      
      // 学习进度信息
      .skeleton-learn-process {
        margin-bottom: 10px;
        
        .skeleton-process-text {
          height: 13px;
          width: 200px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 2px;
        }
      }
      
      // 当前学习信息
      .skeleton-current-learn {
        margin-top: 3px;
        
        .skeleton-current-label {
          height: 13px;
          width: 80px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 2px;
          margin-bottom: 5px;
        }
        
        .skeleton-current-content {
          display: flex;
          align-items: center;
          
          .skeleton-icon {
            width: 12px;
            height: 12px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 2px;
            margin-right: 5px;
            margin-top: 10px;
          }
          
          .skeleton-chapter-title {
            height: 13px;
            width: 180px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 2px;
            margin-top: 10px;
          }
          
          .skeleton-time-tag {
            height: 12px;
            width: 40px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 2px;
            margin-left: 10px;
            margin-top: 10px;
          }
        }
      }
    }
  }
  
  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
  // 重写element变量
  --el-color-primary: var(--color-theme-project);

  display: flex;
  justify-content: center;
  white-space: nowrap;
  width: 100%;
  .learn {
    width: 100%;
    flex-direction: column;
    .learn-up {
      padding-top: 20px;
      background-color: #f2f2f2;
      width: 100%;
      display: flex;
      justify-content: center;
    }
    .learn-down {
      background-color: white;
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
  .learn-up-content {
    width: var(--width-fixed--project);
    display: flex;
    justify-content: space-between;
    min-height: 400px;

    .up-left {
      display: flex;
      flex-direction: column;
      width: 1110px;
      height: 380px;
      background-color: rgba(255, 255, 255, 0.996078431372549);
      //padding-top: 5px;
      //padding-left: 5px;
      //padding-right: 5px;
      border-radius: 8px;


      .header-info {
        display: flex;
        flex-direction: row;
        width: 100%;

        .left-info {
          display: flex;
          gap: 20px;
          flex-direction: row;
          font-family: var(--title-family);
          font-size: var(--fontsize-large-project);

          .icon-info {
            margin-top: 10px !important;
            margin-left: 10px !important;
            display: flex;
            align-items: center;
            height: 20px;
            color: var(--color-theme-project);
            :is(span) {
              margin-left: 10px;
            }

            .inactive {
              cursor: pointer;
              color: var(--color-deep);
              font-weight: 400;

              &:hover {
                color: var(--color-black);
                font-weight: bold;
              }
            }
          }
        }

        .right-icons {
          margin-right: 40px;
          margin-left: auto;
          margin-top: 10px;

          img {
            width: 16px;
            height: 12px;
            cursor: pointer;
          }
        }
      }

      .body-info {
        display: flex;
        width: 100%;
        justify-content: center;

        .lc-wrapper {
          width: 100%;
          min-height: 340px;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .footer-pagination {
            width: 100%;
            display: flex;
            justify-content: center;
            align-content: center;
            margin-top: 20px;
          }

          .lc {
            width: 90%;
            display: grid;
            gap: 30px 30px;
            grid-template-columns: repeat(2, @cardWidth);
            grid-template-rows: repeat(2, @cardHeight);
            padding-left: 10px;
          }
        }

        .an-wrapper {
          width: 100%;
        }
      }
    }
  }
}

.el-backtop {
  --el-backtop-text-color: var(--color-theme-project);
}
</style>
