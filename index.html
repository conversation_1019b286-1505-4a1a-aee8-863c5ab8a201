<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="src/assets/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>无尽本源</title>
    <link rel="stylesheet" href="//at.alicdn.com/t/c/font_4295282_czotfi8mopd.css" />

    <link rel="stylesheet" href="./katex.min.css" />

    <!-- The loading of KaTeX is deferred to speed up page rendering -->
    <script defer src="./katex.min.js" type="module"></script>

    <!-- To automatically render math in text elements, include the auto-render extension: -->
    <script defer src="./contrib/auto-render.min.js" type="module"></script>
    <script defer src="./contrib/mhchem.min.js" type="module"></script>
    <script src="./contrib/copy-tex.min.js" type="module"></script>
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.prod.min.js"></script> -->
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/element-plus-icons-vue/2.1.0/global.iife.min.js"></script> -->
    <!-- <script src="//unpkg.com/@element-plus/icons-vue"></script> -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/luxon@3.4.3/build/global/luxon.min.js"></script> -->
  </head>

  <body>
    <div id="app"></div>

    <script type="module" src="/src/main.ts"></script>
    <script type="module">
      if (import.meta.env.MODE == 'production') {
        const timer = setInterval(() => {
          document.oncopy = function () {
            event.returnValue = false;
          };
        }, 3000);
      }
    </script>
  </body>
</html>
