// 带span版本，will be deprecated soon
import { getQuestionDetailApi } from '@/apis/learning';
import { unescape } from 'lodash-es';
/**
 * 划词功能函数 直接调用此函数 返回可与后端交互的字符串
 * @returns 划词结果 可与后端交互的字符串
 */
const handleLineWord = () => {
  // 划词的内容
  const selection = window.getSelection();
  //只有在htmlContent中的内容才能被划词
  const htmlContent = document.getElementById('htmlContent');
  if (!htmlContent?.contains(selection?.anchorNode as Node)) {
    return null;
  }
  // const selectedText = selection!.toString();
  // console.log(selectedText, selectedText.length);
  if (selection?.isCollapsed) {
    return null;
  }
  const nodes = getSelectedNodes(selection);
  let str = '';
  nodes.forEach((node) => {
    if (node?.querySelector('.equation, .inline-equation')) {
      const cloneNode = node.cloneNode(true);
      const elements = Array.prototype.slice.call(
        cloneNode.querySelectorAll('.equation, .inline-equation')
      );
      elements.forEach((element) => {
        const item = element.querySelector('[encoding="application/x-tex"]');
        const latexCode = unescape(item.innerHTML);
        if (element.className == 'inline-equation') {
          element.outerHTML = `<script type="math/tex">${latexCode}</script>`;
        } else {
          element.outerHTML = `<script type="math/tex; mode=display">${latexCode}</script>`;
        }
      });
      str += cloneNode.outerHTML;
    } else {
      str += node.outerHTML;
    }
  });
  if (str.length > 0) {
    return str;
  } else {
    return null;
  }
};

/**
 * 点击高亮词汇 把和后端交互的过程封装起来了
 * @returns 通过qid要到的问题列表 examSectionType[]
 */
const getQuestionList = async (id: string) => {
  const res = await getQuestionDetailApi(id);
  if (res.success) {
    // const questionList: [] = res.data.questionAndAnswer.map(qa => {
    //   return {
    //     qId: qa.oid,
    //     isValid: true,
    //     relatedText: qa.associatedWords,
    //     qContent: qa.keyword,
    //     qType: qa.questionType, // 是什么 | 为什么
    //     qMode: qa.questionNecessity, // 必要 | 参考
    //     userName:qa.userName,
    //     createTime:qa.createTime,
    //     klg: !qa.answers?null:qa.answers[0].answerKlgs.map(k => {
    //       return {
    //         id: k.klgCode,
    //         name: k.title,
    //       }
    //     }),
    //     explanation: !qa.answers?null:qa.answers[0].answerExplanation,
    //   }
    // })

    // console.log(questionList);
    // return questionList;
    return res.data;
  } else {
    console.log('获取问题列表失败');
  }
};

/**
 * 根据划词接口返回的结果更新span元素上的qid
 * @param val 传入划词接口返回的结果数组 默认所有划词提交接口返回的数据结构相同
 */
const updataDom = (val: any) => {
  val.forEach((item: any) => {
    const element = document.querySelector(`[data-index="${item.dataIndex}"]`);
    // 检查是否找到元素
    if (element) {
      element.setAttribute('data-qid', item.dataQid);
    } else {
      console.log('未找到元素');
    }
  });
};

/**
 * 为有问题的span添加划词标记
 * 在切换为阅读模式时触发此函数
 */
const markWord = () => {
  // 获取所有span
  // const spanArr = document.querySelectorAll('[data-qid]');
  // spanArr.forEach((item: Element) => {
  //   if (item.getAttribute('data-qid') != '') {
  //     // @ts-ignore
  //     item.className = 'highlight';
  //     // item.style.color = 'blue';
  //   }
  // });
};

/**
 * 取消span上的划词标记
 * 在切换为提问/划词模式时触发该函数
 */
const unmarkWord = () => {
  // 获取所有span
  // const spanArr = document.querySelectorAll('[data-qid]');
  // spanArr.forEach((item) => {
  //   if (item.getAttribute('data-qid')) {
  //     item.removeAttribute('class');
  //   }
  // });
};

const getSelectedNodes = (selection: Selection | null): Node[] => {
  if (!selection || selection.isCollapsed) {
    return [];
  }
  const range = selection.getRangeAt(0);
  const nodes: Node[] = [];
  let commonAncestorContainer: any = range.commonAncestorContainer;
  const isElementInTable = (element: HTMLElement) => {
    let currentElement: HTMLElement | null = element;

    while (currentElement) {
      if (currentElement?.tagName?.toLowerCase() == 'table') {
        return currentElement;
      }
      currentElement = currentElement.parentElement;
    }

    return null;
  };
  let element = isElementInTable(commonAncestorContainer);
  if (element) {
    while (!element?.attributes?.getNamedItem('data-index')) {
      if (!element) {
        return [];
      }
      element = element.parentNode;
    }
    return [element];
  }

  const childNode = commonAncestorContainer.querySelector
    ? commonAncestorContainer.querySelector('[data-index]')
    : null;
  if (!childNode) {
    while (!commonAncestorContainer?.attributes?.getNamedItem('data-index')) {
      if (!commonAncestorContainer) {
        return [];
      }
      commonAncestorContainer = commonAncestorContainer.parentNode;
    }
    return [commonAncestorContainer];
  } else {
    // 使用 TreeWalker 遍历选区内的所有节点
    const treeWalker = document.createTreeWalker(commonAncestorContainer, NodeFilter.SHOW_ELEMENT);

    let currentNode = treeWalker.currentNode;
    while (currentNode) {
      if (currentNode.attributes?.getNamedItem('data-index') && range.intersectsNode(currentNode)) {
        nodes.push(currentNode);
      }
      currentNode = treeWalker.nextNode();
    }
    return nodes;
  }
};

export { handleLineWord, getQuestionList, updataDom, markWord, unmarkWord };
