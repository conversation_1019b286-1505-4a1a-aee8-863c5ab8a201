export enum ShowType {
  map = 1,
  draft = 2,
  video = 3
}

export enum ExamType {
  fillBlank = 1,
  choice = 2,
  judgment = 3,
  shortAnswer = 4
}

export interface ExamData {
  examTitle: string;
  answer: string;
  examExplanation: string;
  sectionTitle: string;
  hasAccess: boolean;
  examType: ExamType;
  spuId: string;
  myAnswer: string;
  chapterId: string;
  questionList: any[];
  projectTitle: string;
  examChoices: string[];
  uniqueCode: string;
  contentId: string;
}
