import { http } from '@/apis';
import { PrjForm, PrjType } from '@/types/project';

export function getLearningApi() {
  return http.request<{
    total: number;
    totalPage: number;
    list: Array<{
      masteredKlgCount: number,
      fullyMasteredKlgCount: number,
      klgCount: number,
      userName: string;
      userCoverPic: string;
      lastLearnedTime: string;
      currentLearning: {
        prjType: PrjType;
        prjForm: PrjForm;
        sectionTitle: string;
        spuId: string;
        endTimeTag: string;
      };
      graspKlg: number;
      klgNumbers: number;
      learned: number;
      startTime: string;
      spuId: string;
      goodsType: 0;
      prjType: PrjType;
      skuId: string;
      coverPic: string;
      title: string;
      buyStatus: null;
      timeList: any[];
    }>;
  }>({
    method: 'get',
    url: '/home/<USER>',
    params: {
      current: 1,
      limit: 1
    }
  });
}

export function getCaseinfoApi() {
  return http.request<{
    list: Array<{
      coverPic: string;
      title: string;
      description: string;
      spuId: string;
      prjStudyFrequence: number;
      prjQuestionNumbers: number;
      studyTime: number;
      goodsType: 0;
      isFree: boolean;
      userCoverPic: string;
      userName: string;
      prjType: PrjType;
      prjForm: PrjForm;
      buyStatus: 1;
      latestChapterId: null;
    }>;
  }>({
    method: 'get',
    url: '/home/<USER>',
    params: {
      current: 1,
      limit: 7
    }
  });
}
//测评暂时使用这个 后面要改
export function getKlginfoApi(prjType: PrjType, param: PrjForm) {
  return http.request<{
    list: Array<{
      coverPic: string;
      title: string;
      description: string;
      spuId: string;
      prjStudyFrequence: number;
      prjQuestionNumbers: number;
      studyTime: number;
      duration: string;
      goodsType: 0;
      isFree: 1;
      userCoverPic: string;
      userName: string;
      prjType: PrjType;
      prjForm: PrjForm;
      buyStatus: 1;
      latestChapterId: null;
    }>;
  }>({
    method: 'get',
    url: '/home/<USER>',
    params: {
      prjType,
      param
    }
  });
}
//案例+讲解首页获取列表
export function getPrjinfoApi(type: PrjType) {
  return http.request<{
    list: Array<{
      coverPic: string;
      title: string;
      description: string;
      spuId: string;
      prjStudyFrequence: number;
      prjQuestionNumbers: number;
      studyTime: number;
      duration: string;
      goodsType: 0;
      isFree: 1;
      userCoverPic: string;
      userName: string;
      prjType: PrjType;
      prjForm: PrjForm;
      buyStatus: 1;
      latestChapterId: null;
      countWord?: number;
    }>;
  }>({
    method: 'get',
    url: '/home/<USER>/query',
    params: {
      type
    }
  });
}
