<template>
  <div class="prj-learn-wrapper">
    <div class="prj-learn">
      <div class="left-route">
        <div class="up-detail-card">
          <div class="prj-img">
            <img style="width: 298px; height: 185px" :src="info.coverPic" alt="" />
          </div>
          <div class="prj-info">
            <div class="title">
              <h1>{{ info.title }}</h1>
            </div>
            <div class="detail">
              {{ info.description }}
            </div>
          </div>
        </div>
        <div class="down-routes">
          <div class="route-items active">知识地图</div>
        </div>
      </div>
      <!-- 在此处控制组件的渲染 -->
      <div class="right-info">
        <!-- 在此处渲染详情页 -->
        <KlgMap @startTest="handleStart"></KlgMap>
      </div>
    </div>
    <PayDialog v-model="payDialogVisible" />
  </div>
</template>

<script setup lang="ts">
import KlgMap from './components/KlgMap.vue';
import PayDialog from '@/components/PayDialog.vue';
import { useLearnStore } from '@/stores/learnintro';
import { useProjectStore } from '@/stores/project';
import { getPrjIntroduceApi } from '@/apis/case';
const learnStore = useLearnStore();
const router = useRouter();
const route = useRoute();
const spuId = route.query.spuId as string;

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const payDialogVisible = ref(false);
provide('payDialogVisible', payDialogVisible);

// 点击开始后 控制右侧组件的显示
const handleStart = () => {
  console.log('点击开始');
  try {
    if (
      // istry 0代表不可看，1代表可看
      // isFree 0 付费
      // FIXME: 需要一条属性表示项目是否免费
      info.value.buyStatus ||
      canBeTried.value
    ) {
      router.push({
        path: '/learning',
        query: route.query
      });
    } else {
      // 拒绝
      ElMessage({
        message: '暂无学习权限',
        type: 'error'
      });
    }
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
type klgType = {
  klgId: string;
  klgTitle: string;
};
const prjInfo = ref({ title: '', description: '', coverPic: '', isFree: true });
const prjDetail = ref({
  title: '',
  userName: '',
  userCoverPic: '',
  klgNumbers: 0,
  validDate: 0,
  targetKlgs: [],
  studyInstructions: '',
  price: ''
});
const prjForm = ref(0);

provide('title', prjInfo);
provide('prjDetail', prjDetail);
provide('prjForm', prjForm);

const canBeTried = ref(false);
const getIntroduce = () => {
  getPrjIntroduceApi({ spuId })
    .then((res) => {
      info.value = res.data;
      console.log(res);
      prjInfo.value.title = res.data.title;
      prjInfo.value.description = res.data.description;
      prjInfo.value.coverPic = res.data.coverPic;
      // console.log(prjInfo.value);
      prjForm.value = res.data.prjForm;
      // learnStore.setData(res.data.prjForm, res.data.buyStatus);

      prjDetail.value.klgNumbers = res.data.klgNumbers;
      prjDetail.value.studyInstructions = res.data.studyInstructions;
      prjDetail.value.targetKlgs = res.data.targetKlgs;
      prjDetail.value.title = res.data.title;
      prjDetail.value.userCoverPic = res.data.userCoverPic;
      prjDetail.value.userName = res.data.userName;
      prjDetail.value.validDate = res.data.validDate;
      console.log(prjDetail.value);
      if (res.data.price) {
        prjDetail.value.price = res.data.price;
      }
      if (res.data.chapterList[0].isTry != 0) {
        canBeTried.value = true;
      }
      // FIXME：我觉得这样有问题，感觉应该和后端讨论一下能不能加个字段，真的不会有中间可以试学的情况吗
    })
    .catch((error) => {
      console.log(error);
    });
};

// 初始化时获取课程状态
onMounted(() => {
  getIntroduce();
});
</script>

<style scoped lang="less">
.prj-learn-wrapper {
  background-color: white;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  white-space: nowrap;

  .prj-learn {
    width: var(--width-fixed--project);
    display: flex;
    flex-direction: row;

    .left-route {
      width: 300px;
      display: flex;
      flex-direction: column;

      .up-detail-card {
        color: var(--color-black);
        font-family: var(--text-family);
        width: 100%;
        height: 373px;
        border: 0.8px solid rgba(220, 223, 230, 1);
        border-radius: 5px;

        .prj-info {
          width: 100%;
          padding-left: 10px;
          margin-top: 40px;
          padding-right: 10px;
          padding-bottom: 10px;

          .title {
            font-family: var(--title-family);
            width: 100%;
            overflow: hidden;

            h1 {
              font-size: var(--fontsize-middle-project);
              font-weight: 700;
            }
          }

          .detail {
            width: 100%;
            white-space: pre-wrap;
            font-size: var(--fontsize-small-project);
            margin-top: 12px;
            height: 100px;
            text-overflow: ellipsis;
            /* 2.设置旧版弹性盒 */
            display: -webkit-box;
            /* 3. 控制行数*/
            -webkit-line-clamp: 6;
            /* 4. 设置子元素的排列方式  垂直排列*/
            -webkit-box-orient: vertical;
            /* 5.溢出隐藏 */
            overflow: hidden;
          }
        }
      }

      .down-routes {
        width: 100%;
        display: flex;
        flex-direction: column;
        height: 170px;
        justify-content: space-between;
        margin-top: 30px;

        .route-items {
          height: 50px;
          width: 100%;
          font-size: var(--fontsize-large-project);
          border-radius: 5px;
          border-width: 0.8px;
          border-style: solid;
          line-height: 50px;
          padding-left: 10px;
        }

        .route-items:hover {
          border-color: var(--color-theme-project);
          background-color: var(--color-second);
          color: var(--color-theme-project);
        }

        .active {
          color: white;
          background-color: var(--color-theme-project);
        }
      }
    }
  }
}
</style>
