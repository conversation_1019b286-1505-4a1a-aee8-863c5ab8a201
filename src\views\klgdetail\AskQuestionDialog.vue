<template>
  <el-dialog
    v-model="dialogVisible"
    width="1400px"
    @close="handleClose"
    @closed="handleClosed"
    class="ask-dialog"
  >
    <div class="ask-dialog-main">
      <!-- 左侧内容区 -->
      <div class="ask-dialog-left">
        <div class="desc" style="display: flex; justify-content: space-between">
          <div>
            <el-icon style="margin-right: 4px"><InfoFilled /></el-icon>
            <i>鼠标滑过文本内容，可以提问。</i>
          </div>
          <el-input
            v-model="search"
            size="small"
            placeholder="请输入要查找的关键词"
            prefix-icon="el-icon-search"
            style="max-width: 225px"
            @keyup.enter="handleSearch(search)"
            clearable
          >
            <template #append>
              <el-button :icon="Search" @click="() => handleSearch(search)" />
            </template>
          </el-input>
        </div>
        <div class="content-area">
          <div
            id="underline"
            style="
              font-family:
                '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0',
                sans-serif;
              font-weight: 400;
              font-style: normal;
              font-size: 14px;
            "
          >
            <div
              v-if="props.type === 2"
              v-for="(item, idx) in displayContent"
              :key="`paragraph-${idx}`"
              class="paragraph-wrapper textfont"
            >
              <!-- 时间显示 - 始终显示在段落上方 -->
              <div
                class="select_to_ask_time titlefont"
                v-if="item.timeInfo && item.timeInfo.startTime"
                @click.stop="jumpToQuestionTime(item.timeInfo.startTime)"
              >
                {{ item.timeInfo.startTime }}
              </div>
              <!-- 内容显示 -->
              <div class="text">
                <span v-html="item.htmlContent"></span>
              </div>
            </div>
            <div v-else-if="props.type === 3">
              <AnsweredQuestion
                :contentArray="Array.isArray(renderedContent) ? renderedContent : []"
                :exerciseInfo="props.exerciseInfo"
              />
            </div>
            <div v-else-if="props.type === 1"><div v-html="renderedContent"></div></div>
            <div v-else><div v-html="renderedContent"></div></div>
          </div>
        </div>
      </div>
      <!-- 右侧问题区 -->
      <div class="ask-dialog-right">
        <div v-if="props.type === 1" class="outline-and-questions">
          <div class="outline-content">
            <MarkdownOutline
              v-if="outline.length > 0"
              :outline="outline"
              @jump="handleOutlineJump"
            />
            <div v-else class="no-outline">
              <p>暂无目录大纲</p>
            </div>
          </div>
        </div>
        <!-- 问题列表始终显示 -->
        <div v-if="props.type !== 1" class="question-header" :key="'question-header'">
          <span>问题列表</span>
        </div>
        <div v-if="props.type !== 1" class="question-list" :key="'question-list'">
          <div
            v-if="props.type === 2"
            v-for="q in questionTimelineMatches"
            :key="q.questionId"
            class="question-item"
            @click="handleQuestionItemClick(q)"
          >
            <span
              class="question-time"
              @click.stop="jumpToQuestionTime(q.startTime)"
              style="color: #333333; cursor: pointer"
            >
              {{ getDisplayTime(q.startTime) }}
            </span>
            <span class="description">
              【
              <span class="key-words textfont" v-html="q.keyword || ''"></span>
              】
              <span v-if="q.questionType != '开放性问题'" class="question-type textfont"
                >{{ q.questionType }}?</span
              >
            </span>
          </div>
          <div
            v-else
            v-for="q in questions"
            :key="'filtered-' + q.questionId"
            class="question-item"
            @click="handleQuestionItemClick(q)"
          >
            <span class="description">
              【
              <span class="key-words textfont" v-html="q.keyword || ''"></span>
              】
              <span v-if="q.questionType != '开放性问题'" class="question-type textfont"
                >{{ q.questionType }}?</span
              >
            </span>
          </div>
        </div>
        <!-- 直接插入弹窗组件，不要包裹层 -->
        <QuestionDrawer
          v-if="showQuestionDrawer"
          :visible="showQuestionDrawer"
          :selectedText="selectedText"
          :zIndex="componentZIndex.question"
          :buyStatus="true"
          :local="true"
          @close="handleCloseQuestionDrawer"
        />
        <AnswerDrawerSidebar
          v-if="showAnswerDrawer"
          :visible="showAnswerDrawer"
          :questionData="currentQuestionData"
          :projectAuthor="projectAuthor"
          :zIndex="componentZIndex.answer"
          @close="handleCloseAnswerDrawer"
          @show-question="handleShowQuestionFromFloating"
        />
      </div>
    </div>

    <!-- 问题图标 -->
    <div
      v-if="questionIconVisible"
      ref="questionIconElement"
      class="question-icon"
      :style="{
        position: 'fixed',
        left: questionIconPosition.x + 'px',
        top: questionIconPosition.y + 'px',
        zIndex: 10000
      }"
      @click="handleQuestionIconClick"
    >
      <!-- 悬浮提示 -->
      <div class="question-tooltip">提问</div>
      <!-- 问号图标 -->
      <div class="question-icon-circle">
        <img :src="questionIcon" alt="" />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import type { QuestionType } from '@/types';
//文稿类
import MarkdownOutline from '@/views/learning/components/MarkdownOutline.vue';
import { extractOutline, addIdsToHeadings, scrollToOutlineItem } from '@/utils/outlineUtils';
import type { OutlineItem } from '@/utils/outlineUtils';

import { useRenderManager } from '@/composables/useRenderManager';
import AnsweredQuestion from './components/AnsweredQuestion.vue';

const VideoUrl = ref();

const wordContent = ref();
const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
  const res = await getQuestionListApi(uniqueCode, chapterId);
  questions.value = res.data;
};

//6.25以前
import { useLearningStore } from '@/stores/learning';
import type { Ref } from 'vue';
import { PrjForm } from '@/types/project';
const learningStore = useLearningStore();
//处理contentlist

import type { VideoCaptionListObj, QuestionData } from '@/types/learning';
import type { exerciseItem } from '@/types/exercise';

import {
  ref,
  computed,
  provide,
  onMounted,
  nextTick,
  watch,
  onBeforeUnmount,
  defineExpose
} from 'vue';
import { InfoFilled, Search } from '@element-plus/icons-vue';
//import ExerciseWrapper from './ExerciseWrapper.vue';
import { handleLineWord } from '@/utils/lineWord';
import questionIcon from '@/assets/svgs/question.svg';
import QuestionDrawer from '../../components/QuestionDrawer.vue';
import {
  deleteQuestionApi,
  getManuProjectSectionApi,
  getPrjSectionApi,
  getQuestionListApi,
  getQuestionDetailApi,
  getPrjDetailApi
} from '@/apis/learning';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { saveQuestionApi } from '@/apis/learning';
import AnswerDrawerSidebar from '@/components/AnswerDrawerSidebar.vue';
import { ElMessage } from 'element-plus';
import { getQuestionquestionListApi, saveExerciseQuestionApi } from '@/apis/exercise';
import { renderExerciseHtml } from '@/utils/renderExerciseHtml';
// 引入划词系统相关 composables
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import { useDrawerManager } from '@/composables/useDrawerManager';
//import ReadContent from '@/views/klgdetail/components/readContent.vue';
import {
  matchQuestionsWithTimeline,
  getDisplayTime,
  getSeconds,
  type QuestionTimelineMatch
} from '@/utils/Video';

// 拿到章节信息
const videoCaptionList = ref<VideoCaptionListObj[][]>([]);
const exerciseInfo = ref<exerciseItem>();

// 将videoCaptionList转换为HTML字符串格式 - 用于Render
const convertVideoCaptionListToTextArray = () => {
  if (!videoCaptionList.value || !Array.isArray(videoCaptionList.value)) {
    return [];
  }

  const htmlArray: string[] = [];
  videoCaptionList.value.forEach((paragraphList, paragraphIndex) => {
    if (Array.isArray(paragraphList)) {
      // 构建包含时间戳信息的HTML结构
      let paragraphHtml = `<div class="paragraph-wrapper" data-paragraph="${paragraphIndex}">`;
      paragraphHtml += `<div class="text">`;

      // 为每个句子添加oid、data-start、data-end属性
      paragraphList.forEach((item) => {
        paragraphHtml += `<span oid="${item.oid}" data-start="${item.startTime}" data-end="${item.endTime}">${item.caption}</span>`;
      });

      paragraphHtml += `</div></div>`;

      if (paragraphHtml.trim()) {
        htmlArray.push(paragraphHtml);
      }
    }
  });
  console.log('convertVideoCaptionListToTextArray', htmlArray);
  return htmlArray;
};

const props = defineProps<{
  prjInfo?: any;
  exerciseInfo?: any;
  transmitSpuId?: string;
  transmitChapterId?: string;
  transmitUniqueCode?: string;
  type?: number;
}>();
const dialogVisible = ref(true);
const emit = defineEmits(['close', 'submit']);

const question = ref('');
const search = ref('');

const questions = ref<any[]>([]);

// 渲染后的内容 - 用于传递给模板
const renderedContent = ref('');

// 计算属性：统一处理显示内容，包含时间信息
const displayContent = computed(() => {
  if (renderedContent.value && Array.isArray(renderedContent.value)) {
    console.log('✅ 使用渲染后的内容');
    return renderedContent.value.map((htmlContent: any, idx: number) => {
      // 从原始数据中获取对应的时间信息
      const originalParagraph = videoCaptionList.value?.[idx];
      return {
        htmlContent,
        timeInfo: originalParagraph && originalParagraph.length > 0 ? originalParagraph[0] : null,
        isRendered: true
      };
    });
  }
});
console.log('displayContent', displayContent);
// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  currentSelectedText,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon({
  onIconClick: (selectedText: string) => {
    // 直接控制本地状态
    handleShowQuestionDrawer(
      new CustomEvent('showQuestionDrawer', {
        detail: { selectedText }
      })
    );
  }
});

// 使用抽屉管理 composable
const {
  showQuestionDrawer,
  showAnswerDrawer,
  selectedText,
  currentQuestionData,
  componentZIndex,
  projectAuthor,
  updateComponentLayer,
  handleShowQuestionDrawer,
  handleCloseQuestionDrawer,
  handleCloseAnswerDrawer,
  handleShowQuestionFromFloating,
  initializeEventListeners,
  cleanupEventListeners
} = useDrawerManager();

function handleClose() {
  dialogVisible.value = false;
  emit('close');
}
function handleClosed() {
  question.value = '';
}
function submit() {
  emit('submit', question.value);
  handleClose();
}

// 你可以自定义一个 handleWord 方法
function handleWord(e: Event) {
  // 这里可以做你想要的划词弹窗逻辑
  const html = handleLineWord();
  if (html) {
    alert('你划了：' + html.replace(/<[^>]+>/g, ''));
  }
}
provide('handleWord', handleWord);

let type = 1;

//匹配文稿和时间轴相关
// 问题与视频文稿匹配结果
const questionTimelineMatches = ref<QuestionTimelineMatch[]>([]);

// 跳转到文稿对应部分
function handleOutlineJump(id: string) {
  console.log('到这里了应该能滑动了吧');
  const contentArea = document.querySelector('.content-area') as HTMLElement;
  scrollToOutlineItem(id, contentArea);
}

const outline = ref<OutlineItem[]>([]);

const currentOid = ref<number | null>(null);
const currentTime = ref<number | null>(null);

const activeIdx = ref<number | null>(null);

const scrollElement = ref<HTMLElement>();

function hoverPList(idx: number, curoid: number | null) {
  nextTick(() => {
    scrollElement.value = document.querySelector(`[oid="${curoid}"]`) as HTMLElement;
  });
}

watch(
  () => scrollElement.value,
  (newVal, oldVal) => {
    if (newVal) {
      newVal.style.backgroundColor = '#a6d0ea';
      newVal.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    if (oldVal) {
      oldVal.style.backgroundColor = '';
    }
  }
);

defineExpose({
  setCurrentTime(time: number) {
    console.log('到这里了');
    currentTime.value = time;
    highlightByCurrentTime(time);
  }
});

function highlightByCurrentTime(time: number) {
  let curoid: number | null = null;
  let idx: number | null = null;
  if (Array.isArray(videoCaptionList.value)) {
    videoCaptionList.value.forEach((paragraphList: any[], pIdx: number) => {
      paragraphList.forEach((item: any) => {
        if (getSeconds(item.startTime) <= time && getSeconds(item.endTime) >= time) {
          curoid = item.oid;
          idx = pIdx;
        }
      });
    });
  }
  if (idx !== null && idx !== undefined) {
    console.log('到那里了');
    console.log(idx);
    console.log(curoid);
    hoverPList(idx, curoid);
  }
}

const nowContentId = ref<string>('');

const handleQuestionItemClick = async (question: any) => {
  const res = await getQuestionDetailApi(String(question.questionId));
  if (res.data && res.data.length > 0) {
    currentQuestionData.value = res.data[0];
    showAnswerDrawer.value = true;
    updateComponentLayer('answer');
  }
};
// 使用Render管理器 - 与 useQuestionIcon 和 useDrawerManager 配合
const { handleSearch, addQuestion, removeQuestion, initializeRender } = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => {
    if (props.type === 1) {
      return wordContent.value;
    } else if (props.type === 2) {
      return convertVideoCaptionListToTextArray();
    } else if (props.type === 3) {
      //return renderExerciseHtml(props.exerciseInfo || {}, [], props.type || 3);
      return handleExercise(props.exerciseInfo);
    }
    return '';
  },
  questionList: questions,
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      let textContent = data.content;

      if (textContent && textContent.trim()) {
        // 使用 useQuestionIcon 的 showQuestionIcon 方法
        showQuestionIcon({ content: textContent, selection: data.selection });
        console.log('data', data);
      } else {
        console.log('❌ 选中文本为空或无效');
      }
    }
  },
  onClick: (data: any) => {
    console.log('点击事件:', data.target);
    // 使用 useDrawerManager 的事件处理
    const event = new CustomEvent('showAnswerDrawer', {
      detail: { questionData: data.target }
    });
    window.dispatchEvent(event);
  },
  onFinish: (arg: any) => {
    const content = arg.content;
    console.log('finish', content);
    renderedContent.value = content;
    console.log('renderedContent', renderedContent.value);
  }
  // enableDebugLog: true
});
// 添加问题的处理函数
const addQuestionFn = async (params: {
  associatedWords: string;
  keyword: string;
  questionType: QuestionType;
  questionDescription: string;
  uniqueCode: string;
  chapterId: string;
  contentId?: string;
}) => {
  if (props.type === 1) {
    // type === 1: 文稿类项目

    // 设置必要的参数
    params.chapterId = props.transmitChapterId!;
    params.uniqueCode = props.transmitUniqueCode!;
    params.contentId = nowContentId.value;

    const res = await saveQuestionApi(params);
    const data = res.data.data;

    if (res.success) {
      ElMessage.success('保存问题成功');
      console.log('✅ 文稿类问题保存成功:', data);

      // 使用Render管理器添加问题高亮
      addQuestion(data.associatedWords, data.questionId);

      // 添加到本地问题列表
      const rawQuestionList = toRaw(questions.value);
      if (rawQuestionList) {
        rawQuestionList.push(data);
        triggerRef(questions);
      }

      console.log('📝 文稿类问题列表已更新, 总数:', questions.value?.length);
    } else {
      const errorMsg = res.message || '保存问题失败，请重试';
      console.log('❌ 文稿类问题保存失败:', errorMsg);
      ElMessage.error(errorMsg);
    }
  } else if (props.type === 2) {
    // type === 2: 视频字幕类文稿
    console.log('📺 处理视频字幕类问题添加...');

    // 设置必要的参数
    params.chapterId = props.transmitChapterId!;
    params.uniqueCode = props.transmitUniqueCode!;

    const res = await saveQuestionApi(params);
    const data = res.data.data;

    if (res.success) {
      ElMessage.success('保存问题成功');
      console.log('✅ 视频字幕类问题保存成功:', data);

      // 使用Render管理器添加问题高亮
      addQuestion(data.associatedWords, data.questionId);

      // 添加到本地问题列表
      const rawQuestionList = toRaw(questions.value);
      if (rawQuestionList) {
        rawQuestionList.push(data);
        triggerRef(questions);
      }

      // 重新匹配问题与视频文稿时间轴
      questionTimelineMatches.value = matchQuestionsWithTimeline(
        questions.value || [],
        videoCaptionList.value
      );

      console.log('🎯 视频问题时间轴匹配已更新, 匹配数:', questionTimelineMatches.value?.length);
    } else {
      const errorMsg = res.message || '保存问题失败，请重试';
      console.log('❌ 视频字幕类问题保存失败:', errorMsg);
      ElMessage.error(errorMsg);
    }
  } else if (props.type === 3) {
    // type === 3: 普通题目
    console.log('📋 处理普通题目问题添加...');

    const newparams = {
      exerciseId: exerciseInfo.value!.exerciseId,
      keyword: params.keyword,
      questionType: params.questionType,
      associatedWords: params.associatedWords
    };

    const res = await saveExerciseQuestionApi(newparams);
    const data = res.data;

    if (res.success) {
      ElMessage.success('保存问题成功');
      console.log('✅ 普通题目问题保存成功:', data);

      // 使用Render管理器添加问题高亮
      addQuestion(data.associatedWords, data.questionId);

      // 更新本地 questions 列表，添加缺失的字段
      const newQuestion = {
        ...data,
        questionNecessity: '1' // 默认为必须问题
      } as QuestionData;
      questions.value.push(newQuestion);

      // 匹配问题与视频文稿时间轴（如果需要）
      questionTimelineMatches.value = matchQuestionsWithTimeline(
        questions.value || [],
        videoCaptionList.value
      );
    } else {
      const errorMsg = res.message || '保存问题失败，请重试';
      console.log('❌ 普通题目问题保存失败:', errorMsg);
      ElMessage.error(errorMsg);
    }
  }
};

const removeQuestionFn = async (eventData: any) => {
  // 兼容不同的参数格式
  let questionId: string;
  let associatedWords: any;
  [questionId, associatedWords] = eventData;

  const res = await deleteQuestionApi(questionId);
  if (res.success) {
    ElMessage.success('删除成功');
    const rawQuestionList = toRaw(questions.value);
    const questionIndex = rawQuestionList?.findIndex(
      (item) => item.questionId === Number(questionId)
    );

    // 使用Render管理器处理问题删除
    if (questionIndex !== undefined && questionIndex >= 0) {
      // 优先使用传入的 associatedWords，否则从问题列表中获取
      const wordsToRemove = associatedWords || rawQuestionList![questionIndex].associatedWords;
      if (wordsToRemove) {
        removeQuestion(wordsToRemove, Number(questionId));
      }
    }

    if (questionIndex !== undefined && questionIndex >= 0) {
      rawQuestionList?.splice(questionIndex, 1);
      triggerRef(questions);
    }

    // 重新匹配问题与视频文稿时间轴
    questionTimelineMatches.value = matchQuestionsWithTimeline(
      questions.value || [],
      videoCaptionList.value
    );
  } else {
    ElMessage.error('删除失败');
  }
};

// 假设你的视频播放器实例通过 inject 或 props 传递
// 这里用 inject 示例，如果你用 props 传递请自行调整
import { inject } from 'vue';
import { handleExercise } from '@/utils/lineWordFunction';
const playerRef = inject('playerRef', null); // 父组件需 provide

const jumpToQuestionTime = (startTime: string) => {
  if (startTime) {
    // 由于 AskQuestionDialog 是对话框组件，没有直接的播放器引用
    if (
      startTime &&
      playerRef &&
      playerRef.value &&
      typeof playerRef.value.setTime === 'function'
    ) {
      playerRef.value.setTime(getSeconds(startTime));
    }
  }
};
onMounted(async () => {
  if (props.type === 1) {
    //普通文本
    const spuId = props.transmitSpuId!;
    const chapterId = props.transmitChapterId!;
    const uniqueCode = props.transmitUniqueCode!;

    const res = await getPrjDetailApi(spuId, chapterId);
    wordContent.value = res.data.chapterList[0].wordContent;
    const contentId = res.data.chapterList[0].contentId;
    localStorage.setItem('contentId', contentId);
    nowContentId.value = contentId;
    await handleQuestionList(uniqueCode, chapterId);

    //保存信息
    learningStore.setInfo({
      chapterId: chapterId,
      uniqueCode: uniqueCode,
      contentId: contentId, // 如果需要contentId，请从API响应中获取
      prjForm: 'normal' // 默认为normal，如果是草稿模式需要设置为'draft'
    });

    //大纲
    const outlineContent = addIdsToHeadings(wordContent.value);
    outline.value = extractOutline(outlineContent);
  } else if (props.type === 2) {
    //视频字幕类文稿

    const spuId = props.transmitSpuId!;
    const chapterId = props.transmitChapterId!;
    const uniqueCode = props.transmitUniqueCode!;

    const res = await getPrjSectionApi(spuId, chapterId);

    videoCaptionList.value = res.data.videoCaptionList;
    VideoUrl.value = res.data.videoUrl;

    //获取问题列表
    const questionRes = await getQuestionListApi(uniqueCode, chapterId);
    questions.value = questionRes.data;

    // 匹配问题与视频文稿时间轴
    questionTimelineMatches.value = matchQuestionsWithTimeline(
      questions.value || [],
      videoCaptionList.value
    );

    //保存信息
    learningStore.setInfo({
      chapterId: chapterId,
      uniqueCode: uniqueCode,
      contentId: '', // 如果需要contentId，请从API响应中获取
      prjForm: 'normal' // 默认为normal，如果是草稿模式需要设置为'draft'
    });
  } else if (props.type === 3) {
    //普通题目
    exerciseInfo.value = props.exerciseInfo;

    //获取问题列表
    const questionRes = await getQuestionquestionListApi(exerciseInfo.value!.exerciseId);
    const list = questionRes.data.list || [];
    // 并发获取详情
    const detailPromises = list.map((item) => getQuestionDetailApi(String(item.questionId)));
    const detailResults = await Promise.all(detailPromises);
    // getQuestionDetailApi 返回数组，取第一个元素，并合并 associatedWords
    questions.value = detailResults.map((res: any, idx: number) => ({
      ...(Array.isArray(res.data) && res.data[0] ? res.data[0] : {}),
      associatedWords: list[idx].associatedWords
    }));

    console.log('type=3', questions.value);
  }
  await nextTick(); // 等待 DOM 渲染
  initializeRender();
  // 提供saveType给QuestionDrawer
  provide('saveType', ref(0)); // 0表示项目提交，1表示测评提交
});

onMounted(() => {
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);

  // 添加文档点击事件监听（用于问号图标）
  document.addEventListener('click', handleDocumentClick);

  // 初始化抽屉管理事件监听
  initializeEventListeners();
});
onBeforeUnmount(() => {
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);

  // 清理文档点击事件监听
  document.removeEventListener('click', handleDocumentClick);

  // 清理抽屉管理事件监听
  cleanupEventListeners();
});
</script>

<style src="./css/AskQuestionDialog.less" scoped></style>
