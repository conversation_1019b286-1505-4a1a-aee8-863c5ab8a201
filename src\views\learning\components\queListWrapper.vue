<template>
  <!-- 这个组件还会用到exam页面 -->
  <!-- {{ questionList }} -->
  <!-- {{ wordStore }} -->
  <!-- {{ learningStore.chapterList[validIndex].wordContent}} -->
  <div class="main-wrapper">
    <div
      v-show="!isBig || (isBig && bigScreen == 0)"
      class="left-video-wrapper"
      :class="{ videoPage: $route.name == 'learning' }"
    >
      <!-- 左侧的title列表 -->
      <div class="left-title-list" v-if="$route.name == 'learning' && prjType == PrjType.case">
        <!-- <span class="title">Title</span> -->
        <!--        <el-icon-->
        <!--          class="sectionMenu"-->
        <!--          color="var(&#45;&#45;color-theme-project)"-->
        <!--          @click="isSectionMenuShow = !isSectionMenuShow"-->
        <!--        >-->
        <!--          <Operation />-->
        <!--        </el-icon>-->
        <template v-if="prjType == PrjType.case">
          <img
            class="sectionMenu"
            v-if="!isSectionMenuShow"
            @click="isSectionMenuShow = !isSectionMenuShow"
            src="@/assets/images/prjlearn/u4448.svg"
          />
          <img
            class="sectionMenu"
            v-else
            @click="isSectionMenuShow = !isSectionMenuShow"
            src="@/assets/images/prjlearn/u4449.svg"
          />
          <span
            :style="isSectionMenuShow ? '' : 'width: 0px; height: 0px; border-weight: 0px'"
            class="sectionMenuContent"
          >
            <!-- <span
              v-show="isSectionMenuShow"
              v-for="(chapter, idx) in chapterList"
              :key="'_' + chapter.chapterId"
              class="section"
              @mousedown.prevent="handleChangeSectionFn(chapter.chapterId)"
              :class="{ active: activeIndex == chapter.chapterId }"
            >
              {{ idx + 1 }}
              <span class="sectionTitle" :title="chapter.chapterName">{{
                chapter.chapterName
              }}</span>
            </span> -->
          </span>
        </template>
      </div>
      <!-- 左侧的问题列表和问题详情 -->
      <div class="flex-r">
        <!-- 折叠按钮 -->
        <div class="wrapper-button" v-if="showCatalog" @click="handleShowCatalog">
          <el-icon style="font-size: 25px"><Expand /></el-icon>
        </div>
        <!-- 目录页 -->
        <div class="catalog flex-c" v-if="!showCatalog">
          <div class="flex-r" style="justify-content: space-between">
            <div class="wrapper-button" @click="handleHideCatalog">
              <el-icon style="font-size: 25px"><Fold /></el-icon>
            </div>
            <div style="margin-right: 10px">
              <el-checkbox
                v-model="isSelf"
                v-if="info.hasPermission === 1 ? true : true"
                @click="updateQuesDetail(0)"
                >只看我的提问</el-checkbox
              >
            </div>
          </div>
          <div class="quesCollapse">
            <div class="demo-collapse">
              <el-collapse @change="handleGetQuesDetail" v-model="chapterId" accordion>
                <!-- {{ chapterId }} -->
                <el-collapse-item
                  :title="introduceInfo?.chapterList[chapterIndex].chapterName"
                  v-for="(chapterItem, chapterIndex) in introduceInfo?.chapterList"
                >
                  {{ chapterItem.chapterId }}
                  <div
                    class="chaptQuesBox"
                    v-for="(quesItem, quesIndex) in quesListInfo"
                    @click="questionDetail(quesItem.questionId)"
                  >
                    <div style="display: flex; flex-direction: row">
                      <div style="display: flex; flex-direction: column" v-show="true">
                        <div
                          style="width: 1px; height: 10px; background-color: #fff; margin-left: 5px"
                          v-show="quesIndex > 0 ? false : true"
                        ></div>
                        <div
                          :class="
                            quesListInfo[quesIndex].learned === true
                              ? 'line-learned'
                              : 'line-unlearned'
                          "
                          v-show="quesIndex > 0 ? true : false"
                        ></div>
                        <div
                          :class="quesItem.learned === true ? 'dot-learned' : 'dot-unlearned'"
                        ></div>
                        <div
                          :class="quesItem.learned === true ? 'line-learned' : 'line-unlearned'"
                          v-show="quesIndex + 1 >= quesListInfo.length ? false : true"
                        ></div>
                      </div>
                      <div
                        style="width: 500px"
                        :class="quesItem.questionId == newSpuId ? 'activeQues' : 'defaultQues'"
                      >
                        <!-- <span v-html="'【'+ item.keyword + '】'" id="p_inline" ></span><span>{{item.questionType}}</span> -->
                        <div v-html="'【' + quesItem.keyword" id="p_inline"></div>
                        111
                        <div class="adjust-position">】{{ quesItem.questionType }}</div>
                        <!-- <div>{{ item.learned }}</div> -->
                      </div>
                    </div>
                    <!-- <div v-for="item in quesListInfo">{{ item }}</div> -->
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
        <!-- 问题详情 -->
        <div class="ques-box" v-for="item in questionAndAnswer">
          <div class="ques-title flex-c">
            <div style="margin-bottom: 10px">
              <span
                style="
                  font-family: var(--title-family);
                  color: #333;
                  font-size: 12px;
                  font-weight: 600;
                "
                >{{ item.userName }}&nbsp&nbsp</span
              >
              <span style="font-family: var(--text-family); color: #333; font-size: 12px"
                >的提问</span
              >
            </div>
            <div style="margin-bottom: 10px; display: flex; align-items: center">
              <span
                style="
                  font-family: var(--title-family);
                  color: #333;
                  font-size: 14px;
                  font-weight: 600;
                  display: flex;
                  align-items: center;
                "
                >[<span v-html="item.keyword"></span>]</span
              >
              <span
                style="
                  font-family: var(--text-family);
                  color: #333;
                  font-size: 14px;
                  padding-right: 10px;
                "
                >{{ item.questionType }}?</span
              >
              <el-button color="#1973CB" :dark="isDark" round>必要</el-button>
              <el-button type="warning" round>公开</el-button>
            </div>
            <div class="flex-r" style="justify-content: space-between; padding: 0 10px">
              <span style="font-family: var(--text-family); color: #666; font-size: 12px">{{
                item.createTime
              }}</span>
              <span style="font-family: var(--text-family); color: #0d2040; font-size: 14px"
                >学习进度：1/2</span
              >
            </div>
          </div>
          <!-- 回答列表 -->
          <div class="ansBox flex-c">
            <template v-for="ansItem in item.answers">
              <div style="margin-top: 10px">
                <span
                  style="
                    font-family: var(--title-family);
                    color: #333;
                    font-size: 12px;
                    font-weight: 600;
                  "
                  >{{ ansItem.userName }}&nbsp;&nbsp;</span
                >
                <span
                  style="
                    font-family: var(--text-family);
                    color: #333;
                    font-size: 12px;
                    border: 1.5px solid #ecc32f;
                    border-radius: 10%;
                    color: #ecc32f;
                  "
                  v-show="ansItem.isAuthor === 0 ? false : true"
                  >作者</span
                >
                <span style="font-family: var(--text-family); color: #333; font-size: 12px"
                  >&nbsp;&nbsp;的回答</span
                >
              </div>
              <div class="flex-r" style="margin: 10px 0">
                <div class="klg-id-def" v-for="klgItem in ansItem.answerKlgs">
                  {{ klgItem.title }}
                </div>
                <!-- <div class="klg-id-pri">知识点2</div> -->
              </div>
              <div class="ans-detail">
                <span class="p_inline" v-html="ansItem.answerExplanation"></span>
              </div>
              <div
                style="
                  font-family: var(--text-family);
                  color: #666;
                  font-size: 12px;
                  margin-bottom: 10px;
                "
              >
                {{ ansItem.createTime }}
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧的内容区 -->
    <div
      v-show="!isBig || (isBig && bigScreen == 1)"
      class="right-content"
      :class="{ videoPage: $route.name == 'learning' }"
    >
      <div class="questionList" v-show="isMap">
        <div style="font-size: 13px">问题列表</div>
        <div
          class="question-item"
          v-for="question in questionList"
          :key="question.questionId"
          @click="clickQuestion(question.questionId)"
        >
          <div class="title-line">
            "
            <span v-html="question.keyword"></span>
            "
            <span v-if="question.questionType != '开放性问题'"> {{ question.questionType }}</span>
            <span v-html="question.questionDescription" v-else></span>
          </div>
          <!-- <div
            @click.stop="handleDeleteQuestion(question.questionId)"
            v-if="question.canDelete"
            style="cursor: pointer; z-index: 100000; margin-right: 20px"
          >
            ×
          </div> -->
        </div>
      </div>
      <div id="draft" @wheel.stop class="content-wrapper">
        <!-- 文稿类 -->
        <!-- {{wordContent}} -->
        <template v-if="wordContent">
          <PrjManuscript
            :wordContent="wordContent"
            :questionList="questionList ?? []"
            @refresh="handleQuestionList2"
            ref="manuScript"
            @search="handleSearch2"
          ></PrjManuscript>
        </template>
        <!-- 视频类 -->
        <template v-if="videoCaptionList">
          <PrjManuscript
            @scrollInTop="handleScroll"
            ref="prjManuscript"
            :big="isBig ? true : false"
            :videoCaptionList="videoCaptionList"
            :questionList="questionList"
            @returnInit="handleReturnInitFn"
            @refresh="handleQuestionList"
            @delete-question="handleDeleteQuestion"
            :isMap="isMap"
            v-if="ready"
            @wheel="handleVideoWheel"
            @search="handleSearch"
          >
          </PrjManuscript>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getPrjDetailApi, getPrjSectionApi, getManuProjectSectionApi } from '@/apis/learning';
import type { Chapter, VideoCaptionListObj, QuestionData } from '@/types/learning';
import { STATE_FLAG } from '@/types/learning';
import XgPlayer from '@/components/XgPlayer.vue';
import { defineExpose } from 'vue';
import { getQuestionListApi, deleteQuestionApi } from '@/apis/learning';
import PrjManuscript from './PrjManuscript3.vue';
import ksg_window from './ksg_window.vue';
import { PrjType } from '@/types/project';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { useLearningStore } from '@/stores/learning';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { useWordStore } from '@/stores/word';
import { emitter } from '@/utils/emitter';
import { ArrowUpBold, ArrowDownBold, Expand, Fold } from '@element-plus/icons-vue';
import { getQuesDetailApi, getPrjIntroduceApi } from '@/apis/case';
import { getQuestionDetailApi } from '@/apis/learning';

import { userPrjInfoStore } from '@/stores/prjInfo';

//================================ksg-map================================
import { getAreaData, getFocusData, getChapterGraph } from '@/apis/ksgmap';
import KsgMap from 'ksg-map';
import type {
  GlobalConfig,
  OriginalData,
  AreaData,
  PointData,
  FocusData
} from 'ksg-map/dist/types';
import type { CollapseModelValue } from 'element-plus';

const prjInfoStore = userPrjInfoStore();
const { info } = storeToRefs(prjInfoStore);
const wordContent = ref();
const isSelf = ref(false);
const isHideStudied = ref(false);
const activeName = ref(0);
const validIndex = ref(0);
const showInput = ref(true);
const keywords = ref('');
const router = useRouter();
const route = useRoute();
const learningStore = useLearningStore();
const drawerControllerStore = useDrawerControllerStore();
const wordStore = useWordStore();
const { mode } = storeToRefs(drawerControllerStore);
const questionAndAnswer = ref();
const introduceInfo = ref<{
  chapterList: [{ chapterId: string; chapterName: string }];
  coverPic: string;
  description: string;
  expirationTime: string;
  hasPermission: number;
  klgCount: number;
  learnedKlg: number;
  learnedQuestion: number;
  learnedTime: number;
  masterKlg: number;
  price: number;
  prjForm: string;
  purpose: string;
  questionCount: number;
  startTime: string;
  studyInstructions: string;
  studyTime: number;
  title: string;
}>();
const quesListInfo = ref<
  Array<{
    questionId: string;
    keyword: string;
    questionType: string;
    learned: boolean;
    createTime: string;
  }>
>();

const questionDetail = async (questionId: string) => {
  // console.log('我被点击了')
  newSpuId.value = questionId;
  // console.log('我是新问题id'+newSpuId.value)
  router.replace({
    query: {
      ...route.query,
      questionId: newSpuId.value
    }
  });
  // dialogDetailVisible.value = true;
  // console.log('当前问题详情', currentQuestion.value.associatedWords);
  const res = await getQuestionDetailApi(questionId);
  questionAndAnswer.value = res.data;
  questionList.value = res.data;
  if (wordContent.value) {
    console.log('====debug,wordContent', wordContent);
    handleChangeSectionFn2(route.query.chapterId);
  }
  if (videoCaptionList.value) {
    handleChangeSectionFn(route.query.chapterId);
  }

  // const res2 = await getPrjDetailApi(spuId, route.query.chapterId);
  // learningStore.setInfo(res2.data);
  // learningStore.chapterId = learningStore.chapterList[validIndex.value].chapterId;
  const res3 = await getPrjDetailApi(spuId, route.query.chapterId);
  learningStore.setInfo(res3.data);
  learningStore.chapterId = learningStore.chapterList[0].chapterId;
  // console.log("====res2",res2)
  // let idx = learningStore.validIndex;
  // console.log("====validIndex",validIndex)
  // videoCaptionList.value = (res2.data.chapterList[validIndex.value].wordContent ? res2.data.chapterList[validIndex.value].wordContent : res2.data.chapterList[validIndex.value].videoCaptionList);
  console.log('===validIndex', validIndex);
  console.log('===learningStore', learningStore.chapterList[validIndex.value]);

  // videoCaptionList.value = learningStore.chapterList[validIndex.value]?.videoCaptionList;

  // videoCaptionList.value = (learningStore.chapterList[validIndex.value]?.videoCaptionList ? learningStore.chapterList[validIndex.value]?.videoCaptionList:learningStore.chapterList[validIndex.value]?.wordContent);
  // console.log("====videoCaptionList",videoCaptionList)
  // videoCaptionList.value = res2.data.chapterList[validIndex.value].videoCaptionList
  // console.log('aa',questionList.value)
  // console.log('aa'+JSON.stringify(questionList,null,2))
};

const updateQuesDetail = (change: number) => {
  console.log('xxx');
  let isSelfFlag = isSelf.value;
  let isStudyFlag = isHideStudied.value;
  if (change === 0) {
    isSelfFlag = !isSelfFlag;
  } else if (change === 1) {
    isStudyFlag = !isStudyFlag;
  }
  const params = {
    spuId: spuId,
    // chapterId:302,
    chapterId: info.value.chapterList[nowWrapper.value].chapterId,
    keyword: keywords.value,
    self: isSelfFlag,
    hideLearned: isStudyFlag
  };

  getQuesDetailApi(params).then((res) => {
    info.value.quesList = res.data;
    // info.value.quesList[2].learned = true

    if (info.value.hasPermission === 1) {
      showInput.value = true;
    }
    // info.value.chapterList
  });
};

const handleGetQuesDetail = (val: CollapseModelValue) => {
  // console.log('切换章节')
  validIndex.value = val as number;
  // console.log('我是info'+JSON.stringify(info.value,null,2))
  // console.log('我是query信息'+spuId,questionId,chapterId)
  // console.log("===val:",val)
  // console.log("===introduceInfo:",introduceInfo.value?.chapterList[0])
  console.log(
    '===debug,introduceInfo.value?.chapterList[val].chapterId',
    introduceInfo.value?.chapterList[val as number]
  );
  const params = {
    spuId: spuId,
    // spuId: spuId,
    // chapterId:chapterId,
    chapterId: introduceInfo.value?.chapterList[val as number].chapterId,
    keyword: keywords.value,
    self: isSelf.value,
    hideLearned: isHideStudied.value
  };
  router.replace({
    query: {
      ...route.query,
      chapterId: introduceInfo.value?.chapterList[val as number].chapterId
    }
  });
  getQuesDetailApi(params).then((res) => {
    // console.log('hhh'+res.data)
    // info.value.quesList = res.data
    quesListInfo.value = res.data;
    // info.value.quesList[2].learned = true
    if (info.value.hasPermission === 1) {
      showInput.value = true;
    }
    // info.value.chapterList
  });
};

/**
 * 显示视频功能
 */
const isBig = ref(false); // 大屏小屏
const bigScreen = ref(0); // 0：大屏视频|1：大屏字幕
const isSectionMenuShow = ref(false); //控制多节菜单的显示
const player = ref<InstanceType<typeof XgPlayer> | null>(null);
const prjManuscript = ref<InstanceType<typeof PrjManuscript>>();

// if案例项目，小节号（if从开始学习|继续学习进来的then最新小节，else自选的节号）
const initChapterId = route.query.chapterId as string;
const spuId = route.query.spuId as string;
const newSpuId = ref('');

const chapterId = route.query.chapterId as string;
const questionId = route.query.questionId as string;
const activeChapter = ref('');
const prjType = inject('prjType') as Ref; // 1讲解 2案例 3测评
// 项目的prjId
const prjId = ref<string>('');
const uniqueCode = ref<string>('');
// 项目的章节id
const curChapterId = ref();
// 项目详情
const projectDetailData = ref<Chapter>();
// 章节相关的数据
const chapterList = ref<Chapter[]>();
const activeIndex = ref();

// 拿到章节信息
const videoCaptionList = ref<[VideoCaptionListObj[]]>();
// console.log("====videoCaptionList",videoCaptionList)
const questionList = shallowRef<QuestionData[]>();
// console.log("===questionList",questionList)
const playerTime = ref<number>(0);
const { initUserBehaviour, handleChapterChange } = userBehaviour(
  PrjForm.video,
  activeIndex,
  curChapterId,
  playerTime
);
//控制目录页的显示和隐藏
const showCatalog = ref<boolean>(true);
const nowWrapper = ref();

// 提供isBig数据
provide(
  'isBig',
  computed(() => toValue(isBig))
);
// const isBig = inject('isBig') as Ref;
const isMap = ref(false); // 是否展示地图
// 提供isMap数据
provide(
  'isMap',
  computed(() => toValue(isMap.value))
);
// const emits = defineEmits('closeMap');
const toggleDisplay = () => {
  if (!isBig.value) {
    isMap.value = false;
    isBig.value = true;
    //
    // prjManuscript.value.changeStateFn(STATE_FLAG.init);
  } else {
    isMap.value = true;
    isBig.value = false;
  }
};
const switchScreen = (direction) => {
  if (bigScreen.value == direction) {
    return;
  }
  bigScreen.value = direction;
  if (direction == 0) {
    player.value?.exitPIP();
  } else {
    player.value?.requestPIP();
  }
};

const handleHideCatalog = () => {
  document.getElementsByClassName('left-video-wrapper')[0].style.width = 'calc(61.8vw - 10px)';
  document.getElementsByClassName('content-wrapper')[0].style.width = '100%';
  document.getElementsByClassName('ques-box')[0].style.width = '1130px';
  console.log(document.getElementsByClassName('ansBox')[0]);
  document.getElementsByClassName('ansBox')[0].style.width = '1100px';
  // document.getElementsByClassName('ans-detail')[0].style.width = '1080px';
  showCatalog.value = !showCatalog.value;
};
const handleShowCatalog = () => {
  document.getElementsByClassName('left-video-wrapper')[0].style.width = 'calc(61.8vw + 180px)';
  document.getElementsByClassName('content-wrapper')[0].style.width = '460px';
  document.getElementsByClassName('ques-box')[0].style.width = '877px';
  console.log(document.getElementsByClassName('left-video-wrapper')[0]);
  document.getElementsByClassName('ansBox')[0].style.width = '857px';
  // document.getElementsByClassName('ans-detail')[0].style.width = '837px';
  showCatalog.value = !showCatalog.value;
};

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
const target = ref<HTMLElement | null>(null);
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: true,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: false
});
const data = ref<OriginalData>({
  topAreas: [],
  areas: [],
  focuses: [],
  points: []
});
async function fetchAreaData(id: string): Promise<AreaData[]> {
  return await getAreaData(id);
}
async function fetchFocusData(id: string): Promise<FocusData> {
  return await getFocusData(id);
}
onMounted(async () => {
  target.value = document.getElementById('app')!;
  const unwatch = watch(
    () => curChapterId.value,
    async () => {
      if (curChapterId.value) {
        const pointsData = await getChapterGraph(spuId, curChapterId.value);
        await ksgMap.value?.ready;
        data.value.topAreas = [];
        data.value.points = pointsData;

        ksgMap.value?.reloadData();
      }
    }
  );
});
//================================ksg-map================================

const handleScroll = (e) => {
  switchScreen(0);
};
const toggleMap = () => {
  if (!isBig.value) {
    isMap.value = !isMap.value;
  }
};

// 视频播放器实例
// TODO：不优雅，后期优化
const getSeconds = (seconds: string) => {
  // 将 1   61 这种数字或者字符串转为   00:00:01  00:01:01
  if (!seconds) return 0;
  const array = seconds.split(':');
  let res = 0;
  array.forEach((item) => {
    res = res * 60 + parseInt(item, 10);
  });
  return res;
};
/**
 * 文稿和视频联动功能
 */

const clickQuestion = (questionId: string) => {
  // prjManuscript.value.handleQuestionList(questionId);
  drawerControllerStore.questionId = questionId;
};
const worker = new Worker(new URL('@/utils/videoWorker.ts', import.meta.url), {
  type: 'module'
});
//视频类
const handleQuestionList = async (
  uniqueCode: string,
  chapterId: string,
  questionId: string = route.query.questionId as string
) => {
  console.log('debug');
  const res = await getQuestionListApi(uniqueCode, chapterId);
  //判断条件，筛选
  questionList.value = res.data;
  //  const res = await getQuestionDetailApi(questionId);
  // questionAndAnswer.value = res.data.questionAndAnswer
  // questionList.value = res.data.questionAndAnswer
  const worker = new Worker(new URL('@/utils/videoWorker.ts', import.meta.url), {
    type: 'module'
  });
  worker.onmessage = (
    e: MessageEvent<{
      videoStringList: Array<{ list: Array<string>; start: number }>;
      originalVideoStringList: Array<{ list: Array<string>; start: number }>;
      regString: string;
      unitList: Array<{
        tagName: string;
        children: any[];
        index: number;
        qids: number[];
        highlight: boolean;
        stringIndex: number;
      }>;
      uncommonWordMap: Map<string, string>;
    }>
  ) => {
    wordStore.videoStringList = e.data.videoStringList;
    wordStore.originalVideoStringList = e.data.originalVideoStringList;
    wordStore.regString = e.data.regString;
    wordStore.unitList = e.data.unitList;
    wordStore.uncommonWordMap = e.data.uncommonWordMap;
    let index = 0;
    const videoStringList = wordStore.getVideoStringList;
    videoCaptionList.value?.forEach((videoCaption) => {
      videoCaption.forEach((item) => {
        item.caption = videoStringList[index++];
      });
    });
    // 当在worker中打印日志信息时，需要查看obj数据则注释下面代码
    worker.terminate();
  };
  // //TODO
  // questionList.value = questionList.value?.filter((item)=> item.questionId == questionId)
  // console.log('111', questionList.value)
  worker.postMessage({
    questionList: toRaw(questionList.value),
    videoCaptionList: toRaw(videoCaptionList.value)
  });
};

//文本类
const handleQuestionList2 = async (uniqueCode: string, chapterId: string) => {
  const res = await getQuestionListApi(uniqueCode, chapterId);
  questionList.value = res.data;

  const worker = new Worker(new URL('@/utils/draftWorker.ts', import.meta.url), {
    type: 'module'
  });
  worker.onmessage = function (
    e: MessageEvent<{
      draftStringList: string[];
      originalDraftStringList: string[];
      regString: string;
      unitList: Array<{
        tagName: string;
        children: any[];
        index: number;
        qids: number[];
        highlight: boolean;
        stringIndex: number;
      }>;
      uncommonWordMap: Map<string, string>;
    }>
  ) {
    wordStore.draftStringList = e.data.draftStringList;
    wordStore.originalDraftStringList = e.data.originalDraftStringList;
    wordStore.regString = e.data.regString;
    wordStore.unitList = e.data.unitList;
    wordStore.uncommonWordMap = e.data.uncommonWordMap;

    wordContent.value = wordStore.getDraftString;
    // wordContent.value = e.data.draftStringList.join('');
    worker.terminate();
  };
  worker.postMessage({
    questionList: toRaw(questionList.value),
    wordContent: toRaw(wordContent.value)
  });
};
const handleDeleteQuestion = async (questionId: string) => {
  deleteQuestionApi(questionId).then(async (res) => {
    await handleQuestionList(uniqueCode.value, curChapterId.value as string);
    ElMessage.success('删除成功');
    const newvalue = res.data.result;
    // AsyncPrjManuscript = defineAsyncComponent(() => import('./PrjManuscript.vue'));
    // nextTick(()=>{

    // })
    // setTimeout(() => {

    // }, 5000);
    prjManuscript.value!.afterDeleteQuestion(newvalue);
    // wordContent.value='123'
  });
};

// TODO: 后期优化，增加节流
let scrollBarAble = true;
let scrollBarTimer: any;
const handleVideoWheel = () => {
  scrollBarAble = false;
  if (scrollBarTimer) {
    clearTimeout(scrollBarTimer);
  }
  scrollBarTimer = setTimeout(() => {
    scrollBarAble = true;
  }, 2000);
};

provide(
  'prjSectionInfo',
  computed(() => ({
    chapterId: curChapterId.value,
    prjId: prjId.value,
    uniqueCode: uniqueCode.value
  }))
);

// 处理章节变化

//当我从一个章节切到另一个章节时，我需要发送现在章节的end请求和新章节的start请求, 结束现在章节的start请求和新章节的end请求

const handleChangeSectionFn = async (chapterId: string) => {
  console.log('debug,视频类');
  console.log('====changeChapter');
  // const res = await getPrjSectionApi(spuId, chapterId);
  // console.log("====res",res)
  // videoCaptionList.value = res.data.videoCaptionList;
  if (activeIndex.value != chapterId) {
    router.replace({
      query: {
        ...route.query,
        chapterId: chapterId
      }
    });
    handleChapterChange(chapterId);
    activeIndex.value = chapterId;

    const res = await getPrjSectionApi(spuId, chapterId);

    // 拿到了新的video资源
    projectDetailData.value = res.data;
    //   changeVideoFn(prjDetailData.value?.videoUrl as string);
    curChapterId.value = projectDetailData.value?.chapterId;
    videoCaptionList.value = res.data.videoCaptionList; //
    // console.log("=====videoCaptionList,",videoCaptionList.value)
    // console.log("====res.data.videoCaptionList",res.data.videoCaptionList)
    await handleQuestionList(uniqueCode.value, chapterId.toString());
  }
};

// 小屏转大屏的时候操控状态STATE_FLAG
// 切换状态
const handleReturnInitFn = () => {
  prjManuscript.value!.changeStateFn(STATE_FLAG.init);
};

// 异步组件
// TODO
// @ts-ignore
// let AsyncPrjManuscript;
// const showAsync = ref(false);
const handleDocumentClick = (event: MouseEvent) => {
  if (!event.target.closest('.sectionMenu')) {
    // console.log('点击了其他地方old', isSectionMenuShow.value);
    if (isSectionMenuShow.value == true) {
      isSectionMenuShow.value = !isSectionMenuShow.value;
      // console.log('点击了其他地方new', isSectionMenuShow.value);
    }
  }
};
const assessmentId = ref();
const contentId = ref<string | number>();
const ready = ref(false);
onMounted(async () => {
  // console.log('父组件VideoContent onMounted');
  // // 拿到项目信息
  // if(!spuId)
  //   spuId=sessionStorage.getItem('spuId') as string;
  // const res = await getPrjDetailApi(spuId, initChapterId); // 两个接口返回的数据格式不一致
  // console.log(res)
  // // TODO:这里应该再接受一个type字段
  // let idx = res.data.validIndex; // 后端会把所有小节都返还，但只有一个对象是有信息的，就是这个index对应的小节
  // prjDetailData.value = res.data.list.chapterList[idx];
  // //   initVideo(); // 初始化视频
  // curChapterId.value = prjDetailData.value?.chapterId;
  // chapterList.value = res.data.list.chapterList; // 拿到章节列表
  // activeIdx.value = res.data.list.chapterList[idx].chapterId;
  // //todo 接口修改需要调整,原来从接口读出来的是prjId
  // //gc说改为prjId
  // prjId.value = res.data.list.prjId; // 拿到项目id
  // videoCaptionList.value = res.data.list.chapterList[idx].videoCaptionList; // 拿到章节信息
  // console.log('111', videoCaptionList.value);
  // questionList.value = res.data.list.chapterList[idx].questionList; // 拿到问题信息

  // // AsyncPrjManuscript = defineAsyncComponent(() => import('./PrjManuscript.vue'));
  // handleQuestionList(activeIdx.value)
  // showAsync.value=true;
  // nextTick(()=>{
  //   console.log(prjManuscript.value)
  //     console.log(prjManuscript.value.test)
  //   })
  //点击其他区域的时候修改isSectionMenuShow的值为flase
  if (!learningStore.written) {
    const res = await getPrjDetailApi(spuId, initChapterId);
    learningStore.setInfo(res.data);
    learningStore.chapterId = learningStore.chapterList[validIndex.value].chapterId;
    router.replace({
      query: {
        ...route.query,
        chapterId: learningStore.chapterId
      }
    });
  }
  // TODO:这里应该再接受一个type字段
  let idx = learningStore.validIndex; // 后端会把所有小节都返还，但只有一个对象是有信息的，就是这个index对应的小节
  projectDetailData.value = learningStore.chapterList[idx];
  //文本类
  // if(wordContent.value){
  assessmentId.value = projectDetailData.value?.assessmentId; // assessmentId
  chapterList.value = learningStore.chapterList; // 拿到章节列表
  // }

  //
  //   initVideo(); // 初始化视频
  curChapterId.value = projectDetailData.value?.chapterId;
  if (videoCaptionList.value) {
    chapterList.value = learningStore.chapterList; // 拿到章节列表
    activeIndex.value = learningStore.chapterList[idx]?.chapterId;
  }

  //文本类
  // if(wordContent.value){
  wordContent.value = learningStore.chapterList[idx].wordContent; //拿到富文本内容
  wordContent.value = wordContent.value;
  // }
  //
  //todo 接口修改需要调整,原来从接口读出来的是prjId
  //gc说改为prjId
  prjId.value = learningStore.prjId; // 拿到项目id
  uniqueCode.value = learningStore.uniqueCode;
  videoCaptionList.value = learningStore.chapterList[idx]?.videoCaptionList; // 拿到章节信息，对于文稿类这里是undefined
  // console.log("===初始化videoCaptionList",videoCaptionList)
  //文本类
  contentId.value = projectDetailData.value?.contentId;
  learningStore.contentId = contentId.value;
  initUserBehaviour(curChapterId.value);

  //
  // videoCaptionList.value = learningStore.chapterList[idx]?.wordContent;
  // prjForm.value == PrjForm.video ? props.videoCaptionList : props.wordContent;

  // console.log("========learningStore,2222222222",learningStore.chapterList[0])
  questionList.value = learningStore.chapterList[idx]?.questionList; // 拿到问题信息
  // console.log("====questionList,222222222",questionList)
  // uniqueCode.value = res.data.uniqueCode;
  // AsyncPrjManuscript = defineAsyncComponent(() => import('./PrjManuscript.vue'));

  await handleQuestionList(uniqueCode.value, curChapterId.value as string);

  // showAsync.value = true;
  initUserBehaviour(curChapterId.value);
  ready.value = true;
  document.addEventListener('click', handleDocumentClick);
  //文本类
  if (wordContent.value) {
    emitter.emit('initHandler', async () => {
      const res = await getManuProjectSectionApi(spuId, curChapterId.value);
      // console.log('处理章节变化', res);
      projectDetailData.value = res.data; // 默认第一个
      curChapterId.value = res.data.chapterId;
      activeIndex.value = res.data.chapterId;
      // questionList.value = prjDetailData.value?.questionList; // 拿到问题信息
      contentId.value = res.data.contentId;
      learningStore.contentId = contentId.value;
      // console.log('questionList', questionList.value);
      wordContent.value = res.data.wordContent; //拿到富文本内容
      await handleQuestionList2(uniqueCode.value, curChapterId.value);
    });
  }
  //视频类
  else {
    emitter.emit('initHandler', async () => {
      const res = await getPrjSectionApi(spuId, curChapterId.value);
      // console.log('处理章节变化', res);
      // 拿到了新的video资源
      projectDetailData.value = res.data;
      //   changeVideoFn(prjDetailData.value?.videoUrl as string);
      curChapterId.value = projectDetailData.value?.chapterId;
      videoCaptionList.value = res.data.videoCaptionList; //
      await handleQuestionList(uniqueCode.value, curChapterId.value);
    });
  }
});
// onBeforeUpdate(() => {
// });
onBeforeUnmount(() => {
  document.removeEventListener('click', handleDocumentClick);
});
defineExpose({ curChapterId });
const getInfo = () => {
  getPrjIntroduceApi({ spuId }).then((res) => {
    // console.log('我是introduce的response' + JSON.stringify(res.data,null,2))
    introduceInfo.value = res.data;
    // console.log('我是introduceInfo的新数据',introduceInfo)
  });
  // questionDetail('0')
};
//视频类
const handleSearch = () => {
  let index = 0;
  if (mode.value) {
    const originalVideoStringList = wordStore.getOriginalVideoStringList;
    videoCaptionList.value?.forEach((videoCaption) => {
      videoCaption.forEach((item) => {
        item.caption = originalVideoStringList[index++];
      });
    });
  } else {
    const videoStringList = wordStore.getVideoStringList;
    videoCaptionList.value?.forEach((videoCaption) => {
      videoCaption.forEach((item) => {
        item.caption = videoStringList[index++];
        // console.log("====wordStore",wordStore)
      });
    });
    console.log('===videoStringList', videoStringList);
    // console.log("========videoCaptionList",videoCaptionList)
  }
};

//文稿类
const handleSearch2 = () => {
  if (mode.value) {
    wordContent.value = wordStore.getOriginalDraftString
      ? wordStore.getOriginalDraftString
      : wordContent.value;
  } else {
    wordContent.value = wordStore.getDraftString ? wordStore.getDraftString : wordContent.value;
  }
};

//文稿类
const handleChangeSectionFn2 = async (chapterId: number) => {
  console.log('debug,文稿类');
  if (activeIndex.value != chapterId) {
    router.replace({
      query: {
        ...route.query,
        chapterId: chapterId
      }
    });
    handleChapterChange(chapterId);
    activeIndex.value = chapterId;
    const res = await getManuProjectSectionApi(spuId, chapterId);
    console.log('[debug]', res.data);
    // console.log('处理章节变化', res);
    projectDetailData.value = res.data; // 默认第一个
    curChapterId.value = res.data.chapterId;
    activeIndex.value = res.data.chapterId;
    // questionList.value = prjDetailData.value?.questionList; // 拿到问题信息
    contentId.value = res.data.contentId;
    learningStore.contentId = contentId.value;
    // console.log('questionList', questionList.value);
    wordContent.value = res.data.wordContent; //拿到富文本内容
    await handleQuestionList2(uniqueCode.value, curChapterId.value); //改了获取问题的接口
  }
  isSectionMenuShow.value = false;
};
getInfo();

// handleGetQuesDetail(validIndex.value);
questionDetail(questionId);
console.log('====validIndex', validIndex);
// handleGetQuesDetail(validIndex.value);
// watch(()=>newSpuId.value,(newvalue)=>{
//   console.log("[debug]",newvalue)
// },{deep:true, immediate:true})
//#endregion
</script>

<style scoped lang="less">
#p_inline {
  display: inline-block;
  max-width: 400px;
  overflow: hidden; /* 隐藏溢出的内容 */
  white-space: nowrap; /* 保持文本在同一行 */
  text-overflow: ellipsis; /* 显示省略号 */
  /* position: absolute; */
}
.adjust-position {
  display: inline-block;
  /* border: 1px solid red; */
  position: absolute;
  /* top: -2px; */
}
.dot-learned {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #1973cb;
}
.dot-unlearned {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #f2f2f2;
}
.line-learned {
  width: 1px;
  height: 10px;
  background-color: #1973cb;
  margin-left: 5px;
}
.line-unlearned {
  width: 1px;
  height: 10px;
  background-color: #f2f2f2;
  margin-left: 5px;
}

quesCollapse {
  margin: 10px;
}
.chaptQuesBox {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-left: 10px;
  // border: 1px solid red;

  :is(span) {
    font-size: 14px;
    color: #333;
    font-family: var(--text-family);
  }
}
// :deep(.el-collapse-item__header) {
//   background-color: #f2f2f2;
//   border-bottom: 1px solid rgb(224, 221, 221);
//   padding-left: 10px;
//   color: #333;
//   font-size: 14px;
//   font-family: var(--title-family);
//   font-weight: 600;
//   height: 26px;
//   margin-bottom: 4px;
// }
:deep(.el-collapse-item__header) {
  background-color: #f2f2f2;
  border-bottom: 1px solid rgb(224, 221, 221);
  padding-left: 10px;
  color: #333;
  font-size: 14px;
  font-family: var(--title-family);
  font-weight: 600;
  height: 26px;
  margin-bottom: 4px;
}

.el-button {
  width: 50px;
  height: 10px;
  :is(span) {
    color: '#f00';
    font-size: 11px;
  }
}
.flex-c {
  display: flex;
  flex-direction: column;
}
.flex-r {
  display: flex;
  flex-direction: row;
}

// 自定义复选框样式
:deep(.el-checkbox) {
  .el-checkbox__input {
    .el-checkbox__inner {
      &:hover {
        border-color: #1973cb;
      }
    }

    &.is-checked {
      .el-checkbox__inner {
        background-color: #1973cb;
        border-color: #1973cb;
      }

      .el-checkbox__label {
        color: #1973cb !important;
      }
    }
  }

  .el-checkbox__label {
    &:hover {
      color: #1973cb;
    }
  }

  // 确保选中时文字颜色为 #1973cb
  &.is-checked {
    .el-checkbox__label {
      color: #1973cb !important;
    }
  }
}


.ques-box {
  width: 877px;
  height: 740px;
  border: 1px solid #ccc;
  border-radius: 1%;
  margin-right: 10px;
}
// .ques-box{
//   width: 1130px;
//   height: 740px;
//   border: 1px solid #ccc;
//   border-radius: 1%;
//   margin-right: 10px;
// }
.wrapper-button {
  padding-right: 10px;
}
.ques-title {
  margin: 10px;
}
.ansBox {
  width: 857px;
  // border: 1px solid red;
  background-color: #f2f2f2;
  margin: 10px 10px;
  padding: 0 10px;
}
// .ansBox{
//   width: 1100px;
//   // border: 1px solid red;
//   background-color: #f2f2f2;
//   margin: 0 10px;
//   padding: 0 10px;
// }
.klg-id-pri {
  width: 118px;
  height: 20px;
  color: #f2f2f2;
  background-color: #1973cb;
  font-size: 14px;
  text-align: center;
  margin-right: 10px;
}
.klg-id-def {
  width: 118px;
  height: 20px;
  color: #1973cb;
  background-color: #fff;
  border: 1px solid #1973cb;
  font-size: 14px;
  text-align: center;
}
.ans-detail {
  width: 837px;
  // border: 1px solid red;
  margin-bottom: 20px;
}
// .ans-detail{
//   width: 1080px;
//   // border: 1px solid red;
//   margin-bottom: 20px;
// }
.sectionMenu {
  //margin: 10px;
  cursor: pointer;
  //transform: scale(2);
}
.sectionMenuContent {
  border: 1px solid rgb(220, 223, 230);
  border-radius: 5px;
  position: absolute;
  transition: all 0.5s ease-in-out;
  left: 90px;
  //width: 284px;
  // height: calc(100vh - 70px - 90px - 63px);
  z-index: 10;
  background-color: white;
  box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    background: var(--color-grey);
  }
  .section {
    height: 41px;
    font-size: 14px;
    padding: 0 10px;
    background-color: white;
    display: flex;
    align-items: center;
    font-family: var(--text-family);
    color: var(--color-black);
    cursor: pointer;
    &:hover {
      background-color: #f2f2f2;
    }
    .sectionTitle {
      margin-left: 5px;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.main-wrapper {
  display: flex;
  //margin-top: 10px;
}

.catalog {
  width: 486px;
  // border: 1px solid red;
  padding-right: 10px;
}

.videoPage {
  .left-video-wrapper {
    min-height: 485px;
    height: calc(100vh - 60px - 70px - 250px);
  }

  .right-content {
    min-height: 485px;
  }
}

.left-video-wrapper {
  margin-left: 44px;
  width: calc(61.8vw + 180px);
  // width: calc(61.8vw - 10px);
  margin-right: 10px;
  display: flex;
  scroll-snap-align: start;

  .left-title-list {
    width: 25px;
    //margin-left: 44px;
    margin-right: 13px;
    // background-color: coral;
    display: flex;
    flex-direction: column;
    align-items: center;

    // .section {
    //   height: 41px;
    //   font-size: 14px;
    //   padding: 0 10px;
    //   background-color: white;
    //   display: flex;
    //   align-items: center;
    //   font-family: var(--text-family);
    //   color: var(--color-black);
    //   cursor: pointer;
    //   &:hover {
    //     background-color: #f2f2f2;
    //   }
    //   .sectionTitle {
    //     margin-left: 5px;
    //     word-break: break-all;
    //     overflow: hidden;
    //     text-overflow: ellipsis;
    //     white-space: nowrap;
    //   }
    // }

    .title {
      font-size: 12px;
      font-weight: 400;
      color: #797979;
    }

    //.title-line {
    //  width: 25px;
    //  height: 24px;
    //  text-align: center;
    //  line-height: 24px;
    //  background-color: #f2f2f2;
    //  border-radius: 5px 0px 0px 5px;
    //  font-size: 13px;
    //  font-weight: 400;
    //  color: var(--color-black);
    //  margin-bottom: 2px;
    //  cursor: pointer;
    //
    //  &.active {
    //    background-color: var(--color-theme-project);
    //    color: rgb(254, 254, 246);
    //    border-radius: 5px 0px 0px 5px;
    //    box-shadow: rgba(0, 85, 121, 0.376) 0px 3px 3px 0px;
    //  }
    //}
  }

  .left-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: calc(61.8vw - 10px);
    display: flex;
    &.big {
      height: calc(100vh - 160px);
    }

    .video-title {
      padding-left: 10px;
      padding-right: 10px;
      position: relative;
      bottom: 0px;
      background-color: #f2f2f2;
      height: 63px;
      font-size: 24px;
      font-weight: 400;
      color: var(--color-black);
      font-family: var(--title-family);
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btn {
        background-color: white;
        border-radius: 14px;
        height: 28px;
        font-size: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 10px;
        border: 1px solid var(--color-theme-project);
        color: var(--color-theme-project);
        font-family: var(--text-family);
        &:hover {
          background-color: var(--color-theme-project);
          color: white;
          cursor: pointer;
        }
      }
      .icon {
        cursor: pointer;
      }

      .icon-wrapper {
        width: 16px;
        height: 12px;
        margin: 0 5px;
        background: url('@/assets/svgs/u2985.svg') no-repeat center/contain;
        cursor: pointer;

        &:hover {
          background-image: url('@/assets/svgs/u4176.svg');
        }
      }
    }

    .video {
      // background-color: rgb(242, 242, 242);
      flex: 1;
      position: relative;

      .coverPic-wrapper {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: var(--color-black);
        background-repeat: no-repeat;
        background-size: cover;
        // background-size: 100% auto;
      }

      .video-btn-wrapper {
        width: 50px;
        height: 50px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
      }

      .expand-logo {
        position: absolute;
        right: 10px;
        // bottom: 10px;
        cursor: pointer;

        &:hover {
          font-weight: 500;
        }
      }
    }

    .video-footer-info {
      height: 40px;
      display: flex;
      align-items: center;
      background-color: #ffffff;
      box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
      border: 1px solid rgb(242, 242, 242);
      padding-left: 10px;
      width: 100%;
      position: relative;

      .video-footer {
        vertical-align: middle;
        transform: translateY(-3px);
      }

      .footer-logo-wrapper {
        width: 90%;
        display: flex;
        align-items: center;
        position: absolute;
      }

      .footer-title {
        font-size: 18px;
        font-weight: 300;
        color: var(--color-black);
        margin-left: 17px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
// init
// .right-content {
//   // min-height: 485px;
//   height: calc(100vh - 90px - 70px); // right-content的offsetTop为150px
//   width: 42%;
//   // overflow: hidden;
//   scroll-snap-align: start;

//   .right-map {
//     background-color: var(--color-second);
//     width: 100%;
//     height: 100%;
//     position: relative;

//     .close-icon {
//       position: absolute;
//       right: 10px;
//       top: 10px;
//       cursor: pointer;
//     }
//   }

//   .content-wrapper {
//     // background-color: aqua;
//     background-color: #f2f2f2;
//     width: 100%;
//     height: 100%;
//   }
// }
.right-content {
  // min-height: 485px;
  height: calc(100vh - 60px - 70px - 66.5px); // right-content的offsetTop为150px
  width: 35%;
  // overflow: hidden;
  scroll-snap-align: start;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .right-map {
    // background-color: var(--color-second);
    width: 100%;
    height: 58%;
    position: relative;

    .close-icon {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
    }
  }
  .questionList {
    width: 100%;
    height: 60%;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 2%;
    overflow: auto;
    .question-item {
      font-size: 13px;
      width: 100%;
      min-height: 50px;
      background-color: white;
      border-radius: 4px;
      padding-left: 6px;
      margin-top: 8px;
      box-sizing: border-box;
      padding: 5px;
      display: flex;
      justify-content: space-between;
      &:hover {
        cursor: pointer;
      }
      .title-line {
        display: flex;
        align-items: center;
      }
    }
  }

  .content-wrapper {
    background-color: #f2f2f2;
    width: 460px;
    // width: 100%;
    // height: 100%;
  }
}
.main-wrapper.big {
  display: block;
  .left-video-wrapper {
    width: calc(100% - 77px - 77px + 38px);
    //min-height: 100vh;
  }

  .right-content {
    box-sizing: border-box;
    //height: 100%;
    width: calc(100% - 60px);
    max-width: 1380px;
    // height: 800px;
    //min-height: 100vh;
    margin: 0 auto;
    //height: 100%;

    &:deep(.text-wrapper) {
      height: 800px !important; // 大屏的高度
    }
  }
}
.switchFloor {
  position: absolute;
  right: 10px;
  top: calc(50% + 30px);
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .btn {
    cursor: pointer;
    background-color: #dcdfe6;
    height: 30px;
    width: 30px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--color-deep);
    &:hover,
    &.light {
      background-color: var(--color-theme-project);
      color: white;
    }
    //&.light {
    //  background-color: var(--color-theme-project);
    //  color: white;
    //}
  }
}
.activeQues {
  background-color: #f2f2f2;
}
</style>
<style>
#p_inline > p {
  display: inline;
}
.p_inline > p {
  display: inline;
}
</style>
