// export async function getKsgMapApi(current: number, limit: number, klgCode: String) {
//   const result = await fetch('klg/graph', {
//     method: 'post',
//     headers: {
//       'Content-Type': 'application/json'
//     },
//     body: JSON.stringify({
//       klgCodes: [klgCode],
//       current,
//       limit
//     })
//   });
//   return result.json();
// }
import { http } from '@/apis';
import axios from 'axios';
import { method } from 'lodash-es';
export function getKsgMapApi(klgCode: String, current: number, limit: number) {
  return http.request({
    method: 'post',
    url: '/klg/knowledge/graph',
    data: {
      klgCodes: [klgCode],
      current,
      limit
    }
  });
}
