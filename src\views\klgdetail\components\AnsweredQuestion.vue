<template>
  <div class="qt-detail-answered" v-if="ready">
    <div class="main-content">
      <div class="title-wrapper">
        <div class="title">
          <span class="select_to_ask_time" style="font-weight: 700; font-size: 16px">{{
            questionTypeText
          }}</span>
        </div>
      </div>
      <div><el-divider></el-divider></div>
      <div class="content">
        <div class="content-question" v-html="stemContent"></div>
        <div v-if="isSelectionExercise">
          <ul>
            <li v-for="option in options" :key="option.contentid">
              <span
                class="select_to_ask_time"
                style="font-weight: 700; margin-right: 4px; font-size: 14px"
                >{{ option.contentid }}.</span
              >
              <span v-html="option.content"></span>
            </li>
          </ul>
        </div>
      </div>
      <div class="answer-wrap">
        <div class="answer">
          <div class="detail">
            <div class="choice">
              <div>
                <span
                  class="select_to_ask_time"
                  style="font-weight: 700; margin-right: 8px; font-size: 14px"
                  >答案：</span
                ><span v-html="displayAnswer"></span>
              </div>
            </div>
            <div class="description">
              <span
                class="select_to_ask_time"
                style="font-weight: 700; margin-right: 8px; font-size: 14px"
                >解析：</span
              ><span v-html="explanationContent"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { ExerciseType } from '@/types/exercise';

interface Props {
  exerciseInfo: any;
  contentArray?: string[];
}

const props = defineProps<Props>();

// 检查数据是否准备就绪
const ready = computed(() => {
  // 确保exerciseInfo存在
  if (!props.exerciseInfo) return false;

  // 如果有contentArray，确保它不是空数组
  if (props.contentArray) {
    if (Array.isArray(props.contentArray)) {
      return props.contentArray.length > 0;
    }
  }

  // 如果没有contentArray，检查exerciseInfo.stem
  if (!props.exerciseInfo.stem || props.exerciseInfo.stem.trim().length === 0) {
    return false;
  }

  // 对于选择题，确保options已经计算完成
  if (props.exerciseInfo.type === 1 || props.exerciseInfo.type === 2) {
    return options.value.length > 0;
  }

  return true;
});

// 从contentArray解析内容
const stemContent = computed(() => {
  if (props.contentArray && props.contentArray.length > 0) {
    return props.contentArray.find((item) => item.includes('etype="stem"')) || '';
  }
  return props.exerciseInfo?.stem || '';
});

const options = computed(() => {
  if (props.contentArray && props.contentArray.length > 0) {
    const optionItems: Array<{ contentid: string; content: string }> = [];
    props.contentArray.forEach((item: string) => {
      if (item.includes('etype="content"')) {
        const contentidMatch = item.match(/contentid="([^"]+)"/);
        const contentid = contentidMatch ? contentidMatch[1] : '';
        optionItems.push({
          contentid,
          content: item
        });
      }
    });
    return optionItems;
  }
  return [];
});

const answerContent = computed(() => {
  if (props.contentArray && props.contentArray.length > 0) {
    return props.contentArray.find((item) => item.includes('etype="answer"')) || '';
  }
  return props.exerciseInfo?.answer || '';
});

const explanationContent = computed(() => {
  if (props.contentArray && props.contentArray.length > 0) {
    return props.contentArray.find((item) => item.includes('etype="explanation"')) || '';
  }
  return props.exerciseInfo?.explanation || '';
});

const isSelectionExercise = computed(() => {
  return (
    props.exerciseInfo?.type === ExerciseType.single ||
    props.exerciseInfo?.type === ExerciseType.multi
  );
});

const questionTypeText = computed(() => {
  switch (props.exerciseInfo?.type) {
    case 1:
      return '单选题';
    case 2:
      return '多选题';
    case 3:
      return '填空题';
    case 4:
      return '判断题';
    default:
      return '全部题型';
  }
});

const displayAnswer = computed(() => {
  if (isSelectionExercise.value) {
    try {
      // 从contentArray中解析答案
      if (props.contentArray && props.contentArray.length > 0) {
        const answerMatch = answerContent.value.match(/\["([^"]+)"\]/);
        if (answerMatch) {
          return answerMatch[1];
        }
        // 处理多选题
        const multiAnswerMatch = answerContent.value.match(/\[([^\]]+)\]/);
        if (multiAnswerMatch) {
          return multiAnswerMatch[1]
            .split(',')
            .map((item: string) => item.replace(/"/g, '').trim())
            .join(', ');
        }
      }
      // 回退到原始数据
      const arr = JSON.parse(props.exerciseInfo.answer);
      if (Array.isArray(arr)) {
        return arr.join(', ');
      }
      return props.exerciseInfo.answer;
    } catch {
      return props.exerciseInfo.answer;
    }
  }
  return props.exerciseInfo?.answer || '';
});

// 监听contentArray的变化
watch(
  () => props.contentArray,
  (newContentArray, oldContentArray) => {
    console.log('AnsweredQuestion: contentArray changed', {
      new: newContentArray?.length,
      old: oldContentArray?.length,
      newContent: newContentArray
    });
  },
  { immediate: true, deep: true }
);

// 监听exerciseInfo的变化
watch(
  () => props.exerciseInfo,
  (newExerciseInfo) => {
    console.log('AnsweredQuestion: exerciseInfo changed', {
      type: newExerciseInfo?.type,
      hasStem: !!newExerciseInfo?.stem,
      hasContent: !!newExerciseInfo?.content,
      hasAnswer: !!newExerciseInfo?.answer,
      hasExplanation: !!newExerciseInfo?.explanation
    });
  },
  { immediate: true, deep: true }
);
</script>

<style lang="less" scoped>
.qt-detail-answered {
  padding: 20px;
  background-color: #ffffff;
  min-height: 450px !important;
  //min-height: auto !important;
  margin: 12px;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(220, 223, 230, 1);
  border-radius: 8px;
  box-shadow: none;
  .main-content {
    margin-top: 15px;
    width: 100%;
    background-color: #ffffff;
    .title-wrapper {
      display: flex;
      .title {
        width: 100%;
        height: 33px;
        line-height: 33px;
        font-size: 16px;
        font-weight: 700;
        padding-left: 10px;
      }
    }
    .content {
      padding-left: 31px;
      padding-right: 31px;
      margin-top: 15px;
      .content-question {
        display: flex;
        flex-wrap: wrap;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 14px;
        font-weight: 700;
      }
      ul {
        margin: 10px 0 0 0;
        padding: 0;
        list-style: none;
        li {
          margin-bottom: 6px;
          font-size: 14px;
          list-style: none;
        }
      }
    }
    .answer-wrap {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-top: 10px;
      .answer {
        width: 92%;
        .detail {
          width: 100%;
          border: 1px solid #f2f2f2;
          border-radius: 4px;
          margin-top: 4px;
          padding-left: 25px;
          padding-right: 16px;
          font-size: 14px;
          .choice {
            margin-bottom: 10px;
          }
          .description {
            margin-top: 10px;
            //white-space: pre-wrap;
          }
        }
      }
    }
  }
}
</style>
