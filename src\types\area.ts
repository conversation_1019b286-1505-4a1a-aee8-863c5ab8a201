export interface klgData {
  klgCode: string;
  klgTitle: string;
  learningState: string; //0已学习 1已掌握 2未学习
}
//领域商品
export interface AreaData {
  goodsName: string;
  goodsDescribe: string;
  createTime: string;
  countLearned: number;
  countMastered: number;
  countKnowledgePoint: number;
  editorName: string;
  editorPic: string;
  knowledgePointPage: {
    current: number;
    limit: number;
    total: number;
    records: klgData[];
  };
}
