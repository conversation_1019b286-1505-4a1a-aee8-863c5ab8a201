<template>
  <div class="content-container" v-if="ready">
    <div class="klgdetail-container-content">
      <div class="klgdetail-and-reference">
        <div class="klgdetail">
          <div class="klgdetail-content">
            <p
              class="klgdetail-content-p"
              v-html="processAllLatexEquations(areaDetail.description)"
            ></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { processAllLatexEquations } from '@/utils/latexUtils';
const props = defineProps<{
  areaCode: string;
  areaDetail: {
    title: string;
    areaCode: string;
    authorName: string;
    createTime: string;
    description: string;
  }
}>();


import { useRouter } from 'vue-router';
const router = useRouter();
// 初始化
let description: string = ref("");
const InitData = async () => {
  if (!props.areaCode) {
    console.warn('areaCode is not available, skipping InitData');
    return;
  }
  description = "前端Front-End是指网站或应用程序中用户直接看到并与之交互的部分负责构建用户界面和实现用户体验核心技术包括HTML、CSS和JavaScript。以前会Photoshop和Dreamweaver就可以制作网页随着网站开发难度加大开发方式多样网页制作更接近传统的网站后台开发网页制作更多被称为Web前端开发。前端技术包括4个部分前端美工、浏览器兼容、CSS、HTML“传统”技术与Adobe AIR、Google Gears以及概念性较强的交互式设计艺术性较强的视觉设计等。\n" +
    "在Web1.0时代由于网速和终端能力的限制大部分网站只能呈现简单的图文信息并不能满足用户在界面上的需求对界面技术的要求也不高。随着硬件的完善高性能浏览器的出现和宽带的普及技术可以在用户体验方面实现更多种可能前端技术领域迸发出旺盛的生命力。\n" +
    "2005年以后互联网进入Web2.0时代各种类似桌面软件的Web应用大量涌现前端由此发生了翻天覆地的变化。网页不再只是承载单一的文字和图片各种富媒体让网页的内容更加生动网页上软件化的交互形式为用户提供了更好的使用体验这些都是基于前端技术实现的。\n" +
    "随着手机成为人们生活中不可或缺的一部分成为人们身体的延伸人们迎来了体验为王的时代移动端的前端技术开发前景宽阔此外前端技术还能应用于智能电视、智能手表甚至人工智能领域。";
};

const ready = ref(false);

// 延迟初始化，等待props可用
onMounted(async () => {
  ready.value = false;
  if (props.areaCode) {
    await InitData(); // 等待数据加载完毕
  }
  ready.value = true;
});



</script>

<style scoped>
.content-container {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}
.klgdetail-container-content {
  display: flex;
  flex-direction: row;
}
.klgdetail-and-reference {
  /* width: 781px;  // 删除或注释掉这一行 */
  width: 100%;
  margin-right: 0; /* 可选，去掉右边距 */
}
.klgdetail {
  width: 100%;
}
.klgdetail-content {
  margin-top: 10px;
  word-break: break-all;
  min-height: 322px;
  width: 100%;
  padding-right: 10px;
}


  .klgdetail-content-p {
    white-space: pre-wrap;
    text-indent: 2em;
    font-size: 14px;
    font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
    font-weight: 400;
    font-style: normal;
  }

</style>
