<template>
  <div class="container">
    <div class="search-bar" v-if="props.info.value.hasPermission == BuyStatus.bought">
      <el-checkbox
        v-model="checkMyQuestion"
        @change="handleCheckboxChange"
        style="margin-right: 18px"
      >
        只查看我的提问
      </el-checkbox>
      <el-input
        v-model="mySearch"
        placeholder="请输入关键词"
        @keyup.enter="handleSearch()"
        clearable
        style="width: 226px; margin-right: 8px;"
      >
        <template #append><el-button :icon="Search" @click="handleSearch()" /></template>
      </el-input>
    </div>
    <div 
      :class="{ 'unpurchased-margin': props.info.value.hasPermission != BuyStatus.bought }"
    >
      <div
        class="chaptBox"
        v-for="(item, index) in props.info.value.chapterList"
        :key="item.chapterId"
      >
        <!-- 如果为案例则显示节信息 -->
        <div
          class="chapter-header"
          @click="toggleChapter(item)"
          v-if="
            props.info.value.goodsType == GoodsType.common &&
            props.info.value.prjType == PrjType.case
          "
        >
          <span class="prjDetail">{{ item.chapterName }}</span>
          <el-icon>
            <ArrowUpBold v-if="expandedChapter?.chapterId == item.chapterId" />
            <ArrowDownBold v-else />
          </el-icon>
        </div>
        <!-- 问题列表 -->
        <div
          v-if="expandedChapter?.chapterId == item.chapterId"
          class="questions-container"
        >
          <!-- 骨架屏 -->
          <div v-if="!ready" class="skeleton-container">
            <div
              v-for="i in 10"
              :key="`skeleton-${i}`"
              class="skeleton-question-item"
            >
              <div class="skeleton-left">
                <div class="skeleton-status-dot"></div>
                <div class="skeleton-keyword">
                  <div class="skeleton-text"></div>
                  <div class="skeleton-text-short"></div>
                </div>
              </div>
              <div class="skeleton-right">
                <div class="skeleton-labels-group"></div>
              </div>
            </div>
          </div>
          
          <!-- 实际内容 -->
          <div
            v-else-if="questionList && questionList.length > 0"
            class="questions-list"
          >
            <div
              class="question-item"
              v-for="(question, qIndex) in questionList"
              :key="question.questionId"
              @click="toQuestionDetail(question.questionId)"
            >
              <div class="question-left">
                <span class="status-dot" :class="{ learned: question.learned }"></span>
                <div class="keyword-container">
                  <el-tooltip
                    placement="top"
                    :content="question.keyword"
                    :raw-content="true"
                    :show-after="200"
                    effect="customized"
                  >
                    <span class="my-stem">
                      <span class="bracket-left" v-if="question.questionType != '开放性问题'">
                        【
                      </span>

                      <span
                        v-html="question.keyword"
                        class="keyword-content ellipsis"
                        :class="{ 'word-content-open': question.questionType === '开放性问题' }"
                      ></span>
                      <span class="bracket-right" v-if="question.questionType != '开放性问题'">
                        】
                      </span>
                    </span>
                  </el-tooltip>
                  <span class="questionType" v-if="question.questionType != '开放性问题'"
                    >{{ question.questionType }}?
                  </span>
                </div>
              </div>
              <div class="question-right">
                <span
                  class="necessity-label"
                  :class="{
                    necessary: question.questionNecessity == '1',
                    reference: question.questionNecessity == '2'
                  }"
                >
                  {{ questionNecessityText(question.questionNecessity) }}
                </span>
                <span
                  class="weight-label"
                  :class="{
                    public: question.questionWeight == '2',
                    private: question.questionWeight == '1'
                  }"
                >
                  {{ questionWeightText(question.questionWeight) }}
                </span>
                <el-icon>
                  <ArrowRight />
                </el-icon>
              </div>
            </div>
          </div>
          <div v-else-if="ready" class="lc-empty">
            <div class="empty-wrapper">
              <el-empty description="暂无问题数据" />
            </div>
          </div>
          
          <!-- 分页组件 -->
          <div 
            v-if="total > pageSize && ready && expandedChapter?.chapterId == item.chapterId"
            class="pagination-container"
          >
            <el-pagination
              layout="prev, pager, next"
              :total="total"
              :current-page="currentPage"
              :page-size="pageSize"
              @current-change="handlePageChange"
              :background="false"
              class="simple-pagination"
            />
          </div>
        </div>
        <!-- 只有未购买的商品会显示以下内容 -->
        <div
          v-if="
            questionList && questionList.length > 0 &&
            props.info.value.hasPermission == BuyStatus.nobuy &&
            ready &&
            expandedChapter?.chapterId == item.chapterId
          "
          class="chapter-footer"
          @click="setPayDialogVisibleTrue"
        >
          <el-icon class="custom-icon"><Lock /></el-icon>
          <span>更多内容</span>
        </div>
      </div>
    </div>
    <QRcodeDialog
      v-model="payDialogVisible"
      v-if="ready"
      :skuId="props.info.value.priceList[0].skuId"
      @paySuccess="handleBuy"
    />
  </div>
</template>
<script setup lang="ts">
import { getQuestionListApi } from '@/apis/case';
import { ArrowDownBold, ArrowUpBold, ArrowRight, Search, Lock } from '@element-plus/icons-vue';
import { BuyStatus, GoodsType } from '@/types/goods';
import { PrjType } from '@/types/project';
import { getPrjIntroduceApi } from '@/apis/case';
import { useProjectStore } from '@/stores/project';
import { processAllLatexEquations } from '@/utils/latexUtils';
import type { ChapterItem, QuestionItem } from '@/types/introduce';
import { questionNecessityText, questionWeightText } from '@/types/introduce';

const expandedChapter = ref<ChapterItem>(); // 当前展开的小节
const questionList = ref<QuestionItem[]>([]); // 当前问题列表
const allQuestionList = ref<QuestionItem[]>([]); // 所有问题列表（用于分页）
const mySearch = ref(''); //搜索关键字
const checkMyQuestion = ref(false); //是否查看自己的问题
const hideMyLearned = ref(false); //是否隐藏已学过的问题
const ready = ref(false);
const payDialogVisible = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
provide('payDialogVisible', payDialogVisible);
const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const props = defineProps(['info', 'skuId']);
const router = useRouter();
const route = useRoute();
const spuId = route.query.spuId as string;
const handleBuy = async () => {
  ready.value = false;
  const res = await getPrjIntroduceApi({
    spuId: route.query.spuId as string
  });
  projectStore.setPrjInfo(res.data);
  projectStore.setSpuId(spuId);
  ready.value = true;
};
// 移除换行符的工具函数
const removeLineBreaks = (text: string): string => {
  if (!text) return '';
  // 移除所有类型的换行符和多余的空白字符
  return text
    .replace(/[\r\n\t]+/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
};

const fetchQuestions = async (chapterId: number) => {
  ready.value = false;
  const query = {
    spuId: props.info.value.spuId,
    chapterId: chapterId,
    keyword: mySearch.value,
    self: checkMyQuestion.value,
    hideLearned: hideMyLearned.value
  };
  const res = await getQuestionListApi(query);
  // 使用类型断言确保数据符合 QuestionItem 类型
  allQuestionList.value = res.data as unknown as QuestionItem[];
  total.value = allQuestionList.value.length;
  
  // 重置到第一页
  currentPage.value = 1;
  
  // 计算当前页显示的数据
  updateCurrentPageData();

  console.log('allQuestionList:', allQuestionList.value);
  ready.value = true;
};

// 更新当前页显示的数据
const updateCurrentPageData = () => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  questionList.value = allQuestionList.value.slice(startIndex, endIndex);
};

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page;
  updateCurrentPageData();
};
const toggleChapter = async (chapter: ChapterItem) => {
  if (expandedChapter.value?.chapterId == chapter.chapterId) {
    // 如果点击的是当前展开的小节，则收起
    expandedChapter.value = {} as ChapterItem;
    questionList.value = [];
    allQuestionList.value = [];
    total.value = 0;
  } else {
    // 立即展开新章节并显示骨架屏
    expandedChapter.value = chapter;
    ready.value = false; // 显示骨架屏
    questionList.value = [];
    allQuestionList.value = [];
    total.value = 0;
    
    // 异步加载问题数据
    await fetchQuestions(chapter.chapterId);
  }
  console.log('expand:', expandedChapter.value);
};
const handleSearch = async () => {
  ready.value = false;
  if (expandedChapter.value) {
    await fetchQuestions(expandedChapter.value.chapterId);
  }
  ready.value = true;
};
const handleCheckboxChange = async () => {
  ready.value = false;
  if (expandedChapter.value) {
    await fetchQuestions(expandedChapter.value.chapterId);
  }
  ready.value = true;
};

const setPayDialogVisibleTrue = () => {
  if (!infoStore.getUserId()) {
    emit('showLoginDialog');
    return;
  }
  payDialogVisible.value = true;
}
// 定义emit事件
const emit = defineEmits<{
  showLoginDialog: []
}>();
//登录状态相关，用前两个,v-show="infoStore.getUserId()"
import { userInfoStore } from '@/stores/userInfo';
const infoStore = userInfoStore();
import checkLoginStatus from '@/utils/checkLoginStatus';
import LoginDialog from '@/components/LoginDialog.vue';
const isLogined = checkLoginStatus();

const toQuestionDetail = async (questionId: number) => {
  // 已购买的项目可以跳转所有问题,试学项目中，如果当前章节是可预览的(preview=true)，也允许跳转
  if (props.info.value.hasPermission == BuyStatus.bought || expandedChapter.value?.preview) {
    //跳转问题详情页面
    router.push({
      path: '/questionDetail',
      query: {
        spuId,
        chapterId: expandedChapter.value?.chapterId,
        questionId
      }
    });
  } else {
    //未登录状态下，给父组件发信息，打开登录弹窗
    if (!infoStore.getUserId()) {
      emit('showLoginDialog');
      return;
    }
    payDialogVisible.value = true;
  }
};
onMounted(async () => {
  if (props.info.value.goodsType == GoodsType.common && props.info.value.prjType != PrjType.case) {
    expandedChapter.value = props.info.value.chapterList[0];
    await fetchQuestions(props.info.value.chapterList[0].chapterId);
  }
});
watch(
  () => props.info.value,
  async (newVal) => {
    //console.log('9090', newVal);
    if (newVal) {
      if (newVal.value.goodsType == GoodsType.common && newVal.value.prjType != PrjType.case) {
        expandedChapter.value = props.info.value.chapterList[0];
        await fetchQuestions(props.info.value.chapterList[0].chapterId);
      }
    }
  }
);
</script>
<style lang="less" scoped>
.custom-icon {
  width: 26px;
  height: 26px;
}
.disabled-icon {
  cursor: not-allowed;
  opacity: 0.6;
}
.prjDetail {
  padding-left: 20px;
  color: #333;
  font-family: var(--text-family);
}

// 自定义复选框样式
:deep(.el-checkbox) {
  .el-checkbox__input {
    .el-checkbox__inner {
      &:hover {
        border-color: #1973cb;
      }
    }
    
    &.is-checked {
      .el-checkbox__inner {
        background-color: #1973cb;
        border-color: #1973cb;
      }
      
      .el-checkbox__label {
        color: #1973cb !important;
      }
    }
  }
  
  .el-checkbox__label {
    &:hover {
      color: #1973cb;
    }
  }
  
  // 确保选中时文字颜色为 #1973cb
  &.is-checked {
    .el-checkbox__label {
      color: #1973cb !important;
    }
  }
}

.search-bar {
  margin: 10px 0;

  display: flex;
  justify-content: flex-end;
  align-items: center;
  .search-input {
    width: 226px;
    height: 32px;
    padding: 8px;
    font-size: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
}

// 未购买状态下的样式
.unpurchased-margin {
  margin-top: 20px;
}
.chaptBox {
  display: flex;
  flex-direction: column;

  margin-left: 10px;
  font-size: 14px;
  justify-content: space-between;
  border-bottom: 1px solid #ccc;

  .chapter-header {
    display: flex;
    flex: 1;
    height: 46px;
    padding-right: 8px;
    justify-content: space-between;
    align-items: center;
    line-height: 46px;
    cursor: pointer;
    background-color: #f2f2f2;
  }
  .questions-container {
    //background-color: transparent;
    //padding-left: 10px;
    /* 移除固定高度，让容器根据内容动态调整 */
    display: flex;
    flex-direction: column;

    .question-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #eaeaea;
      height: 65px;
      .question-left {
        display: flex;
        align-items: center;
        flex: 1;

        .status-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          margin-right: 18px;
          background-color: #ccc;

          &.learned {
            background-color: var(--color-theme-project);
          }
        }

        /* 注释掉空规则集 */
        /* span {
          margin-right: 8px;
        } */
      }
      .question-right {
        display: flex;
        align-items: center;
        font-size: 12px;

        .necessity-label {
          width: 50px;
          text-align: center;
          justify-content: center;
          padding: 2px 6px;
          border-radius: 10px;
          color: #fff;
          background-color: var(--color-theme-project);

          // &.necessary {
          //   background-color: #007bff;
          // }
          // &.reference {
          //   background-color: #6c757d;
          // }
        }

        .weight-label {
          width: 50px;
          text-align: center;
          margin-left: 8px;
          padding: 2px 6px;
          border-radius: 10px;
          color: #333;
          background-color: #ffd24a;

          // &.public {
          //   background-color: #ffd24a; /* 黄色 */
          // }
          // &.private {
          //   background-color: #ff5733; /* 红色 */
          // }
        }

        .el-icon {
          margin-left: 18px;
        }
      }
    }

    .question-item:hover {
      cursor: pointer;
    }
    
    /* 骨架屏样式 */
    .skeleton-container {
      
      .skeleton-question-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        height: 65px;
        
        .skeleton-left {
          display: flex;
          align-items: center;
          flex: 1;
          
          .skeleton-status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 20px;
            background: #f0f0f0;
            animation: skeleton-pulse 1.5s ease-in-out infinite;
          }
          
          .skeleton-keyword {
            flex: 1;
            
            .skeleton-text {
              height: 18px;
              width: 70%;
              background: #f0f0f0;
              border-radius: 4px;
              margin-bottom: 6px;
              animation: skeleton-pulse 1.5s ease-in-out infinite;
            }
            
            .skeleton-text-short {
              height: 14px;
              width: 30%;
              background: #f0f0f0;
              border-radius: 4px;
              animation: skeleton-pulse 1.5s ease-in-out infinite;
            }
          }
        }
        
        .skeleton-right {
          display: flex;
          align-items: center;
          
          .skeleton-labels-group {
            width: 120px;
            height: 22px;
            background: #f0f0f0;
            border-radius: 12px;
            animation: skeleton-pulse 1.5s ease-in-out infinite;
          }
        }
      }
    }
    
    @keyframes skeleton-pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }
    
    /* 问题列表样式 */
    .questions-list {
      flex: 1;
    }
    
    /* 空状态样式 */
    .lc-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      
      .empty-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .question-item:last-child {
    border-bottom: none;
  }

  .chapter-footer {
    display: flex;
    flex: 1;
    height: 46px;
    line-height: 46px;
    justify-content: center;
    align-items: center;
    background-color: #f2f2f2;
  }
  .keyword-container {
    display: flex;
    align-items: center;
    width: 700px;
    height: 100%;
    overflow: hidden;
    position: relative;
  }

  .my-stem {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
    max-width: 90%;
    height: 100%;
  }

  .bracket-left,
  .bracket-right {
    flex-shrink: 0;
  }

  .keyword-content {
    max-width: 90%;
    align-items: center;
    line-height: 25px !important;

    // 公式元素样式
    :deep(.equation) {
      display: inline-block !important;
      height: 25px !important;
      margin-top: -20px !important;
      cursor: pointer !important;
      transition: opacity 0.2s ease !important;
      font-size: 10px !important;
    }

    // 图片元素样式
    :deep(img) {
      display: inline !important;
      height: 14px !important;
      cursor: pointer !important;
      transition: opacity 0.2s ease !important;
    }
  }
}
.word-content-open {
  margin-left: 10px;
}
/* 省略号文本样式 */
.ellipsis-text {
  display: flex;
  max-width: calc(100% - 20px); /* 为引号预留更多空间 */
  color: black; /* 确保文字为黑色 */
  position: relative; /* 为渐变遮罩定位 */
  align-items: center; /* 垂直居中 */
  overflow: hidden;
  height: 100%;
}

/* 对不包含公式的内容应用省略号效果以及公式的垂直居中 */
:deep(.ellipsis-text p) {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-height: 100%;
}

/* 分页组件样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  //padding: 10px 0;
  /*border-top: 1px solid #eaeaea;*/
  //margin-top: 6px;
  flex-shrink: 0; /* 防止分页组件被压缩 */
}

/* 简约分页器样式 */
:deep(.simple-pagination) {
  .el-pagination__prev,
  .el-pagination__next {
    background: none !important;
    border: none !important;
    color: #333 !important;
    font-weight: normal !important;
    
    &:hover {
      background: none !important;
      color: #1973cb !important;
    }
    
    &.is-disabled {
      color: #c0c4cc !important;
    }
  }
  
  .el-pager {
    li {
      background: none !important;
      border: none !important;
      color: #333 !important;
      font-weight: normal !important;
      margin: 0 4px;
      
      &:hover {
        background: none !important;
        color: #1973cb !important;
      }
    }
  }
}

/* 强制覆盖当前页码样式 */
:deep(.simple-pagination .el-pager li.is-active),
:deep(.simple-pagination .el-pager li.active),
:deep(.simple-pagination .el-pager li[class*="active"]) {
  background: none !important;
  border: none !important;
  color: #1973cb !important;
  font-weight: normal !important;
}

/* 额外的强制覆盖 */
:deep(.simple-pagination) {
  .el-pager li.is-active {
    background: none !important;
    border: none !important;
    color: #1973cb !important;
    font-weight: normal !important;
  }
}
</style>
