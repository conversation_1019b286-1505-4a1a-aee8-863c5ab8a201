<!-- 页面左侧信息栏和未支付状态下的相关弹窗页面 -->
<template>
  <div class="prj-learn-wrapper">
    <div class="prj-learn">
      <div class="left-route">
        <div class="up-detail-card">
          <div class="prj-img">
            <img style="width: 298px; height: 185px" :src="info.coverPic" alt="" />
          </div>
          <div class="prj-info">
            <div class="title">
              <h1>{{ info.title }}</h1>
            </div>
            <div class="detail">
              {{ info.description }}
            </div>
          </div>
        </div>
        <div class="down-routes">
          <div
            v-for="(route, index) in routesInfo"
            :key="route.label"
            :class="['route-items', index == activedIndex ? 'active' : 'inactive']"
            @click="goPage(index)"
          >
            {{ route.label }}
          </div>
        </div>
      </div>
      <div class="right-info">
        <!-- 占位符 路由变化时用于渲染 -->
        <router-view v-slot="{ Component }">
          <keep-alive>
            <component :is="Component" />
          </keep-alive>
        </router-view>
      </div>
    </div>
    <!-- 对话框  同一个对话框 展示不同的内容-->
    <el-dialog class="dg" v-model="visible" :show-close="false" width="526px" top="40vh">
      <template #header>
        <div class="dg-header">
          <h1>{{ diaTitle }}</h1>
          <img
            @click="visible = !visible"
            style="width: 16px; height: 16px; cursor: pointer"
            src="@/assets/images/prjlearn/close.svg"
            alt=""
          />
        </div>
      </template>
      <div class="content">
        {{ diaContent }}
      </div>
      <template #footer>
        <div class="foot-btns">
          <CmpButton class="btn" type="info" @click="visible = !visible">我知道了</CmpButton>
          <CmpButton
            class="btn"
            type="primary"
            @click="
              payDialogVisible = !payDialogVisible;
              visible = !visible;
            "
          >
            马上支付
          </CmpButton>
        </div>
      </template>
    </el-dialog>
    <QRcodeDialog v-model="payDialogVisible" :info="info" v-if="Number(info.price) > 0" />
    <FreeDialog v-model="payDialogVisible" v-else />
    <!-- <div v-if="dialogPayVisible">
      领域支付样式
      <dialogPayment
        v-if="isArea == GoodsType.vip"
        :paymentObj="paymentObj"
        @closeDialog="closeDialog"
      >
      </dialogPayment>
      非领域支付样式
      <QRcodeDialog
        v-else
        :dialogPayVisible="dialogPayVisible"
        @closeDialog="closeDialog"
        @paySuccess="handleWatch"
      >
      </QRcodeDialog>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import QRcodeDialog from '@/components/QRcodeDialog.vue';
import FreeDialog from '@/components/FreeDialog.vue';
import { getPrjIntroduceApi } from '@/apis/case';
import { BuyStatus, GoodsType } from '@/types/goods';
import { useProjectStore } from '@/stores/project';

const router = useRouter();
const route = useRoute();
const spuId = route.query.spuId as string;

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const payDialogVisible = ref(false);
provide('payDialogVisible', payDialogVisible);

// 左侧导航栏  导航栏刷新变化问题待解决
const routesInfo = [
  {
    label: '知识地图',
    path: 'map'
  },
  {
    label: '学习进程',
    path: 'process'
  },
  {
    label: '测评方案',
    path: 'exam'
  }
];
// 对话框显示变量控制isArea
const visible = ref(false);
//设置一个默认值，可删可不删
const diaTitle = ref('学习进程');
const diaContent = ref(
  '开始学习后，系统会根据您的学习情况，呈现您的学习进程，让您清晰看到自己的学习之路。'
);
const diaContentOption = [
  '',
  '开始学习后，系统会根据您的学习情况，呈现您的学习进程，让您清晰看到自己的学习之路。',
  '开始学习后，系统会自动给您匹配对应的测验方案，以方便您检验学习成功。'
];

// 支付弹窗控制
// const dialogPayVisible = ref(false);
//注入的状态变量初始化
const isArea = ref(0);
const prjForm = ref(0);
const buyStatus = ref(0);
// const skuList = ref([])
const skuId = ref('');
const skuList = reactive([]);
// paymentObj:当为领域时的数据，需要在相关页面点击时传递到本页面
// 购买状态

// const paymentObj = {
//   fieldName: '芯片设计',
//   goodsPriceList: [
//     {
//       cardType: '月卡',
//       goodsPrice: '89'
//     },
//     {
//       cardType: '季卡',
//       goodsPrice: '189'
//     },
//     {
//       cardType: '年卡',
//       goodsPrice: '289'
//     }
//   ]
// };
// checkedpay：非领域商品的价格
// const checkedpay = ref(89);

// 导航栏状态变量
const activedIndex = ref(0);
// 导航切换变量
const goPage = (index: number) => {
  // 首先判断是否购买
  // 为购买弹出支付弹框
  if (activedIndex.value == index) {
    return;
  }
  if (info.value.buyStatus == BuyStatus.nobuy) {
    // 如果index相同 则什么都不做

    // 如果没购买 则弹出购买框 并更新数据
    visible.value = !visible.value;
    diaTitle.value = routesInfo[index].label;
    diaContent.value = diaContentOption[index];
    return;
  }
  // 已购买则直接跳转
  else {
    activedIndex.value = index;
    router.push({
      path: routesInfo[index].path,
      query: route.query
    });
  }
};

// 已购买组件传参
const boughtInfo = ref({
  userCoverPic: '',
  userName: '',
  klgCount: 0,
  learned: 0,
  graspKlg: 0
});

// 未购买组件传参
const nobuyInfo = ref({
  userCoverPic: '',
  userName: '',
  klgCount: 0,
  cardType: 1,
  price: '',
  priceArr: [],
  studyTime: 30
});
const myPrice = ref();

// 项目信息
const prjInfo = ref({
  title: '',
  description: '',
  coverPic: ''
});

// 学习说明
const studyInstructions = ref('');
// 小节信息
//必须在注入的时候加默认值！！否则后续孙子组件在生命周期中可能出现问题
let lessonData = ref([
  {
    chapterName: '',
    isLearned: false,
    isTry: false,
    chapterNum: 0,
    sectionId: 0
  }
]);
// 案例学习的最新一节id，用来判断继续学习按钮进入的小节
const latestChapterId = ref('');

// 判断是不是领域来进行不同的请求, jumpGoodsType，需要从所有跳转至本页面的地方传入
// 后续可以在这里设置统一的数据
// const jumpGoodsType = ref();
const transuniqueCode = ref();
// console.log('route.query.goodsType', route.query.goodsType)
// jumpGoodsType.value = route.query.goodsType
// watch(
//   () => route.query.goodsType,
//   () => {
//     jumpGoodsType.value = route.query.goodsType || 1; // 如果 query.goodsType 不存在，默认值为 1
//     console.log("nv.goodsType", jumpGoodsType.value)
//   });

// if (jumpGoodsType.value == GoodsType.project) {
//   transuniqueCode.value = learnStore.uniqueCode;
// } else {
//   console.log('jumpGoodsType.value', jumpGoodsType.value)
//   console.log('GoodsType.project', GoodsType.project)
//   transuniqueCode.value = 'defaultValue';
// }

// 请求 对内容做分发(只在index中请求，分发数据到其他组件)
const getInfo = () => {
  getPrjIntroduceApi({ spuId })
    .then((res) => {
      info.value = res.data;
      // 此处统一赋值 避免出现数据未初始化渲染页面问题
      prjInfo.value.title = res.data.title;
      prjInfo.value.description = res.data.description;
      prjInfo.value.coverPic = res.data.coverPic;
      isArea.value = res.data.goodsType; //1表示已购买，0表示未购买
      buyStatus.value = res.data.buyStatus;
      prjForm.value = res.data.prjForm;
      skuId.value = res.data.skuId;
      // skuList.value=res.data.skuList;
      if (res.data.skuList) {
        res.data.skuList.forEach((item) => {
          skuList.push(item);
        });
      }

      // console.log(skuList.value)
      // FIXME
      latestChapterId.value = res.data.latestChapterId;

      if (res.data.buyStatus == 1) {
        // 已购买信息
        boughtInfo.value.userCoverPic = res.data.userCoverPic;
        boughtInfo.value.userName = res.data.userName;
        boughtInfo.value.klgCount = res.data.klgCount;
        boughtInfo.value.learned = res.data.learned;
        boughtInfo.value.graspKlg = res.data.graspKlg;
        // console.log('领域', isArea.value);
      } else {
        // 未购买信息

        nobuyInfo.value.userCoverPic = res.data.userCoverPic;
        nobuyInfo.value.userName = res.data.userName;
        nobuyInfo.value.cardType = res.data.cardType;
        nobuyInfo.value.klgCount = res.data.klgCount;
        nobuyInfo.value.studyTime = res.data.studyTime;
        // 对价格做特殊处理
        if (isArea.value == GoodsType.vip) {
          // 领域学习
          nobuyInfo.value.price = res.data.price[0][1];
          // console.log('输出价格', res.data.price[0])
          myPrice.value = res.data.price[0][1];
          const priceArr = res.data.price[0];
          const cardTypeName = ['', '年卡', '季卡', '月卡'];
          for (let i = 1; i < 4; i++) {
            //@ts-ignore
            nobuyInfo.value.priceArr.push({
              label: cardTypeName[i],
              value: i,
              price: priceArr[i]
            });
          }
          console.log(nobuyInfo.value);
        } else {
          nobuyInfo.value.price = res.data.price;
          myPrice.value = res.data.price;
        }
      }
      studyInstructions.value = res.data.studyInstructions;
      lessonData.value = res.data.chapterList;
    })
    .catch((error) => {
      console.log(error);
    });
};

getInfo();
// 最新小节号
provide('latestChapterId', latestChapterId);
// 已购买状态信息
provide('boughtInfo', boughtInfo);
// 未购买状态信息
provide('nobuyInfo', nobuyInfo);
provide('myprice', myPrice);
// 项目标题
provide('title', prjInfo);

// 项目介绍
provide('studyInstructions', studyInstructions);

provide('lessonData', lessonData);

//是否领域商品
provide('goodsType', isArea);

provide('buyStatus', buyStatus);
provide('prjForm', prjForm);

//sku
provide('skuId', skuId);
provide('skuList', skuList);

// provide('studyTime',info.value.studyTime)
// watchEffect(nobuyInfo, (newValue) => {
//    console.log('异步数据更新了:', newValue);
//    provide('nobuyInfo', nobuyInfo);
// });
// console.log(skuList.value)

// 挂载时 获取路径
onMounted(() => {
  // 当前路径
  const curPath = route.path.split('/')[2];
  routesInfo.forEach((routeItem, index) => {
    if (routeItem.path.indexOf(curPath) != -1) {
      activedIndex.value = index;
    }
  });
});
</script>

<style scoped lang="less">
.prj-learn-wrapper {
  color: var(--color-black);
  font-family: var(--title-family);
  background-color: white;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  white-space: nowrap;
  // position: relative;

  .prj-learn {
    width: var(--width-fixed--project);
    display: flex;
    flex-direction: row;

    .left-route {
      width: 300px;
      display: flex;
      flex-direction: column;

      .up-detail-card {
        width: 100%;
        height: 373px;
        border: 0.8px solid #f2f2f2;
        border-radius: 5px;
        overflow: hidden;
        text-overflow: ellipsis;

        .prj-info {
          width: 100%;
          padding-left: 10px;
          margin-top: 40px;
          padding-right: 10px;
          padding-bottom: 10px;

          .title {
            width: 100%;
            overflow: hidden;

            h1 {
              font-size: var(--fontsize-middle-project);
              font-weight: 700;
            }
          }

          .detail {
            width: 100%;
            white-space: pre-wrap;
            font-family: var(--text-family);
            font-size: var(--fontsize-small-project);
            margin-top: 12px;
            height: 100px;
            text-overflow: ellipsis;
            /* 2.设置旧版弹性盒 */
            display: -webkit-box;
            /* 3. 控制行数*/
            -webkit-line-clamp: 6;
            /* 4. 设置子元素的排列方式  垂直排列*/
            -webkit-box-orient: vertical;
            /* 5.溢出隐藏 */
            overflow: hidden;
          }
        }
      }

      .down-routes {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-top: 30px;
        height: 170px;

        .route-items {
          height: 50px;
          width: 100%;
          margin-bottom: 10px;
          font-family: var(--text-family);
          font-size: var(--fontsize-large-project);
          border-radius: 5px;
          border-width: 0.8px;
          border-style: solid;
          line-height: 50px;
          padding-left: 10px;
          cursor: pointer;
        }

        .route-items:hover {
          border-color: var(--color-theme-project);
          background-color: var(--color-second);
          color: var(--color-theme-project);
          font-weight: 600;
        }

        .active {
          color: white;
          background-color: var(--color-theme-project);
        }

        .inactive {
          border-color: rgba(220, 223, 230, 1);
        }
      }
    }

    .right-info {
      // height: 800px;
    }
  }

  .dg {
    .dg-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      border-bottom: 2px solid #f2f2f2;
      padding-bottom: 15px;

      h1 {
        font-size: 18px;
        font-weight: 700;
        color: var(--color-theme-project);
      }
    }

    .content {
      width: 100%;
      white-space: pre-wrap;
    }

    .foot-btns {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;

      .btn {
        width: 160px;
        height: 43px;

        &:nth-child(2) {
          margin-left: 20px;
        }
      }
    }
  }

  .mask-layer {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.4);
    width: 100%;
    /*宽度设置为100%，这样才能使隐藏背景层覆盖原页面*/
    height: 100%;
    filter: alpha(opacity=60);
    /*设置透明度为60%*/
    /*opacity: 0.6;*/
    /*非IE浏览器下设置透明度为60%*/
    z-index: 9999;
  }
}
</style>
