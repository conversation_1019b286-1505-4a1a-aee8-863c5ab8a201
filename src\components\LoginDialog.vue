<template>
  <el-dialog 
    v-model="visible" 
    width="800px" 
    :close-on-click-modal="false" 
    :show-close="true" 
    :modal="true"
    :append-to-body="true"
    class="login-dialog"
  >
    <div class="login-container">
      <!-- 左侧：账号登录 -->
      <div class="login-left">
        <h2 class="login-title">无尽本源账号登录</h2>
        
        <div class="input-group">
          <div class="input-wrapper">
            <div class="custom-input" :class="{ 'error': phoneError }">
              <input 
                v-model="phone" 
                type="text"
                placeholder="请输入您注册的手机号"
                class="input-field"
                @input="validatePhone"
                @blur="validatePhone"
              />
              <img 
                src="@/assets/svgs/paperAirplane.svg" 
                alt="发送" 
                class="paper-airplane"
                :class="{ disabled: isCountingDown || !isPhoneValid }"
                @click="sendVerifyCode"
              />
            </div>
            <div v-if="phoneError" class="error-message">{{ phoneError }}</div>
          </div>
          
          <div class="input-wrapper">
            <div class="custom-input" :class="{ 'error': verifyCodeError }">
              <input 
                v-model="verifyCode" 
                type="text"
                placeholder="请输入验证码"
                class="input-field"
                @input="validateVerifyCode"
                @blur="validateVerifyCode"
                @keyup.enter="login"
              />
              <span v-if="isCountingDown" class="countdown-text">{{ countdown }}s</span>
            </div>
            <div v-if="verifyCodeError" class="error-message">{{ verifyCodeError }}</div>
          </div>
        </div>
        
        <div class="button-group">
          <el-button class="login-btn" @click="login">登录</el-button>
          <el-button class="register-btn" @click="register">注册</el-button>
        </div>
        
        <div class="disclaimer">
          登录即代表您浏览并认可《相关声明》。
        </div>
      </div>
      
      <!-- 分隔线 -->
      <div class="divider"></div>
      
      <!-- 右侧：微信扫码登录 -->
      <div class="login-right">
        <h2 class="login-title">微信扫码登录</h2>
        
        <div class="qr-container">
          <div class="qr-code">
            <img src="@/assets/svgs/scanCode.svg" alt="扫码登录" class="qr-image" />
          </div>
          
          <p class="qr-tip">请用绑定的微信号进行扫码登录。</p>
        </div>
        
        <div class="wechat-steps">
          <div class="phone-demo">
            <img src="@/assets/svgs/wechat.svg" alt="微信" class="wechat-icon" />
          </div>
          
          <div class="steps-list">
            <div class="step-item">
              <span class="step-text">1.打开您的微信。</span>
            </div>
            <div class="step-item">
              <span class="step-text">2.点击右上角加号"+"。</span>
            </div>
            <div class="step-item">
              <span class="step-text">3.点击"扫一扫"。</span>
            </div>
            <div class="step-item">
              <span class="step-text">4.扫描屏幕二维码登录。</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose } from 'vue'
import { sendCode } from '@/apis/login'

const visible = ref(false)
const phone = ref('')
const verifyCode = ref('')
const countdown = ref(0)
const isCountingDown = ref(false)

// 校验相关变量
const phoneError = ref('')
const verifyCodeError = ref('')
const isPhoneValid = ref(false)

// 手机号校验
const validatePhone = () => {
  const phoneRegex = /^1\d{10}$/
  if (!phone.value) {
    phoneError.value = ''
    isPhoneValid.value = false
  } else if (!phoneRegex.test(phone.value)) {
    phoneError.value = '请输入正确的手机号格式'
    isPhoneValid.value = false
  } else {
    phoneError.value = ''
    isPhoneValid.value = true
  }
}

// 验证码校验
const validateVerifyCode = () => {
  const codeRegex = /^\d{6}$/
  if (!verifyCode.value) {
    verifyCodeError.value = ''
  } else if (!codeRegex.test(verifyCode.value)) {
    verifyCodeError.value = '验证码为6位数字'
  } else {
    verifyCodeError.value = ''
  }
}

// 发送验证码
const sendVerifyCode = () => {
  if (isCountingDown.value) return
  
  // 校验手机号
  validatePhone()
  if (!isPhoneValid.value) {
    ElMessage.error('请输入正确的手机号')
    return
  }
  
  // 这里可以添加发送验证码的API调用
  console.log('发送验证码到:', phone.value)
  
  // 开始倒计时
  isCountingDown.value = true
  countdown.value = 60
  
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      isCountingDown.value = false
    }
  }, 1000)
}

import { loginApi } from '@/apis/login'
const login = async () => {
  // 校验输入
  validatePhone()
  validateVerifyCode()
  
  if (!phone.value) {
    ElMessage.error('请输入手机号')
    return
  }
  
  if (!isPhoneValid.value) {
    ElMessage.error('请输入正确的手机号')
    return
  }
  
  if (!verifyCode.value) {
    ElMessage.error('请输入验证码')
    return
  }
  
  if (verifyCodeError.value) {
    ElMessage.error('请输入正确的验证码')
    return
  }
  
  //发请求，登录
  try {
    const res = await loginApi({
      phone: phone.value,
      code: verifyCode.value
    })
    if (res.success) {
      ElMessage.success('登录成功')
      console.log('登录成功', res)
      //弹窗关闭
      visible.value = false;
      
      // 发送登录成功事件，通知父组件刷新页面
      const event = new CustomEvent('loginSuccess', {
        detail: { userInfo: res.data }
      });
      window.dispatchEvent(event);
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    console.log('登录失败', error)
  }
}


import { toLogin } from '@/utils/gopage'
const register = () => {
  console.log('注册') 
  toLogin('', undefined, 'register')
}

defineExpose({
  open: () => { visible.value = true },
  close: () => { visible.value = false }
})
</script>

<style scoped>
.login-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.login-dialog :deep(.el-dialog__header) {
  display: none;
}

.login-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.login-dialog :deep(.el-overlay) {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.login-dialog :deep(.el-overlay-dialog) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  display: flex;
  min-height: 500px;
}

.login-left {
  flex: 1;
  padding: 10px 20px 10px 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-right {
  flex: 1;
  padding: 10px 20px 10px 20px;
  /* background-color: #f8f9fa; */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-title {
  color: #333;
  margin-bottom: 30px;
  text-align: center;
  font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
  font-weight: 600;
  font-style: normal;
  font-size: 28px;
}

.input-group {
  margin-bottom: 30px;
}

.input-wrapper {
  margin-bottom: 20px;
  height: 40px;
}

.custom-input {
  position: relative;
  width: 100%;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  display: flex;
  align-items: center;
  padding: 0 12px;
  transition: border-color 0.3s ease;
}

.custom-input.error {
  border-color: #f56c6c;
}

.input-field {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #333;
}

.input-field::placeholder {
  color: #c0c4cc;
}

.paper-airplane {
  width: 16px;
  height: 16px;
  cursor: pointer;
  margin-left: 8px;
  transition: opacity 0.3s ease;
}

.paper-airplane.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.countdown-text {
  color: #1973cb;
  font-size: 14px;
  margin-left: 8px;
  cursor: pointer;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.2;
}



.button-group {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.login-btn {
  width: 100% !important;
  height: 44px !important;
  font-size: 16px !important;
  background: #1973CB !important;
  border: none !important;
  color: white !important;
  margin: 0 !important;
}

.login-btn:hover {
  background: #1661ab !important;
}

.register-btn {
  width: 100% !important;
  height: 44px !important;
  font-size: 16px !important;
  background: white !important;
  color: #1973cb !important;
  margin: 0 !important;
}

.register-btn:hover {
  border: 1px solid #1973cb !important;
}

.disclaimer {
  font-size: 12px;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

.divider {
  width: 1px;
  background-color: #e4e7ed;
  margin: 0 20px;
}

.qr-container {
  text-align: center;
  margin-bottom: 30px;
}

.qr-code {
  width: 120px;
  height: 120px;
  margin: 0 auto 15px;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.qr-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.qr-tip {
  margin: 0;
  font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #999999;
}

.wechat-steps {
  display: flex;
  align-items: flex-start;
  gap: 40px;
}

.phone-demo {
  flex-shrink: 0;
}

.wechat-icon {
  width: 105px;
  height: 80px;
  object-fit: contain;
}

.steps-list {
  flex: 1;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}



.step-text {
  line-height: 1.4;
  font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  color: #666666;
}
</style> 