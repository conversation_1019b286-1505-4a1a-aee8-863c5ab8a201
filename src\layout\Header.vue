<template>
  <div class="header-wrapper">
    <!-- FIXME: 妈呀我觉得我这样写好愚蠢 -->
    <div class="header" :class="{ wideScreen: useWideScreen }">
      <div class="header-left">
        <div class="logo" @click="backHome">
          <!-- <div class="chinese">本源</div>
          <div class="eng">EndlessOrigin</div> -->
          <img src="@/assets/logo.png" style="width: 80px" />
        </div>

        <RouterLink :to="{ name: 'home' }"
            ><span :class="{ active: $route.path.match('/home') }" style="cursor: pointer;
            color: #333333;
            font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 18px;"
             >首页</span
            ></RouterLink
        >
        <RouterLink :to="{ name: 'classification' }" style="margin-left: 40px;
        color: #333333;
        font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 18px;"
            ><span :class="{ active: $route.path.match('/classification') }" style="cursor: pointer"
              >资料</span
            ></RouterLink
          >
        <RouterLink :to="{ name: 'knowledge' }" style="margin-left: 40px;
        color: #333333;
        font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 18px;"
            ><span :class="{ active: $route.path.match('/knowledge') }" style="cursor: pointer"
              >知识</span
            ></RouterLink
          >
        <el-input
          style="margin-left: 137px;"
          v-model="searchKey"
          placeholder="你想学习什么?"
          size="large"
          @keyup.enter="handleSearch()"
          clearable
        >
          <template #append><el-button :icon="Search" @click="handleSearch()" /></template>
        </el-input>
      </div>
      <div class="header-right">
        <div class="list">
          <!-- <RouterLink
            :to="{ name: 'home' }"
            style="margin-left: 40px"
          >
          <span :class="{ active: $route.path.match('/home') }" style="cursor: pointer">首页</span> -->
            <!--<span
              class="icon-wrapper inactive"
              :class="{ active: $route.path.match('/home') }"
              style="cursor: pointer"
            >
              //   <el-icon :size="20" color="" @click="() => $router.push({ name: 'home' })">
              <el-icon :size="20">
                <HomeFilled />
              </el-icon>
            </span>-->
          <!-- </RouterLink> -->

          <!--<RouterLink
            :to="{ name: 'learnStatistics' }"
            style="margin-left: 40px"
            v-if="infoStore.getUserId()"
            ><span
              :class="{ active: $route.path.match('/learnStatistics') }"
              style="cursor: pointer"
              >学习</span
            ></RouterLink
          >
          <div style="margin-left: 40px" v-else>
            <span :class="false" style="cursor: pointer" @click="toLogin('/learnStatistics')"
              >学习</span
            >
          </div>-->

          <!-- <RouterLink :to="{ name: 'classification' }" style="margin-left: 40px"
            ><span :class="{ active: $route.path.match('/classification') }" style="cursor: pointer"
              >资料</span
            ></RouterLink
          >
          <RouterLink :to="{ name: 'knowledge' }" style="margin-left: 40px"
            ><span :class="{ active: $route.path.match('/knowledge') }" style="cursor: pointer"
              >知识</span
            >
          </RouterLink> -->
          <RouterLink
            :to="{ name: 'shoppinglist' }"
            style="margin-left: 40px; display: flex; flex-direction: column; align-items: center; text-decoration: none;"
            v-show="infoStore.getUserId()"
          >
            <span
              class="icon-wrapper inactive"
              :class="{ active: $route.path.match('/shoppinglist') }"
              style="cursor: pointer; margin-bottom: 4px;"
            >
              <el-icon :size="18" :style="{ color: $route.path.match('/shoppinglist') ? 'var(--color-theme-project)' : '#999999' }">
                <List />
              </el-icon>
            </span>
            <span :style="{ 
              fontFamily: '阿里巴巴普惠体 3.0 55 L3, 阿里巴巴普惠体 3.0 55, 阿里巴巴普惠体 3.0, sans-serif',
              fontWeight: '400',
              fontStyle: 'normal',
              fontSize: '12px',
              color: $route.path.match('/shoppinglist') ? 'var(--color-theme-project)' : '#999999'
            }">订单</span>
          </RouterLink>
          <RouterLink
            :to="{ name: 'message' }"
            style="margin-left: 40px; display: flex; flex-direction: column; align-items: center; text-decoration: none;"
            v-show="infoStore.getUserId()"
          >
            <span
              class="icon-wrapper inactive"
              :class="{ active: $route.path.match('/message') }"
              style="cursor: pointer; margin-bottom: 4px;"
            >
              <el-icon :size="18" :style="{ color: $route.path.match('/message') ? 'var(--color-theme-project)' : '#999999' }">
                <BellFilled />
              </el-icon>
            </span>
            <span :style="{ 
              fontFamily: '阿里巴巴普惠体 3.0 55 L3, 阿里巴巴普惠体 3.0 55, 阿里巴巴普惠体 3.0, sans-serif',
              fontWeight: '400',
              fontStyle: 'normal',
              fontSize: '12px',
              color: $route.path.match('/message') ? 'var(--color-theme-project)' : '#999999'
            }">消息</span>
          </RouterLink>
        </div>
        <div class="picture"></div>
        <div class="avatar-wrapper">
          <el-dropdown v-if="infoStore.getUserId()">
            <!-- TODO:样式没改好 -->
            <el-avatar :src="avatar" style="cursor: pointer"></el-avatar>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="() => $router.push({ name: 'userspace' })"
                  >个人空间</el-dropdown-item
                >
                <el-dropdown-item @click="() => $router.push({ name: 'learnStatistics' })"
                  >学习历史</el-dropdown-item
                >
                <el-dropdown-item class="el-dropdown-item" @click="toUserInfo"
                  >账号资料</el-dropdown-item
                >
                <el-dropdown-item v-if="showBackMange" @click="toADMIN">后台管理</el-dropdown-item>
                <el-dropdown-item @click="logout()">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button v-else type="primary" @click="showLoginDialog" style="margin-left: 102px">登录</el-button>
        </div>
        
      </div>
    </div>
    <LoginDialog ref="loginDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { Search, List, BellFilled } from '@element-plus/icons-vue';
import { getUserDetailApi } from '@/apis/learnStatistics';
import { ref, onMounted } from 'vue';
import { toLogin, toADMIN, toUserInfo } from '@/utils/gopage';
import { useUserStore } from '@/stores/user';
import checkLoginStatus from '@/utils/checkLoginStatus';
import { logoutApi } from '@/apis/userinfo';
import { userInfoStore } from '@/stores/userInfo';
import LoginDialog from '@/components/LoginDialog.vue'

const infoStore = userInfoStore();
const isLogined = checkLoginStatus();
const router = useRouter();
const userStore = useUserStore();
const userInfo = ref();
const useWideScreen = inject('useWideScreen') as Ref<boolean>;
const avatar = ref();
const loginDialogRef = ref()
function showLoginDialog() {
  loginDialogRef.value?.open()
}

import { useRouter, useRoute } from 'vue-router';
const route = useRoute();

// 监听登录成功事件
const handleLoginSuccess = async (event: CustomEvent) => {
  console.log('收到登录成功消息:', event.detail);
  
  // 重新获取用户信息
  //await infoStore.getUserInfo();
  
  // 重新获取商品信息
  //await refreshGoodInfo();
  router.go(0);
}

//
onMounted(() => {
  // 注册登录成功事件监听器
  window.addEventListener('loginSuccess', handleLoginSuccess as EventListener);
})
// 组件销毁时清理事件监听器
onBeforeUnmount(() => {
  window.removeEventListener('loginSuccess', handleLoginSuccess as EventListener);
});


// watch(
//   () => userInfo.value.userId, // 监听 userInfo 的 uniqueCode 属性
//   (newVal, oldVal) => {
//     if (newVal !== oldVal && !oldVal && oldVal !== '') {
//       console.log('old:', oldVal);
//       console.log('new:', newVal);
//       ElMessage.success('账号切换成功！'); // 显示提示信息
//     }
//     console.log(userInfo.value.coverPic);
//     console.log('value:', userInfo.value);
//   }
// );

const searchKey = ref('');
const showAccountInfo = ref(true);
const showBackMange = ref(false);
const showPersonSpace = ref(true);
interface PrjPermission {
  name: string;
  isAble: boolean;
  children?: any[];
}
const handleSearch = () => {
  router.push({
    path: '/classification',
    query: {
      keyword: searchKey.value
    }
  });
};

// 获取用户信息

const backHome = () => {
  router.push('/home');
};

const logout = async () => {
  try {
    const res = await logoutApi();
    if (res.success) {
      ElMessage.success('退出成功');
    }
  } catch (error) {
    console.error(error);
  }
};

avatar.value = infoStore.getAvatar();
const permissions = infoStore.getPermission();
const admin = permissions.find((system) => system.service === 'admin');
if (admin && admin.access) {
  showBackMange.value = true;
}
</script>

<style scoped lang="less">
:deep(.el-dropdown-menu__item) {
  &:hover {
    color: #1973cb !important;
    font-weight: bold;
    background-color: #f5f5f5 !important;
  }
  .active {
    color: #1973cb !important;
    font-weight: bold;
  }
}

// 额外的样式确保覆盖Element Plus默认样式
:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item:hover {
    color: #1973cb !important;
    background-color: #f5f5f5 !important;
  }
}
.active {
  color: var(--color-theme-project) !important;
  font-weight: bold;
}
.header-wrapper {
  // 重写element变量
  --el-color-primary: var(--color-theme-project);
  
  // 重写el-dropdown的css变量
  --el-dropdown-menuItem-hover-fill: #f5f5f5 !important;
  --el-dropdown-menuItem-hover-color: #1973cb !important;

  display: flex;
  justify-content: center;
  box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
  white-space: nowrap;
  color: var(--color-black);
  font-family: var(--text-family);

  .header {
    transition: width 0.5s ease-in-out;
    width: var(--width-fixed--project);
    padding: 0 10px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-left {
      display: flex;
      align-items: center;

      .logo {
        color: var(--color-theme-project);
        font-feature-settings: 'kern';
        margin-right: 56px;
        cursor: pointer;

        .chinese {
          font-family:
            'Alimama FangYuanTi VF SemiBold', 'Alimama FangYuanTi VF Regular',
            'Alimama FangYuanTi VF', sans-serif;
          // TODO: 未生效
          font-weight: 600;
          font-size: var(--fontsize-large-header);
          text-align: center;
        }

        .eng {
          font-family: 'Alimama FangYuanTi VF', sans-serif;
          font-weight: 400;
          font-size: var(--fontsize-mini-header);
          // TODO: 浏览器貌似有最小字号的限制，怎么改都是12px的
        }
      }

      .list {
        display: flex;
        align-items: center;

        span {
          // margin-left: 40px;
          cursor: pointer;
          color: var(--color-black);
          font-size: var(--fontsize-primary-header);

          &:hover {
            color: var(--color-theme-project);
          }
        }

        .icon-wrapper {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;

          &.active {
            color: var(--color-theme-project);
            // color: rgb(120, 26, 31);
          }

          &.inactive {
            color: var(--color-inactive-project);
            // color: rgb(26, 120, 101);
            // color:
          }

          &:hover {
            color: var(--color-theme-project);
          }
        }
      }

      .el-input {
        width: 600px;
      }

      :deep(.el-input__inner) {
        font-size: var(--fontsize-small-header);
      }

      :deep(.el-input__inner::placeholder) {
        font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 12px;
        color: #999999;
        text-align: left;
      }
    }

    .header-right {
      display: flex;
      align-items: center;

      .list {
        display: flex;
        align-items: center;

        span {
          // margin-left: 40px;
          cursor: pointer;
          color: var(--color-black);
          font-size: var(--fontsize-primary-header);

          &:hover {
            color: var(--color-theme-project);
          }
        }

        .icon-wrapper {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;

          &.active {
            color: var(--color-theme-project);
            // color: rgb(120, 26, 31);
          }

          &.inactive {
            color: var(--color-inactive-project);
            // color: rgb(26, 120, 101);
            // color:
          }

          &:hover {
            color: var(--color-theme-project);
          }
        }
      }

      .avatar-wrapper {
        margin-left: 40px;
      }
    }
  }
  .wideScreen {
    width: 100%;
  }
}
.avatar-wrapper {
  :deep(.el-avatar) {
    border: 2px solid rgba(255, 255, 255, 0.5);
    transition: border-color 0.3s ease;
  }

  :deep(.el-avatar:hover) {
    border-color: var(--color-theme-project);
  }
}
</style>
