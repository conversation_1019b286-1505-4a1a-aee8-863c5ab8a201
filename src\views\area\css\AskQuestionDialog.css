.ask-dialog-main ::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}
.ask-dialog-main ::-webkit-scrollbar-thumb {
  background: #e0e0e0;
  border-radius: 8px;
  min-height: 40px;
}
.ask-dialog-main ::-webkit-scrollbar-thumb:hover {
  background: #bdbdbd;
}
.ask-dialog-main ::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}
.ask-dialog-main {
  display: flex;
  height: 600px;
  width: 1400px;
  overflow: hidden;
  /* 防止内容溢出 */
}
.ask-dialog-left {
  flex: 2.2;
  padding: 16px 16px 0 0;
  overflow-y: auto;
  border-right: 1px solid #eee;
}
.ask-dialog-right {
  flex: 0.8;
  min-width: 320px;
  max-width: 420px;
  padding: 16px 0 0 16px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: visible;
}
.desc {
  margin-bottom: 12px;
  color: #888;
  font-size: 14px;
  display: flex;
  align-items: center;
}
.content-area {
  overflow-y: auto;
  max-height: 520px;
  position: relative;
}
.content-block {
  margin-bottom: 18px;
}
.content-time {
  font-size: 13px;
  color: #888;
  margin-bottom: 2px;
}
.content-text {
  font-size: 15px;
  color: #222;
}
.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.question-search {
  width: 180px;
}
.question-list {
  flex: 1;
  overflow-y: auto;
  border-top: 1px solid #eee;
  margin-top: 8px;
  padding-top: 8px;
  width: 300px;
}
.question-item {
  font-size: 14px;
  margin-bottom: 8px;
  display: flex;
  gap: 8px;
  align-items: center;
}
.question-time {
  color: #888;
  min-width: 48px;
  font-family: monospace;
}
.question-content {
  color: #333;
}
.question-keyword {
  color: #333;
  font-weight: bold;
  margin-right: 2px;
}
.question-input {
  width: 100%;
  margin-bottom: 8px;
}
/* 划词相关样式 */
/* 问号图标样式 */
.question-icon {
  cursor: pointer;
  position: relative;
  animation: questionIconFadeIn 0.2s ease-in-out;
}
.question-icon-circle {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.question-icon-circle img {
  width: 16px;
  height: 16px;
}
.question-icon:hover .question-icon-circle {
  background: #f2f2f2;
}
/* 悬浮提示样式 */
.question-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  background: #666666;
  color: white;
  padding: 2px 5px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}
.question-icon:hover .question-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%);
}
/* 问号图标淡入动画 */
@keyframes questionIconFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
/* 重置滚动条样式 */
::webkit-scrollbar-track {
  background: transparent !important;
  /* 或者 background: none; 效果一样 */
}
/* ======= 统一弹窗全覆盖样式，参考 PrjVideoWrapper ======= */
.ask-dialog-right :deep(.question-drawer-wrapper),
.ask-dialog-right :deep(.answer-drawer-wrapper) {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 88% !important;
  height: 100% !important;
  background: #fff;
  border-radius: 8px;
  /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); */
  z-index: 2000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
/* ======= 移除/注释掉所有 position: static、width: 80%、height: auto 等样式 ======= */
/* .ask-dialog-right :deep(.question-drawer-wrapper) { ... }  <-- 已被上方统一样式覆盖，无需重复 */
/* .ask-dialog-right :deep(.answer-drawer-wrapper) { ... }  <-- 已被上方统一样式覆盖，无需重复 */
/* .ask-dialog-right :deep(.question-drawer-wrapper .body) { ... }  <-- 移除 */
/* .ask-dialog-right :deep(.answer-drawer-wrapper .drawer-body) { ... }  <-- 移除 */
.ask-dialog-right :deep(.question-drawer-wrapper .header) {
  margin-bottom: 16px;
  padding: 0;
}
.ask-dialog-right :deep(.question-drawer-wrapper .question-container) {
  width: 100% !important;
  height: auto !important;
  margin: 10px 0;
}
.ask-dialog-right :deep(.answer-drawer-wrapper .header) {
  margin-bottom: 16px;
  padding: 0;
}
.drawer-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 20;
  pointer-events: none;
  /* 让弹窗始终和右侧区域一样高 */
  padding: 0;
}
.drawer-layer > * {
  pointer-events: auto;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.ask-dialog-right :deep(.el-dialog__headerbtn),
.ask-dialog-right :deep(.close-btn),
.ask-dialog-right :deep(.drawer-close-btn) {
  position: absolute;
  top: 18px;
  right: 18px;
  z-index: 2100;
  background: #f5f5f5;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: background 0.2s;
}
.ask-dialog-right :deep(.el-dialog__headerbtn:hover),
.ask-dialog-right :deep(.close-btn:hover),
.ask-dialog-right :deep(.drawer-close-btn:hover) {
  background: #e0e0e0;
}
.floating-video {
  position: absolute;
  right: 16px;
  bottom: 16px;
  width: 320px;
  height: 180px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 20;
}
/* ==== TestWrapper.vue 结构化题目展示样式 ==== */
.qt-detail {
  padding: 20px;
  background-color: #ffffff;
  min-height: 560px;
  margin: 12px;
  border-width: 1px;
  border-style: solid;
  border-color: #dcdfe6;
  border-radius: 8px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.qt-detail .main-content {
  margin-top: 15px;
  width: 100%;
  background-color: #ffffff;
}
.qt-detail .main-content .content {
  padding-left: 31px;
  padding-right: 31px;
  margin-top: 15px;
}
.qt-detail .main-content .content-question {
  display: flex;
  flex-wrap: wrap;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 14px;
  font-weight: 700;
}
.qt-detail .main-content .choice {
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-between;
}
.qt-detail .main-content .description {
  margin-top: 20px;
  white-space: pre-wrap;
}
.qt-detail .answer-wrap {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 10px;
  max-height: 235px;
  overflow-y: auto;
}
.qt-detail .answer-wrap .answer {
  width: 92%;
}
.qt-detail .answer-wrap .answer .detail {
  width: 100%;
  border: 1px solid #f2f2f2;
  border-radius: 4px;
  margin-top: 4px;
  padding-left: 25px;
  padding-right: 16px;
  font-size: 14px;
}
.qt-detail .answer-wrap .answer .detail .choice {
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-between;
}
.qt-detail .answer-wrap .answer .detail .description {
  margin-top: 20px;
  white-space: pre-wrap;
}
/* ==== End TestWrapper.vue 样式 ==== */
/* 大纲和问题列表同级，宽度合适 */
.outline-and-questions {
  display: flex;
  flex-direction: column;
  width: 300px;
  min-width: 220px;
  max-width: 350px;
}
.outline-content {
  max-height: 550px;
  /* 可根据实际需求调整 */
  overflow-y: auto;
  padding: 0;
  box-sizing: border-box;
  background: #fff;
  /* border-right: 1px solid #eee; */
}
.no-outline {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  padding: 20px;
  min-height: 100px;
}
.paragraph-wrapper {
  margin-bottom: 10px;
}
.paragraph-wrapper .time {
  width: 70px;
  font-weight: 600;
  color: #333333;
  font-size: 12px;
  margin-bottom: 5px;
  user-select: none;
}
.paragraph-wrapper .time:hover {
  cursor: pointer;
  color: var(--color-theme-project);
}
.paragraph-wrapper .text:deep(.highlight2) {
  background-color: yellow;
  cursor: pointer;
  font-weight: 700;
}
.paragraph-wrapper .text:deep(.text-hover) {
  color: var(--color-theme-project);
  cursor: pointer;
}
.paragraph-wrapper .text:deep(.text-hover:hover) {
  font-weight: 700;
}
.el-dialog {
  margin-top: calc((100vh - 660px) / 2) !important;
  margin-bottom: calc((100vh - 660px) / 2) !important;
}
