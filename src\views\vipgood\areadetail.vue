<template>
  <div class="wrapper">
    <div class="header">
      <img class="backBtn" @click="handleBack()" src="@/assets/images/prjlearn/u4508.svg" />
      <AreaInfo></AreaInfo>
    </div>
    <div class="main">
      <!-- 预留的组件 -->
      <div class="left"></div>
      <!-- 右侧知识列表 -->
      <div class="right">
        <div class="top">
          <span>领域知识</span>
          <el-checkbox-group v-model="selectedStatus" @change="handleCheckboxChange">
            <el-checkbox label="2">未学习</el-checkbox>
            <el-checkbox label="0">已学习</el-checkbox>
            <el-checkbox label="1">已掌握</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="progress">
          <div class="learn-footer-info percentage" v-if="learnedPct || learnedPct == 0">
            <el-progress
              :percentage="learnedPct"
              :color="`--color-theme-project`"
              stroke-linecap="square"
              status="warning"
            />
            <span>已学习：{{ areaInfo.countLearned }} / {{ areaInfo.countKnowledgePoint }}</span>
          </div>
          <div class="learn-footer-info percentage" v-if="graspKlgPct || graspKlgPct == 0">
            <el-progress
              :percentage="graspKlgPct"
              :color="`--color-theme-project`"
              stroke-linecap="square"
              status="success"
            />
            <span>已掌握：{{ areaInfo.countMastered }} / {{ areaInfo.countKnowledgePoint }}</span>
          </div>
        </div>
        <div class="list-item" style="text-align: center">
          <span style="text-align: center" v-if="total == 0">暂无数据</span>
        </div>
        <div class="list">
          <div
            class="list-item"
            v-for="item in klglist"
            :key="item.klgCode"
            :class="item.klgCode == checkedpre ? 'clickedClass' : 'defaultClass'"
            @click="pickKlg(item)"
            :style="{
              backgroundColor: item.learningState == '1' ? '#67C23A' : '',
              border: item.learningState != '2' ? '1px solid rgba(255, 221, 108, 1)' : ''
            }"
          >
            <span class="item1" v-html="item.klgTitle"></span>
          </div>
          <div class="pagination-block">
            <el-pagination
              v-model:current-page="currentPage"
              :page-size="limit"
              small
              background
              layout="prev, pager, next"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AreaInfo from './components/AreaInfo.vue';
import { AreaData, klgData } from '@/types/area';
import { getAreaGoodApi } from '@/apis/area';

const route = useRoute();
const router = useRouter();

const learnedPct = ref(0);
const graspKlgPct = ref(0);
//todo
const notlearnPct = ref();
const klgNumber = ref(0);

const selectedStatus = ref(['0', '1', '2']);
const lastSelect = ref('');

const handleCheckboxChange = (newStatus: string[]) => {
  if (newStatus.length < 1) {
    selectedStatus.value[0] = lastSelect.value;
    alert('请至少选择一个状态！');
  } else if (newStatus.length == 1) {
    lastSelect.value = newStatus[0];
    getAreaGood(currentPage.value, newStatus, spuId);
  } else {
    selectedStatus.value = newStatus;
    getAreaGood(currentPage.value, newStatus, spuId);
  }
};

const handleBack = () => {
  router.push('/home');
};

//知识列表

const areaInfo = ref<AreaData>({
  goodsName: '',
  goodsDescribe: '',
  createTime: '',
  countLearned: 0,
  countMastered: 0,
  countKnowledgePoint: 0,
  editorName: '',
  editorPic: '',
  knowledgePointPage: {
    current: 0,
    limit: 0,
    total: 0,
    records: [{ klgCode: '', klgTitle: '', learningState: '' }]
  }
});
provide('areaInfo', areaInfo);

let currentPage = ref(1);
const limit = 10;
const total = ref(0);
const klglist = ref<klgData[]>([]);
let checkedpre = ref('0');
//const isDataLoaded = ref(false);

const spuId = route.query.spuId as string;

// 获取知识列表
const getAreaGood = async (current: number, queryType: string[], spuId: string) => {
  const res = await getAreaGoodApi(current, queryType, spuId);
  areaInfo.value = res.data;

  klglist.value = res.data.knowledgePointPage.records;
  total.value = res.data.knowledgePointPage.total;
};

//点击知识
function pickKlg(item: any) {
  //checkedpre.value = item.klgCode;
  const { href } = router.resolve({
    path: '/klgdetail',
    query: {
      klgCode: item.klgCode
    }
  });
  window.open(href, '_blank');
}

const handleCurrentChange = async (val: number) => {
  getAreaGood(val, selectedStatus.value, spuId);
};
// 初始化
const InitData = async () => {
  currentPage.value = 1;
  getAreaGood(currentPage.value, selectedStatus.value, spuId);
};
//InitData();

onMounted(async () => {
  currentPage.value = 1;
  const res = await getAreaGoodApi(currentPage.value, selectedStatus.value, spuId);
  areaInfo.value = res.data;
  console.log('991', areaInfo.value);

  klglist.value = res.data.knowledgePointPage.records;
  total.value = res.data.knowledgePointPage.total;
  klgNumber.value = areaInfo.value.countKnowledgePoint;
  // 已经学习的比例
  learnedPct.value =
    klgNumber.value == 0 ? 0 : Math.floor((areaInfo.value.countLearned / klgNumber.value) * 100);
  // 已经掌握的比例
  graspKlgPct.value =
    klgNumber.value == 0 ? 0 : Math.floor((areaInfo.value.countMastered / klgNumber.value) * 100);
  console.log('123', klgNumber, learnedPct.value, graspKlgPct.value);
});
</script>
<style scoped lang="less">
.backBtn {
  cursor: pointer;
  margin: 12px 16px 12px 0px; // 因为要中点对齐18px的标题，所以上下各加2px
  height: 14px;
  width: 15px;
}
.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  .header {
    display: flex;
    width: 85%;
    height: 75px;
    margin-top: 20px;
    margin-bottom: 5px;
  }
  .main {
    display: flex;
    width: 80%;
    justify-content: center;
    height: 600px;
    .left {
      flex: 3;
      background-color: #ddd8d8;
    }
    .right {
      flex: 2;
      padding-left: 10px;
      .top {
        display: flex;
        justify-content: space-between;
        height: 25px;
        font-size: 16px;
        font-weight: 700;

        .el-checkbox-group {
          display: flex;
          .el-checkbox__label {
            font-size: 13px;
          }
        }
      }
      .progress {
        height: 60px;
        margin-top: 20px;
        .learn-footer-info {
          margin-top: 10px;
          display: flex;
          align-items: center;
          white-space: no-wrap;

          .el-progress--line {
            width: 332px;
            .el-progress__text {
              width: 25px;
            }
          }
        }
        .percentage {
          font-size: 14px;
          font-weight: 400;
          color: #333;
          white-space: nowrap;
        }
      }
      .list {
        height: 500px;

        .clickedClass {
          background-color: #1973cb;
          color: #fff;
        }

        .defaultClass {
          background-color: #ffff;
        }

        .list-item {
          height: 32px;
          border: 1px solid #eee;
          margin-top: 5px;
          line-height: 32px;
          text-indent: 5px;
          display: flex;
          justify-content: space-between;
          padding-right: 15px;
          width: 450px;
          .item1 {
            font-size: 14px;
            font-weight: 700;

            white-space: nowrap;
            overflow: hidden;

            :deep(p) {
              display: inline;
            }
          }

          .item2 {
            font-size: 12px;
            font-weight: 400;
          }
        }

        .pagination-block {
          display: flex;
          width: 100%;
          flex-direction: row;
          justify-content: center;
          margin-top: 35px;
        }
      }
    }
  }
}
</style>
