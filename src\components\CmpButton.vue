<template>
  <button class="btn" :class="[type]" @click="handleClick">
    <slot></slot>
  </button>
</template>

<script setup lang="ts">
defineProps<{
  type?: string; // type 为info和primary类型
}>();

const emit = defineEmits<{
  click: [event?: Event]
}>();

const handleClick = (event: Event) => {
  emit('click', event);
};
</script>

<style scoped lang="less">
.btn {
  // width: 130px;
  // height: 25px;
  font-family: var(--text-family);
  font-size: var(--fontsize-small-project);
  border-radius: 2px;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  // font-size: 12px;
  cursor: pointer;
  &.info {
    background-color: rgba(242, 242, 242, 1);
    border: 1px solid rgba(220, 223, 230, 1);
    color: var(--color-black);
  }
  &.primary {
    // border: none;
    color: rgba(255, 255, 255, 0.996);
    background-color: var(--color-theme-project);
    border: 1px solid transparent;
  }
  &:hover {
    border: 1px solid var(--color-theme-project);
    background-color: var(--color-second);
    color: var(--color-theme-project);
  }
}
</style>
