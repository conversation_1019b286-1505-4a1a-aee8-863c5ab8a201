<template>
  <div class="map-container">
    <div class="map">
      <KsgMap ref="ksgMap" v-model:config="config" :data="data" :fetchLevelData="fetchLevelData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import KsgMap from 'ksg-map';
import { getLevelData } from '@/apis/ksgmap';
import type { GlobalConfig, OriginalData, AreaData, PointData } from 'ksg-map/dist/types';

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: true
});
const data = ref<OriginalData>({
  topAreas: [],
  points: [],
  areas: [],
  focuses: []
});
const props = defineProps<{
  areaDetail: {
    title: string;
    areaCode: string;
    authorName: string;
    createTime: string;
    description: string;
  };
  areaCode: string;
}>();
onMounted(async () => {
  // TODO: prjId 之后改为 uniqueCode
  const points = (await getLevelData([props.areaCode], 0, 5)).data;
  await ksgMap.value!.ready;
  data.value = {
    topAreas: [],
    points,
    areas: [],
    focuses: []
  };
  ksgMap.value?.reloadData();
});
async function fetchLevelData(ids: string[], type: 'area' | 'focus', level: number, limit: number) {
  return (await getLevelData([props.areaCode], level, limit)).data;
}
</script>

<style scoped>
.map-container {
  flex: 1;

  .map {
    overflow: hidden;
    height: 675px;
    width: 1200px;
    margin-bottom: 20px;
    /* border: 1px solid red; */
  }
}
</style>
