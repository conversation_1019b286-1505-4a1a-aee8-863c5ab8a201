import { uploadImage<PERSON>pi } from '@/apis/upLoad';
class MyUploadAdapter {
  constructor(loader) {
    this.loader = loader;
  }
  // 启动上载过程
  // upload() {
  //     const data = new FormData();
  //     data.append("upload",this.loader.file);
  //     uploadImage4CkEditor(data).then( res => {
  //         return {
  //             default:res.data.url
  //         }
  //     })
  // }
  async upload() {
    const data = new FormData();
    data.append('file', await this.loader.file);
    const res = await uploadImageApi(data);
    return {
      default: res.data.Image
    };
  }
  // 中止上载过程
  abort() {
    if (this.xhr) {
      this.xhr.abort();
    }
  }
}
export default MyUploadAdapter;
