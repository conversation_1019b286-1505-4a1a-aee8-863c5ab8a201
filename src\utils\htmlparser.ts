import { Parser } from 'htmlparser2';
import { <PERSON><PERSON><PERSON><PERSON>, type ChildN<PERSON> } from 'domhandler';

let globalDom: ChildNode[] = [];
const handler = new DomHandler((error, dom) => {
  if (error) {
    console.log(error);
    globalDom = [];
  } else {
    globalDom = dom;
  }
});

const parser = new Parser(handler);

export default parser;

export function parseHtml(html: string) {
  parser.parseComplete(html);
  return globalDom;
}
