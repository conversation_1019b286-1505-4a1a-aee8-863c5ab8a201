<template>
  <div class="map-content" style="margin-top: 20px">
    <!-- KsgMap 组件容器 -->
    <div class="ksg-map-container">
      <KsgMap
        ref="ksgRef"
        :config="config"
        @load-more="handleLoadMore"
        :loading="loading"
        @click-label="handleClickLabel"
        class="ksgmap"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { projectGraph } from '@/apis/ksgmap';
import { KsgMap } from '@endlessorigin/KsgMap';
import { MODE, type Options } from '@endlessorigin/KsgMap';

// 定义 props
interface Props {
  spuId: string;
}

const props = defineProps<Props>();

// KsgMap 相关变量
const ksgRef = ref<any>();
let loading = ref<'loading' | 'loaded' | 'error'>('loading');

// KsgMap 配置项
const config: Options = {
  model: MODE.MULTIPLE_ROOT, //Single_ROOT单根节点模,多根知识点场景(MODE.MULTIPLE_ROOT)，
  // 配置加载更多参数
  pointsLevelPager: {
    current: 1, //当前层级（从1层开始，默认第一层）
    levelSize: 2 //每次加载层数
  }
};
// 存储知识图谱数据的响应式变量
const ksgMapData = ref<any>(null);
const dataList = ref('');
const total = ref(0);
const rootid = ref(0);

// 初始化 KsgMap
const init = async () => {
  loading.value = 'loading';
  // 设置数据
  dataList.value = ksgMapData.value.records;
  console.log('dataList.value:', dataList.value);
  total.value = ksgMapData.value.total;
  rootid.value = 0;

  // 初始化 KsgMap - 使用 firstLoadPointsData 方法
  ksgRef.value?.firstLoadPointsData(dataList.value, total.value, rootid.value);
  loading.value = 'loaded';
};

watch(
  () => props.spuId,
  async (newSpuId) => {
    if (newSpuId) {
      // 调用 projectGraph API，不传递 newChapterId 参数（传递空字符串）
      const pointsData = await projectGraph(newSpuId, '', 0, 10);

      // 将数据存储到响应式变量中
      ksgMapData.value = pointsData;
      await init();
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="less"></style>
