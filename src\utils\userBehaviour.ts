import { PrjForm } from '@/types/project';
import type { Ref } from 'vue';
import { ref } from 'vue';
import { saveLearnProcessApi } from '@/apis/learning';
const TIMEOUT = 5000;
import { useRoute } from 'vue-router';
import { onBeforeUnmount, onMounted } from 'vue';
export default function userBehaviour(
  prjForm: PrjForm,
  activeIndex?: Ref<number> | Ref<null>,
  curChapterId?: Ref<number> | Ref<null>,
  playerTime?: Ref<number>
) {
  activeIndex = activeIndex || ref(null);
  curChapterId = curChapterId || ref(null);
  playerTime = playerTime || ref(0);
  const route = useRoute();
  const spuId = route.query.spuId as string;
  const startTimeMap = new Map<string, any>();
  const startTimers = new Map<string, any>();
  const endTimers = new Map<string, any>();
  const playerEndTime = new Map<string, any>();
  const handleVisible = (e: any) => {
    if (prjForm == PrjForm.video) {
      switch (e.target.visibilityState) {
        case 'hidden':
          if (!startTimers.has(String(curChapterId.value))) {
            playerEndTime.set(String(curChapterId.value), playerTime.value);
            endTimers.set(
              String(curChapterId.value),
              setTimeout(
                async (
                  spuId: string,
                  processStartTime: number,
                  processEndTime: number,
                  processSectionId: string | number,
                  endTimeTag?: number
                ) => {
                  await saveLearnProcessApi(
                    false,
                    spuId,
                    processStartTime,
                    processEndTime,
                    processSectionId,
                    endTimeTag
                  );
                  endTimers.delete(String(processSectionId));
                  startTimeMap.delete(String(processSectionId));
                  playerEndTime.delete(String(processSectionId));
                },
                TIMEOUT,
                spuId,
                startTimeMap.get(String(curChapterId.value)),
                Date.now(),
                curChapterId.value,
                playerEndTime.get(String(curChapterId.value))
              )
            );
          } else {
            clearTimeout(startTimers.get(String(curChapterId.value)) as NodeJS.Timeout);
            startTimers.delete(String(curChapterId.value));
          }
          break;
        case 'visible':
          if (!endTimers.has(String(curChapterId.value))) {
            startTimers.set(
              String(curChapterId.value),
              setTimeout(
                (time, chapterId) => {
                  startTimeMap.set(chapterId, time);
                  startTimers.delete(chapterId);
                },
                TIMEOUT,
                Date.now(),
                curChapterId.value
              )
            );
          } else {
            clearTimeout(endTimers.get(String(curChapterId.value)) as NodeJS.Timeout);
            endTimers.delete(String(curChapterId.value));
          }
          break;
      }
    } else if (prjForm == PrjForm.draft) {
      switch (e.target.visibilityState) {
        case 'hidden':
          if (!startTimers.has(String(curChapterId.value))) {
            endTimers.set(
              String(curChapterId.value),
              setTimeout(
                async (
                  parentSpuId: string,
                  processStartTime: number,
                  processEndTime: number,
                  processSectionId: string | number
                ) => {
                  await saveLearnProcessApi(
                    false,
                    parentSpuId,
                    processStartTime,
                    processEndTime,
                    processSectionId
                  );
                  endTimers.delete(String(processSectionId));
                  startTimeMap.delete(String(processSectionId));
                },
                TIMEOUT,
                spuId,
                startTimeMap.get(String(curChapterId.value)),
                Date.now(),
                curChapterId.value
              )
            );
          } else {
            clearTimeout(startTimers.get(String(curChapterId.value)) as NodeJS.Timeout);
            startTimers.delete(String(curChapterId.value));
          }
          break;
        case 'visible':
          if (!endTimers.has(String(curChapterId.value))) {
            startTimers.set(
              String(curChapterId.value),
              setTimeout(
                (time, chapterId) => {
                  startTimeMap.set(String(chapterId), time);
                  startTimers.delete(String(chapterId));
                },
                TIMEOUT,
                Date.now(),
                curChapterId.value
              )
            );
          } else {
            clearTimeout(endTimers.get(String(curChapterId.value)) as NodeJS.Timeout);
            endTimers.delete(String(curChapterId.value));
          }
          break;
      }
    }
  };
  const handleChapterChange = async (chapterId: number) => {
    if (prjForm == PrjForm.video) {
      if (activeIndex.value != chapterId) {
        if (startTimers.has(String(activeIndex.value))) {
          clearTimeout(startTimers.get(String(activeIndex.value)) as NodeJS.Timeout);
          startTimers.delete(String(activeIndex.value));
        } else {
          playerEndTime.set(String(activeIndex.value), playerTime.value);
          endTimers.set(
            String(activeIndex.value),
            setTimeout(
              async (
                spuId: string,
                processStartTime: number,
                processEndTime: number,
                processSectionId: string | number,
                endTimeTag?: number
              ) => {
                await saveLearnProcessApi(
                  false,
                  spuId,
                  processStartTime,
                  processEndTime,
                  processSectionId,
                  endTimeTag
                );
                endTimers.delete(String(processSectionId));
                startTimeMap.delete(String(processSectionId));
                playerEndTime.delete(String(processSectionId));
              },
              TIMEOUT,
              spuId,
              startTimeMap.get(String(activeIndex.value)),
              Date.now(),
              activeIndex.value,
              playerEndTime.get(String(activeIndex.value))
            )
          );
        }
        if (!endTimers.has(String(chapterId))) {
          startTimers.set(
            String(chapterId),
            setTimeout(
              (time) => {
                startTimeMap.set(String(chapterId), time);
                startTimers.delete(String(chapterId));
              },
              TIMEOUT,
              Date.now()
            )
          );
        } else {
          clearTimeout(endTimers.get(String(chapterId)) as NodeJS.Timeout);
          endTimers.delete(String(chapterId));
        }
      }
    } else if (prjForm == PrjForm.draft) {
      if (activeIndex.value != chapterId) {
        if (startTimers.has(String(activeIndex.value))) {
          clearTimeout(startTimers.get(String(activeIndex.value)) as NodeJS.Timeout);
          startTimers.delete(String(activeIndex.value));
        } else {
          endTimers.set(
            String(activeIndex.value),
            setTimeout(
              async (
                spuId: string,
                processStartTime: number,
                processEndTime: number,
                processSectionId: string | number
              ) => {
                await saveLearnProcessApi(
                  false,
                  spuId,
                  processStartTime,
                  processEndTime,
                  processSectionId
                );
                endTimers.delete(String(processSectionId));
                startTimeMap.delete(String(processSectionId));
              },
              TIMEOUT,
              spuId,
              startTimeMap.get(String(activeIndex.value)),
              Date.now(),
              activeIndex.value
            )
          );
        }
        if (!endTimers.has(String(chapterId))) {
          startTimers.set(
            String(chapterId),
            setTimeout(
              (time) => {
                startTimeMap.set(String(chapterId), time);
                startTimers.delete(String(chapterId));
              },
              TIMEOUT,
              Date.now()
            )
          );
        } else {
          clearTimeout(endTimers.get(String(chapterId)) as NodeJS.Timeout);
          endTimers.delete(String(chapterId));
        }
      }
    }
  };
  const init = (chapterId: string | null) => {
    startTimers.set(
      String(chapterId),
      setTimeout(
        (time, chapterId) => {
          startTimeMap.set(String(chapterId), time);
          startTimers.delete(String(chapterId));
        },
        TIMEOUT,
        Date.now(),
        chapterId
      )
    );
  };
  const leave = (useBeacon: boolean) => {
    if (prjForm == PrjForm.draft) {
      if (startTimeMap.has(String(curChapterId.value))) {
        saveLearnProcessApi(
          useBeacon,
          spuId,
          startTimeMap.get(String(curChapterId.value)) as number,
          Date.now(),
          curChapterId.value
        );
        startTimeMap.delete(String(curChapterId.value));
      }
      for (const key of startTimeMap.keys()) {
        saveLearnProcessApi(useBeacon, spuId, startTimeMap.get(key), Date.now(), key);
      }
    } else if (prjForm == PrjForm.video) {
      if (startTimeMap.has(String(curChapterId.value))) {
        saveLearnProcessApi(
          useBeacon,
          spuId,
          startTimeMap.get(String(curChapterId.value)) as number,
          Date.now(),
          curChapterId.value,
          playerTime.value
        );
        startTimeMap.delete(String(curChapterId.value));
      }
      for (const key of startTimeMap.keys()) {
        saveLearnProcessApi(
          useBeacon,
          spuId,
          startTimeMap.get(key),
          Date.now(),
          curChapterId.value,
          playerEndTime.get(key)
        );
      }
    }
  };
  const handleUnload = () => {
    leave(true);
  };
  onMounted(() => {
    document.addEventListener('visibilitychange', handleVisible);
  });
  onBeforeUnmount(() => {
    document.removeEventListener('visibilitychange', handleVisible);
    window.removeEventListener('beforeunload', handleUnload);
    leave(false);
  });
  window.addEventListener('beforeunload', handleUnload);
  return {
    initUserBehaviour: init,
    handleChapterChange
  };
}
