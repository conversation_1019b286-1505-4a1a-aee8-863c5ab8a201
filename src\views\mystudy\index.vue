<template>
  <div class="shop-container">
    <div class="shop-menu">
      <router-link
        to="map"
        class="shop-menu-item"
        :class="childPath == path.map ? 'pickStyle' : 'defaultStyle'"
        >我的知识图谱</router-link
      >
      <router-link
        to="question"
        class="shop-menu-item"
        :class="childPath == path.question ? 'pickStyle' : 'defaultStyle'"
        >我的学习提问</router-link
      >
      <router-link
        to="collect"
        class="shop-menu-item"
        :class="
          childPath == path.collect || childPath == path.detail ? 'pickStyle' : 'defaultStyle'
        "
        >收藏夹</router-link
      >
    </div>
    <div class="shop-list">
      <router-view v-slot="{ Component }">
        <keep-alive exclude="detail">
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
enum path {
  map = 'map',
  question = 'question',
  collect = 'collect',
  detail = 'detail'
}
const route = useRoute();
const childPath = ref(route.path.split('/')[2]);
watch(
  () => route.path,
  (newVal, oldVal) => {
    childPath.value = newVal.split('/')[2];
  }
);
</script>

<style scoped lang="less">
@width: 1400px;
@menuwidth: 300px;
@menuheight: 50px;
@fontfamily: var(--text-family);
@fontsize: 16px;
@bgdeepcolor: var(--color-theme-project);
@bglightcolor: #fff;
@bghovercolor: var(--color-second);
@margin: 10px;

.router-link-active {
  text-decoration: none;
  color: #fff;
}

a {
  text-decoration: none;
  color: #fff;
}

.hoverstyle {
  background-color: @bghovercolor;
  color: @bgdeepcolor;
  border: 1px solid @bgdeepcolor;
}

.shop-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-top: 2 * @margin;

  .shop-menu {
    .shop-menu-item {
      height: @menuheight;
      width: @menuwidth;
      font-family: @fontfamily;
      font-size: @fontsize;
      margin-bottom: @margin;
      display: flex;
      align-items: center;
      padding-left: @margin;
      cursor: pointer;
    }

    .pickStyle {
      background-color: @bgdeepcolor;
      color: #fff;
    }

    .defaultStyle {
      background-color: @bglightcolor;
      color: black;
    }

    .shop-menu-item:hover {
      .hoverstyle();
    }
  }
}
</style>
