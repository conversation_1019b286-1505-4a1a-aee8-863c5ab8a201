import { http } from '@/apis';

// 消息列表
export function getMessagelistApi(current?: number, limit?: number) {
  return http.request({
    method: 'post',
    url: '/system-message/page',
    data: {
      current,
      limit
    }
  });
}

//页面消息
export function getPageMessageApi() {
  return http.request<{
    list: Array<{
      publicTime: string;
      link: string;
      messageTitle: string;
      endTime: string;
      messageContent: string;
    }>;
  }>({
    method: 'get',
    url: '/page-message/getInfo'
  });
}
