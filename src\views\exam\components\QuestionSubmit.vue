<template>
  <div class="qt-detail">
    <!-- 头部 -->
    <div class="header">
      <div class="header-left">
        <CmpButton class="back-btn" type="primary" @click="back">返回</CmpButton>
        <h1>提问</h1>
      </div>
    </div>
    <div class="content">
      <AskQuestionCard :questionObj="questionObj"></AskQuestionCard>
    </div>
  </div>
</template>
<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import AskQuestionCard from './AskQuestionCard.vue';
import type { documentData } from '@/types/data';
// 返回
const emit = defineEmits(['back']);
const back = () => {
  emit('back');
};
// 从父组件获取题目具体信息
const props = defineProps(['questionObj']);
</script>

<style>
.highlight {
  background-color: #cde2e9;
  font-weight: bold;
}
</style>
<style lang="less" scoped>
.qt-detail {
  font-family: var(--text-family);
  color: var(--color-black);
  width: 1100px;
  padding-left: 10px;
  padding-bottom: 20px;

  .header {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      flex-direction: row;
      height: 100%;
      align-items: center;

      .back-btn {
        height: 56px;
        width: 80px;
        font-size: 16px;
      }

      h1 {
        margin-left: 18px;
        font-size: 20px;
        font-weight: 700;
        font-family: var(--title-family);
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    margin-top: 68px;
    margin-left: 62px;
    // justify-content: space-between;
  }
}
</style>
