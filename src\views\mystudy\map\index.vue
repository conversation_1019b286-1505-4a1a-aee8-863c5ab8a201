<template>
  <div class="maplist-container">
    <div class="maplist-header">
      <span class="maplist-title" @click="goBack">
        <img style="height: 10px; width: 10px" src="@/assets/images/myStudy/back.svg" />
        返回个人空间
      </span>
      <div class="maplist-search">
        <el-input
          v-model="searchInput"
          class="w-50 m-2"
          placeholder="请输入知识名称"
          :prefix-icon="Search"
          clearable
        />
      </div>
    </div>
    <div class="maplist-middle">
      我已经学习了{{ learnedKlg }}个知识点，我已经掌握了{{ graspKlg }}个知识点
    </div>
    <div class="maplist-bottom">
      <KsgMap ref="ksgMap"
        v-model:config="config"
        :data="data"
        :fetchAreaData="fetchAreaData"
        :fetchFocusData="fetchFocusData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
const learnedKlg = ref(0);
const graspKlg = ref(0);
function goBack() {
  router.push('/userspace');
}
let searchInput: any = ref('');

//================================ksg-map================================
import { getAreaData, getFocusData, getUserKlgGraph } from '@/apis/ksgmap';
import KsgMap from 'ksg-map'
import type { GlobalConfig, OriginalData, AreaData, PointData, FocusData } from 'ksg-map/dist/types';
const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null)
const target = ref<HTMLElement | null>(null)
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: false,
})
const data= ref<OriginalData>({
  topAreas: [],
  areas: [],
  focuses: [],
  points: [],
})
async function fetchAreaData(id: string): Promise<AreaData[]> {
  return await getAreaData(id);
}
async function fetchFocusData(id: string): Promise<FocusData> {
  return await getFocusData(id);
}
onMounted(async () => {
  const pointsData = (await getUserKlgGraph())
  await ksgMap.value?.ready;
  data.value.topAreas = []
  data.value.points = pointsData
  
  ksgMap.value?.reloadData();
})
//================================ksg-map================================

</script>

<style lang="less" scoped>
@titlefontsize: 18px;
@titlefontfamily: '黑体';
@headerfontfamily: '华文细黑';
@headerfontsize: 14px;
@margin: 10px;
@bgclor: #eee;
.maplist-container {
  display: flex;
  flex-direction: column;
  width: 1090px;
  padding-left: 2 * @margin;
  padding-top: 13px;
  .maplist-header {
    display: flex;
    justify-content: space-between;
    .maplist-title {
      font-family: @titlefontfamily;
      font-size: @headerfontsize;
      color: #005579;
      &:hover {
        font-weight: 700;
        cursor: pointer;
      }
    }
    .maplist-search {
      width: 225px;
    }
  }
  .maplist-middle {
    font-size: 12px;
  }
  .maplist-bottom {
    margin-top: 18px;
    margin-bottom: 44px;
    width: 100%;
    height: 751px;
    border: 1px solid red;
  }
}
</style>
