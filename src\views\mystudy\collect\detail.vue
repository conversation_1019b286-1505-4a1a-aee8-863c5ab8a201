<template>
  <div class="collect">
    <div class="collectup">
      <div class="backbuttom" @click="back">返回</div>
      <div class="collecttitle">收藏夹</div>
    </div>
    <div><el-divider style="margin-top: 3px; margin-bottom: 3px; border-width: 2px;"></el-divider></div>

          <div class="collectdown">
        <!-- 测试组件内容 -->
        <div v-if="collectdetail && collectdetail.exerciseId && collectdetail.stem" class="test-wrapper">

        <div class="qt-detail">
          <div class="main-content">
            <div class="title-wrapper">
              <div class="title" style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center;">
                  <span>{{ questionTypeText }}</span>
                  <el-icon style="font-size: 22px; margin-left: 8px; cursor: pointer;" @click="addCollect">
                    <StarFilled :style="{ color: isCollected ? '#FFD700' : '#C0C4CC' }" />
                  </el-icon>
                </div>
                <div style="display: flex; align-items: center; gap: 10px;margin-right: 10px;">
                  <el-button class="ask-btn" type="info" size="small" plain @click="handleAskQuestion" style="border: unset;">
                    <el-icon style="margin-right: 4px; border: unset;"><QuestionFilled /></el-icon>
                    有看不懂的地方点我提问题
                  </el-button>
                  <img 
                    src="@/assets/svgs/left.svg" 
                    class="nav-btn" 
                    :class="{ disabled: !hasPrevious }"
                    @click="hasPrevious && last()"
                    alt="上一个"
                  />
                  <img 
                    src="@/assets/svgs/right.svg" 
                    class="nav-btn" 
                    :class="{ disabled: !hasNext }"
                    @click="hasNext && next()"
                    alt="下一个"
                  />
                </div>
              </div>
            </div>
            <div>
              <el-divider style="border-width: 2px;"></el-divider>
            </div>

            <div class="content">
              <div class="content-question" v-html="collectdetail.stem"></div>
              <!-- 显示选项但不允许答题 -->
              <div v-if="collectdetail.type == ExerciseType.single">
                <el-form-item label="">
                  <el-radio-group class="selection-style">
                    <el-radio
                      :label="indexIntoAlpha(index)"
                      v-for="(selection, index) in collectdetail.content"
                      :key="index"
                    >
                      <span class="inline-label">
                        <span>{{ indexIntoAlpha(index) }}</span
                        ><span v-html="selection.text"></span> </span
                      ></el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div v-if="collectdetail.type == ExerciseType.multi">
                <el-form-item label="">
                  <el-checkbox-group class="selection-style">
                    <el-checkbox
                      :label="indexIntoAlpha(index)"
                      v-for="(selection, index) in collectdetail.content"
                      :key="index"
                    >
                      <span class="inline-label">
                        <span style="margin-right: 5px">{{ indexIntoAlpha(index) }}</span
                        ><span v-html="selection.text"></span>
                      </span>
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </div>
              <div v-if="collectdetail.type == ExerciseType.judge">
                <el-form-item>
                  <el-radio-group class="selection-style">
                    <el-radio value="1" disabled><span class="iconfont icon-duigou1"></span></el-radio>
                    <el-radio value="0" disabled><span class="iconfont icon-cuowu"></span></el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </div>
          </div>
          <!-- 直接显示答案 -->
          <div class="answer-section">
            <div class="answer-label">答案</div>
            <div class="answer-content">
              <template v-if="collectdetail.type == ExerciseType.judge">
                {{
                  collectdetail.answer.replace(/<\/?span[^>]*>/g, '') == '1' ? '正确' : '错误'
                }}
              </template>
              <template v-else-if="collectdetail.type == ExerciseType.multi || collectdetail.type == ExerciseType.single">
                <span v-html="JSON.parse(collectdetail.answer.replace(/<\/?span[^>]*>/g, '')).join(', ')"></span>
              </template>
              <template v-else>
                <span v-html="collectdetail.answer"></span>
              </template>
            </div>
            <div class="explanation-label">解析说明</div>
            <div class="explanation-content" v-html="collectdetail.explanation"></div>
          </div>
        </div>
        <AskQuestionDialog
          v-if="askDialogVisible"
          :exercise-info="collectdetail"
          :klg-code="klgCode"
          :transmit-spuId="transmitSpuId"
          :transmit-chapterId="transmitChapterId"
          :transmit-uniqueCode="transmitUniqueCode"
          :type="3"
          @close="askDialogVisible = false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="detail">
import ClassicEditor from '@/components/editors/Veditor.vue';
import CmpButton from '@/components/CmpButton.vue';
import {
  getCollectDetailDataApi,
  removeCollectDetailDataApi,
  cancelFavoritesApi,
  addFavoritesApi
} from '@/apis/collect';
import { getExerciseApi } from '@/apis/collect';
import type { exerciseItem } from '@/types/exercise';
import { useIdListStore } from '@/stores/idListStore';
import { ExerciseType } from '@/types/exercise';
import indexIntoAlpha from '@/utils/indexIntoAlpha';
import { ElMessage } from 'element-plus';
import { StarFilled, QuestionFilled } from '@element-plus/icons-vue';
import AskQuestionDialog from '@/views/klgdetail/AskQuestionDialog.vue';

const idListStore = useIdListStore();
const router = useRouter();
const route = useRoute();
//这里后续可能需要传递参数！！！回退都上一页
const back = async () => {
  // 在返回时调用接口，根据当前收藏状态决定调用哪个接口
  try {
    if (isCollected.value) {
      // 如果当前是收藏状态，调用添加收藏接口
      //await addFavoritesApi(collectdetail.value.exerciseId);
    } else {
      // 如果当前不是收藏状态，调用取消收藏接口
      await cancelFavoritesApi(collectdetail.value.exerciseId);
    }
  } catch (e) {
    console.error('收藏操作失败:', e);
    // 即使接口调用失败，也继续返回
  }
  
  router.push('/mystudy/collect');
};

const collectdetail = ref<exerciseItem>({
  exerciseId: '',
  type: 0, //1.单选 2多选 3填空 4判断
  stem: '', //题目
  content: [],
  answer: '', //答案
  explanation: '' //解释说明
});



const isCollected = ref(true);

const askDialogVisible = ref(false);

// 测试相关参数
const klgCode = ref('');
const transmitSpuId = ref('');
const transmitChapterId = ref('');
const transmitUniqueCode = ref('');
const isLastItem = ref(false);

const questionTypeText = computed(() => {
  switch (collectdetail.value.type) {
    case 1:
      return '单选题';
    case 2:
      return '多选题';
    case 3:
      return '填空题';
    case 4:
      return '判断题';
    default:
      return '全部题型';
  }
});
const idList = ref<string[]>([]);
const exerciseId = ref('');
const currentIndex = computed(() => idList.value.indexOf(exerciseId.value));
const hasPrevious = computed(() => currentIndex.value > 0);
const hasNext = computed(() => currentIndex.value < idList.value.length - 1);

// 保留必要的变量
const saveType = inject('saveType') as Ref;




watch(
  () => route.query.exerciseId,
  async (newVal) => {
    if (newVal) {
      exerciseId.value = newVal as string;
      const res = await getExerciseApi(exerciseId.value);
      collectdetail.value = res.data;
    }
  }
);
//获取题目的详细数据
const getCollectData = async (exerciseId: string) => {
  const res = await getExerciseApi(exerciseId);
  collectdetail.value = res.data;
  console.log('getCollectData result:', res.data);
};

onMounted(async () => {
  exerciseId.value = route.query.exerciseId as string;
  await getCollectData(exerciseId.value);

  idList.value = idListStore.getIdList;
});



const next = async () => {
  if (hasNext.value) {
    const nextId = idList.value[currentIndex.value + 1];
    await getCollectData(nextId);
    router.push({
      path: route.path,
      query: {
        ...route.query,
        exerciseId: nextId
      }
    });
  }
};
const last = async () => {
  if (hasPrevious.value) {
    const previousId = idList.value[currentIndex.value - 1];
    await getCollectData(previousId);
    router.push({
      path: route.path,
      query: {
        ...route.query,
        exerciseId: previousId
      }
    });
  }
};
//移除题目
//移除以后需要进行判断，如果有下一个就展示下一个页面，如果没有下一个就展示上一个页面，同时也要切换localstorage
const remove = async () => {
  await cancelFavoritesApi(exerciseId.value);
  next();
  last();
};




const addCollect = () => {
  // 只切换收藏状态，不调用接口
  isCollected.value = !isCollected.value;
  
  ElMessage({
    type: 'info',
    message: isCollected.value ? '已加入收藏夹' : '已取消收藏'
  });
};

function handleAskQuestion() {
  askDialogVisible.value = true;
}
</script>

<style lang="less" scoped>
.collect {
  width: 1100px;
  // height: 600px;
  margin-left: 10px;
  padding-left: 10px;
  padding-right: 18px;
  display: flex;
  flex-direction: column;

  .collectup {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-top: 8px;

    .backbuttom {
      width: 80px;
      height: 59px;
      background-color: var(--color-theme-project);
      border-radius: 4px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.996);
      line-height: normal;
      font-feature-settings: 'kern';
      border-width: 0.8px;
      border-style: solid;
      padding-top: 20px;
      padding-left: 26px;
      // font-size: var(--fontsize-large-project);
      cursor: pointer;
    }

    .backbuttom:hover {
      border-color: var(--color-theme-project);
      background-color: var(--color-second);
      color: var(--color-theme-project);
    }

    .collecttitle {
      font-size: 18px;
      font-weight: 700;
      color: #333333;
      line-height: normal;
      font-feature-settings: 'kern';
      margin-left: 16px;
    }
  }

  .collectdown {
    //margin-left: 50px;

    .left_operate {
      display: flex;
      //flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .title {
        width: 100%;
        height: 33px;
        line-height: 33px;
        font-size: 16px;
        font-weight: 700;
        //background-color: #fffff;
        padding-left: 10px;
        //border-bottom: 2px solid #f2f2f2;
      }
      .button-wrapper {
        //margin-left: auto;
        display: flex;
        .hover_style {
          //margin-left: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: normal;
          font-feature-settings: 'kern';
          width: 80px;
        }

        .hover_style:hover {
          color: #005579;
        }
      }
    }

    .collectdown_detail {
      margin-top: 8px;
      display: flex;
      flex-direction: column;

      .selection-style {
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        align-items: flex-start;
        max-height: 220px; /* 控制最大高度，设置合适的值 */
        overflow-y: auto; /* 添加竖向滚动条 */
        overflow-x: hidden;
        .inline-label {
          display: inline-flex;
          align-items: center;
        }

        .el-radio {
          width: 100%;
          margin-bottom: 5px;
          white-space: nowrap; /* 保证文字不换行 */
          :is(span) {
            // margin-left: 32px;
            margin-right: 5px;
            :deep(.el-radio__input) {
              display: none !important;
            }
          }
        }
        .el-checkbox {
          width: 100%;
          margin-bottom: 5px;
          white-space: nowrap; /* 保证文字不换行 */

          :deep(.el-checkbox__input) {
            display: none !important;
          }
        }
      }
      .answer-wrap {
        width: 100%;
        display: flex;
        //justify-content: center;
        margin-top: 10px;
        max-height: 235px;
        overflow-y: auto;

        .answer {
          width: 92%;

          .msg {
            height: 37px;
            display: flex;
            align-items: center;

            width: 100%;

            img {
              margin-left: 7px;
              margin-right: 7px;
            }
          }

          .correct-color {
            color: #67c23a;
            background-color: #f0f9eb;
          }

          .error-color {
            background-color: #fdf6ec;
            color: #e6a23c;
          }

          .detail {
            width: 100%;
            // border: 1px solid #f2f2f2;
            // border-radius: 4px;
            margin-top: 4px;
            //padding-left: 25px;
            padding-right: 16px;
            font-size: 14px;

            .choice {
              display: flex;
              flex-direction: row;
              width: 100%;
              justify-content: space-between;
            }

            .description {
              margin-top: 20px;
              white-space: pre-wrap;
            }
          }
        }
      }
      .problemtitle {
        //height: 33px;
        padding-top: 9px;
        //padding-left: 2px;
        // background-color: #f2f2f2;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: normal;
        font-feature-settings: 'kern';
      }

      .problem {
        display: flex;
        flex-direction: row;
        margin-top: 15px;
        justify-content: space-between;
      }
    }
    &:deep(.search-keys) {
      color: red;
      cursor: pointer;
      font-weight: 700;
    }

    &:deep(.activeElement) {
      background-color: #ff9632;
    }
  }
  // :deep(.highlight) {
  //   color: var(--color-theme-project);
  //   cursor: pointer;
  // }
  :deep(.highlightHover) {
    font-weight: 700;
    * {
      font-weight: 700;
    }
  }
  
  // TestWrapper 样式
  .test-wrapper {
    .hover_style {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      line-height: normal;
      font-feature-settings: 'kern';
      cursor: pointer;
    }

    .hover_style:hover {
      color: #005579;
    }

    .nav-btn {
      width: 12px;
      height: 15px;
      cursor: pointer;
      transition: opacity 0.2s ease;
      margin-right: 10px;
      
      &:hover {
        opacity: 0.8;
      }
      
      &.disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }
    }
    .header-wrapper,
    .left-header {
      width: 100% !important;
      max-width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      display: block !important;
    }
    .left-header {
      .title-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .title {
        font-size: 18px;
        font-weight: 600;
        margin: 10px 0px 10px 0;
      }
    }
    .qt-detail {
      padding: 8px;
      background-color: #ffffff;
      min-height: 450px;
      margin: 8px;
      border-width: 1px;
      //border-style: solid;
      border-color: rgba(220, 223, 230, 1);
      border-radius: 8px;
      -moz-box-shadow: none;
      -webkit-box-shadow: none;
      box-shadow: none;
      .main-content {
        margin-top: 15px;
        width: 100%;
        background-color: #ffffff;

        .title-wrapper {
          display: flex;
          .title {
            width: 100%;
            height: 3px;
            line-height: 3px;
            font-size: 16px;
            font-weight: 400;
            padding-left: 10px;
            font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
          }
          .add-collect {
            font-size: 14px;
            width: 100px;
            color: var(--color-theme-project);
            display: flex;
            justify-content: center;
            text-align: center;
            align-items: center;
            font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
          }
        }
        .content {
          padding-left: 31px;
          padding-right: 31px;
          margin-top: 15px;
          .content-question {
            display: flex;
            flex-wrap: wrap;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 16px;
            font-weight: 400;
            font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
          }
          .selection-style {
            display: flex;
            flex-direction: column;
            margin-top: 15px;
            align-items: flex-start;
            .inline-label {
              display: inline-flex;
              align-items: center;
              font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
              font-weight: 400;
              font-size: 15px;
            }
            .el-radio {
              width: 100%;
              margin-bottom: 5px;
              white-space: nowrap;
              span {
                margin-right: 5px;
              }
              // 确保禁用状态下文字不变灰
              &.is-disabled {
                .el-radio__label {
                  color: #333 !important;
                }
              }
            }
            .el-checkbox {
              width: 100%;
              margin-bottom: 5px;
              white-space: nowrap;
              // 确保禁用状态下文字不变灰
              &.is-disabled {
                .el-checkbox__label {
                  color: #333 !important;
                }
              }
            }
          }
          .replycontent-style {
            margin-top: 31px;
            margin-left: 32px;
            font-size: 14px;
            font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
            .replycontent-title {
              margin-bottom: 10px;
            font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
            }
          }
        }
      }
      .btns {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        .btn {
          width: 80px;
          height: 32px;
          margin-left: 40px;
          border-radius: 4px;
        }
      }
      .answer-check {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-top: 10px;
        align-items: center;
        .answer {
          width: 92%;
          font-size: 14px;
          font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
          .msg {
            height: 37px;
            display: flex;
            align-items: center;
            width: 100%;
            img {
              margin-left: 7px;
              margin-right: 7px;
            }
          }
          .correct-color {
            color: #67c23a;
            background-color: #f0f9eb;
          }
          .error-color {
            background-color: #fdf6ec;
            color: #e6a23c;
          }
          .detail {
            width: 100%;
            border: 1px solid #f2f2f2;
            border-radius: 4px;
            margin-top: 4px;
            padding-left: 25px;
            padding-right: 16px;
            font-size: 14px;
            font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
            .choice {
              display: flex;
              flex-direction: row;
              width: 100%;
              justify-content: space-between;
            }
            .description {
              margin-top: 20px;
              white-space: pre-wrap;
            }
          }
        }
      }
      .answer-wrap {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 10px;
        .answer {
          width: 92%;
          .msg {
            height: 37px;
            display: flex;
            align-items: center;
            width: 100%;
            img {
              margin-left: 7px;
              margin-right: 7px;
            }
          }
          .correct-color {
            color: #67c23a;
            background-color: #f0f9eb;
          }
          .error-color {
            background-color: #fdf6ec;
            color: #e6a23c;
          }
          .detail {
            width: 100%;
            border: 1px solid #f2f2f2;
            border-radius: 4px;
            margin-top: 4px;
            padding-left: 25px;
            padding-right: 16px;
            font-size: 14px;
            font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
            .choice {
              display: flex;
              flex-direction: row;
              width: 100%;
              justify-content: space-between;
            }
            .description {
              margin-top: 20px;
              white-space: pre-wrap;
            }
          }
        }
      }
      .answer-section {
        margin-top: 32px;
        padding-left: 32px;
        .answer-label, .explanation-label {
          margin: 24px 0 8px 0;
          font-size: 16px;
          font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
        }
        .answer-content, .explanation-content {
          font-size: 15px;
          color: #222;
          margin-bottom: 8px;
          line-height: 2;
          word-break: break-all;
          font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
        }
        .answer-label {
          margin-top: 0;
        }
      }
    }
  }
}
</style>
