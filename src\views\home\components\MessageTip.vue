<template>
  <div class="tip-wrapper" v-if="!isClose">
    <div class="tip">
      <!-- 新增了芯片设计领域，包含890个知识点。快来学习一起学习吧~ -->
      {{ content }}
      <span class="close" @click="closeContent">×</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getPageMessageApi } from '@/apis/message';
//import { tr } from 'element-plus/es/locale';
import { onMounted, onBeforeUnmount } from 'vue';
const isClose = ref(true);
const contentList = ref([]);
const content = ref('');
const timer = ref();
let i = 1;

onMounted(() => {
  getPageMessageApi().then((res) => {
    if (res.data.list.length != 0) {
      console.log(res.data.list);
      contentList.value = res.data.list;
      let localTime = localStorage.getItem('closetime');
      if (!localTime || new Date().getTime() > Number(localTime) + 3600000) {
        console.log('111');
        content.value = contentList.value[0].messageContent;
        showContent();
        isClose.value = false;
      }
    } else {
      isClose.value = true;
    }
  });
});

const showContent = () => {
  if (timer.value) clearInterval(timer.value);
  timer.value = setInterval(() => {
    console.log(contentList.value.length);
    content.value = contentList.value[i % contentList.value.length].messageContent;
    i += 1;
  }, 10000);
};

const closeContent = () => {
  isClose.value = true;
  localStorage.setItem('closetime', String(new Date().getTime()));
};

onBeforeUnmount(() => {
  if (timer.value) clearInterval(timer.value);
});
</script>

<style scoped lang="less">
.tip-wrapper {
  padding-left: 290px;
  padding-right: 160px;
  line-height: 40px;
  line-height: 40px;
  font-size: 14px;
  background-color: #ffd37a;
  font-family: var(--text-family);

  .tip {
    cursor: pointer;

    &:hover {
    }

    .close {
      float: right;
    }
  }
}
</style>
