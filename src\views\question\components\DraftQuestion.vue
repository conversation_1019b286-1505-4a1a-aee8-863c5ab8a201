<template>
  <div class="question-main textfont">
    <!-- 章节标题区域 - 全宽度 -->
    <div class="chapter-header titlefont">
      {{ chapterList[validIndexRef]?.chapterName }}
    </div>
    <el-divider class="divider" />

    <!-- 主内容区域 - 三栏布局 -->
    <div class="main-content-area">
      <!-- 本问题学习进度 -->

      <div class="theQuestionProgress">
        <div class="title titlefont">本问题学习进度</div>

        <div class="progress-circles">
          <div class="progress-item">
            <el-progress type="circle" :percentage="graspKlgPct" :width="56" :stroke-width="3" />
            <div class="progress-label">
              <span class="label-text textfont"
                >已掌握{{ curQuestion.masteredKlgCount || 0 }}/{{ curQuestion.klgCount || 0 }}
              </span>
            </div>
          </div>
          <div class="progress-item">
            <el-progress
              type="circle"
              :percentage="fullyGraspKlgPct"
              :width="56"
              :stroke-width="3"
            />
            <div class="progress-label">
              <span class="label-text textfont"
                >全掌握{{ curQuestion.fullyMasteredKlgCount || 0 }}/{{ curQuestion.klgCount || 0 }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 问题详情区域 -->
      <div class="question-content hover-scrollbar textfont">
        <div class="question">
          <div class="title">
            <div class="creator-name">{{ curQuestion.userName }}</div>
            的提问
          </div>
          <div class="description">
            <div class="question-info-row">
              <template v-if="curQuestion.questionType != '开放性问题'">
                <div
                  class="keyWords"
                  v-html="'【' + curQuestion.keyword + '】' + curQuestion.questionType + '?'"
                ></div>
              </template>
              <template v-else>
                <div class="keyWords" v-html="'【' + curQuestion.keyword + '】'"></div>
              </template>
              <div class="tags">
                <div v-if="curQuestion.questionNecessity == 1" class="tag1">必要</div>
                <div v-else class="tag1">参考</div>
                <div v-if="curQuestion.questionWeight == 1" class="tag2">私有</div>
                <div v-else class="tag2">公开</div>
              </div>
            </div>
          </div>
          <div class="bottom textfont">
            <div class="date">
              {{ curQuestion.createTime.split(' ')[0] }}
            </div>
            <div>
              <div v-if="curQuestion.canDelete" class="click-delete" @click="showDeleteConfirm()">
                删除
              </div>
            </div>
          </div>
        </div>

        <!-- 回答列表区域 -->
        <div class="answer-section">
          <div v-for="answer in curQuestion.answers" :key="answer.answerId" class="answer-list">
            <div class="answer-card">
              <span v-if="answer.pinToTop === 1" class="pin-badge">置顶</span>

              <div class="title textfont">
                <span class="name">
                  {{ answer.userName }}
                </span>
                <span v-if="answer.isAuthor" class="author-badge">作者</span>
                <span class="other">的回答</span>
              </div>
              <div class="klg-list">
                <div
                  class="klg-item"
                  v-for="item in answer.knowledgeList"
                  :key="item.code"
                  @click="goToDetail(item.code, item.type)"
                >
                  <el-tag class="klg-tag">
                    <div class="klg-tag-text" v-html="item.title"></div>
                  </el-tag>
                </div>
              </div>
              <div class="description">
                <div class="content" v-html="answer.answerExplanation"></div>
              </div>
              <div class="bottom">
                <div class="date">
                  {{ answer.createTime.split(' ')[0] }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：文档内容区域 -->
      <div class="right-content">
        <div class="content-wrapper hover-scrollbar textfont" style="padding-top: 0">
          <el-watermark :font="font" :content="userInfo.username + '@' + userInfo.phone">
            <!-- 文稿项目显示 -->
            <div v-if="prjForm === PrjForm.draft" id="underline" v-html="renderedContent" class=""></div>
            <!-- 视频项目显示 -->
            <div v-else-if="prjForm === PrjForm.video" class="video-content">
              <!-- 视频字幕内容 - 参考PrjVideoScript.vue的结构 -->
              <div class="content">
                <div id="underline">
                  <!-- 手动渲染段落结构，时间戳单独处理 -->
                  <div
                    v-for="(item, idx) in displayContent"
                    :key="`paragraph-${idx}`"
                    class="paragraph-wrapper textfont"
                  >
                    <div class="time titlefont" v-if="item.timeInfo && item.timeInfo.startTime">
                      {{ item.timeInfo.startTime }}
                    </div>
                    <div class="textfont renderItem">
                      <!-- 这里的内容会被render library处理和高亮 -->
                      <span v-html="item.htmlContent"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-watermark>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <el-dialog
      v-model="showDeleteDialog"
      top="30vh"
      width="400px"
      :before-close="handleCloseDeleteDialog"
    >
      <div class="delete-dialog-content">
        <p>一旦删除无法恢复，您确定要删除此问题？</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDeleteDialog">取消</el-button>
          <el-button type="danger" @click="confirmDeleteQuestion">确认删除</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, nextTick, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { getQuestionDetailApi, getPrjDetailApi, deleteQuestionApi } from '@/apis/learning';
import { KlgType } from '@/types/knowledge';
import { useUserStore } from '@/stores/user';
import { useRenderManager } from '@/composables/useRenderManager';
import { PrjForm } from '@/types/project';
import type { QuestionData } from '@/types/learning';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { ElMessage } from 'element-plus';

const route = useRoute();
const router = useRouter();
const spuId = route.query.spuId as string;
const curChapterId = ref(route.query.chapterId as string);
const curQuestionId = ref(route.query.questionId as string);
const chapterList = ref<any[]>([]);
const curQuestion = ref<any>({
  userName: '',
  keyword: '',
  questionType: '',
  questionDescription: '',
  createTime: '',
  learnedCount: 0,
  totalCount: 0,
  answers: []
});
const validIndexRef = ref(0);

// 项目类型相关
const prjForm = inject('prjForm') as Ref<PrjForm>;

// 问题列表数据
const questionList = ref<QuestionData[]>([]);

// 学习进度数据
const graspKlgPct = ref(0);
const fullyGraspKlgPct = ref(0);

// 删除确认弹窗控制
const showDeleteDialog = ref(false);

// 用户信息
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

// 右侧内容相关数据
const htmlString = ref('');
const videoCaptionList = ref<any[]>([]);
const font = reactive({
  color: 'rgba(0, 0, 0, .07)'
});

// 章节内容缓存
interface ChapterContentCache {
  chapterId: string;
  validIndex: number;
  chapterList: any[];
  wordContent?: string;
  videoCaptionList?: any[];
  timestamp: number;
}

const chapterContentCache = ref<Map<string, ChapterContentCache>>(new Map());
const CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5分钟缓存过期时间

// 清理过期缓存
const cleanExpiredCache = () => {
  const now = Date.now();
  const expiredKeys: string[] = [];

  chapterContentCache.value.forEach((cache, key) => {
    if (now - cache.timestamp >= CACHE_EXPIRY_TIME) {
      expiredKeys.push(key);
    }
  });

  expiredKeys.forEach((key) => {
    chapterContentCache.value.delete(key);
  });
};

// 检查缓存是否有效
const isCacheValid = (cache: ChapterContentCache): boolean => {
  const now = Date.now();
  return now - cache.timestamp < CACHE_EXPIRY_TIME;
};

// 从缓存获取章节内容
const getChapterContentFromCache = (chapterId: string): ChapterContentCache | null => {
  const cache = chapterContentCache.value.get(chapterId);
  if (cache && isCacheValid(cache)) {
    return cache;
  }
  return null;
};

// 缓存章节内容
const cacheChapterContent = (chapterId: string, data: any) => {
  const cache: ChapterContentCache = {
    chapterId,
    validIndex: data.validIndex,
    chapterList: data.chapterList,
    wordContent: data.chapterList[data.validIndex]?.wordContent,
    videoCaptionList: data.chapterList[data.validIndex]?.videoCaptionList,
    timestamp: Date.now()
  };
  chapterContentCache.value.set(chapterId, cache);
};

// 更新右侧内容（优化版本）
const updateContent = async (chapterId: string, forceRefresh = false) => {
  // 检查缓存
  if (!forceRefresh) {
    const cachedContent = getChapterContentFromCache(chapterId);
    if (cachedContent) {
      // 使用缓存数据
      validIndexRef.value = cachedContent.validIndex;
      chapterList.value = cachedContent.chapterList;

      if (prjForm.value === PrjForm.draft) {
        htmlString.value = cachedContent.wordContent || '';
      } else if (prjForm.value === PrjForm.video) {
        videoCaptionList.value = cachedContent.videoCaptionList || [];
      }

      // 重新初始化render实例
      await nextTick();
      if (curQuestion.value && curQuestion.value.associatedWords) {
        questionList.value = [curQuestion.value];
      }
      await reinitializeRender();
      return;
    }
  }

  // 缓存未命中或强制刷新，从API获取
  const res = await getPrjDetailApi(spuId, chapterId);
  if (res.data) {
    // 缓存数据
    cacheChapterContent(chapterId, res.data);

    // 更新组件状态
    const validIndex = res.data.validIndex;
    validIndexRef.value = validIndex;
    chapterList.value = res.data.chapterList;

    if (prjForm.value === PrjForm.draft) {
      htmlString.value = res.data.chapterList[validIndex]?.wordContent || '';
    } else if (prjForm.value === PrjForm.video) {
      videoCaptionList.value = res.data.chapterList[validIndex]?.videoCaptionList || [];
    }

    // 内容更新后重新初始化render实例
    await nextTick();
    // 如果当前有问题，确保问题列表已设置
    if (curQuestion.value && curQuestion.value.associatedWords) {
      questionList.value = [curQuestion.value];
    }
    await reinitializeRender();
  }
};

// 切换问题（优化版本）
const changeQuestion = async (questionId: string, chapterId: string, forceUpdate = false) => {
  // 检查是否需要更新
  const questionChanged = questionId !== curQuestionId.value;
  const chapterChanged = chapterId !== curChapterId.value;

  if (!questionChanged && !chapterChanged && !forceUpdate) {
    return;
  }

  // 更新问题ID
  if (questionChanged || forceUpdate) {
    curQuestionId.value = questionId;

    const res = await getQuestionDetailApi(curQuestionId.value);

    if (res && res.success && res.data) {
      // 处理不同的数据结构
      if (Array.isArray(res.data) && res.data.length > 0) {
        curQuestion.value = res.data[0];
      } else if (typeof res.data === 'object' && !Array.isArray(res.data)) {
        curQuestion.value = res.data;
      }
    }
  }

  // 只有当章节发生变化时才更新内容
  if (chapterChanged || forceUpdate) {
    curChapterId.value = chapterId;
    await updateContent(chapterId, forceUpdate);
  } else {
    // 即使章节未变化，也需要重新初始化render实例以显示新问题的高亮
    if (curQuestion.value && curQuestion.value.associatedWords) {
      questionList.value = [curQuestion.value];
      await reinitializeRender();
    }
  }

  scrollToHighlight();
};
// 显示删除确认弹窗
const showDeleteConfirm = () => {
  showDeleteDialog.value = true;
};

// 关闭删除确认弹窗
const handleCloseDeleteDialog = () => {
  showDeleteDialog.value = false;
};

// 确认删除问题
const confirmDeleteQuestion = async () => {
  if (curQuestion.value?.questionId) {
    const questionId = curQuestion.value.questionId.toString();
    const chapterId = curChapterId.value;

    const res = await deleteQuestionApi(questionId);
    if (res.success) {
      ElMessage.success('删除成功');

      // 发出删除事件通知其他组件，并传递导航信息
      emitter.emit(Event.REMOVE_QUESTION, {
        questionId,
        chapterId,
        needNavigation: true
      });

      // 关闭弹窗
      showDeleteDialog.value = false;
    } else {
      ElMessage.error('删除失败，请重试');
    }
  }
};
const scrollToHighlight = () => {
  nextTick(() => {
    const renderText = document.getElementById('underline');
    const firstHighLightQuestion = renderText?.querySelector('.highlight');
    const contentWrapper = document.querySelector('.content-wrapper.hover-scrollbar');

    if (firstHighLightQuestion && contentWrapper) {
      // 查找最近的 katex 容器（处理公式场景）
      const katexContainer = firstHighLightQuestion.closest('.katex');
      const scrollTarget = katexContainer || firstHighLightQuestion;

      // 获取目标元素的位置信息
      const targetRect = scrollTarget.getBoundingClientRect();

      // 计算目标元素相对于容器的位置
      const targetOffsetTop = (scrollTarget as HTMLElement).offsetTop;
      const containerHeight = contentWrapper.clientHeight;

      // 计算滚动位置，使目标元素在容器中央
      const scrollTop = targetOffsetTop - containerHeight / 2 + targetRect.height / 2;

      // 平滑滚动到计算位置，限制在content-wrapper容器内
      contentWrapper.scrollTo({
        top: Math.max(0, scrollTop), // 确保不会滚动到负值
        behavior: 'smooth'
      });
    }
  });
};

// 设置初始数据
const setInitialData = async (
  chapters: any[],
  currentChapterId: string,
  currentQuestionId: string,
  rightContentData?: any
) => {
  chapterList.value = chapters;
  curChapterId.value = currentChapterId;
  curQuestionId.value = currentQuestionId;

  // 找到当前章节的索引
  const chapterIndex = chapters.findIndex((chapter) => chapter.chapterId == currentChapterId);
  if (chapterIndex !== -1) {
    validIndexRef.value = chapterIndex;
  }

  // 设置右侧内容数据
  if (rightContentData) {
    const validIndex = rightContentData.validIndex || 0;
    validIndexRef.value = validIndex;

    if (prjForm.value === PrjForm.draft) {
      htmlString.value = rightContentData.chapterList?.[validIndex]?.wordContent || '';
    } else if (prjForm.value === PrjForm.video) {
      videoCaptionList.value = rightContentData.chapterList?.[validIndex]?.videoCaptionList || [];
    }
  }

  // 获取当前问题详情
  await changeQuestion(currentQuestionId, currentChapterId, true); // 强制更新

  // 即使没有问题，也要初始化render实例以显示内容
  await nextTick();
  await initializeRender();
};

// 转换视频字幕为文本数组（用于render library）- 参考PrjVideoWrapper.vue的实现
const convertVideoCaptionListToTextArray = (): string[] => {
  if (!videoCaptionList.value || !Array.isArray(videoCaptionList.value)) {
    return [];
  }

  const htmlArray: string[] = [];
  videoCaptionList.value.forEach((paragraphList: any, paragraphIndex: number) => {
    if (Array.isArray(paragraphList)) {
      // 构建包含时间戳信息的HTML结构 - 与PrjVideoWrapper.vue保持一致
      let paragraphHtml = `<div class="paragraph-wrapper" data-paragraph="${paragraphIndex}">`;
      paragraphHtml += `<div class="text">`;

      // 为每个句子添加oid、data-start、data-end属性
      paragraphList.forEach((item: any) => {
        paragraphHtml += `<span oid="${item.oid}" data-start="${item.startTime}" data-end="${item.endTime}">${item.caption}</span>`;
      });

      paragraphHtml += `</div></div>`;

      if (paragraphHtml.trim()) {
        htmlArray.push(paragraphHtml);
      }
    }
  });

  return htmlArray;
};

// 获取段落的HTML字符串 - 参考PrjVideoScript.vue的实现
const getParagraphHtmlString = (paragraphList: any[]) => {
  if (!paragraphList || paragraphList.length === 0) return '';
  return paragraphList.map((item) => item.caption).join('');
};

// 获取内容数据（根据项目类型返回不同格式）
const getContentData = () => {
  if (prjForm.value === PrjForm.video) {
    return convertVideoCaptionListToTextArray();
  } else {
    return htmlString.value;
  }
};

// 使用Render管理器进行问题反标（高亮显示）
const { initializeRender, reinitializeRender } = useRenderManager({
  containerSelector: '#underline',
  getContentData,
  questionList,
  onFinish: (arg: any) => {
    const content = arg.content;
    console.log('finish', content);
    renderedContent.value = content;
    console.log('renderedContent', renderedContent.value);
  }
});

const renderedContent = ref('');

// 计算属性：统一处理显示内容，包含时间信息
const displayContent = computed(() => {
  if (renderedContent.value && Array.isArray(renderedContent.value)) {
    console.log('✅ 使用渲染后的内容');
    return renderedContent.value.map((htmlContent: any, idx: number) => {
      // 从原始数据中获取对应的时间信息
      const originalParagraph = videoCaptionList.value?.[idx];
      return {
        htmlContent,
        timeInfo: originalParagraph && originalParagraph.length > 0 ? originalParagraph[0] : null,
        isRendered: true
      };
    });
  }
});
console.log('displayContent', displayContent);

// 判断指定回答者是否为项目作者
const isAnswererAuthor = (answerUserName: string) => {
  // 这里需要根据实际的项目作者信息进行判断
  // 可以通过 inject 获取项目作者信息或者通过 props 传入
  // 暂时返回 false，需要根据实际业务逻辑调整
  return false;
};

const goToDetail = (code: string, type: KlgType) => {
  if (type == KlgType.knowledge) {
    const { href } = router.resolve({
      path: '/klgdetail',
      query: {
        klgCode: code
      }
    });
    window.open(href, '_blank');
  } else if (type == KlgType.area) {
    // const { href } = router.resolve({
    //   path: '/areadetail',
    //   query: {
    //     areaCode: code
    //   }
    // });
    const { href } = router.resolve({
      path: '/area',
      query: {
        areaCode: code
      }
    });
    window.open(href, '_blank');
  }
};

// 暴露方法给父组件
defineExpose({
  changeQuestion,
  setInitialData,
  updateContent
});

// 定期清理过期缓存
let cacheCleanupInterval: NodeJS.Timeout | null = null;

onMounted(() => {
  // 组件挂载后初始化render实例
  nextTick(() => {
    if (questionList.value.length > 0) {
      initializeRender();
    }
  });

  // 启动定期缓存清理（每5分钟检查一次）
  cacheCleanupInterval = setInterval(
    () => {
      cleanExpiredCache();
    },
    5 * 60 * 1000
  );
});

onBeforeUnmount(() => {
  // 组件卸载前清理
  if (cacheCleanupInterval) {
    clearInterval(cacheCleanupInterval);
    cacheCleanupInterval = null;
  }

  // 清理所有缓存
  chapterContentCache.value.clear();
});
</script>

<style lang="less" scoped>
.question-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;

  .chapter-header {
    height: 25px;
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    padding-left: 10px;
  }
  .divider {
    flex-shrink: 0;
    margin: 0;
    margin-bottom: 20px;
  }
  .main-content-area {
    flex: 1;
    display: flex;
    height: calc(100% - 100px); // 减去header和divider的高度
    .theQuestionProgress {
      margin-top: 10px;
      width: 150px;
      height: 270px;
      box-sizing: border-box;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(220, 223, 230, 1);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        height: 40px;
        line-height: 40px;
        width: 100%;
        text-align: center;
        border-bottom: 1px solid rgba(220, 223, 230, 1);
      }
      .progress-circles {
        padding-top: 10px;
        .progress-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 20px;

          .progress-label {
            margin-top: 8px;
            text-align: center;

            .label-text {
              margin-bottom: 2px;
            }
          }
        }
      }
    }
  }

  .question-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow-y: auto;

    .question {
      display: flex;
      flex-direction: column;
      align-items: left;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e0e0e0;
      width: 100%;

      .title {
        margin-top: 10px;
        display: flex;
        justify-content: left;
        font-size: 12px;

        .creator-name {
          margin-right: 10px;
          font-family:
            '阿里巴巴普惠体 3.0 85 Bold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0',
            sans-serif;
          font-weight: 700;
          font-style: normal;
          font-size: 12px;
        }
      }

      .description {
        margin-top: 10px;
        margin-left: 5px;
        font-size: 14px;
        width: 100%;

        .keyWords {
          :deep(p) {
            display: inline !important; 
            margin: 0 !important;
            padding: 0 !important;
          }
        }

        .question-type {
          display: inline-block;
          margin-left: 0;
        }

        span {
          display: inline-block;
        }
      }

      .bottom {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #666666;

        .click-delete {
          &:hover {
            font-weight: 700;
            cursor: pointer;
          }
          margin: 0 10px;
        }
      }
    }

    .answer-section {
      padding: 0 10px;
      margin-bottom: 10px;
      width: 100%;

      .answer-list {
        margin-bottom: 15px;

        .answer-card {
          border-radius: 4px;
          border-bottom: 1px solid #e0e0e0;
          padding-bottom: 10px;
          width: 100%;

          .title {
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 8px;

            .name {
              font-family:
                '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
                '阿里巴巴普惠体 3.0', sans-serif;
              font-weight: 600;
              font-style: normal;
              font-size: 12px;
            }

            .isAuthor {
              background-color: #ecc32f;
              color: white;
              padding: 2px 6px;
              border-radius: 10px;
              font-size: 10px;
              margin-left: 8px;
            }
            .other {
              font-size: 12px;
            }
          }

          .klg-list {
            margin: 10px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .klg-item {
              cursor: pointer;
              transition: transform 0.2s ease;

              &:hover {
                transform: scale(1.05);
              }
              :deep(.klg-tag) {
                padding: 6px 12px;
                background-color: #f0f2f5;
                border-radius: 16px;
                font-size: 11px;
                color: #333333;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                transition: all 0.2s ease;
              }
            }
          }

          .description {
            font-size: 14px;
            margin-top: 15px;
            margin-bottom: 10px;
            line-height: 1.5;
            width: 100%;
          }

          .bottom {
            .date {
              color: #999999;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  // 右侧内容区域
  .right-content {
    width: 565px;
    display: flex;
    flex-direction: column;
    padding: 10px;

    .content-wrapper {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
    }
    :deep(.highlight) {
      color: #1973cb !important;
      &:hover {
        cursor: unset !important;
      }
    }
    :deep(.highlightHover) {
      font-size: unset !important;
      background-color: unset !important;
      cursor: unset !important;
    }
    :deep(.highlightHover .equation) {
      font-size: unset !important;
      background-color: unset !important;
      cursor: unset !important;
    }

    // 视频内容样式 - 参考PrjVideoScript.vue
    .video-content {
      .chaptertitle2 {
        font-weight: 600;
        padding-left: 10px;
        margin-top: 10px;
        margin-bottom: 5px;
        &:hover {
          cursor: pointer;
          color: #1973cb;
        }
      }

      .content {
        padding: 10px;
        font-size: 14px;
        color: #333333;
        width: 100%;
        word-wrap: break-word;
        overflow-wrap: break-word;
        white-space: normal;
        line-height: 1.6;

        .paragraph-wrapper {
          margin-bottom: 10px;

          .time {
            width: 70px;
            font-weight: 600;
            color: #333333;
            font-size: 12px;
            margin-bottom: 5px;
            user-select: none;
          }
        }
      }
    }
  }
}
.author-badge {
  color: #ecc32f;
  border: 1px solid;
  font-size: 12px;
  padding: 2px 6px;
}

// 删除确认弹窗样式
.delete-dialog-content {
  text-align: center;
  padding: 20px 0;

  p {
    font-size: 16px;
    color: #333;
    margin: 0;
    line-height: 1.5;
  }
}

.dialog-footer {
  text-align: center;

  .el-button {
    margin: 0 8px;
    min-width: 80px;
  }
}
</style>
