import type { exerciseItem } from '@/types/exercise';

/**
 * 生成结构化的题目 HTML，支持高亮，样式结构参考 TestWrapper.vue
 * @param exerciseInfo 题目信息
 * @param highlightWords 需要高亮的关键词数组
 * @param type 题目类型
 */
export function renderExerciseHtml(
  exerciseInfo: exerciseItem,
  highlightWords: string[] = [],
  type: number = 1
): string {
  let answerHtml = '';
  let contentHtml = '';

  // 选项渲染
  if (exerciseInfo.type === 1 || exerciseInfo.type === 2) {
    // 单选/多选
    contentHtml = `
      <div class="exq-question">${exerciseInfo.stem}</div>
      <ul class="exq-options">
        ${(exerciseInfo.content || [])
          .map(
            (item, idx) =>
              `<li class="exq-option-item">${String.fromCharCode(65 + idx)}. <span>${item.text}</span></li>`
          )
          .join('')}
      </ul>
    `;
    // 答案
    let answerArr: string[] = [];
    try {
      answerArr = Array.isArray(exerciseInfo.answer)
        ? exerciseInfo.answer
        : JSON.parse(typeof exerciseInfo.answer === 'string' ? exerciseInfo.answer : '[]');
    } catch {
      answerArr = [];
    }
    answerHtml = `<span class="exq-answer-label">答案：</span><span class="exq-answer-value">${answerArr.join(', ')}</span>`;
  } else if (exerciseInfo.type === 3) {
    // 填空题
    contentHtml = `<div class="exq-question">${exerciseInfo.stem}</div>`;
    answerHtml = `<span class="exq-answer-label">答案：</span><span class="exq-answer-value">${
      Array.isArray(exerciseInfo.answer)
        ? exerciseInfo.answer.join(', ')
        : exerciseInfo.answer || ''
    }</span>`;
  } else if (exerciseInfo.type === 4) {
    // 判断题
    contentHtml = `<div class="exq-question">${exerciseInfo.stem}</div>`;
    let ans = exerciseInfo.answer;
    if (typeof ans === 'string') {
      try {
        ans = ans.replace(/<\/?span[^>]*>/g, '');
      } catch {}
    }
    answerHtml = `<span class="exq-answer-label">答案：</span><span class="exq-answer-value">${ans == '1' ? '正确' : '错误'}</span>`;
  } else {
    // 其它类型
    contentHtml = `<div class="exq-question">${exerciseInfo.stem}</div>`;
    answerHtml = `<span class="exq-answer-label">答案：</span><span class="exq-answer-value">${exerciseInfo.answer || ''}</span>`;
  }

  // 说明
  const explanationHtml = `
    <div class="exq-explanation">
      <span class="exq-explanation-label">说明：</span><span class="exq-explanation-value">${exerciseInfo.explanation || ''}</span>
    </div>
  `;

  return `
    <div class="exq-root">
      <div class="exq-main-content">
        <div class="exq-content-block">
          ${contentHtml}
        </div>
        <div class="exq-answer-wrap">
          <div class="exq-answer-block">
            ${answerHtml}
          </div>
          ${explanationHtml}
        </div>
      </div>
    </div>
  `;
}
