<template>
  <!--  阅读模式的字幕段落：
          不提供划词功能
          数据渲染的是经过处理的文字 -->
  <!-- 文稿的段落 -->
  <div class="paragraph-wrapper">
    <div class="time" v-if="myParagraphList && (alwaysShowTime || !mode)" @click="changePlayerTime">
      {{ myParagraphList[0].startTime }}
    </div>
    <div class="text">
      <!-- <span v-for="(sentence, index) in paragraphList" @click="handleClickFn" :key="index"> -->
      <!-- <CaptionItem
        v-for="(sentence, index) in myParagraphList"
        :key="sentence.oid"
        :class="{ activeManu: sentence.oid == curoid }"
        v-model="sentence.caption"
        v-memo="sentence.caption"
      ></CaptionItem> -->
      <span v-html="htmlString"></span>
      <!-- {{ sentence.caption }} -->
      <!-- <div v-html="sentence.caption" > </div> -->

      <!--        <span v-if="paragraphList && index < paragraphList.length - 1">，</span>-->
      <!--        <span v-else>。</span>-->
    </div>
    <!-- <div :style="{ display: isBig ? 'flex' : '', justifyContent: 'center' }">
      <template v-if="isShowQuestion">
        <slot></slot>
      </template>
    </div> -->
    <!-- waqtodo:此处与划词有样式重叠问题 -->
  </div>
</template>

<script setup lang="ts">
// import PrjAskCard from './PrjAskCard.vue';
import type { QuestionData, VideoCaptionListObj } from '@/types/learning';
import type { FormRules } from 'element-plus';
import { handleLineWord, updataDom, markWord, unmarkWord } from '@/utils/lineWord';
import { PrjForm } from '@/types/project';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { usePlayerStore } from '@/stores/player';
import { processAllLatexEquations } from '@/utils/latexUtils';
const tryformula =
  '$$\n\\begin{align}\n(a+b)^2 &amp;= a^2 + 2ab + b^2 \\\\\n(a+b)(a-b) &amp;= a^2 - b^2\n\\end{align}\n$$';
// console.log("===props.paragraphList===", props.paragraphList)
// props.paragraphList.forEach((element) => {
//   // console.log(element.caption)
// });
const playerStore = usePlayerStore();
const { player } = storeToRefs(playerStore);
const drawerControllerStore = useDrawerControllerStore();
enum questionTypes {
  'what' = '是什么',
  'why' = '为什么',
  'how' = '怎么做',
  'open' = '开放问题'
}

interface RuleForm {
  referenceContent: string;
  keyword: string;
  type: number;
}

// const props = defineProps<{
//   id?: number;
//   startTime?: string;
//   caption: string;
//   endTime?: string;
//   beginning?: string;
// }>();
const props = defineProps<{
  questionList?: QuestionData[] | [];
  paragraphList?: VideoCaptionListObj[] | [];
  paraIndex?: number;
  searchKey?: string;
  curoid?: number;
  alwaysShowTime?: boolean;
}>();
// const searchKey = toRef(props.searchKey)

// watch(()=>[props.manuidx, props.curoid], ([newIndex, newIdx], [oldIndex, oldIdx]) => {
//   // console.log(`index changed from ${oldIndex} to ${newIndex}`);
//   console.log(`idx changed from ${oldIdx} to ${newIdx}`);
//   // handle(newIndex, newIdx);  // 调用 handle 函数

// });
const myParagraphList = ref<VideoCaptionListObj[]>();

const htmlString = computed(() => {
  return myParagraphList.value?.map((item) => item.caption).join('');
});
console.log('htmlString', htmlString);
//console.log('captionnnnnnn', props.caption)
const { mode } = storeToRefs(drawerControllerStore); // 模式
const isBig = inject('isBig') as Ref; // 是否大屏
const prjForm = inject('prjForm') as Ref; // 项目类型
// const prjType = ref(1); // 项目类型
const assessmentId = inject('assessmentId') as Ref;
// const assessmentId = ref(1);
const route = useRoute();
const spuId = route.query.spuId as string;

const emits = defineEmits(['askQuestion', 'showQuestion']);
const isShowQuestion = ref(false);
// const isShowAnswer = ref(false);
const prjSectionInfo = inject('prjSectionInfo') as Ref; // 章节信息

const getSeconds = (seconds: string) => {
  // 将 1   61 这种数字或者字符串转为   00:00:01  00:01:01
  if (!seconds) return 0;
  const array = seconds.split(':');
  let res = 0;
  array.forEach((item) => {
    res = res * 60 + parseInt(item, 10);
  });
  return res;
};
const changePlayerTime = () => {
  if (myParagraphList.value && myParagraphList.value.length > 0) {
    player.value!.currentTime = getSeconds(myParagraphList.value[0].startTime);
  }
};
// const searchKey = inject('searchKey') as Ref;
// const searchKey = toRef(props.searchKey);
// watch(searchKey, (newValue, oldValue) => {
//   handleSearch(newValue!);
// })
// console.log(4444444444444444444,props.paragraphList)
watch(
  () => props.paragraphList,
  (newVal) => {
    // console.log('我改变了',v)
    // console.log(v,v.paragraphList)
    if (newVal) {
      myParagraphList.value = newVal;
    }
  }
);

const handleSearch = async (searchKey: string) => {
  if (prjForm.value == PrjForm.video) {
    myParagraphList.value = initParagraphList.value;
    if (!searchKey) {
      await nextTick();
      // if (!mode.value) {
      //   markWord(); // 阅读模式
      // } else {
      //   unmarkWord(); // 提问模式
      // }
      return;
    }
    // wordsSet.add(searchKey.value);
    // TODO: 这个地方有点风险，在其他地方\\b\\b会起作用，
    // TODO: 而不是完全不匹配内容，如果这个地方出bug，可以重点看一下\\b\\b是否匹配上什么东西了
    // const pattern3 = searchKey.length > 0 ? new RegExp(searchKey, 'g') : new RegExp('\\b\\b'); // 正则
    let str = '';
    for (let char of searchKey) {
      str += `<span data-index=\"[^<]*\" data-qid=\"[^<]*\" onclick=\"handleWord\\(this\\)\">${char}</span>`;
    }
    const pattern3 = new RegExp(str, 'g');
    myParagraphList.value = getPlist(myParagraphList.value!, 'highlight2', pattern3);
    await nextTick();
    // if (!mode.value) {
    //   markWord(); // 阅读模式
    // } else {
    //   unmarkWord(); // 提问模式
    // }
  }
};
const lightWords = computed(
  () =>
    props.questionList?.map((question) => {
      // console.log("===========question.keyword==", question)
      return {
        keywords: question.keyword,
        id: question.questionId
      };
    }) ?? []
); // 高亮词语

const pattern2 = ref(
  lightWords.value.length > 0
    ? new RegExp(lightWords.value.map((item) => item.keywords).join('|'), 'g')
    : // : new RegExp('\\b\\b')
      new RegExp(
        '=不可能匹配==不可能匹配===不可能匹配====不可能匹配==不可能匹配===不可能匹配===不可能匹配====不可能匹配=='
      )
  // TODO: 上面的这个\\b\\b匹配上东西了，不知道为什么能匹配上，按道理不应该匹配任何东西，现在只能暂时用一个很长的=不可能匹配==不可能匹配
  // TODO: ===不可能匹配====不可能匹配==不可能匹配===不可能匹配===不可能匹配====不可能匹配==来确保不会匹配上任何内容，但是后续得
  // TODO: 研究一下为什么\\b\\b没有起作用
); // 正则
// console.log("pattern2@@@", pattern2.value)

const getPlist = (
  originPList: VideoCaptionListObj[],
  myClassName: string,
  pattern: RegExp = pattern2.value
) => {
  if (!originPList || originPList.length == 0) return [];
  // console.log(12345)
  const res = originPList.map((sentense) => {
    // console.log("sentense==++", sentense)
    const newSentense = JSON.parse(JSON.stringify(sentense));
    // console.log(newSentense.caption)
    // console.log(pattern)
    // console.log("pattern2"+pattern2)
    newSentense.caption = newSentense.caption?.replace(pattern, (match: string) => {
      // console.log(lightWords.value)
      // console.log(item)
      return `<span class="${myClassName}"">${match}</span>`;
    });
    return newSentense;
  });
  return res;
};
const handleClickKeyWords = () => {
  alert('id == ');
};
// 显示问题
const handleClickFn = (event: MouseEvent) => {
  emits('showQuestion', props.paraIndex);
};
const initParagraphList = ref<VideoCaptionListObj[]>();
onMounted(() => {
  initParagraphList.value = getPlist(props.paragraphList!, 'text-hover');
  // paragraphList.value = initParagraphList.value;
  myParagraphList.value = props.paragraphList;
});
defineExpose({
  handleSearch
});
</script>

<style scoped lang="less">
.time {
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  &:hover {
    cursor: pointer;
    color: var(--color-theme-project);
  }
}

.text {
  font-family: var(--text-family);
  font-size: var(--fontsize-middle-project);
  font-weight: 300;
  color: var(--color-black);
  padding: 5px 5px 5px 10px;
  background-color: #ffffff;
  border-radius: 5px;
  margin-bottom: 5px;
  white-space: initial;

  &:deep(.highlight2) {
    // color: var(--color-theme-project);
    background-color: yellow;
    cursor: pointer;
    font-weight: 700;
  }
  &:deep(.text-hover) {
    color: var(--color-theme-project);
    cursor: pointer;
  }

  &:deep(.text-hover:hover) {
    font-weight: 700;
    //color: red;
  }
  .activeManu {
    background-color: #a6d0ea;
  }
}

.current-param {
  background-color: #aaa;
  padding: 5px;
  border-radius: 5px;
  margin-bottom: 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.q-header {
  font-size: 14px;
  font-weight: 700;
  color: #005579;
  padding: 5px 10px;
  background-color: rgb(222, 233, 238);
  border-radius: 5px;
}

.q-primary-key,
.q-content {
  margin-top: 10px;
}

.q-staic-key {
  font-size: 12px;
  font-weight: 400;
  color: #333333;
}

// .highlight .text {
//   color: var(--color-theme-project);
//   font-weight: bold;
// }
</style>
