<template>
  <div class="footer-wrapper">
    <div class="footer">
      <div class="left">
        <p>© 北京无尽本源科技有限公司版权所有</p>
        <!-- <p>
          网站备案/许可证号：京ICP备2022008888号-1 京公网安备11010502048888号 增值电信业务经营许可证
        </p> -->
        <div>
          <span>网站备案/许可证号：</span>
          <img src="@/assets/gonganlogo.png" class="w-full" style="width: 16px;">
          <a href="https://beian.mps.gov.cn/#/query/webSearch?code=11010802043849" rel="noreferrer" target="_blank">京公网安备11010802043849</a>&nbsp;
          <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备2023021578号-2</a>&nbsp;
          <span>增值电信业务经营许可证：</span>
          <a href="#">京B2-20240700</a>
        </div>
        <p>
          <span @click="goPage('/helpcenter', '2')">用户协议</span>&nbsp;
          <span @click="goPage('/helpcenter', '3')">隐私政策</span>&nbsp;
          <span @click="goPage('/helpcenter', '4')">知识产权声明</span>
        </p>
      </div>
      <div class="right">
        <h3 class="title">无尽本源</h3>
        <p style="cursor: pointer" @click="goPage('/aboutus', '0')">关于我们</p>
        <p style="cursor: pointer" @click="goPage('/aboutus', '0')">联系我们</p>
        <p style="cursor: pointer" @click="goPage('/helpcenter', '1')">帮助中心</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
const router = useRouter();

const goPage = (path: string, id: string) => {
  router.push({ path: path, query: { pageId: id } });
};
</script>

<style scoped lang="less">
.footer-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  background: #f2f2f2;
  white-space: nowrap;
  height: 200px;

  &.footer-fixed {
    position: relative;
    bottom: 0;
  }

  .footer {
    width: var(--width-fixed--project);
    padding: 55px 220px 40px 45px;
    font-size: var(--fontsize-middle-project);
    display: flex;
    justify-content: space-between;

    .left {
      font-family: var(--text-family);
      display: flex;
      flex-direction: column;
      color: var(--color-grey);
      p {
        font-weight: 400;
        line-height: 1.8;
        span {
          cursor: pointer;
        }
      }
      a, a:visited,a:link,a:hover{
        font-weight: 400;
        line-height: 1.8;
        color: #999999;
        margin-block-start: 1em;
        margin-block-end: 1em;
        cursor: pointer;
      }
    }

    .right {
      color: var(--color-black);
      font-family: var(--text-family);
      .title {
        font-family: var(--title-family);
        font-weight: 600;
        margin-bottom: 5px;
        font-size: 20px;
      }

      p {
        span {
          cursor: pointer;
          margin-right: 14px;
        }
      }
    }
  }
}
</style>
