<template>
  <el-dialog :close-on-click-modal="false" v-model="isDialogShow" width="596" @close="closeAddKlg">
    <template #header="{ titleId }">
      <div :id="titleId" class="title">添加知识</div>
    </template>
    <div class="content-container">
      <div class="searchBar">
        <el-input
          class="input"
          v-model="searchKey"
          @keydown.enter="handleSearch()"
          :placeholder="'请输入知识点名称'"
        >
          <template #suffix>
            <el-icon class="btn el-input__icon" @click="handleSearch()">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div class="tList" @wheel="handleWheelFn" id="scroll">
        <div
          class="tItem"
          :class="item.isSelected ? 'isSelected' : ''"
          v-for="item in tList"
          :key="item.id"
          @click="handleSelect(item)"
        >
          <span v-html="item.name"></span>
        </div>
      </div>
      <!-- <RecycleScroller class="tList" key-field="id" :items="tList" :item-size="6" v-slot="{ item }"
        ><div
          class="tItem"
          :class="item.isSelected ? 'isSelected' : ''"
          @click="handleSelect(item)"
        >
          <span v-html="item.name"></span>
        </div>
      </RecycleScroller> -->
      <div class="selectedTList" id="bottom">
        <my-tag
          class="t"
          @delete="handleDelete"
          :tag-id="item.id"
          v-for="item in selectedTList"
          :key="item.id"
        >
          <el-tooltip popper-class="tooltip-width" :content="item.name" raw-content>
            <span v-html="item.name"></span>
          </el-tooltip>
        </my-tag>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <CmpButton type="info" @click="handleClose" class="btn">关闭</CmpButton>
          <CmpButton type="primary" @click="handleSubmit" class="btn">确认</CmpButton>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import MyTag from './MyTag.vue';
// import {editingPrjStore} from "@/stores/editingPrj";
// import type {tagType} from "@/utils/type";
// import {getTagList, getTargetList, type params2tList} from "@/apis/path/createProject";
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { getKlgListApi } from '@/apis/learning';

const props = defineProps({
  showAddKlg: Boolean
});

const emit = defineEmits(['submit', 'close']);
const isDialogShow = ref(false);
const tForm = ref(''); // tag | target
const dialogTitle = ref('');
const searchKey = ref('');
const current = ref(1);
// const editingPrj = editingPrjStore();
const tList = ref([]);
const selectedTList = ref<[]>([]);
const limit = ref(-1); // 在step3的时候，限制选择条数
isDialogShow.value = props.showAddKlg;
console.log(isDialogShow);
const total = ref(0);

const closeAddKlg = () => {
  emit('close', false);
};
// const showDialog = (form: string, step: number, maxLimit?: number, sTList?: tagType[]) => {
//   if (maxLimit != undefined) {
//     limit.value = maxLimit;
//   }
//   isDialogShow.value = true;
//   tForm.value = form;
//   dialogTitle.value = form == 'tag' ? '添加标签' : '添加目标';
//   // 数据初始化
//   current.value = 1;
//   tList.value = [];
//   if (step == 1) {
//     if (tForm.value == 'tag') {
//       selectedTList.value = [...editingPrj.getPrjTagList()];
//     }
//     else {
//       selectedTList.value = [...editingPrj.getPrjTargetList()];
//     }
//   }
//   if (sTList) {
//     selectedTList.value = [...sTList];
//   }
//   if (limit.value != -1) {
//     // 限制选择条数
//     selectedTList.value = selectedTList.value.slice(0, limit.value);
//   }
//   // FIXME:目前标签开启了自动搜索，因为key为空时可以搜出来全部标签
//   // 目标知识点搜索，key为空时搜不到东西，开了就是浪费通信资源
//   if (tForm.value == 'tag') getTList();
// }
const handleWheelFn = (e) => {
  // 如果已经是全部的标签，直接返回
  if (tList.value && tList.value.length >= total.value) return;
  let bottom = document.querySelector('#bottom')?.offsetTop as number;
  let now = document.querySelector('#scroll')?.scrollTop as number;
  setTimeout(() => {
    if (now >= bottom && e.deltaY > 0) {
      // TODO: 暂未启用，需要改成触发时往后端要数据
      console.log('bottom! ');
    }
  }, 100);
};
const getTList = () => {
  // 和后端通信+洗数据
  // 1.保证tag和target数据结构一致（都弄成tagType类型）
  // 2.保证choose字段和值和前端暂存数据selectedTList一致
  // FIXME: 后端还没做分页
  // let param = ref({
  //     current: current.value,
  //     limit: 5,
  //     keywords: searchKey.value,
  //     status: 1,
  // });
  getKlgListApi(searchKey.value)
    .then((res) => {
      if (res.success) {
        // 翻页：追加写
        // 如果后端改结构了，这里t的结构也要改
        let appendList = res.data.list?.map(
          (t: { klgCode: string; title: string; choose: boolean }) => {
            const matchedTarget = selectedTList.value.find((st) => st.id == t.klgCode);
            if (matchedTarget) {
              return {
                id: t.klgCode,
                name: t.title,
                isSelected: matchedTarget.isSelected
              };
            } else {
              return {
                id: t.klgCode,
                name: t.title,
                isSelected: t.choose
              };
            }
          }
        );
        tList.value.push(...appendList);
        // console.log('==>' + JSON.stringify(tList.value, null ,2))
      } else {
        ElMessage.error('加载目标列表异常');
      }
    })
    .catch();
  current.value++;
};
const handleSearch = () => {
  // 数据初始化
  current.value = 1;
  tList.value = [];
  getTList();
};
const handleSelect = (item) => {
  item.isSelected = !item.isSelected;
  if (limit.value != -1 && selectedTList.value.length >= limit.value) {
    ElMessage.error('最多选择' + limit.value + '个标签');
    return;
  }
  const foundItem = tList.value.find((i) => i.id == item.id);
  if (foundItem) {
    // 维护selectedTList
    const index = selectedTList.value.findIndex((i) => i.id == item.id);
    if (index != -1) {
      selectedTList.value.splice(index, 1);
    } else {
      selectedTList.value.push(item);
    }
    // 维护tList
    // Vue.set(foundItem, 'isSelected', !foundItem.isSelected);
  }
};
const handleDelete = (aimId: string) => {
  // 只维护selectedTList
  const aimItem = tList.value.find((t) => t.id == aimId);
  if (aimItem) {
    aimItem.isSelected = false;
  }
  const index = selectedTList.value.findIndex((i) => i.id == aimId);
  if (index != -1) {
    selectedTList.value.splice(index, 1);
  }
};
const handleClose = () => {
  isDialogShow.value = false;
};
const handleSubmit = () => {
  emit('submit', selectedTList.value);
  isDialogShow.value = false;
};
// defineExpose({
//   showDialog,
// })
</script>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.title {
  font-size: 16px;
  font-family: var(--text-family);
}

.content-container {
  /*--el-color-primary: var(--color-primary);*/
  font-family: var(--text-family);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .searchBar {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 35px;
    margin-bottom: 30px;

    .input {
      --el-color-primary: rgba(242, 242, 242, 1);
      width: 536px;

      .btn {
        cursor: pointer;
      }
    }
  }

  .tList {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 215px;
    overflow-y: auto;
    margin-bottom: 10px;
    width: 100%;

    .tItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      /*height: 35px;*/
      line-height: 35px;
      padding: 0 20px;
      border: 1px solid rgba(242, 242, 242, 1);
      cursor: pointer;
      margin-bottom: 10px;

      &:hover,
      &.isSelected {
        background-color: rgba(242, 242, 242, 1);
      }
    }

    &::-webkit-scrollbar {
      position: absolute;
      /*滚动条整体样式*/
      width: 5px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      background: rgba(242, 242, 242, 1);
    }
  }

  .selectedTList {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: auto;
    padding-top: 10px;

    .t {
      margin: 0 10px 10px 0;
    }

    &::after {
      content: '';
      height: 1px;
      width: 100%;
      background-color: rgba(242, 242, 242, 1);
      position: absolute;
      top: 0;
    }
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-around;
    width: 480px;
    .btn {
      width: 150px;
      height: 40px;
      font-size: var(--fontsize-middle-project);
    }
  }
}
</style>
