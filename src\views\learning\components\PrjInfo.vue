<template>
  <div ref="warpper" class="warpper hover-scrollbar">
    <!-- 学习进度卡片 -->
    <div class="progress-card">
      <!-- 卡片头部 - 始终显示 -->
      <div class="card-header">
        <div class="header-left">
          <div class="project-info">
            <div>
              <img class="backBtn" @click="handleBack()" src="@/assets/images/prjlearn/u4508.svg" />
              <span class="titlefont title" v-html="prjInfo.title"></span>
            </div>
            <div class="meta-info">
              <div class="author-info">
                <img :src="prjInfo.userCoverPic" class="avatar" />
                <span class="author-name">{{ prjInfo.userName }}</span>
              </div>
              <span class="create-time">{{ prjInfo.createTime }}</span>
              <el-icon
                class="expand-arrow"
                :class="{ expanded: progressExpanded }"
                @click="toggleProgressExpand"
              >
                <ArrowDown />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 可折叠的进度内容 -->
      <el-collapse-transition>
        <div v-show="progressExpanded" class="progress-content textfont">
          <!-- 进度圆环区域 -->
          <div class="progress-circles">
            <div class="progress-item">
              <el-progress
                type="circle"
                :percentage="graspKlgPct"
                :width="56"
                :stroke-width="3"
                color="#67c23a"
              />
              <div class="progress-label">
                <span class="label-text textfont"
                  >已掌握 {{ klg.masteredKlgCount || 0 }}/{{ klg.klgCount || 0 }}</span
                >
              </div>
            </div>
            <div class="progress-item">
              <el-progress
                type="circle"
                :percentage="fullyGraspKlgPct"
                :width="56"
                :stroke-width="3"
                color="#67c23a"
              />
              <div class="progress-label">
                <span class="label-text textfont"
                  >全掌握{{ klg.fullyMasteredKlgCount || 0 }}/{{ klg.klgCount || 0 }}
                </span>
              </div>
            </div>
          </div>

          <!-- 标签区域 -->
          <div class="tags-section">
            <div class="tags-grid">
              <div v-for="tag in displayTags" :key="tag.id" class="tag-item">
                {{ tag.content }}
              </div>
              <div v-if="tags.length > 4" class="tag-item more-tag" @click="handleExpandTag">
                <el-icon><More /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <div class="divider"></div>

    <!-- 章节列表 -->
    <div class="chapter-section">
      <div class="section-header">
        <span class="titlefont chapterCatalog">项目章节</span>
        <span class="chapter-count">共 {{ chapterList?.length || 0 }} 章</span>
      </div>
      <div class="chapter-list" v-if="chapterList && chapterList.length > 0">
        <div
          v-for="(chapter, index) in chapterList"
          :key="chapter.chapterId"
          class="chapter-item"
          :class="{
            active: activeChapterId == chapter.chapterId,
            locked: !canAccessChapter(chapter)
          }"
          @click="handleChapterChange(chapter.chapterId, chapter.preview)"
        >
          <!-- <div class="chapter-number">{{ index + 1 }}</div> -->
          <div class="chapter-content">
            <div class="textfont chapter-name" :title="chapter.chapterName">
              {{ chapter.chapterName }}
            </div>
            <div class="chapter-meta" v-if="chapter.duration">
              <span class="duration">{{ formatDuration(chapter.duration) }}</span>
            </div>
          </div>
          <div class="chapter-status">
            <el-icon v-if="!canAccessChapter(chapter)" class="lock-icon">
              <Lock />
            </el-icon>
          </div>
        </div>
      </div>
      <div v-else class="no-chapters">暂无章节内容</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getPrjMoreInfoApi } from '@/apis/learning';
import type { PrjinfoItf, PrjTag as PrjTagType, Chapter } from '@/types/learning';
import { PrjType } from '@/types/project';
import { useRouter } from 'vue-router';
import { useLearningStore } from '@/stores/learning';
import {
  Lock,
  VideoPlay,
  ArrowDown,
  ArrowLeft,
  ArrowUp,
  More,
  InfoFilled
} from '@element-plus/icons-vue';

// 定义props
const props = defineProps<{
  buyStatus: boolean;
}>();

// 定义emits
const emit = defineEmits<{
  chapterChange: [chapterId: number, preview: boolean];
}>();

const prjInfo = inject('prjInfo') as Ref<PrjinfoItf>;
const route = useRoute();
const router = useRouter();
const learningStore = useLearningStore();

const spuId = route.query.spuId as string;
const targetKlgs = ref<any>([]);
const klg = reactive({
  klgCount: 0,
  masteredKlgCount: 0,
  fullyMasteredKlgCount: 0
});
const prjTargetObj = ref({
  description: '',
  purpose: ''
});
const graspKlgPct = computed(() => {
  if (!klg.klgCount) return 0;
  return Math.floor((klg.masteredKlgCount / klg.klgCount) * 100);
});
const fullyGraspKlgPct = computed(() => {
  if (!klg.klgCount) return 0;
  return Math.floor((klg.fullyMasteredKlgCount / klg.klgCount) * 100);
});
const descriptionVisible = ref(false);
const tagsVisible = ref(false);
const warpper = ref();
const more = ref();
const prjType = inject('prjType') as Ref;
const tags = prjInfo.value.prjTags as Array<PrjTagType>;

// 章节相关数据
const chapterList = ref<Chapter[]>([]);
const activeChapterId = ref<number>();
const hasPermission = ref(false);

// 进度卡片相关数据
const progressExpanded = ref(false);

// 计算显示的标签
const displayTags = computed(() => {
  return tagsVisible.value ? tags : tags.slice(0, 4);
});

function handleClose(event: MouseEvent) {
  if (warpper.value.contains(event.target as HTMLElement)) {
    return;
  }
  descriptionVisible.value = false;
  tagsVisible.value = false;
}

function handleExpandTag() {
  tagsVisible.value = !tagsVisible.value;
}

// 切换进度卡片展开状态
const toggleProgressExpand = () => {
  progressExpanded.value = !progressExpanded.value;
};
// 处理返回
const handleBack = () => {
  router.push({
    path: '/goodIntroduce',
    query: { spuId: spuId }
  });
};
// 格式化时长
const formatDuration = (duration: string | number) => {
  if (!duration) return '';
  if (typeof duration === 'string') return duration;
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

// 判断是否可以访问章节
const canAccessChapter = (chapter: Chapter) => {
  // 预览章节可以访问
  if (chapter.preview) return true;
  // 已购买可以访问
  if (props.buyStatus) return true;
  // 有权限可以访问
  if (hasPermission.value) return true;
  // 其他情况不能访问
  return false;
};

// 处理章节切换
const handleChapterChange = (chapterId: number, preview: boolean) => {
  const chapter = chapterList.value.find((c) => c.chapterId === chapterId);
  if (!chapter) return;

  // 检查是否可以访问该章节
  if (!canAccessChapter(chapter)) {
    // 没有权限，触发购买流程
    emit('chapterChange', chapterId, preview);
    return;
  }

  if (activeChapterId.value !== chapterId) {
    activeChapterId.value = chapterId;
    emit('chapterChange', chapterId, preview);
  }
};

// 监听购买状态变化，实现局部更新
watch(
  () => props.buyStatus,
  (newBuyStatus) => {
    // 当购买状态变化时，更新hasPermission
    hasPermission.value = newBuyStatus;
  },
  { immediate: true }
);

// 监听prjInfo变化，实现项目信息的实时更新
watch(
  () => prjInfo.value,
  async (newPrjInfo) => {
    if (newPrjInfo && newPrjInfo.spuId) {
      // 当项目信息更新时，重新获取详细信息
      try {
        const res = await getPrjMoreInfoApi(newPrjInfo.spuId);
        const data = res.data as any;

        // 更新知识点统计
        if (data.klg) {
          Object.assign(klg, {
            klgCount: data.klg.klgCount || 0,
            masteredKlgCount: data.klg.masteredKlgCount || 0,
            fullyMasteredKlgCount: data.klg.fullyMasteredKlgCount || 0
          });
        }

        // 更新项目目标信息
        prjTargetObj.value = data.prj || { description: '', purpose: '' };
      } catch (error) {
        console.error('更新项目详细信息失败:', error);
      }
    }
  },
  { deep: true }
);

onMounted(async () => {
  // 获取项目详细信息
  const res = await getPrjMoreInfoApi(spuId);

  const data = res.data as any;
  targetKlgs.value = data.targetKlgs || [];

  // 正确更新响应式对象的属性
  if (data.klg) {
    Object.assign(klg, {
      klgCount: data.klg.klgCount || 0,
      masteredKlgCount: data.klg.masteredKlgCount || 0,
      fullyMasteredKlgCount: data.klg.fullyMasteredKlgCount || 0
    });
  }

  prjTargetObj.value = data.prj || { description: '', purpose: '' };

  // 获取章节列表
  chapterList.value = learningStore.chapterList || [];
  const validChapter = learningStore.chapterList?.[learningStore.validIndex];
  if (validChapter && typeof validChapter === 'object' && 'chapterId' in validChapter) {
    activeChapterId.value = (validChapter as any).chapterId;
  }

  // 权限信息主要通过props.buyStatus来判断，hasPermission作为备用
  hasPermission.value = props.buyStatus; // 初始化时使用props.buyStatus
});

onMounted(() => {
  document.addEventListener('click', handleClose);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClose);
});
</script>

<style scoped src="./css/prjInfo.less"></style>
