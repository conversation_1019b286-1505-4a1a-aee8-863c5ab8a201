<template>
  <div class="video-wrapper">
    <div id="videoPlayer" class="video"></div>
    <!-- <div @click="handleClick()">点击</div> -->
  </div>
</template>

<script setup lang="ts">
import { throttle } from 'lodash-es';
import { getVideoSrc } from '@/utils/getStoreAuth';
import Player, { Events } from 'xgplayer';
// import HlsPlugin, { EVENT } from 'xgplayer-hls';
import HlsJsPlugin from 'xgplayer-hls.js';
import Danmu from 'xgplayer/es/plugins/danmu';
import 'xgplayer/es/plugins/danmu/index.css';
import 'xgplayer/dist/index.min.css';
import createXgPlayerWiderPlugin from '@/utils/createXgPlayerWiderPlugin';
import { useUserStore } from '@/stores/user';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { sample } from 'lodash-es';
import { usePlayerStore } from '@/stores/player';
import favicon from '@/assets/favicon.ico';

const playerStore = usePlayerStore();
const { player } = storeToRefs(playerStore);
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);
const drawerControllerStore = useDrawerControllerStore();
const { mode } = storeToRefs(drawerControllerStore);
import xhook from 'xhook';

xhook.after(function (request, response) {
  if (String.prototype.includes.call(request.url, 'key')) {
    //response.data = 'xxx';
  }
});

const comment = {
  txt: userInfo.value.username + '@' + userInfo.value.phone
};

const props = defineProps<{
  videoSrc: string;
  canBeWiden: boolean;
}>();

const emits = defineEmits([
  'timeupdate',
  'wider',
  'endTrigger'
  // 'closePip'
]);

//   const exitPIP = () => {
//     const plugin = player.value!.getPlugin('pip');
//     plugin.exitPIP();
//   };

//   const requestPIP = () => {
//     const plugin = player.value!.getPlugin('pip');
//     plugin.requestPIP();
//   };

const setTime = (time) => {
  player.value!.currentTime = time;
};
const pause = () => {
  player.value!.pause();
};

const play = () => {
  player.value!.play();
};

watch(
  () => props.videoSrc,
  async (newVal) => {
    const res = await getVideoSrc(newVal);
    player.value!.playNext({
      url: res.url + '&ci-process=pm3u8&expires=3600'
    });
  }
);

watch(
  () => mode.value,
  (newVal) => {
    if (newVal) {
      pause();
    }
  }
);

let timer: NodeJS.Timeout | null = null;
onMounted(async () => {
  const res = await getVideoSrc(props.videoSrc);
  const m3u8Url = res.url + '&ci-process=pm3u8&expires=3600';
  if (props.canBeWiden) {
    const myPlugin = createXgPlayerWiderPlugin(function (event) {
      emits('wider');
    });
    player.value = new Player({
      id: 'videoPlayer',
      url: m3u8Url,
      plugins: [HlsJsPlugin, myPlugin, Danmu],
      definitionActive: 'click',
      fluid: true,
      ignores: ['cssfullscreen'],
      videoFillMode: 'contain',
      miniprogress: true,
      danmu: {
        closeDefaultBtn: true,
        fontSize: 40,
        channelSize: 60
      },
      commonStyle: {
        // 进度条底色
        progressColor: '',
        // 播放完成部分进度条底色
        playedColor: '#1973CB',
        // 缓存部分进度条底色
        cachedColor: '',
        // 进度条滑块样式
        sliderBtnStyle: { backgroundColor: '#1973CB' },
        // 音量颜色
        volumeColor: '#1973CB'
      }
    });
    player.value.registerPlugin(myPlugin);
    //此处是因为xgplayer渲染播放器的默认机制是设置height为0
    player.value.on(Events.READY, () => {
      (document.getElementById('videoPlayer') as HTMLElement).style.height = '100%';
      (document.getElementById('videoPlayer') as HTMLElement).style['padding-top'] = 0;
    });
    // player.value.on(Events.PIP_CHANGE, (isPip) => {
    //   if (!isPip) {
    //     emits('closePip');
    //   }
    // });
  } else {
    player.value = new Player({
      id: 'videoPlayer',
      url: m3u8Url,
      plugins: [HlsJsPlugin, Danmu],
      definitionActive: 'click',
      fluid: true,
      ignores: ['cssfullscreen'],
      videoFillMode: 'contain',
      miniProgress: true,
      danmu: {
        closeDefaultBtn: true,
        fontSize: 30,
        channelSize: 50
      },
      commonStyle: {
        // 进度条底色
        progressColor: '',
        // 播放完成部分进度条底色
        playedColor: '#1973CB',
        // 缓存部分进度条底色
        cachedColor: '',
        // 进度条滑块样式
        sliderBtnStyle: { backgroundColor: '#1973CB' },
        // 音量颜色
        volumeColor: '#1973CB'
      }
    });
  }
  // player.value!.on('core_event', (e) => {
  //   // if (e.eventName === EVENT.HLS_LEVEL_LOADED) {
  //   //   console.log(e.playlist);
  //   //   e.playlist.segments.forEach((segment, index) => {
  //   //     segment.url = 'xxx';
  //   //     console.log(index);
  //   //   });
  //   // }
  //   if (e.eventName === EVENT.METADATA_PARSED) {
  //     console.log(e);
  //   }
  // });
  // 此处不能用防抖，使用节流
  const func = throttle((val) => {
    emits('timeupdate', val.currentTime);
  }, 300);
  player.value.on('timeupdate', func);
  const areas = [
    {
      start: 0,
      end: 1
    },
    {
      start: 0.1,
      end: 1
    },
    {
      start: 0.2,
      end: 1
    },
    {
      start: 0.3,
      end: 1
    },
    {
      start: 0.4,
      end: 1
    },
    {
      start: 0.5,
      end: 1
    },
    {
      start: 0.6,
      end: 1
    },
    {
      start: 0.7,
      end: 1
    },
    {
      start: 0.8,
      end: 1
    },
    {
      start: 0.9,
      end: 1
    }
  ];
  const xOffsets = [
    '-60%',
    '-50%',
    '-40%',
    '-30%',
    '-20%',
    '-10%',
    '0',
    '10%',
    '20%',
    '30%',
    '40%',
    '50%',
    '60%'
  ];
  timer = setInterval(() => {
    const danmu = player.value!.getPlugin('danmu');
    if (!player.value?.paused) {
      danmu.setArea(sample(areas));
      danmu.sendComment({
        duration: 5000,
        id: Date.now(),
        ...comment,
        style: {
          color: 'rgba(0, 0, 0, .1)',
          padding: '5px 5px',
          transform: `rotate(-15deg) translateX(${sample(xOffsets)})`
        },
        mode: 'top'
      });
    }
  }, 5000);
});

onBeforeUnmount(() => {
  player.value!.destroy();
  player.value = null;
  clearInterval(timer);
});

defineExpose({
  // exitPIP, requestPIP,
  setTime,
  pause,
  play
});
</script>

<style scoped lang="less">
.video-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  .video {
    height: 100%;
    min-height: 100%;
    background: #f2f2f2;
  }
}
</style>
