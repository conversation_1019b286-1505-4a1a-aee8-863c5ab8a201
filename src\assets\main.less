@import './base.css';
@import './reset.css';

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
}
body.question-detail {
  overflow: hidden;
}
#app {
  height: 100%;
  background-color: #ffffff;
  position: relative;
  ::selection {
    background: #1973cb;
    color: #ffffff;
  }
}

/** 解决ckeditor mathjax 位于overlay之下*/
/* .ck.ck-math-preview{
  z-index: 9999;
}
.ck.ck-balloon-panel.ck-balloon-panel_arrow_nw.ck-balloon-panel_visible.ck-balloon-panel_with-arrow{
  position: relative;
  z-index: 3000;
} */
.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-center {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative; /* 为绝对定位提供参考 */
}
.flex-start {
  display: flex;
  align-items: flex-start;
}
.flex-space-between {
  display: flex;
  justify-content: space-between;
}
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  // background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
  background: white;
  max-width: 500px;
  text-wrap: wrap;
  color: black;
  z-index: 9999 !important;
  box-shadow: 2px 2px 4px 5px #dcdfe6;
}

.el-popper.is-customized .el-popper__arrow::before {
  // background: linear-gradient(45deg, #b2e68d, #bce689);
  background: white;
  right: 0;
}
.titlefont {
  font-family: var(--title-family);
  font-weight: 600;
  font-style: normal;
}

.textfont {
  font-family: var(--text-family);
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  .titlefont();
  font-weight: 600; /* 假设 titlefont 具有较粗的字重 */
  margin: 0; /* 重置默认外边距 */
  padding: 0; /* 重置默认内边距 */
}

h1 {
  font-size: 24px;
}

h2 {
  font-size: 22px;
}

h3 {
  font-size: 20px;
}

h4 {
  font-size: 18px;
}

h5 {
  font-size: 16px;
}

h6 {
  font-size: 14px;
}

/* 省略号文本样式 */
.ellipsis {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle; /* 垂直居中 */
}
.ellipsis-text {
  display: flex;
  max-width: calc(100% - 10px); /* 为引号预留更多空间 */
  color: black; /* 确保文字为黑色 */
  position: relative; /* 为渐变遮罩定位 */
  align-items: center; /* 垂直居中 */
  overflow: hidden;
  height: 100%;
}
/* 省略号文本样式 */
.ellipsis-text-inline {
  display: inline-flex; /* 使用inline-flex以支持垂直居中 */
  max-width: calc(100% - 10px); /* 为引号预留更多空间 */
  position: relative; /* 为渐变遮罩定位 */
  align-items: center; /* 垂直居中 */
  overflow: hidden;
  height: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ellipsis-text::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 10px; /* 渐变区域宽度 */
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), white); /* 从透明到白色的渐变 */
  pointer-events: none; /* 确保不影响鼠标事件 */
}

.ellipsis-text p,
.ellipsis-text-inline p {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle; /* 垂直居中 */
}
ul {
  list-style: initial;
  padding-left: 1.5em; /* 为顶级列表添加左边距 */
}
ol {
  list-style: decimal;
  padding-left: 1.5em; /* 为顶级列表添加左边距 */
}
ul ul,
ul ol,
ol ul,
ol ol {
  padding-left: 1.5em; /* 保持嵌套列表的左边距 */
}

/* 滚动条样式 - 默认隐藏，hover时显示 **/
.hover-scrollbar {
  /* 现代浏览器支持 - 预留滚动条空间 */
  scrollbar-gutter: stable;

  /* 兼容性方案 - 始终显示滚动条轨道 */
  &::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(220, 223, 230, 0); /* 默认半透明 */
    border-radius: 3px;
    transition: background-color 0.3s ease;
  }

  &:hover::-webkit-scrollbar-thumb {
    background-color: #dcdfe6; /* hover时完全不透明 */
  }
}
/* 问号图标样式 */
.question-icon {
  cursor: pointer;
  position: relative;
  animation: questionIconFadeIn 0.2s ease-in-out;
}

.question-icon-circle {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  img {
    width: 16px;
    height: 16px;
  }
}

.question-icon:hover .question-icon-circle {
  background: #f2f2f2;
}

/* 悬浮提示样式 */
.question-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  background: #666666;
  color: white;
  padding: 2px 5px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.question-icon:hover .question-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%);
}
/* 知识源图tooltip样式 */

/* 悬浮提示样式 */
.klg-tooltip {
  .question-tooltip ();
  width: 70px;
  height: 24px;

  top: 100%;
  left: 20%;
  padding: 4px 12px;
  white-space: nowrap;
  font-size: 12px;
}
.klg-icon {
  cursor: pointer;
  position: relative;
  animation: questionIconFadeIn 0.2s ease-in-out;
  display: flex;
}

.klg-icon-circle {
  .question-icon-circle();
}

.klg-icon:hover .klg-icon-circle {
  background: #f2f2f2;
}

.klg-icon:hover .klg-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%);
  z-index: 999;
} /* 问号图标淡入动画 */
@keyframes questionIconFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
/* 覆盖highlight.js库的.highlight样式 */
.highlight {
  color: inherit !important;
  background-color: transparent !important;
  cursor: inherit !important;
}

.highlightHover {
  text-decoration: none !important;
  font-size: 1.02em;
  background-color: #d6e9f6 !important;
  transform-origin: center;
  transition: background-color 0.2s ease-in-out;
  position: relative;
  z-index: 999;
}
//置顶按钮
.pin-badge {
  background-color: #1973cb;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  font-weight: 500;
}
.highlightHover .equation {
  background-color: #d6e9f6 !important;
  text-decoration: none;
  font-size: 1.02em;
  transform-origin: center;
  transition: background-color 0.2s ease-in-out;
  position: relative;
  z-index: 10;
}

/* 确保内部元素不继承下划线，但排除floating-content */
.highlightHover *:not(.floating-content):not(.floating-content *) {
  text-decoration: none !important;
}
.el-progress__text {
  font-weight: 400;
  font-style: normal;
  font-family: 'Arial Normal', 'Arial', sans-serif;
  font-size: 13px !important;
}

#underline ul {
  list-style: disc;
  padding-left: 1.5em;
  margin: 0.5em 0;
}

#underline ol {
  list-style: decimal;
  padding-left: 1.5em;
  margin: 0.5em 0;
}

/* 确保嵌套列表的样式 */
#underline ul ul,
#underline ul ol,
#underline ol ul,
#underline ol ol {
  margin: 0.25em 0;
  padding-left: 1.5em;
}

/* 列表项样式 */
#underline li {
  margin: 0.25em 0;
  line-height: 1.5;
}
#underline table {
  border: 1px solid #ddd !important;
  border-collapse: collapse;
  background-color: #fff;
}

#underline thead {
  background-color: #f5f5f5;
}

#underline tr {
  border-bottom: 1px solid #eee;
}

#underline th,
#underline td {
  padding: 8px 12px;
  text-align: left;
  border-right: 1px solid #eee;
  vertical-align: top;
}
#underline th {
  font-weight: bold;
  background-color: #f0f0f0;
  color: #333;
}
// 必要、公开标签
.tags {
  margin-top: 10px;
  margin-left: 6px;
  display: flex;
  gap: 3px;
  font-size: 12px;

  .tag1,
  .tag2 {
    height: 20px;
    width: 50px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tag1 {
    background-color: #1973cb;
    color: white;
  }

  .tag2 {
    background-color: #ffd24a;
    color: #4d0819;
  }
}
// qustion
.question-content .keyWords,
// 针对DraftQuestion.vue中的keyWords
.question-info-row .keyWords ,
.description .keyWords
.keyword-container,.keyWords {
  // 通用keyWords选择器作为兜底 {
  max-width: 100% !important;
  line-height: 25px !important;
  & *:not(.equation *):not(.inline-equation *) {
    //display: inline-block !important;
  }
  // 特别处理p标签，确保内联显示
  p {
    display: inline !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  // 公式元素样式
  .equation {
    display: inline-block !important;
    //margin-top: -20px !important;
    cursor: pointer !important;
    transition: opacity 0.2s ease !important;
    // overflow: hidden;
    .textfont();
    font-size: 10px !important;
    base {
      max-height: 25px !important;
    }
  }

  // 图片元素样式
  img {
    display: inline !important;
    height: 14px !important;
    cursor: pointer !important;
    transition: opacity 0.2s ease !important;
  }
}
// 时间
.select_to_ask_time {
  width: 70px;
  font-weight: 600;
  color: #333333;
  font-size: 12px;
  margin-bottom: 5px;
  user-select: none;

  &:hover {
    cursor: pointer;
    color: var(--color-theme-project);
  }
}
// 行内公式取消默认样式
code:not(.vditor-reset code) {
  padding: 0.065em 0.4em;
  word-break: break-word;
  overflow-x: auto;
  background-color: #fff4f4 !important;
  border-radius: 2px;
  color: #c2185b !important;
  span {
    color: #c2185b !important;
  }
}
