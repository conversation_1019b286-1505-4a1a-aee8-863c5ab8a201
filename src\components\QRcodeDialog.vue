<template>
  <div class="warpper-pay">
    <el-dialog
      v-model="model"
      width="908px"
      :show-close="false"
      class="dialog-container"
      :before-close="handleClose"
      :destroy-on-close="true"
    >
      <span class="iconfont icon-icon_close" @click="handleClose()"></span>
      <div class="left">
        <div class="header">
          <div class="title">订单信息</div>
          <el-divider class="divider"></el-divider>
        </div>
        <div class="prj-info">
          <img class="image" :src="orderDetail.coverPic" />

          <div class="title" v-html="processAllLatexEquations(orderDetail.title)"></div>
        </div>
        <div class="content">
          <div class="box">
            <div class="normal">包含知识</div>
            <div class="highlight">{{ orderDetail.klgCount }}个</div>

            <div class="tip">赠送讲解和测评项目</div>
          </div>
          <div class="box">
            <div class="normal">学习周期</div>
            <div class="highlight">{{ orderDetail.studyTime }}天</div>
          </div>
        </div>
        <el-card class="order" shadow="never">
          <div class="warpper">
            <div class="col-1">
              <div class="row">
                <div class="normal">订单编号：</div>
                <div class="normal">{{ orderInfo?.orderNo }}</div>
              </div>
              <div class="row">
                <div class="normal">下单时间：</div>
                <div class="normal">{{ orderInfo?.createTime }}</div>
              </div>
              <div class="row">
                <div class="normal">订单状态：</div>
                <div class="normal" v-show="orderInfo?.orderNo">{{ dealStatus[orderInfo?.orderStatus ?? 0] }}</div>
                <div class="last-time" v-show="orderInfo?.orderNo">
                  <el-icon><Clock /></el-icon>
                  <span> {{ lastTime }} </span>
                </div>
              </div>
            </div>
            <div class="col-2">
              <div class="row">
                <div class="normal">价格：</div>
                <div class="normal">{{ orderInfo?.subtotal }}元</div>
              </div>
              <div class="row">
                <div class="normal">折扣：</div>
                <div class="normal">{{ orderInfo?.discount }}折</div>
              </div>
              <div class="row">
                <div class="highlight">实付：</div>
                <div class="highlight">{{ orderInfo?.actualPaidAmount }}元</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      <FreePayCard
        v-if="orderDetail.actualPaidAmount == 0"
        class="right"
        @paySuccess="handleBuy"
        :skuId="props.skuId"
        :title="orderDetail.title"
        :actualPaidAmount="orderDetail.actualPaidAmount"
        :studyTime="orderDetail.studyTime"
      ></FreePayCard>
      <QRcode
        v-show="isPay"
        ref="qrcode"
        class="right"
        :skuId="props.skuId"
        @paySuccess="handleBuy"
        :orderNo="orderNo"
        :subtotal="orderDetail.subtotal"
        :discount="orderDetail.discount"
        :actualPaidAmount="orderDetail.actualPaidAmount"
      ></QRcode>

      <Transition name="fade">
        <div v-if="confirmCloseDialog" class="before-close-warpper">
          <el-card class="before-close-dialog" shadow="never">
            <div class="title">提醒</div>
            <el-divider class="divider" />
            <div class="content">您还没有完成支付，确定要离开嘛？</div>
            <CmpButton
              class="btn leave"
              type="info"
              @click="
                confirmClose = true;
                confirmCloseDialog = false;
              "
              >狠心离开</CmpButton
            >
            <CmpButton
              class="btn"
              type="primary"
              @click="
                confirmClose = false;
                confirmCloseDialog = false;
              "
              >继续支付</CmpButton
            >
          </el-card>
        </div>
      </Transition>
    </el-dialog>
    <SuccessAnimation v-model="paySuccess" />
  </div>
</template>

<script setup lang="ts">
import SuccessAnimation from './SuccessAnimation.vue';
import QRcode from './QRcode.vue';
import CmpButton from '@/components/CmpButton.vue';
import { Clock } from '@element-plus/icons-vue';
import { useProjectStore } from '@/stores/project';
import { getPrjIntroduceApi, getOrderInfoApi } from '@/apis/case';
import { getCreatedOrder } from '@/apis/payment'
import FreePayCard from './FreePayCard.vue';
import { useLearningStore } from '@/stores/learning';
import { processAllLatexEquations } from '@/utils/latexUtils';
const projectStore = useProjectStore();
const learningStore = useLearningStore();

const { info } = storeToRefs(projectStore);

const route = useRoute();

// 加载二维码
// 关闭dialog
// 接收父组件传递的dialogAddVisible
const props = defineProps(['orderNo', 'skuId']);

const agreementChecked = ref(false); // 用来判断是否勾选协议
let orderInfo = ref<any>({})

const qrcode = ref<InstanceType<typeof QRcode> | null>(null);

//orderInfo.value = computed(() => {
  //console.log('qqqqqqqqqqqq')
  //return qrcode.value?.orderInfo;
//});

const confirmCloseDialog = ref<boolean>(false);
const confirmClose = ref(false);
const now = ref(new Date());
let timer: string | number | NodeJS.Timeout | undefined;
const paySuccess = ref(false);
const orderDetail = ref();
const model = defineModel();
onMounted(() => {
  watch(
    () => model.value,
    (newVal) => {
      clearInterval(timer);
      if (newVal) {
        timer = setInterval(() => {
          now.value = new Date();
        }, 1000);
      }
    }
  );
});

const skuId = ref('');
const isPay = ref(true);
watch(
  () => orderDetail.value,
  (newVal) => {
    if (newVal) {
      if (orderDetail.value.actualPaidAmount == 0) {
        isPay.value = false;
      } else {
        isPay.value = true;
      }
    }
  }
);

watch(
  () => props.skuId,
  async (newVal) => {
    if (newVal) {
      const res = await getOrderInfoApi(newVal);
      orderDetail.value = res.data;
    }
  }
);

onMounted(async () => {
  const res = await getOrderInfoApi(props.skuId);
  orderDetail.value = res.data;
  learningStore.mounted = true
});

const lastTime = computed(() => {
  if (!orderInfo.value) return '';

  const diffInSeconds =
    7200 -
    Math.floor((now.value.getTime() - new Date(orderInfo.value.createTime).getTime()) / 1000);

  // 计算小时、分钟和秒
  const hours = Math.floor(diffInSeconds / 3600)
    .toString()
    .padStart(2, '0');
  const minutes = Math.floor((diffInSeconds % 3600) / 60)
    .toString()
    .padStart(2, '0');
  const seconds = (diffInSeconds % 60).toString().padStart(2, '0');

  return `${hours}:${minutes}:${seconds}`;
});

const dealStatus: Record<number, string> = {
  0: '未支付',
  20: '已支付'
};

function handleClose(done?: () => void) {
  confirmCloseDialog.value = true;
  const unwatch = watch(confirmCloseDialog, (val) => {
    if (val) return;
    if (confirmClose.value) {
      if (done) done();
      else {
        model.value = false;
      }
    }
    unwatch();
  });
}
const emit = defineEmits(['paySuccess']);
const handleBuy = async () => {
  paySuccess.value = true;
  model.value = false;
  emit('paySuccess');
};

//向后端发送一个请求，看看该用户有没有已经创建过的该商品订单
onMounted(async()=>{
  //在商品页面的购买需要进行判断，但是在订单列表的时候一定有已经创建好的订单
  if(route.path == "/goodIntroduce" || route.path == "/learning") {
    const skuId = props.skuId
    //const uniqueCode = userStore.userInfo.uniqueCode
    let res = await getCreatedOrder(skuId)
    //有创建好的订单
    if(res.message == '成功' && res.data.orderNo) {
      orderInfo.value = res.data
    } else {
      orderInfo.value.discount = info.value.priceList[0].discount;
      orderInfo.value.subtotal = info.value.priceList[0].subtotal;
      orderInfo.value.actualPaidAmount = info.value.priceList[0].actualPaidAmount;
      watch(
        () => qrcode.value?.orderInfo.orderNo,
        () => {
        orderInfo.value.orderNo = qrcode.value?.orderInfo.orderNo;
        orderInfo.value.createTime = qrcode.value?.orderInfo.createTime;
        orderInfo.value.orderStatus = qrcode.value?.orderInfo.orderStatus;
  }
)
    }
  }
})

onUpdated(async()=>{
  if(route.path == "/shoppinglist/order/prjorder"){
    let createdOrder = projectStore.createdOrder;
    orderInfo.value.orderNo = createdOrder.orderNo;
    orderInfo.value.createTime = createdOrder.creatTime;
    orderInfo.value.orderStatus = createdOrder.orderStatus;
    orderInfo.value.subtotal = createdOrder.subtotal;
    orderInfo.value.discount = createdOrder.discountAmount;
    orderInfo.value.actualPaidAmount = createdOrder.actualPaidAmount;
  } else if (route.path == "/goodIntroduce" || route.path == "/learning") {
    if(route.path == "/goodIntroduce") {
      const skuId = props.skuId
      //const uniqueCode = userStore.userInfo.uniqueCode
      let res = await getCreatedOrder(skuId)
      //有创建好的订单
      if(res.message == '成功' && res.data.orderNo) {
        orderInfo.value = res.data
    }
  }
  }
})
</script>

<style scoped lang="less">
.graystyle {
  color: #797979;
}
.bluestyle {
  color: var(--color-theme-project);
}
.userspace-fontstyle1 {
  font-size: 18px;
  font-weight: 700;
  color: #4d0819;
}

.userspace-fontstyle2 {
  font-size: 14px;
  font-weight: 400;
}

.userspace-fontstyle3 {
  font-size: 12px;
  font-weight: 400;
  color: #797979;
}

.userspace-fontstyle4 {
  font-size: 28px;
  font-weight: 400;
}

.img-style {
  width: 60px;
  height: 60px;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-icon_close {
  position: absolute;
  right: 10px;
  top: 10px;
}

.warpper-pay {
  &:deep(.el-dialog) {
    border-radius: 5px;
    height: 543px;
    padding: 0;
  }
  &:deep(.el-dialog__header) {
    padding: 0;
  }
  &:deep(.el-dialog__body) {
    padding: 0;
    color: #333333;
    font-size: 12px;
    font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
      '阿里巴巴普惠体 3.0';
    font-weight: 400;
    display: flex;
    flex-direction: row;
  }
  .dialog-container {
    width: 908px;
    height: 543px;
    display: flex;
    flex-direction: row;

    .left {
      width: 556px;
      height: 543px;
      display: flex;
      flex-direction: column;

      .header {
        padding: 10px 30px 10px 30px;
        .title {
          color: black;
          font-size: 18px;
          font-weight: 700;
        }
        .divider {
          margin: 10px 0;
        }
      }

      .prj-info {
        padding: 10px 10px 10px 30px;
        display: flex;
        flex-direction: row;
        .image {
          width: 140px;
          height: 102px;
          border-radius: 5px;
        }
        .title {
          margin-left: 20px;
          display: flex;
          flex-wrap: wrap;
          align-content: center;
          color: black;
          font-size: 18px;
          font-weight: 700;
        }
      }

      .content {
        display: flex;
        flex-direction: row;
        justify-content: center;

        .box {
          width: 160px;
          height: 160px;
          background-color: #ffd37a;
          border-radius: 5px;
          margin: 10px 30px;
          display: flex;
          flex-direction: column;
          box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.1);

          .normal {
            margin: 30px auto 5px auto;
            color: #4d0819;
            font-size: 18px;
            font-weight: 700;
          }

          .highlight {
            margin: 5px auto 5px auto;
            color: #4d0819;
            background-color: unset;
            font-size: 28px;
            font-weight: 700;
          }

          .tip {
            margin: 5px auto;
            color: #4d0819;
            font-size: 14px;
            font-weight: 400;
          }
        }
      }

      .order {
        margin: 10px 20px;
        background-color: #fafafa;
        border: unset;
        .warpper {
          display: grid;
          grid-template-columns: 3fr 1fr;
          .col-1 {
            grid-column: 1 / 2;
          }
          .col-2 {
            grid-column: 2 / 3;
          }

          .row {
            display: flex;
            align-items: center;
            .normal {
              display: inline-block;
              color: #333333;
              font-size: 14px;
              font-weight: 400;
            }

            .highlight {
              display: inline-block;
              color: #333333;
              background-color: unset;
              font-size: 16px;
              font-weight: 700;
            }

            .last-time {
              display: flex;
              align-items: center;
              margin: 1px 0 0 5px;
              color: var(--color-theme-project);
            }
          }
        }
      }
    }

    .right {
      width: 352px;
      height: 543px;
      display: flex;
      flex-direction: column;
      border-radius: 0 5px 5px 0;
    }
    .before-close-warpper {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      background-color: white;
      justify-content: center;
      align-items: center;
      .before-close-dialog {
        width: 500px;
        background-color: #f2f2f2;
        border: unset;
        font-size: 12px;
        font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
          '阿里巴巴普惠体 3.0';
        font-weight: 400;
        .title {
          align-content: center;
          color: black;
          font-size: 18px;
          font-weight: 700;
        }
        .divider {
          margin: 10px 0;
        }
        .content {
          color: #333333;
          margin: 10px 0 100px 0;
          font-size: 14px;
          font-weight: 400;
        }
        .btn {
          margin: 10px 30px;
          width: 150px;
          height: 35px;
          font-size: var(--fontsize-middle-project);
        }
        .leave{
          background-color:#ffffff ;
        }
      }
    }
  }
}

.dialog-container-info {
  width: 556px;
  display: flex;
  flex-direction: column;

  .dialog-container-myinfo {
    height: 110px;
    background: linear-gradient(to right, #ffd37a, #fff9eb);

    .dialog-container-myinfo-content {
      position: absolute;
      left: 23px;
      top: 31px;
      display: flex;
      flex-direction: row;

      .dialog-container-name {
        margin-left: 10px;
        margin-top: 10px;

        .dialog-container-name-top {
          margin-bottom: 10px;
          .dialog-container-name-topleft {
            .userspace-fontstyle1();
            margin-right: 20px;
          }

          .dialog-container-name-topright {
            .userspace-fontstyle2();
            color: #4d0819;
          }
        }
      }
    }
  }

  .dialog-container-fieldinfo {
    width: 556px;
    flex: 1;
    background-color: #fff;

    .dialog-container-fieldinfo-content {
      margin-left: 23px;
      margin-top: 18px;

      .dialog-container-fieldinfo-top {
        .userspace-fontstyle2();
        letter-spacing: 1.5px;
      }

      .field-tags {
        margin-top: 27px;
        width: 508px;
        height: 314px;

        .field-tags-top {
          height: 28px;
          display: flex;
          flex-direction: row;
          position: relative;
          .icon-arrowcircleleft2 {
            position: absolute;
            left: -18px;
            top: 5px;
          }

          .icon-arrowcircleright2 {
            position: absolute;
            right: -18px;
            top: 5px;
          }
          .field-tags-item {
            width: 126px;
            height: 28px;
            margin-right: 9px;
            font-size: 13px;
            font-weight: 400;
            text-align: center;
            line-height: 28px;
            border: 1px solid #f2f2f2;
          }
          .defaultSty {
            background-color: #fff;
          }
          .clickSty {
            background-color: #f2f2f2;
          }
          .field-tags-item:hover {
            background-color: #f2f2f2;
          }
          .field-tags-item:last-of-type {
            margin-right: 0;
          }
        }
        .field-tags-bottom {
          background-color: #f2f2f2;
          .description {
            .userspace-fontstyle2();
            padding-top: 12px;
            padding-left: 10px;
          }
          .payforcard {
            display: flex;
            height: 259px;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            .payforcard-base {
              width: 163px;
              height: 165px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: space-around;
              border-radius: 5px;
            }
            .payforcard-month {
              .payforcard-base();

              margin-right: 30px;
            }
            .pickstyle {
              background-color: var(--color-theme-project);
              color: #fff;
            }
            .defaultstyle {
              background-color: #fff;
            }
            .payforcard-year {
              .payforcard-base();
            }
          }
        }
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
