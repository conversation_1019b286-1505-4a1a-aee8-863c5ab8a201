<template>
  <!-- 知识讲解 -->
  <LayoutCard v-bind="data"> </LayoutCard>
</template>

<script setup lang="ts">
import LayoutCard from './LayoutCard.vue';
import { getKlginfoApi } from '@/apis/home';
import { PrjType, PrjForm } from '@/types/project';
const data = ref({
  title: '知识测评',
  word: '通过完成评测巩固自己的知识体系',
  tip: '只是看过知识并不能确保真的学会了，能运用的知识才是自己的',
  prjType: PrjType.exam,
  prjFormList: [PrjForm.video, PrjForm.draft],
  big: false,
  getListApi: getKlginfoApi
});
</script>

<style scoped lang="less"></style>
