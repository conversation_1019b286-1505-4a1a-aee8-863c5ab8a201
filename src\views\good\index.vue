<template>
  <el-skeleton :loading="!ready" animated>
    <template #template>
      <div class="main-wrapper">
        <div class="main">
          <div class="top" style="display: flex;">
            <el-skeleton-item variant="image" style="width:200px;height:150px;margin-right:24px;" />
            <div style="flex:1;">
              <el-skeleton-item variant="rect" style="width:80%;height:28px;margin-bottom:18px;" />
              <el-skeleton-item variant="rect" style="width:60%;height:20px;margin-bottom:12px;" />
              <el-skeleton-item variant="rect" style="width:60%;height:36px;" />
            </div>
          </div>
          <div class="middle" style="margin:32px 0;">
            <el-skeleton-item variant="rect" style="width:90%;height:38px;margin-bottom:18px;" />
            <el-skeleton-item variant="rect" style="width:90%;height:38px;" />
          </div>
          <div class="menu-wrapper">
            <div class="menu-title" style="display:flex; border-bottom: unset;">
              <el-skeleton-item variant="rect" style="width:360px;height:36px;" />
            </div>
            <div class="menu-content" style="margin-top:20px;">
              <el-skeleton-item variant="rect" style="width:100%;height:260px;border-radius:5px;" />
            </div>
          </div>
        </div>
        <div class="remainder" style="width:290px;margin-left:12px;">
          <el-skeleton-item variant="rect" style="width:100%;height:80px;margin-bottom:18px;" />
          <el-skeleton-item variant="rect" style="width:100%;height:80px;" />
        </div>
      </div>
    </template>
    <template #default>
      <!-- 原有内容 -->
      <div class="main-wrapper">
        <div class="main">
          <div class="top">
            <div class="img">
              <img :src="info.coverPic" alt="" style="height: 100%; width: 100%"/>
            </div>
            <div class="info">
              <div class="title"><span v-html="info.title"></span></div>
              <div class="user">
                <img :src="info.editorPic" class="logo" />
                <span class="wjby">{{ info.editorName }}</span>
              </div>
              <div class="price-button">
                <template v-if="info.hasPermission == BuyStatus.nobuy"
                  ><!-- 未购买的领域商品要显示 学习周期：单选框 以及对应的价格 -->
                  <div class="vip-wrap" v-if="info.goodsType == GoodsType.vip">
                    <span>学习周期:</span>
                    <el-select
                      v-model="selectedPriceDetails.skuId"
                      @change="handlePriceChange"
                      style="width: 75px; margin-left: 10px; margin-right: 10px"
                    >
                      <el-option
                        v-for="item in info.priceList"
                        :key="item.skuId"
                        :label="getCardTypeLabel(item.cardType)"
                        :value="item.skuId"
                      />
                    </el-select>
                    <p>购买费用: ￥{{ selectedPriceDetails.actualPaidAmount }} 元</p>
                  </div>
                  <!-- 普通商品直接显示价格 -->
                  <div v-else class="common-wrap">
                    购买费用：{{ info.priceList[0].actualPaidAmount }} 元
                  </div>
                </template>
                <!-- 根据是否购买了此商品显示不同的按钮 -->
                <div class="button-wrapper">
                  <template v-if="info.hasPermission == BuyStatus.bought">
                    <div class="btn-wrapper">
                      <CmpButton class="btn1" type="primary" @click="toDetail()">{{
                        computePercent(info.learnedDays, info.klgCount) == 0 ? '开始学习' : '继续学习'
                      }}</CmpButton>
                    </div>
                  </template>
                  <template v-else
                    ><div class="btn-wrapper" v-if="info.goodsType != GoodsType.vip">
                      <CmpButton class="btn2" type="primary" @click="setPayDialogVisibleTrue"
                        >购买</CmpButton
                      >
                      <CmpButton class="btn2" type="info" @click="setTryWatchTrue" v-show="withTryChpater"
                        >试学</CmpButton
                      >
                    </div>
                    <!-- 会员商品没有试学 -->
                    <div class="btn-wrapper" v-else>
                      <CmpButton
                        class="btn2"
                        type="primary"
                        @click="setPayDialogVisibleTrue"
                        style="width: 100px"
                        >购买</CmpButton
                      >
                    </div></template
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="middle">
            <!-- 会员商品没有问题数 -->
            <template v-if="isVip && buyStatus">
              <div class="process-group">
                <div class="points">
                  <div class="number-wrap">
                    <span class="large">{{ info.masteredKlgCount }}/</span>
                    <span class="large">{{ info.fullyMasteredKlgCount }}</span>
                    <span class="small">/{{ info.klgCount }}</span>
                  </div>

                  <div class="label">已掌握 / 全掌握 / 知识点数</div>
                </div>
                <div class="days">
                  <div class="number-wrap">
                    <span class="large">{{ info.learnedDays }}</span>
                    <span class="small">/{{ info.validDays }}</span>
                  </div>

                  <div class="label">已学习 / 学习天数</div>
                </div>
              </div>
            </template>
            <template v-else-if="isVip && !buyStatus">
              <div class="process-group">
                <div class="points">
                  <span class="small">{{ info.klgCount }}</span>
                  <div class="label">知识点总数</div>
                </div>
                <div class="days">
                  <span class="small">{{ selectedPriceDetails.studyTime }}</span>
                  <div class="label">总学习天数</div>
                </div>
              </div>
            </template>
            <template v-else-if="!isVip && buyStatus">
              <div class="process-group">
                <div class="points">
                  <div class="number-wrap">
                    <span class="large">{{ info.masteredKlgCount }}/</span>
                    <span class="large">{{ info.fullyMasteredKlgCount }}</span>
                    <span class="small">/{{ info.klgCount }}</span>
                  </div>

                  <div class="label">已掌握 / 全掌握 / 目标知识点数</div>
                </div>
                <div class="days">
                  <div class="number-wrap">
                    <span class="large">{{ info.learnedDays }}</span>
                    <span class="small">/{{ info.validDays }}</span>
                  </div>

                  <div class="label">已学习 / 学习天数</div>
                </div>
                <div class="questions">
                  <div class="number-wrap">
                    <span class="large">{{ info.learnedQuestionCount }}</span>
                    <span class="small">/{{ info.questionCount }}</span>
                  </div>

                  <div class="label">已学习 / 问题总数</div>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="process-group">
                <!-- 案例需要显示知识点数 -->
                <div class="points" v-if="info.prjType == PrjType.case">
                  <span class="small">{{ info.klgCount }}</span>
                  <div class="label">知识点总数</div>
                </div>
                <!-- 领域讲解需要显示目标领域总数 -->
                <div class="points" v-else-if="info.prjType == PrjType.area">
                  <span class="small">{{ info.targetKnowledgeCount }}</span>
                  <div class="label">目标领域总数</div>
                </div>
                <!-- 知识讲解需要显示目标知识点数 -->
                <div class="points" v-else>
                  <span class="small">{{ info.targetKnowledgeCount }}</span>
                  <div class="label">目标知识点总数</div>
                </div>
                <div class="days">
                  <span class="small">{{ info.priceList[0].studyTime }}</span>
                  <div class="label">总学习天数</div>
                </div>
                <div class="questions">
                  <span class="small">{{ info.questionCount }}</span>
                  <div class="label">问题总数</div>
                </div>
              </div>
            </template>
          </div>
          <div class="menu-wrapper">
            <div class="menu-title">
              <!-- 普通商品和会员商品显示的菜单选项不同 -->
              <div
                @click="setActive('goodContent')"
                :class="['nav-item', { active: currentActive == 'goodContent' }]"
              >
                <span v-if="isVip">会员内容</span>
                <span v-else>项目内容</span>
              </div>
              <div
                v-if="!isVip"
                @click="setActive('questionList')"
                :class="['nav-item', { active: currentActive == 'questionList' }]"
              >
                <span>问题列表</span>
              </div>
              <div @click="setActive('map')" :class="['nav-item', { active: currentActive == 'map' }]">
                <span>知识源图</span>
              </div>
            </div>
            <div class="menu-content">
              <component
                v-if="ready && currentContentComponent"
                :is="currentContentComponent.component"
                v-bind="currentContentComponent.props"
                @showLoginDialog="showLoginDialog"
              ></component>
            </div>
          </div>
        </div>
        <div class="remainder">
          <!--购买后显示学习计划  -->
          <div class="study-schedule" v-if="info.hasPermission == BuyStatus.bought">
            <div style="display: flex">
              <el-icon><Calendar /></el-icon>
              <span style="font-size: 14px; font-weight: 700; margin-left: 12px">学习计划</span>
            </div>
            <div class="study-time">学习开始时间： {{ info.startTime }}</div>
            <div style="display: flex; flex-direction: row">
              <div class="study-time">学习结束时间： {{ info.expirationTime }}</div>
              <div
                v-if="!isFree"
                style="
                  width: 18px;
                  height: 30px;
                  display: flex;
                  align-items: flex-end;
                  justify-content: center;
                  margin-left: 80px;
                "
              >
                <img src="@/assets/images/prjlearn/危险警告.svg" alt="" />
              </div>
              <div class="money-renew" @click="setPayDialogVisibleTrue" v-if="!isFree">续费</div>
            </div>
          </div>
          <div v-if="info.hasPermission == BuyStatus.bought"><el-divider></el-divider></div>
          <div class="study-description">
            <div class="description-header">
              <img src="@/assets/images/prjlearn/u4108.svg" alt="" />
              <span style="margin-left: 12px">学习说明</span>
            </div>
            <div
              style="color: #333; font-size: 14px; font-family: var(--text-family); padding-top: 10px"
            >
              {{ info.studyInstructions }}
            </div>
          </div>
        </div>

        <!-- 目前只有会员商品使用这个组件  -->
        <PayDialog
          v-model="payDialogVisible"
          v-if="ready && isVip"
          :spuId="info.spuId"
          :selectedSkuId="selectedSkuId"
          :isUserAdd="false"
          @paySuccess="handleBuy"
        />

        <!-- 从介绍页直接打开购买页的一定不是会员商品 会员商品需要先选择 -->
        <QRcodeDialog
          v-model="payDialogVisible"
          :skuId="skuId"
          v-if="ready && !isVip"
          @paySuccess="handleBuy"
        />
      </div>

      <LoginDialog ref="loginDialogRef" />
    </template>
  </el-skeleton>
</template>


<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import QRcodeDialog from '@/components/QRcodeDialog.vue';
import FreeDialog from '@/components/FreeDialog.vue';
import PayDialog from '@/components/PayDialog.vue';
import { getPrjIntroduceApi, getVipIntroduceApi } from '@/apis/case';
import { BuyStatus, GoodsType } from '@/types/goods';
import { useProjectStore } from '@/stores/project';
import { useLearningStore } from '@/stores/learning';
import GoodContent from './components/GoodContent.vue';
import QuestionListContent from './components/QuestionListContent.vue';
import MapContent from './components/MapContent.vue';
import { computePercent } from '@/utils/computeNumber';
import { Calendar } from '@element-plus/icons-vue';
import { PrjForm, PrjType } from '@/types/project';
import { onBeforeUnmount } from 'vue';

//登录状态相关，用前两个,v-show="infoStore.getUserId()"
import { userInfoStore } from '@/stores/userInfo';
const infoStore = userInfoStore();
import checkLoginStatus from '@/utils/checkLoginStatus';
import LoginDialog from '@/components/LoginDialog.vue';
const isLogined = checkLoginStatus();

//登录弹窗相关
import { ref } from 'vue';

const loginDialogRef = ref()
function showLoginDialog() {
  loginDialogRef.value?.open()
}

import { useRouter, useRoute } from 'vue-router';

// 监听登录成功事件
const handleLoginSuccess = async (event: CustomEvent) => {
  console.log('收到登录成功消息:', event.detail);
  
  // 重新获取用户信息
  //await infoStore.getUserInfo();
  
  // 重新获取商品信息
  //await refreshGoodInfo();
  router.go(0);
}

const setPayDialogVisibleTrue = () => {
  if (!infoStore.getUserId()) {
    showLoginDialog();
    return;
  }
  payDialogVisible.value = true;
}

const setTryWatchTrue = () => {
  if (!infoStore.getUserId()) {
    showLoginDialog();
    return;
  }
  tryWatch();
}


//设置布尔值用于根据不同商品的不同购买状态显示不同内容
const isVip = ref(false); //是否是会员商品
const buyStatus = ref(false); // 是否已购买

const isFree = ref(false);
const router = useRouter();

const route = useRoute();
const spuId = route.query.spuId as string;
const tabParam = route.query.tab as string; // 获取标签页参数

const projectStore = useProjectStore();
const learningStore = useLearningStore();
const { info } = storeToRefs(projectStore);
const payDialogVisible = ref(false);
provide('payDialogVisible', payDialogVisible);

const ready = ref(false);

const diaTitle = ref('学习进程');
const diaContent = ref(
  '开始学习后，系统会根据您的学习情况，呈现您的学习进程，让您清晰看到自己的学习之路。'
);
const diaContentOption = [
  '',
  '开始学习后，系统会根据您的学习情况，呈现您的学习进程，让您清晰看到自己的学习之路。',
  '开始学习后，系统会自动给您匹配对应的测验方案，以方便您检验学习成功。'
];

//如果是会员商品 则需要在页面下拉框选择购买的种类 默认选择第一个
// 存储选中的skuId
const selectedSkuId = ref('');
// 存储选中价格明细
const selectedPriceDetails = reactive(info.value.priceList[0]);

const handleBuy = async () => {
  //购买不刷新这个页面
  //ready.value = false;
  buyStatus.value = true;
  const res = await getPrjIntroduceApi({
    spuId: route.query.spuId as string
  });
  projectStore.setPrjInfo(res.data);
  projectStore.setSpuId(spuId);
  //ready.value = true;
};
const getCardTypeLabel = (cardType: number) => {
  switch (cardType) {
    case 1:
      return '年卡';
    case 2:
      return '季卡';
    case 3:
      return '月卡';
    default:
      return '未知卡';
  }
};
// 更新价格明细
const handlePriceChange = (skuId: string) => {
  ready.value = false;
  const selected = info.value.priceList.find((item) => item.skuId == skuId);
  if (selected) {
    Object.assign(selectedPriceDetails, selected);
    selectedSkuId.value = skuId;
  }

  ready.value = true;
};

interface resData {
  title: string;
  coverPic: string;
  purpose: string;
  description: string;
  prjForm: PrjForm;
  prjType: PrjType;
  goodsType: GoodsType;
  klgCount: number;
  priceList: [
    {
      skuId: string;
      cardType: number;
      subtotal: number;
      discount: number;
      actualPaidAmount: number;
      studyTime: number;
    }
  ];
  chapterList: [
    {
      chapterId: number;
      chapterName: string;
      preview: boolean;
    }
  ];
  editorName: string;
  editorPic: string;
  startTime: string;
  expirationTime: string;
  latestChapterId: number;
  hasPermission: BuyStatus;
  validDays: number;
  learnedDays: number;
  learnedQuestionCount: number;
  learnedKlgCount: number;
  masteredKlgCount: number;
  questionCount: number;
  targetKnowledgeCount: number;
  studyInstructions: string;
}
//后期将info换为这个
const resInfo = ref<resData>();

//试学部分
const withTryChpater = ref(false);
const chapterId = ref<string>();
const tryWatch = () => {
  // 触发试看事件
  if (chapterId.value) {
    router.push({
      path: '/learning',
      query: {
        spuId,
        chapterId: chapterId.value
      }
    });
  }
};

const toDetail = () => {
  if (!infoStore.getUserId()) {
    //未登录状态下，让他登录
    showLoginDialog();

    return;
  }
  let chaId = 0;
  //只有普通项目有chapter
  if (info.value.goodsType == GoodsType.common) {
    if ('latestChapterId' in info.value && info.value.latestChapterId) {
      chaId = info.value.latestChapterId;
      console.log('late!');
    } else {
      chaId = info.value.chapterList[0].chapterId;
      console.log('nolate!');
    }
    router.push({
      path: '/learning',
      query: {
        spuId: route.query.spuId,
        chapterId: chaId
      }
    });
  }
  //todo 跳转到领域/会员商品详情页
  else {
    router.push({
      path: '/arealearning',
      query: {
        spuId: route.query.spuId
      }
    });
  }
};

// 根据 URL 参数设置默认显示的标签页
const getInitialTab = () => {
  // 如果 URL 中有 tab 参数且值为 questionList，则显示问题列表
  if (tabParam === 'questionList') {
    return 'questionList';
  }
  return 'goodContent'; // 默认显示商品内容
};

const currentContent = ref(getInitialTab());
const currentActive = ref(getInitialTab());
const setActive = (content: string) => {
  currentContent.value = content;
  currentActive.value = content;
};
const currentContentComponent = computed(() => {
  if (currentContent.value == 'goodContent') {
    return {
      component: GoodContent,
      props: { info: info }
    };
  } else if (currentContent.value == 'questionList') {
    return {
      component: QuestionListContent,
      props: { info: info, skuId: skuId }
    };
  } else if (currentContent.value == 'map') {
    return {
      component: MapContent,
      props: { spuId: spuId }
    };
  }
  return null;
});
const skuId = ref('');
onMounted(async () => {
  // 注册登录成功事件监听器
  window.addEventListener('loginSuccess', handleLoginSuccess as EventListener);
  
  let getPrjIntroduce = getPrjIntroduceApi;
  const res = await getPrjIntroduce({ spuId: spuId });
  if (!res.data) {
    ElMessage.error('项目不存在');
    router.push({ path: '/home' });
  } else {
    projectStore.setPrjInfo(res.data);

    console.log(res.data);

    projectStore.setSpuId(spuId);
    //给购买组件的查询条件
    skuId.value = info.value.priceList[0].skuId;
    //更新布尔值用于显示不同的组件
    if (info.value.goodsType == GoodsType.vip) {
      isVip.value = true;
    } else {
      isVip.value = false;
    }

    const priceList = info.value.priceList;
    priceList.forEach((price) => {
      if (price.actualPaidAmount == 0) {
        isFree.value = true;
      }
    });

    if (info.value.buyStatus == BuyStatus.bought) {
      buyStatus.value = true;
    } else {
      buyStatus.value = false;
    }
    //如果是会员商品且未购买则下拉框设置默认值
    //初始化时候赋值不生效 特此单独赋值
    if (!buyStatus.value && isVip.value) {
      selectedPriceDetails.actualPaidAmount = info.value.priceList[0].actualPaidAmount;
      selectedPriceDetails.cardType = info.value.priceList[0].cardType;
      selectedPriceDetails.discount = info.value.priceList[0].discount;
      selectedPriceDetails.skuId = info.value.priceList[0].skuId;
      selectedPriceDetails.studyTime = info.value.priceList[0].studyTime;
      selectedPriceDetails.subtotal = info.value.priceList[0].subtotal;
      selectedSkuId.value = info.value.priceList[0].skuId;
    }

    //试学
    if (info.value.chapterList) {
      const chapterList = info.value.chapterList;
      const tryChapterId = chapterList.find((item) => item.preview == true)?.chapterId;
      if (tryChapterId) {
        withTryChpater.value = true;
        chapterId.value = tryChapterId;
      }
    }
  }
  ready.value = true;
});

onMounted(() => {
  let flag = false;
  let mounted = ref(learningStore.mounted);
  let autoPay = ref(learningStore.autoPay);
  let auto = setInterval(() => {
    if (autoPay.value && mounted.value) {
      payDialogVisible.value = true;
      flag = true;
    }
    if (flag) {
      clearInterval(auto);
      learningStore.autoPay = false;
    }
  }, 1000);
});

watch(
  () => learningStore.clickLockAtGoodContent,
  (newVal) => {
    if (newVal) {
      if (learningStore.clickLockAtGoodContent) payDialogVisible.value = true;
      learningStore.clickLockAtGoodContent = false;
    }
  }
);

// 组件销毁时清理事件监听器
onBeforeUnmount(() => {
  window.removeEventListener('loginSuccess', handleLoginSuccess as EventListener);
});
</script>
<style scoped lang="less">
.main-wrapper {
  display: flex;
  margin: 30px auto;
  width: 1400px;
  min-height: 900px;
  /* 允许容器动态延伸高度，确保内容不会被截断 */
  height: auto;

  .dg {
    .dg-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      border-bottom: 2px solid #f2f2f2;
      padding-bottom: 15px;

      h1 {
        font-size: 18px;
        font-weight: 700;
        color: var(--color-theme-project);
      }
    }

    .content {
      width: 100%;
      white-space: pre-wrap;
    }

    .foot-btns {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;

      .btn {
        width: 160px;
        height: 35px;

        &:nth-child(2) {
          margin-left: 20px;
        }
      }
    }
  }

  .remainder {
    width: 290px;
    /* 移除固定高度，允许动态延伸 */
    height: auto;
    margin-left: 12px;
    .study-schedule {
      font-size: 14px;
      .study-time {
        margin-top: 15px;
        color: #333;

        font-family: var(--text-family);
      }
      .money-renew {
        color: rgb(80, 102, 191);
        margin-top: auto;
        margin-left: 6px;
        text-decoration: underline;
      }
    }
    .study-description {
      .description-header {
        font-size: 14px;
        font-weight: 700;
        display: flex;
        align-items: center;
      }
    }
  }
  .main {
    flex: 3;
    /* 移除固定高度，允许动态延伸 */
    height: auto;

    .top {
      display: flex;
      background-color: #fafafafe;
      align-items: center;
      .img {
        width: 200px;
        height: 150px;
        background-color: #aaaaaa;
      }
      .info {
        margin-left: 12px;
        .title {
          font-size: 20px;
          margin-top: 26px;
        }
        .user {
          margin-top: 20px;
          margin-bottom: 30px;
          height: 20px;
          line-height: 20px;
          display: flex;
          .logo {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            overflow: hidden;
          }

          .wjby {
            font-size: var(--fontsize-small-project);
            font-weight: 400;
            color: #797979;
            margin-left: 10px;
          }
        }
        .price-button {
          display: flex;
          margin-bottom: 12px;
          .vip-wrap {
            display: flex;
            font-size: 14px;
            margin-top: 10px;
            font-weight: 700;
            :deep(.el-select__wrapper) {
              min-height: 20px !important;
              height: 20px;
            }
          }
          .common-wrap {
            font-size: 14px;
            margin-top: 10px;
            font-weight: 700;
            margin-right: 38px;
          }
          .button-wrapper {
            display: flex;
            //background-color: aqua;
            width: 300px;
            margin-left: 10px;
            .btn-wrapper {
              //width: 75px;
              height: 35px;

              .btn1 {
                width: 105px;
                height: 35px;
                font-size: var(--fontsize-middle-project);
                border-radius: 5px;
              }
              .btn2 {
                width: 70px;
                height: 35px;
                font-size: var(--fontsize-middle-project);
                border-radius: 5px;
              }
            }
          }
        }
      }
    }
    .middle {
      //display: flex;
      margin-top: 32px;
      margin-bottom: 32px;
      .process-group {
        width: 1066px;
        height: 80px;
        display: flex;
        justify-content: space-between;

        .points,
        .days,
        .questions {
          flex: 1;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          border-right: 1px solid #ccc;
        }
        .number-wrap {
          text-align: center;
          display: flex;
          justify-content: center;
        }
        .points:last-child,
        .days:last-child,
        .questions:last-child {
          border-right: none;
        }
        .label {
          font-size: 14px;
        }
        .large {
          font-size: 28px;
          font-weight: bold;
        }
        .small {
          font-size: 18px;
          margin-top: auto;
        }
      }
    }
    .menu-wrapper {
      .menu-title {
        display: flex;
        border-bottom: 1px solid#ccc;
        font-size: 16px;
        .nav-item {
          width: 120px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: border-color 0.3s ease;
          cursor: pointer;
        }
        .nav-item.active {
          border-bottom: 4px solid var(--color-theme-project);
          font-weight: 700;
        }
      }
      .menu-content {
        :deep(.ksgmap) {
          /* 设置具体的宽高，确保按设计要求显示 */
          height: 550px !important;
          width: 1100px !important;
          /* 限制最大尺寸，保持良好的用户体验 */
          border-radius: 5px;
        }
      }
    }
  }
}
</style>
