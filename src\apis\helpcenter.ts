import { http } from '@/apis';

/**waq */
export function getHelpCenterApi() {
  return http.get<{
    list: Array<{
      title: string;
      content: string;
    }>;
  }>('/help-center/getInfo');
}

export function getPrivacyApi() {
  return http.get<{
    title: string;
    content: string;
  }>('/privacy-policy/getInfo');
}
export function getStatementApi() {
  return http.get<{
    title: string;
    content: string;
  }>('/statement/getInfo');
}
export function getUserAgreementApi() {
  return http.get<{
    title: string;
    content: string;
  }>('/user-agreement/getInfo');
}
