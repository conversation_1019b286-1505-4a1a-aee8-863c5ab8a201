/**
 * 大纲相关工具函数
 */

export interface OutlineItem {
  id: string;
  text: string;
  level: number;
  children?: OutlineItem[];
}

// el-tree 组件需要的数据结构
export interface TreeNode {
  id: string;
  label: string;
  level: number;
  children?: TreeNode[];
}

/**
 * 从HTML内容中提取大纲
 * @param htmlContent HTML内容字符串
 * @returns 大纲项目数组
 */
export function extractOutline(htmlContent: string): OutlineItem[] {
  // 创建临时DOM元素来解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  // 查找所有标题元素
  const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');

  const allOutline: OutlineItem[] = [];

  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1)); // 获取标题级别 (1-6)
    const text = heading.textContent?.trim() || '';
    const id = `outline-${level}-${index}`;

    // 为标题元素添加ID，用于锚点跳转
    heading.id = id;

    if (text) {
      allOutline.push({
        id,
        text,
        level
      });
    }
  });

  // 如果没有标题，返回空数组
  if (allOutline.length === 0) {
    return [];
  }

  // 找到最小的两个级别
  const levels = [...new Set(allOutline.map((item) => item.level))].sort((a, b) => a - b);

  // 只保留最高的两层标题
  const topTwoLevels = levels.slice(0, 2);

  const filteredOutline = allOutline.filter((item) => topTwoLevels.includes(item.level));

  return filteredOutline;
}

/**
 * 将平铺的大纲转换为层级结构
 * @param flatOutline 平铺的大纲数组
 * @returns 层级化的大纲数组
 */
export function buildHierarchicalOutline(flatOutline: OutlineItem[]): OutlineItem[] {
  if (!flatOutline.length) return [];

  // 获取所有级别并排序
  const levels = [...new Set(flatOutline.map((item) => item.level))].sort((a, b) => a - b);

  // 如果只有一个级别，直接返回平铺结构
  if (levels.length === 1) {
    return flatOutline;
  }

  const result: OutlineItem[] = [];
  const stack: OutlineItem[] = [];
  const minLevel = levels[0];
  const secondLevel = levels[1];

  flatOutline.forEach((item) => {
    // 清理栈，移除级别大于等于当前项的元素
    while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {
      stack.pop();
    }

    // 如果是最高级别，或者栈为空，说明这是顶级项
    if (item.level === minLevel || stack.length === 0) {
      result.push(item);
    } else {
      // 否则添加到栈顶元素的children中
      const parent = stack[stack.length - 1];
      if (!parent.children) {
        parent.children = [];
      }
      parent.children.push(item);
    }

    // 将当前项推入栈
    stack.push(item);
  });

  return result;
}

/**
 * 将大纲数据转换为el-tree组件需要的格式
 * @param outline 大纲数据
 * @returns el-tree格式的数据
 */
export function convertToTreeData(outline: OutlineItem[]): TreeNode[] {
  const convertItem = (item: OutlineItem): TreeNode => {
    const treeNode: TreeNode = {
      id: item.id,
      label: item.text,
      level: item.level
    };

    if (item.children && item.children.length > 0) {
      treeNode.children = item.children.map(convertItem);
    }

    return treeNode;
  };

  return outline.map(convertItem);
}

/**
 * 滚动到指定的大纲项
 * @param id 大纲项的ID
 * @param container 滚动容器，默认为文档主体
 */
export function scrollToOutlineItem(id: string, container?: HTMLElement) {
  // 首先尝试在#underline容器中查找元素
  let element = document.querySelector(`#underline #${id}`) as HTMLElement;

  // 如果在#underline中没找到，再尝试全局查找
  if (!element) {
    element = document.getElementById(id);
  }

  // 如果还是没找到，尝试通过文本内容匹配
  if (!element) {
    const underlineContainer = document.querySelector('#underline');
    if (underlineContainer) {
      const headings = underlineContainer.querySelectorAll('h1, h2, h3, h4, h5, h6');
      // 从ID中提取级别和索引信息
      const match = id.match(/outline-(\d+)-(\d+)/);
      if (match) {
        const level = parseInt(match[1]);
        const index = parseInt(match[2]);

        // 查找对应级别的第index个标题
        const levelHeadings = Array.from(headings).filter(
          (h) => parseInt(h.tagName.charAt(1)) === level
        );

        if (levelHeadings[index]) {
          element = levelHeadings[index] as HTMLElement;
          // 为找到的元素添加ID
          element.id = id;
        }
      }
    }
  }

  if (!element) {
    return;
  }

  // 尝试多个可能的滚动容器，优先使用content-text-wrapper
  const possibleContainers = [
    container,
    document.querySelector('.content-text-wrapper'),
    document.querySelector('.main-content'),
    document.querySelector('.myContent'),
    document.querySelector('.ck-content'),
    document.querySelector('#underline'),
    document.documentElement,
    document.body
  ].filter(Boolean);

  let scrollContainer: Element | null = null;

  // 找到第一个有滚动能力的容器
  for (const cont of possibleContainers) {
    if (
      cont &&
      (cont.scrollHeight > cont.clientHeight ||
        cont === document.documentElement ||
        cont === document.body)
    ) {
      scrollContainer = cont as Element;
      break;
    }
  }

  if (
    scrollContainer &&
    scrollContainer !== document.documentElement &&
    scrollContainer !== document.body
  ) {
    // 计算元素相对于滚动容器的位置
    const containerRect = scrollContainer.getBoundingClientRect();
    const elementRect = element.getBoundingClientRect();
    const scrollTop = scrollContainer.scrollTop;

    // 计算目标滚动位置（元素顶部距离容器顶部的距离）
    const targetScrollTop = scrollTop + elementRect.top - containerRect.top - 20; // 20px的偏移量

    scrollContainer.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    });
  } else {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest'
    });
  }

  // 添加临时高亮效果
  addHighlightEffect(element);
}

/**
 * 为元素添加临时高亮效果
 * @param element 要高亮的元素
 */
function addHighlightEffect(element: HTMLElement) {
  // 移除之前的高亮
  document.querySelectorAll('.outline-highlight').forEach((el) => {
    el.classList.remove('outline-highlight');
  });

  // 添加高亮类
  element.classList.add('outline-highlight');

  // 3秒后移除高亮
  setTimeout(() => {
    element.classList.remove('outline-highlight');
  }, 3000);
}

/**
 * 为HTML内容中的标题添加ID
 * @param htmlContent HTML内容字符串
 * @returns 添加了ID的HTML内容
 */
export function addIdsToHeadings(htmlContent: string): string {
  if (!htmlContent) {
    return '';
  }

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');

  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));
    const id = `outline-${level}-${index}`;
    heading.id = id;
  });

  const result = tempDiv.innerHTML;

  return result;
}
