<template>
  <div class="hot-area-section">
    <h2 class="section-title">推荐学习领域：</h2>
    <div class="goods-list">
      <GoodsCard
        v-for="(item, idx) in list"
        :key="item.spuId || idx"
        v-bind="item"
      />
    </div>
    <div class="pagination-block" v-if="showMore">
      <span class="footerBtn" @click="getMoreFn" id="getMoreFn">
        <span class="myicon"></span>
        加载更多
      </span>
    </div>
    <div class="pagination-block" v-else v-if="current != 1">
      <span>暂无更多内容</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import GoodsCard from '@/components/GoodsCard.vue';
import { getPrjinfoApi } from '@/apis/home';
import { PrjForm, type PrjType } from '@/types/project';
import { getPrjListApi } from '@/apis/classification';
import { getAreaArea } from '@/apis/area';
const areaCode = inject('areaCode') as string;

interface PrjInfoItem {
  coverPic: string;
  title: string;
  description: string;
  spuId: string;
  prjStudyFrequence: number;
  prjQuestionNumbers: number;
  studyTime: number;
  duration: string;
  goodsType: number;
  isFree: number;
  userCoverPic: string;
  userName: string;
  prjType: PrjType;
  prjForm: number;
  buyStatus: number;
  latestChapterId: number | null;
  countWord?: number;
}

const list = ref<PrjInfoItem[]>([]);
const total = ref(0);
const totalPage = ref(0);
const showMore = ref(false);
const current = ref(1);
const limit = ref(6);

// 重置参数
const resetParams = () => {
  current.value = 1;
  totalPage.value = 0;
  total.value = 0;
};

// 获取列表数据
const getList = async () => {
  resetParams();
  const res = await getAreaArea(areaCode, current.value, limit.value);

  list.value = res.data.records || [];
  total.value = res.data.total;
  totalPage.value = Math.ceil(res.data.total / res.data.limit);
  if (current.value < totalPage.value) {
    showMore.value = true;
  } else {
    showMore.value = false;
  }
};

// 加载更多
const getMoreFn = async () => {
  current.value += 1;

  const res = await getAreaArea(areaCode, current.value, limit.value);
  list.value.push(...(res.data.records || []));
  total.value = res.data.total;
  totalPage.value = Math.ceil(res.data.total / res.data.limit);
  if (current.value < totalPage.value) {
    showMore.value = true;
  } else {
    showMore.value = false;
  }
};

onMounted(async () => {
  //const res = await getAreaArea(areaCode, current.value, limit.value);
  //console.log(res)
  await getList();
});
</script>

<style scoped>
.hot-area-section {
  //width: 1400px;
  margin: 40px auto 0 auto;
  background: #fff;
  border-radius: 8px;
  //box-shadow: 0 4px 16px rgba(0,0,0,0.06);
  padding: 16px 16px 20px 16px;
}
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-bottom: 24px;
}
.goods-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 12px;
  justify-items: center;
}
.pagination-block {
  display: flex;
  text-align: center;
  justify-content: center;
  color: var(--color-theme-project);
  font-family: var(--text-family);
  font-weight: 400;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  margin: 10px 0;
}
.footerBtn {
  width: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.footerBtn .myicon {
  width: 14px;
  height: 12px;
  margin-right: 5px;
  background-image: url('@/assets/images/project/u3964.svg');
}
.footerBtn:hover {
  font-weight: bold;
}
</style>
