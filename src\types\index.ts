import { PrjType, PrjForm } from '@/types/project';
import type { BuyStatus, GoodsType } from '@/types/goods';
import type { LessonInfoType } from './case';

export interface PrjIntroduce {
  buyStatus: BuyStatus;
  coverPic: string;
  description: string;
  goodsType: GoodsType;
  graspKlg: number;
  klgNumbers: number;
  prjType: PrjType;
  learned: number;
  price: string;
  prjForm: PrjForm;
  spuId: string;
  studyInstructions: string;
  skuId: string;
  chapterList?: LessonInfoType[];
  skuList?: Sku[];
  studyTime?: number;
  targetKlgs?: Klg[];
  title: string;
  userCoverPic: string;
  userName: string;
  latestChapterId?: number;
  validDate: number;
  prjTags?: any[];
}

export interface Klg {
  klgCode: string;
  klgTitle: string;
}

export interface Sku {
  cardType: 1 | 2 | 3;
  goodsName: string;
  discount: number;
  paymentAmount: string;
  price: string;
  skuId: string;
  studyTime: number;
}

export * from './case';
export * from './constant';
export * from './data';
export * from './exam';
export * from './goods';
export * from './history';
export * from './knowledge';
export * from './learning';
export * from './project';
export * from './question';
