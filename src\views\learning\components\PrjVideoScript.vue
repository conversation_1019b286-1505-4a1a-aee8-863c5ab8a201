<template>
  <!-- 项目文稿 -->
  <div class="script-layout">
    <div class="content-card textfont" v-if="prjForm != PrjForm.video || props.videoCaptionList">
      <div id="header" class="tool flex-space-between">
        <div class="remind flex-center">
          <img :src="remindIcon" alt="" />
          鼠标滑过讲稿内容，可以提问哦。
        </div>
        <div class="input textfont">
          <el-input
            placeholder="请输入要查找的关键字"
            v-model="searchKey"
            @keyup.enter="handleSearchFn"
            clearable
            class="custom-el-input"
          >
            <template #append>
              <el-button :icon="Search" @click="handleSearchFn" />
            </template>
          </el-input>
        </div>
      </div>
      <el-watermark :font="font" :content="userInfo.username + '@' + userInfo.phone">
        <div
          class="content-text-wrapper hover-scrollbar"
          :class="{ learning: $route.name == 'learning' }"
          ref="contentWrapperRef"
          id="script"
        >
          <!-- 视频项目的文稿 -->
          <template v-if="prjForm == PrjForm.video">
            <!--        mode: false=>read true=>question -->
            <div id="underline">
              <!-- 统一的模板，支持渲染后的内容和原始内容 -->
              <div
                v-for="(item, idx) in displayContent"
                :key="`paragraph-${idx}`"
                class="paragraph-wrapper textfont"
              >
                <!-- 时间显示 - 始终显示在段落上方 -->
                <div
                  class="select_to_ask_time titlefont"
                  v-if="item.timeInfo && item.timeInfo.startTime"
                  @click.stop="jumpToQuestionTime(item.timeInfo.startTime)"
                >
                  {{ item.timeInfo.startTime }}
                </div>
                <!-- 内容显示 -->
                <div class="text renderItem">
                  <span v-html="item.htmlContent"></span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </el-watermark>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, type Ref, type Events } from 'vue';
import { Search } from '@element-plus/icons-vue';

import { STATE_FLAG } from '@/types/learning';
import type { VideoCaptionListObj, QuestionData } from '@/types/learning';
import { PrjForm } from '@/types/project';
import { handleLineWord, getQuestionList, updataDom, markWord, unmarkWord } from '@/utils/lineWord';

import { useUserStore } from '@/stores/user';
import type XgPlayer from '@/components/XgPlayer.vue';
import { storeToRefs } from 'pinia';
import remindIcon from '@/assets/svgs/remind.svg';
import { getSeconds } from '@/utils/Video';

const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

// 从父组件注入播放器实例
const player = inject('player') as Ref<InstanceType<typeof XgPlayer> | null>;

// 打包注释
// import { tr } from 'element-plus/es/locale';
// markWord()
interface Props {
  videoCaptionList?: [VideoCaptionListObj[]]; // 讲稿段落
  questionList?: QuestionData[]; // 问题列表
  wordContent?: ''; // 文本项目文本
  renderedContent?: any; // 渲染后的内容
  // big?: false;
  isMap?: Boolean;
}
const emits = defineEmits([
  'returnInit',
  'scrollInTop',
  'refresh',
  'refreshContent',
  'deleteQuestion',
  'search',
  'toggle-video'
]);

const font = reactive({
  color: 'rgba(0, 0, 0, .07)'
});
// emit事件
const props = defineProps<Props>();
// const showContentWord=ref(false)

const prjForm = inject('prjForm') as Ref;
// const prjType = ref(1);
const isBig = inject('isBig') as Ref;

const searchKey = ref('');
const qList = ref<QuestionData[]>([]);

const myWordContent = ref();

// 计算属性：统一处理显示内容，包含时间信息
const displayContent = computed(() => {
  // 如果有渲染后的内容，需要将其转换为包含时间信息的格式
  if (props.renderedContent && Array.isArray(props.renderedContent)) {
    return props.renderedContent.map((htmlContent, idx) => {
      // 从原始数据中获取对应的时间信息
      const originalParagraph = myWordContent.value?.[idx];
      return {
        htmlContent,
        timeInfo: originalParagraph && originalParagraph.length > 0 ? originalParagraph[0] : null,
        isRendered: true
      };
    });
  }
});
console.log('displayContent', displayContent);

watch(
  () => props.wordContent,
  async (newVal) => {
    myWordContent.value = newVal;
  }
);
watch(
  () => props.videoCaptionList,
  async (newVal, oldVal) => {
    // 只有在值真正发生变化时才更新内容
    if (newVal !== oldVal && newVal) {
      myWordContent.value = newVal;
      console.log('📹 videoCaptionList变化，更新内容');
    }
  }
);

// 监听渲染后的内容变化
watch(
  () => props.renderedContent,
  (newVal) => {
    if (newVal) {
      console.log('PrjVideoScript: 接收到渲染后的内容', newVal);
    }
  }
);

// 移除问题列表变化监听，由父组件的Render对象处理
myWordContent.value = prjForm.value == PrjForm.video ? props.videoCaptionList : props.wordContent;

const stateFlag = ref(STATE_FLAG.init);

const tempSearchKey = ref('');

const handleSearchFn = async () => {
  // 通过emit事件调用父组件PrjVideoWrapper的搜索功能
  emits('search', searchKey.value);

  // 保存当前搜索关键字，用于后续比较
  tempSearchKey.value = searchKey.value;
};

// ===== 从 PrjParagraph4Read.vue 整合的功能 =====

// 时间戳点击跳转功能
const jumpToQuestionTime = (startTime: string) => {
  if (startTime && player.value) {
    const seconds = getSeconds(startTime);
    player.value.setTime(seconds);
  }
};

const handleQuestionList = (id: any) => {
  getQuestionList(id).then((res) => {
    // questionDialogRef.value.showDialog(questionList, 2);
    qList.value = res;
    open.value = false;
  });
};

const changeStateFn = (state: STATE_FLAG) => {
  stateFlag.value = state;
};
const contentWrapperRef = ref();
// 阅读时滑动滚
// 阅读模式，双向绑定，提问模式单项绑定。而且滚动条在最中间
const scrollElement = ref<HTMLElement>();
watch(
  () => scrollElement.value,
  (newVal, oldVal) => {
    if (newVal) {
      newVal.style.backgroundColor = '#a6d0ea';
      newVal.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    if (oldVal) {
      oldVal.style.backgroundColor = '';
    }
  }
);

const scrollBar = async (idx: number) => {
  const containerHeight = contentWrapperRef.value.offsetHeight;
  // 找到元素并且滑动
  const allParagraph = Array.from(document.querySelectorAll('.paragraph-wrapper')) as HTMLElement[];
  const activeParagraph = allParagraph?.[idx];
  sethighlightColor(idx);
  // 小屏可以使用这个api
  await nextTick();
  scrollElement.value = document.querySelector(`[oid="${curoid.value}"]`) as HTMLElement;
};
const activeIdx = ref(0);
// 设置高亮色
const sethighlightColor = (idx: number) => {
  activeIdx.value = idx;
};

const afterDeleteQuestion = (newcontent) => {
  myWordContent.value = newcontent;
  nextTick(() => {
    // if (!mode.value) {
    //   markWord();
    // } else {
    //   unmarkWord();
    // }
  });
};
const curoid = ref();
const hoverPList = (_idx: Number) => {
  curoid.value = _idx;
};

const open = ref(false);

const handleDrawerStateChange = (event: CustomEvent) => {
  const { hasActiveDrawer } = event.detail;
  console.log('PrjVideoScript收到弹窗状态变化事件:', hasActiveDrawer);

  // 当有弹窗激活时，可以禁用某些交互或调整样式
  if (hasActiveDrawer) {
    // 可以在这里添加禁用划词功能的逻辑
  }
};

onMounted(() => {
  // showContentWord.value=true;

  // 添加弹窗状态变化事件监听
  window.addEventListener('drawerStateChange', handleDrawerStateChange as EventListener);

  nextTick(() => {
    markWord();
  });
});

// 停止监听
onBeforeUnmount(() => {
  // 移除事件监听
  window.removeEventListener('drawerStateChange', handleDrawerStateChange as EventListener);

  // 移除了全局点击处理状态重置，因为已改用原生DOM事件
});
// 停止监听
defineExpose({
  changeStateFn,
  scrollBar,
  sethighlightColor,
  handleQuestionList,
  afterDeleteQuestion,
  hoverPList
});
</script>
<style scoped src="./css/PrjVideoScript.css"></style>
