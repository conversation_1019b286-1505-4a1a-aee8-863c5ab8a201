<template>
  <!-- 这个组件还会用到exam页面 -->
  <!-- {{ learningStore }} -->
  <div @wheel="handleWheel" class="main-wrapper" :class="{ big: isBig }">
    <div class="left-header" ref="header">
      <div class="title">{{ prjInfo.title + '题目' }}</div>
      <div class="base-info">
        <div class="creater">
          <img :src="prjInfo.userCoverPic" class="avatar" />
          <div class="name">{{ prjInfo.userName }}</div>
        </div>
        <div class="time">{{ prjInfo.createTime }}</div>
        <el-popover
          v-model:visible="descriptionVisible"
          placement="bottom-start"
          trigger="click"
          width="200"
          class="custom-popover"
        >
          <!-- 气泡卡片内容 -->
          <div class="popover-content">
            <span v-html="prjTargetObj.description"></span>
            <span v-html="prjTargetObj.purpose"></span>
          </div>

          <!-- 触发内容 -->
          <template #reference>
            <div class="function-tag">项目介绍</div>
          </template>
        </el-popover>
        <!-- <div class="function-tag" @click="descriptionVisible = !descriptionVisible">项目介绍</div> -->
        <!-- <div class="function-tag">案例知识地图</div> -->
      </div>
    </div>
    <div class="body">
      <div class="left">
        <div class="left-video-wrapper" :class="{ videoPage: $route.name == 'learning' }">
          <!-- 左侧的video -->
          <div class="left-main" :class="{ big: isBig }">
            <template v-if="projectDetailData?.videoUrl">
              <XgPlayerNew
                :videoSrc="projectDetailData?.videoUrl"
                :canBeWiden="prjType == PrjType.exam ? false : true"
                @timeupdate="handleTimeupdateFn"
                @wider="toggleDisplay"
                ref="player"
              >
              </XgPlayerNew>
            </template>
          </div>
        </div>
        <div class="down-wrapper">
          <PrjStudyInfo
            ref="studyInfo"
            :knowledgeSum="klg.klgNumbers"
            :studyPercent="learnedPct"
            :acquaintePercent="graspKlgPct"
            :targetKlgs="prjType == PrjType.case ? null : targetKlgs"
            class="prj-study-info"
          ></PrjStudyInfo>
          <div v-if="tags.length <= 6" class="tags">
            <el-tooltip
              v-for="tag in tags"
              :key="tag.id"
              :content="tag.content"
              effect="customized"
            >
              <PrjTag :label="tag.content" class="prj-tag"></PrjTag>
            </el-tooltip>
          </div>
          <div v-else class="tags">
            <el-tooltip
              v-for="tag in tags.slice(0, 6)"
              :key="tag.id"
              :content="tag.content"
              effect="customized"
            >
              <PrjTag :label="tag.content" class="prj-tag"></PrjTag>
            </el-tooltip>
            <div ref="more">
              <el-icon class="more" @click="handleExpandTag"><ArrowDown /></el-icon>
            </div>
          </div>
        </div>
        <Transition name="fade">
          <div v-if="descriptionVisible || tagsVisible" ref="float" class="float">
            <div v-if="tagsVisible" class="float-right">
              <div class="tags">
                <el-tooltip
                  v-for="tag in tags.slice(6)"
                  :key="tag.id"
                  :content="tag.content"
                  effect="customized"
                >
                  <div class="prj-tag">
                    <div class="tag-content">
                      {{ tag.content }}
                    </div>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </Transition>
      </div>

      <!-- 右侧的内容区 -->
      <div v-show="!isBig" class="right-content" :class="{ videoPage: $route.name == 'learning' }">
        <div id="draft" @wheel.stop class="content-wrapper">
          <!-- <PrjManuscript
            ref="prjManuscript"
            :big="isBig ? true : false"
            :videoCaptionList="videoCaptionList"
            :questionList="questionList"
            @returnInit="handleReturnInitFn"
            @refresh="handleQuestionList"
            @delete-question="handleDeleteQuestion"
            :isMap="!isMap"
            v-if="ready"
            @wheel="handleVideoWheel"
            @search="handleSearch"
            class="lineWordContent"
          >
          </PrjManuscript> @scrollInTop="handleScroll" -->
          <PrjVideoScript
            ref="prjVideoScript"
            :big="isBig ? true : false"
            :videoCaptionList="videoCaptionList"
            :questionList="questionList"
            @returnInit="handleReturnInitFn"
            @refresh="handleQuestionList"
            @delete-question="handleDeleteQuestion"
            :isMap="!isMap"
            v-if="ready"
            @wheel="handleVideoWheel"
            @search="handleSearch"
            class="lineWordContent"
          >
          </PrjVideoScript>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PrjManuscript from '@/views/areadetail/PrjManuscript.vue';
import PrjStudyInfo from '@/components/PrjStudyInfo.vue';
import { getPrjDetailApi, getPrjSectionApi, saveQuestionApi } from '@/apis/learning';
import type { Chapter, VideoCaptionListObj, QuestionData } from '@/types/learning';
import { STATE_FLAG } from '@/types/learning';
import XgPlayerNew from '@/views/areadetail/XgPlayerNew.vue';
import { defineExpose } from 'vue';
import { getQuestionListApi, deleteQuestionApi } from '@/apis/learning';
// import PrjManuscript from '@/views/learning/components/PrjManuscript.vue';
import PrjVideoScript from './PrjVideoScript.vue';

import { PrjType } from '@/types/project';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { useLearningStore } from '@/stores/learning';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { useWordStore } from '@/stores/word';
import { emitter } from '@/utils/emitter';
import { ArrowUpBold, ArrowDownBold } from '@element-plus/icons-vue';
import { getPrjMoreInfoApi } from '@/apis/learning';
import { useVideoWordStoreV2 } from '@/stores/videoWordV2';
import type { PrjinfoItf, PrjTag as PrjTagType } from '@/types/learning';
import PrjTag from '@/views/learning/components/PrjTag.vue';
import anime from 'animejs/lib/anime.es.js';

const router = useRouter();
const route = useRoute();
const learningStore = useLearningStore();
const drawerControllerStore = useDrawerControllerStore();
const videoWordStore = useVideoWordStoreV2();

const wordStore = useWordStore();
const { mode } = storeToRefs(drawerControllerStore);

const learnedPct = ref();
const graspKlgPct = ref();
const targetKlgs = ref();

// const prjInfo = inject('prjInfo') as Ref<PrjinfoItf>;
const props = defineProps(['prjInfo', 'targetKlgs', 'prjTargetObj']);

const klg = inject('klg') as Ref;
const prjTargetObj = ref();
const saveType = inject('saveType') as Ref;

onMounted(async () => {
  saveType.value = 0;
  learnedPct.value =
    klg.value.klgNumbers == 0 ? 0 : Math.floor((klg.value.learned / klg.value.klgNumbers) * 100); // 已经学习的比例
  graspKlgPct.value =
    klg.value.klgNumbers == 0 ? 0 : Math.floor((klg.value.graspKlg / klg.value.klgNumbers) * 100); // 已经掌握的比例
});
const emits = defineEmits(['move']);
const tags = ref<PrjTagType[]>([]);

const header = ref();
const float = ref();

function handleClose(event: MouseEvent) {
  if (header.value.contains(event.target as HTMLElement)) {
    return;
  }
  descriptionVisible.value = false;
  tagsVisible.value = false;
}
onMounted(() => {
  document.addEventListener('click', handleClose);
  watch(
    () => descriptionVisible || tagsVisible,
    () => {
      if (descriptionVisible || tagsVisible) {
        anime({
          targets: header.value,
          backgroundColor: {
            value: '#F2F2F2',
            duration: 300,
            easing: 'linear'
          }
        });
      } else {
        anime({
          targets: header.value,
          backgroundColor: {
            value: '#FFFFFF',
            duration: 300,
            easing: 'linear'
          }
        });
      }
    }
  );
});
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClose);
});
watch(
  () => props,
  (newvalue) => {
    if (newvalue.prjTargetObj) prjTargetObj.value = newvalue.prjTargetObj;

    if (newvalue.targetKlgs) targetKlgs.value = newvalue.targetKlgs;

    if (newvalue.prjInfo.prjTags) tags.value = newvalue.prjInfo.prjTags as Array<PrjTagType>;

    nextTick(() => {
      console.log('new', props);
    });
  },
  { deep: true, immediate: true }
);
watch(
  () => mode.value,
  (newVal) => {
    if (newVal == Mode.read) {
      const videoStrings = videoWordStore.getReadModeVideoStrings();
      if (videoStrings.length > 0) {
        const rawVideoCaptionList = toRaw(videoCaptionList.value);
        const rawVideoCaptionListFlat = rawVideoCaptionList?.flat();
        rawVideoCaptionListFlat?.forEach((item, index) => {
          item.caption = videoStrings[index];
        });
        videoCaptionList.value = cloneDeep(rawVideoCaptionList);
      }
    } else {
      const videoStrings = videoWordStore.getAskModeVideoStrings();
      if (videoStrings.length > 0) {
        const rawVideoCaptionList = toRaw(videoCaptionList.value);
        const rawVideoCaptionListFlat = rawVideoCaptionList?.flat();
        rawVideoCaptionListFlat?.forEach((item, index) => {
          item.caption = videoStrings[index];
        });
        videoCaptionList.value = cloneDeep(rawVideoCaptionList);
      }
    }
  }
);
const tagsVisible = ref(false);

const handleSearch = () => {
  if (mode.value == Mode.read) {
    const videoStrings = videoWordStore.getReadModeVideoStrings();
    if (videoStrings.length > 0) {
      const rawVideoCaptionList = toRaw(videoCaptionList.value);
      const rawVideoCaptionListFlat = rawVideoCaptionList?.flat();
      rawVideoCaptionListFlat?.forEach((item, index) => {
        item.caption = videoStrings[index];
      });
      videoCaptionList.value = cloneDeep(rawVideoCaptionList);
    }
  } else {
    const videoStrings = videoWordStore.getAskModeVideoStrings();
    if (videoStrings.length > 0) {
      const rawVideoCaptionList = toRaw(videoCaptionList.value);
      const rawVideoCaptionListFlat = rawVideoCaptionList?.flat();
      rawVideoCaptionListFlat?.forEach((item, index) => {
        item.caption = videoStrings[index];
      });
      videoCaptionList.value = cloneDeep(rawVideoCaptionList);
    }
  }
};
function handleExpandTag() {
  tagsVisible.value = !tagsVisible.value;
  // anime({
  //   targets: more.value,
  //   rotate: {
  //     value: '+=0.5turn',
  //     duration: 100,
  //     easing: 'easeInOutQuad'
  //   }
  // });
}
/**
 * 显示视频功能
 */
const isBig = ref(false); // 大屏小屏
const bigScreen = ref(0); // 0：大屏视频|1：大屏字幕
const isSectionMenuShow = ref(false); //控制多节菜单的显示
const player = ref<InstanceType<typeof XgPlayerNew> | null>(null);
const prjManuscript = ref<InstanceType<typeof PrjManuscript>>();
const prjVideoScript = ref<InstanceType<typeof PrjVideoScript>>();
// if案例项目，小节号（if从开始学习|继续学习进来的then最新小节，else自选的节号）
const initChapterId = route.query.chapterId as string;
const spuId = route.query.spuId as string;
const prjType = inject('prjType') as Ref; // 1讲解 2案例 3测评

const descriptionVisible = ref(false);

// 项目的prjId
const prjId = ref<string>('');
const uniqueCode = ref<string>('');
// 项目的章节id
const curChapterId = ref();
// 项目详情
const projectDetailData = ref<Chapter>();
// 章节相关的数据
const chapterList = ref<Chapter[]>();
const activeIndex = ref();
// 拿到章节信息
const videoCaptionList = ref<[VideoCaptionListObj[]]>();
const questionList = shallowRef<QuestionData[]>();
const playerTime = ref<number>(0);
const { initUserBehaviour, handleChapterChange } = userBehaviour(
  PrjForm.video,
  activeIndex,
  curChapterId,
  playerTime
);

// 提供isBig数据
provide(
  'isBig',
  computed(() => toValue(isBig))
);
// const isBig = inject('isBig') as Ref;
const isMap = ref(true); // 是否展示地图
// 提供isMap数据
provide(
  'isMap',
  computed(() => toValue(isMap.value))
);
// const emits = defineEmits('closeMap');
const toggleDisplay = () => {
  if (!isBig.value) {
    emits('move', true);
    isMap.value = false;
    isBig.value = true;
  } else {
    emits('move', false);
    isMap.value = true;
    isBig.value = false;
    // bigScreen.value = 1;
  }
};
// const switchScreen = (direction) => {
//   if (bigScreen.value == direction) {
//     return;
//   }
//   bigScreen.value = direction;
//   if (direction == 0) {
//     player.value?.exitPIP();
//   } else {
//     player.value?.requestPIP();
//   }
// };

//================================ksg-map================================
import { getAreaData, getFocusData, getChapterGraph } from '@/apis/ksgmap';
import KsgMap from 'ksg-map';
import type {
  GlobalConfig,
  OriginalData,
  AreaData,
  PointData,
  FocusData
} from 'ksg-map/dist/types';

import { Mode, QuestionAction, type RenderInfo } from '@/types/word';
import { cloneDeep } from 'lodash-es';
import { ITEM_RENDER_EVT } from 'element-plus/es/components/virtual-list/src/defaults';
import { handleVideoQuestion } from '@/utils/handleQuestion';
import { Event } from '@/types/event';
import type { QuestionType } from '@/types/question';
const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
const target = ref<HTMLElement | null>(null);
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: true,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: false
});
const data = ref<OriginalData>({
  topAreas: [],
  areas: [],
  focuses: [],
  points: []
});
async function fetchAreaData(id: string): Promise<AreaData[]> {
  return await getAreaData(id);
}
async function fetchFocusData(id: string): Promise<FocusData> {
  return await getFocusData(id);
}
onMounted(async () => {
  saveType.value = 0;
  target.value = document.getElementById('app')!;
  const unwatch = watch(
    () => curChapterId.value,
    async () => {
      if (curChapterId.value) {
        const pointsData = await getChapterGraph(spuId, curChapterId.value);
        await ksgMap.value?.ready;
        data.value.topAreas = [];
        data.value.points = pointsData;

        ksgMap.value?.reloadData();
      }
    }
  );
});
//================================ksg-map================================

const handleWheel = (e) => {
  if (!isBig.value) return;
  // console.log(e.deltaY)
  //   if (e.deltaY > 0) {
  //     switchScreen(1);
  //   } else if (e.deltaY < 0) {
  //     switchScreen(0);
  //   } else {
  //     alert('用户鼠标没有上下滑动');
  //   }
};
// const handleScroll = (e) => {
//   switchScreen(0);
// };
//节知识地图
const toggleMap = () => {
  //   if (!isBig.value) {
  //     isMap.value = !isMap.value;
  //   }
};

// 视频播放器实例
// TODO：不优雅，后期优化
const getSeconds = (seconds: string) => {
  // 将 1   61 这种数字或者字符串转为   00:00:01  00:01:01
  if (!seconds) return 0;
  const array = seconds.split(':');
  let res = 0;
  array.forEach((item) => {
    res = res * 60 + parseInt(item, 10);
  });
  return res;
};
/**
 * 文稿和视频联动功能
 */

const clickQuestion = (questionId: string) => {
  // prjManuscript.value.handleQuestionList(questionId);
  drawerControllerStore.questionId = questionId;
};
const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
  const res = await getQuestionListApi(uniqueCode, chapterId);
  questionList.value = res.data;
  const worker = new Worker(new URL('@/worker/videoWorker.ts', import.meta.url), {
    type: 'module'
  });
  const rawVideoCaptionList = toRaw(videoCaptionList.value);
  const rawVideoCaptionListFlat = rawVideoCaptionList.flat();
  const htmlStrings = rawVideoCaptionListFlat.map(
    (item) => `<span oid="${item.oid}">${item.caption}</span>`
  );
  worker.onmessage = (
    e: MessageEvent<{
      regString: string;
      renderInfoIndexes: Array<{
        listIndex: number;
        index: number;
      }>;
      renderInfoListList: RenderInfo[][];
    }>
  ) => {
    videoWordStore.regString = e.data.regString;
    videoWordStore.renderInfoIndexes = e.data.renderInfoIndexes;
    videoWordStore.renderInfoListList = e.data.renderInfoListList;
    toRaw(questionList.value)?.forEach((question) => {
      handleVideoQuestion(
        question,
        toRaw(videoWordStore.regString),
        toRaw(videoWordStore.renderInfoIndexes),
        toRaw(videoWordStore.renderInfoListList)
      );
    });
    const readModeVideoStrings = videoWordStore.getReadModeVideoStrings();
    rawVideoCaptionListFlat.forEach((item, index) => {
      item.caption = readModeVideoStrings[index];
    });
    videoCaptionList.value = cloneDeep(rawVideoCaptionList);
    // 当在worker中打印日志信息时，需要查看obj数据则注释下面代码
    // worker.terminate();
  };
  // //TODO
  // questionList.value = questionList.value?.filter((item)=> item.questionId == questionId)
  // console.log('111', questionList.value)
  worker.postMessage({
    htmlStrings,
    regString: toRaw(videoWordStore.regString),
    renderInfoIndexes: toRaw(videoWordStore.renderInfoIndexes),
    renderInfoListList: toRaw(videoWordStore.renderInfoListList)
  });
};
const handleDeleteQuestion = (questionId: string) => {
  emitter.emit(Event.REMOVE_QUESTION, questionId);
};

// TODO: 后期优化，增加节流
let scrollBarAble = true;
let scrollBarTimer: any;
const handleVideoWheel = () => {
  scrollBarAble = false;
  if (scrollBarTimer) {
    clearTimeout(scrollBarTimer);
  }
  scrollBarTimer = setTimeout(() => {
    scrollBarAble = true;
  }, 2000);
};
const tempOid = ref(-1);
const handleTimeupdateFn = (currentTime: number | string) => {
  // myPlayer的获取有问题，waq注释了这一句 todo需要修改
  // if (!myPlayer.value) return;
  playerTime.value = currentTime as number;
  // console.log('currentTime1111', currentTime)
  // FIXME: hoverPList没了，可能得换一个东西看
  // const hoverPList = prjManuscript.value?.hoverPList as VideoCaptionListObj[];
  // const hoverPList=videoCaptionList.value
  // console.log((e.target as HTMLVideoElement).currentTime.toFixed(2), typeof currentTime);
  // console.log(currentTime, typeof currentTime);
  // console.log('hoverPList', prjManuscript.value?.hoverPList);
  // console.count();
  let manuIndex = 0; //当前字幕段
  let idx = -1;
  let curoid = -1;
  manuIndex = videoCaptionList.value!.findIndex((hoverPList) => {
    idx = hoverPList?.findIndex((hoverParam) => {
      // console.log(hoverParam)
      if (
        getSeconds(hoverParam.startTime) <= currentTime &&
        getSeconds(hoverParam.endTime) >= currentTime
      ) {
        // console.log(hoverParam.caption)
        curoid = hoverParam.oid;
        return true;
      }
    });
    // console.log('idx', idx)
    if (idx != -1) return true;
  });
  if (idx != -1 && curoid != tempOid.value) {
    // console.log('idx',idx)
    // console.log('index',index)
    tempOid.value = curoid;
    prjVideoScript.value!.hoverPList(curoid); //字幕高亮
    if (scrollBarAble) {
      prjVideoScript.value!.scrollBar(manuIndex); // 滑动滚动条
    }
  }
};

provide(
  'prjSectionInfo',
  computed(() => ({
    chapterId: curChapterId.value,
    prjId: prjId.value,
    uniqueCode: uniqueCode.value
  }))
);

// 处理章节变化

//当我从一个章节切到另一个章节时，我需要发送现在章节的end请求和新章节的start请求, 结束现在章节的start请求和新章节的end请求

const handleChangeSectionFn = async (chapterId: number) => {
  if (activeIndex.value != chapterId) {
    router.replace({
      query: {
        ...route.query,
        chapterId: chapterId
      }
    });
    handleChapterChange(chapterId);
    activeIndex.value = chapterId;
    // console.log(uniqueCode.value, chapterId);
    const res = await getPrjSectionApi(spuId, chapterId);
    // console.log('处理章节变化', res);
    // 拿到了新的video资源
    projectDetailData.value = res.data;
    //   changeVideoFn(prjDetailData.value?.videoUrl as string);
    curChapterId.value = projectDetailData.value?.chapterId;
    videoCaptionList.value = res.data.videoCaptionList; //
    await handleQuestionList(uniqueCode.value, chapterId.toString());
  }
};

// 小屏转大屏的时候操控状态STATE_FLAG
// 切换状态
const handleReturnInitFn = () => {
  prjVideoScript.value!.changeStateFn(STATE_FLAG.init);
};

// 异步组件
// TODO
// @ts-ignore
// let AsyncPrjManuscript;
// const showAsync = ref(false);
const handleDocumentClick = (event: MouseEvent) => {
  if (!event.target.closest('.sectionMenu')) {
    console.log('点击了其他地方old', isSectionMenuShow.value);
    if (isSectionMenuShow.value == true) {
      isSectionMenuShow.value = !isSectionMenuShow.value;
      console.log('点击了其他地方new', isSectionMenuShow.value);
    }
  }
};
const ready = ref(false);

onMounted(async () => {
  console.log('learning-onMounted!');
  // if (!learningStore.written) {
  //   const res = await getPrjDetailApi(spuId, initChapterId);

  //   learningStore.setInfo(res.data);

  //   learningStore.chapterId = learningStore.chapterList[0].chapterId;
  // router.replace({
  //   query: {
  //     ...route.query,
  //     chapterId: learningStore.chapterId
  //   }
  // });
  //}

  // TODO:这里应该再接受一个type字段
  let idx = learningStore.validIndex; // 后端会把所有小节都返还，但只有一个对象是有信息的，就是这个index对应的小节
  projectDetailData.value = learningStore.chapterList[idx];
  //   initVideo(); // 初始化视频
  curChapterId.value = projectDetailData.value?.chapterId;

  chapterList.value = learningStore.chapterList; // 拿到章节列表
  activeIndex.value = learningStore.chapterList[idx]?.chapterId;
  //todo 接口修改需要调整,原来从接口读出来的是prjId
  //gc说改为prjId
  prjId.value = learningStore.prjId; // 拿到项目id
  uniqueCode.value = learningStore.uniqueCode;

  videoCaptionList.value = learningStore.chapterList[idx]?.videoCaptionList; // 拿到章节信息
  //console.log('onMounted', videoCaptionList.value);
  questionList.value = learningStore.chapterList[idx]?.questionList; // 拿到问题信息

  // showAsync.value = true;
  //initUserBehaviour(curChapterId.value);
  if (videoCaptionList.value) {
    await handleQuestionList(uniqueCode.value, curChapterId.value as string);
  }
  ready.value = true;
});
watch(
  () => route.query.spuId,
  async (newValue) => {
    if (newValue) {
      ready.value = false;
      videoWordStore.regString = '';
      // console.log('watch!');
      // let sid = newValue as string;
      // let cid = route.query.chapterId as string;
      // const res = await getPrjDetailApi(sid, cid);
      // console.log('res1:', res.data);
      // learningStore.setInfo(res.data);
      // console.log('9090', learningStore.chapterId);
      // learningStore.chapterId = learningStore.chapterList[0].chapterId;
      // TODO: 这里应该再接受一个type字段;
      let idx = learningStore.validIndex; // 后端会把所有小节都返还，但只有一个对象是有信息的，就是这个index对应的小节
      projectDetailData.value = learningStore.chapterList[idx];
      //   initVideo(); // 初始化视频
      curChapterId.value = projectDetailData.value?.chapterId;

      chapterList.value = learningStore.chapterList; // 拿到章节列表
      activeIndex.value = learningStore.chapterList[idx]?.chapterId;
      //todo 接口修改需要调整,原来从接口读出来的是prjId
      //gc说改为prjId
      prjId.value = learningStore.prjId; // 拿到项目id
      uniqueCode.value = learningStore.uniqueCode;

      console.log(
        'watch-spuId-learningStore:',
        learningStore.chapterList[idx]?.videoCaptionList[0][0].caption
      );

      videoCaptionList.value = learningStore.chapterList[idx]?.videoCaptionList; // 拿到章节信息
      //console.log('watchVideo', videoCaptionList);
      questionList.value = learningStore.chapterList[idx]?.questionList; // 拿到问题信息
      if (videoCaptionList.value) {
        await handleQuestionList(uniqueCode.value, curChapterId.value as string);
      }
      console.log('chuandiqian-learning', videoCaptionList.value[0][0].caption);

      // showAsync.value = true;
      console.log('9999999', curChapterId.value);
      //initUserBehaviour(curChapterId.value);
      ready.value = true;
      //document.addEventListener('click', handleDocumentClick);
      // emitter.emit('initHandler', async () => {
      //   const res2 = await getPrjSectionApi(spuId, curChapterId.value);
      //   projectDetailData.value = res2.data;
      //   curChapterId.value = projectDetailData.value?.chapterId;
      //   videoCaptionList.value = res2.data.videoCaptionList;
      //   await handleQuestionList(uniqueCode.value, curChapterId.value);
      // });
    }
  },
  { deep: true, immediate: true }
);
const addQuestionFn = async (params: {
  associatedWords: string;
  keyword: string;
  questionType: QuestionType;
  questionDescription: string;
  uniqueCode: string;
  chapterId: string;
  contentId?: string;
}) => {
  const res = await saveQuestionApi(params);
  if (res.success) {
    ElMessage.success('保存问题成功');
    handleVideoQuestion(
      res.data,
      toRaw(videoWordStore.regString),
      toRaw(videoWordStore.renderInfoIndexes),
      toRaw(videoWordStore.renderInfoListList),
      QuestionAction.add
    );
    const videoStrings = videoWordStore.getReadModeVideoStrings();
    if (videoStrings.length > 0) {
      const rawVideoCaptionList = toRaw(videoCaptionList.value);
      const rawVideoCaptionListFlat = rawVideoCaptionList?.flat();
      rawVideoCaptionListFlat?.forEach((item, index) => {
        item.caption = videoStrings[index];
      });
      videoCaptionList.value = cloneDeep(rawVideoCaptionList);
    }
  } else {
    ElMessage.error('保存问题失败');
  }
};

const removeQuestionFn = async (questionId: string) => {
  const res = await deleteQuestionApi(questionId);
  if (res.success) {
    ElMessage.success('删除成功');
    const rawQuestionList = toRaw(questionList.value);
    const questionIndex = rawQuestionList?.findIndex((item) => item.questionId == questionId);
    handleVideoQuestion(
      rawQuestionList?.[questionIndex as number],
      toRaw(videoWordStore.regString),
      toRaw(videoWordStore.renderInfoIndexes),
      toRaw(videoWordStore.renderInfoListList)
    );
    rawQuestionList?.splice(questionIndex as number, 1);
    triggerRef(questionList);
    const videoStrings = videoWordStore.getReadModeVideoStrings();
    if (videoStrings.length > 0) {
      const rawVideoCaptionList = toRaw(videoCaptionList.value);
      const rawVideoCaptionListFlat = rawVideoCaptionList?.flat();
      rawVideoCaptionListFlat?.forEach((item, index) => {
        item.caption = videoStrings[index];
      });
      videoCaptionList.value = cloneDeep(rawVideoCaptionList);
    }
  } else {
    ElMessage.error('删除失败');
  }
};
onMounted(() => {
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);
  document.addEventListener('click', handleDocumentClick);
});

onBeforeUnmount(() => {
  // console.log('父组件onBeforeUnmount');
  // console.log('视频结束时长'+endTime.value.endTime)
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);
  document.removeEventListener('click', handleDocumentClick);
});

defineExpose({ curChapterId });
</script>

<style scoped lang="less">
.sectionMenu {
  //margin: 10px;
  cursor: pointer;
  //transform: scale(2);
}
.sectionMenuContent {
  border: 1px solid rgb(220, 223, 230);
  border-radius: 5px;
  position: absolute;
  transition: all 0.5s ease-in-out;
  left: 90px;
  //width: 284px;
  // height: calc(100vh - 70px - 90px - 63px);
  z-index: 10;
  background-color: white;
  box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    background: var(--color-grey);
  }
  .section {
    height: 41px;
    font-size: 14px;
    padding: 0 10px;
    background-color: white;
    display: flex;
    align-items: center;
    font-family: var(--text-family);
    color: var(--color-black);
    cursor: pointer;
    &:hover {
      background-color: #f2f2f2;
    }
    .sectionTitle {
      margin-left: 5px;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.main-wrapper {
  width: 1220px;
  display: flex;
  height: 500px;
  //margin-top: 10px;
  flex-direction: column;
}
.left-header {
  background-color: #f2f2f2;
  margin-left: 10px;
  width: 668px;
  /* width: calc(61.8vw - 10px); */
  &.big {
    margin: 0 auto;
  }
  .title {
    font-size: 18px;
    font-weight: 700;
    margin: 10px 0px 10px 0;
  }
  .base-info {
    font-size: 12px;
    display: flex;
    align-items: center;
    color: var(--color-deep);
    .creater {
      display: flex;
      align-items: center;
      .avatar {
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-right: 5px;
      }
      .name {
      }
    }
    .time {
      margin: 0 10px;
    }
    .function-tag {
      margin: 0 10px;
      padding: 0 10px;
      border-radius: 10px;
      border: 1px solid var(--color-deep);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;

      &:hover {
        background-color: var(--color-inactive-project);
        cursor: pointer;
      }
    }
  }
}
.videoPage {
  .left-video-wrapper {
    min-height: 485px;
    height: calc(100vh - 60px - 70px - 250px);
  }

  .right-content {
    min-height: 485px;
  }
}
.body {
  display: flex;
}
.left-video-wrapper {
  margin-left: 10px;
  width: 668px;
  height: 450px;
  //   width: calc(51.8vw - 10px);
  margin-right: 10px;
  display: flex;
  //flex-direction: column;
  scroll-snap-align: start;

  .left-main {
    // flex: 1;
    display: flex;
    flex-direction: column;
    width: 668px;
    // width: calc(51.8vw - 10px);

    margin: 0 auto;
    &.big {
      //   height: calc(100vh - 160px);
      //   display: flex;

      height: 500px;
      width: 668px;
    }

    .video-title {
      padding-left: 10px;
      padding-right: 10px;
      position: relative;
      bottom: 0px;
      background-color: #f2f2f2;
      height: 63px;
      font-size: 24px;
      font-weight: 400;
      color: var(--color-black);
      font-family: var(--title-family);
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btn {
        background-color: white;
        border-radius: 14px;
        height: 28px;
        font-size: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 10px;
        border: 1px solid var(--color-theme-project);
        color: var(--color-theme-project);
        font-family: var(--text-family);
        &:hover {
          background-color: var(--color-theme-project);
          color: white;
          cursor: pointer;
        }
      }
      .icon {
        cursor: pointer;
      }

      .icon-wrapper {
        width: 16px;
        height: 12px;
        margin: 0 5px;
        background: url('@/assets/svgs/u2985.svg') no-repeat center/contain;
        cursor: pointer;

        &:hover {
          background-image: url('@/assets/svgs/u4176.svg');
        }
      }
    }

    .video {
      // background-color: rgb(242, 242, 242);
      flex: 1;
      position: relative;

      .coverPic-wrapper {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: var(--color-black);
        background-repeat: no-repeat;
        background-size: cover;
        // background-size: 100% auto;
      }

      .video-btn-wrapper {
        width: 50px;
        height: 50px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
      }

      .expand-logo {
        position: absolute;
        right: 10px;
        // bottom: 10px;
        cursor: pointer;

        &:hover {
          font-weight: 500;
        }
      }
    }

    .video-footer-info {
      height: 40px;
      display: flex;
      align-items: center;
      background-color: #ffffff;
      box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
      border: 1px solid rgb(242, 242, 242);
      padding-left: 10px;
      width: 100%;
      position: relative;

      .video-footer {
        vertical-align: middle;
        transform: translateY(-3px);
      }

      .footer-logo-wrapper {
        width: 90%;
        display: flex;
        align-items: center;
        position: absolute;
      }

      .footer-title {
        font-size: 18px;
        font-weight: 300;
        color: var(--color-black);
        margin-left: 17px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
.down-wrapper {
  margin: 0 auto;
  .prj-study-info {
    // margin: 5px 0px 0px 0;
  }
  .tags {
    align-items: center;
  }
}
.tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 15px;
  .prj-tag {
    width: 80px;
    height: 20px;
    margin: 5px 5px;
    border-radius: 10px;
    color: var(--color-black);
    background-color: var(--color-inactive-project);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-sizing: border-box;
    transition: border 0.3s ease;

    .tag-content {
      max-width: 70px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &:hover {
      border: 1px solid var(--color-deep);
    }
  }
  .more {
    width: 18px;
    height: 18px;
    margin: 5px 5px;
    border-radius: 10px;
    color: var(--color-black);
    background-color: var(--color-inactive-project);
    &:hover {
      border: 1px solid var(--color-deep);
    }
  }
}
.right-content {
  // min-height: 485px;
  height: calc(100vh - 60px - 70px - 66.5px); // right-content的offsetTop为150px
  width: 558px;
  // overflow: hidden;
  scroll-snap-align: start;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .content-wrapper {
    background-color: #f2f2f2;
    // width: 100%;
    height: 525px;
  }
}
.main-wrapper.big {
  display: block;
  //text-align: center;
  .left {
    height: 450px;
    margin: 0 auto;
  }
  .left-header {
    margin: 0 auto;
  }
  .left-video-wrapper {
    //display: block;
    height: 450px;
    margin: 0 auto;
    //min-height: 100vh;
  }
  .video-wrapper {
    height: 450px;
  }

  .right-content {
    box-sizing: border-box;
    //height: 100%;
    width: calc(100% - 60px);
    max-width: 1380px;
    // height: 800px;
    //min-height: 100vh;
    margin: 0 auto;
    //height: 100%;

    &:deep(.text-wrapper) {
      height: 800px !important; // 大屏的高度
    }
  }
}
.switchFloor {
  position: absolute;
  right: 10px;
  top: calc(50% + 30px);
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .btn {
    cursor: pointer;
    background-color: #dcdfe6;
    height: 30px;
    width: 30px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--color-deep);
    &:hover,
    &.light {
      background-color: var(--color-theme-project);
      color: white;
    }
  }
}
.custom-popover {
  .el-popper {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1); /* 修改阴影 */
    border: 1px solid #dcdfe6; /* 增加边框 */
    padding: 10px; /* 增加内边距 */
  }

  .el-popper__arrow {
    border-color: transparent transparent #f4f4f9 transparent; /* 修改箭头颜色 */
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
