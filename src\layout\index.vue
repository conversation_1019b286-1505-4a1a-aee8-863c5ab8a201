<template>
  <div class="layout">
    <Header class="fixed-header" />
    <div class="content-wrapper">
      <RouterView />
    </div>
  </div>
</template>
<script setup lang="ts">
import Header from '@/layout/Header.vue';
</script>
<style>
.layout {
  min-height: cal(100vh - 200px);
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
}

.content-wrapper {
  padding-top: 70px; /* 根据Header的实际高度调整 */
}
</style>
