<!-- title 和 info封装成一个组件 -->
<!-- 第一次封装 -->
<template>
  <PrjTitle></PrjTitle>
  <div class="info1">
    <StudyInfo
      v-if="info.buyStatus == BuyStatus.bought"
      :isTest="istest"
      @watch="handleWatch"
    ></StudyInfo>
    <StudyInfoNoBuy v-else></StudyInfoNoBuy>
  </div>
</template>

<script setup lang="ts">
import PrjTitle from '../components/PrjTitle.vue';
import StudyInfo from '../components/StudyInfo.vue';
import StudyInfoNoBuy from '../components/StudyInfoNoBuy.vue';
import { BuyStatus } from '@/types/goods';
import { useLearnStore } from '@/stores/learnintro';
import { useRouter } from 'vue-router';
import { useProjectStore } from '@/stores/project';

const router = useRouter();
const route = useRoute();
// 使用统一的数据管理方法
const learnStore = useLearnStore();
const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);

watch(
  () => info.value,
  (newVal, oldVal) => {
    console.log('info.value', newVal);
  }
);

// 已购买组件传参
// const boughtInfo = inject('boughtInfo') as BoughtInfoType;

// 获取当前页面是否测试页面传递给子组件
const props = defineProps(['istest']);
let istest = props.istest;

//支付的部分参数
// paymentObj:当为领域时的数据，需要在相关页面点击时传递到本页面
// const paymentObj = {
//   fieldName: '芯片设计',
//   goodsPriceList: [
//     {
//       cardType: '月卡',
//       goodsPrice: '89'
//     },
//     {
//       cardType: '季卡',
//       goodsPrice: '189'
//     },
//     {
//       cardType: '年卡',
//       goodsPrice: '289'
//     }
//   ]
// };
// const cdtype=ref(1)
// const learnday=ref(365)
// const checkedpay = ref(89);
// const isBuy = inject('buyStatus') as BuyStatus;
// console.log('收到的isBuy支付状态', isBuy);
// if (isBuy == BuyStatus.bought) {
// console.log('isBuy == BuyStatus.bought', isBuy);
// }
// const isArea = inject('prjForm') as ContentType;
// const isArea = inject('goodsType') as GoodsType;
// const titleinfo=inject('title') as Ref;
// const spuid=inject('spuId')
// const skuList=inject('skuList') as Ref<any>
// const myPrice = inject('myprice') as Ref;

// 支付弹窗控制
// const dialogPayVisible = ref(false);
// 关闭弹窗
// const closeDialog = (val: any) => {
//   dialogPayVisible.value = val;
// };

// const confirmBuy = () => {
//   let params;
//   if(isArea==1){
//     const skuid=skuList.find(item=>{
//       if(item.cardType==cdtype.value){
//         return true
//       }
//     }).skuId
//     params = {
//       spuId: spuid.value,
//       skuId:skuid
//     };
//   }else{
//     params = {
//       spuId: spuid.value,
//     };
//   }

//   buyFreeGoods(params).then(()=>{
//     handleWatch();
//   })

// }
// const handleDay=(type:number)=>{
//   // console.log(skuList.length)
//   // console.log(skuList.value.length)
//   cdtype.value=type;
//   learnday.value = info.value.skuList.find(item => {
//     if(item.cardType==type){
//       return true
//     }
//   }).studyTime
//   console.log(learnday.value)
// }
// const latestChapterId = inject('latestChapterId') as Ref;

const handleWatch = () => {
  // 买了，学时的动作
  // router.push('/learning');
  router.push({
    path: '/learning',
    query: {
      spuId: route.query.spuId,
      chapterId: info.value.latestChapterId
    }
  });
};

// const handleBuy = () => {
//   // 购买时的动作
//   dialogPayVisible.value = true;
// };
onMounted(() => {
  // if(isArea.value==0){
  //     learnday.value=info.value.studyTime
  //   }
});
</script>

<style lang="less" scoped>
.info1 {
  width: 100%;
  height: 50px;

  margin-top: 10px;
  display: flex;
  align-items: center;
}
.dg {
  .dg-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #f2f2f2;
    padding-bottom: 15px;

    h1 {
      font-size: 18px;
      font-weight: 700;
      color: var(--color-theme-project);
    }
  }

  .content {
    width: 100%;
    white-space: pre-wrap;
  }

  .foot-btns {
    width: 100%;
    display: flex;
    // flex-direction: row;
    justify-content: center;

    .btn {
      width: 160px;
      height: 43px;

      &:nth-child(2) {
        margin-left: 20px;
      }
    }
  }
}
</style>
