import { ref } from 'vue';
import { processAllLatexEquations } from '@/utils/latexUtils';

// 处理论证块
// export function handleProof(proofList: any[]): string[] {
//   const tempProofList = [];
//   proofList.forEach((block: any) => {
//     block.klgProofCondList.forEach((cond: any) => {
//       tempProofList.push(
//         `<span blockid="${block.klgProofBlockId}" condid="${cond.klgProofCondId}" sortid="${cond.sort}">${cond.cnt}</span>`
//       );
//     });
//     tempProofList.push(`<span blockid="${block.klgProofBlockId}">${block.conclusion}</span>`);
//   });
//   return tempProofList;
// }

// 处理习题
/**
 * 处理习题内容,将习题内容转换为带有特定标记的HTML字符串数组
 * @param exercise 习题对象,包含题干、选项、答案和解析等信息
 * @returns 返回包含处理后HTML字符串的数组
 */
export const handleExercise = (exercise: any): string[] => {
  // 存储处理后的习题内容
  const tempExerciseList = [];

  // 添加题干,带有etype和type属性
  tempExerciseList.push(
    `<span etype="stem" type="${exercise.type}" style="width: 100%;">${(
      exercise.stem
    )}</span>`
  );

  // 如果有选项内容,处理每个选项
  if (exercise.content) {
    exercise.content.forEach((item) => {
      tempExerciseList.push(
        `<span etype="content" contentid="${item.optionId}">${(
          item.text
        )}</span>`
      );
    });
  }

  // 添加答案
  tempExerciseList.push(`<span etype="answer">${exercise.answer}</span>`);

  // 添加解析
  tempExerciseList.push(
    `<span etype="explanation">${(exercise.explanation)}</span>`
  );

  return tempExerciseList;
};

// // 把worker的list转化为exercise
// export const transferList2Exercise = (list: any[]): any => {
//   const tempExercise = ref<{
//     type: number;
//     stem: string;
//     content: any[];
//     answer: string;
//     explanation: string;
//   }>({
//     type: 0,
//     stem: '',
//     content: [],
//     answer: '',
//     explanation: ''
//   });
//   const tempList = ref<any[]>([]);
//   list.forEach((item: any) => {
//     const parser = new DOMParser();
//     const doc = parser.parseFromString(item, 'text/html');
//     const span = doc.querySelector('span'); // 获取最外层的 span 元素
//     if (span && span.hasAttribute('etype')) {
//       const etype = span.getAttribute('etype');
//       if (span.hasAttribute('type')) {
//         const type = span.getAttribute('type');
//         if (type) {
//           tempExercise.value.type = parseInt(type);
//         }
//       }
//       if (etype === 'content') {
//         const op = {
//           optionId: span.getAttribute('contentid'),
//           text: item
//         };
//         tempList.value.push(op);
//       } else {
//         tempExercise.value[etype] = item;
//       }
//     }
//   });
//   tempExercise.value.content = tempList.value;
//   return tempExercise.value;
// };
