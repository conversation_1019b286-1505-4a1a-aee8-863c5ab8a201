import { http } from '@/apis';
import qs from 'qs';
import { timestampToTime, formatTime } from '@/utils/timeUtils';
import type { ExerciseType, contentType } from '@/types/exercise';

//知识详情页面获取测评
export function getExerciseApi(exerciseId: string) {
  return http.request<{
    exerciseId: string;
    stem: string;
    type: ExerciseType;
    content: contentType[];
    answer: string[];
    explanation: string;
  }>({
    method: 'get',
    url: '/project-goods/getExerciseDetail',
    params: {
      exerciseId
    }
  });
}
interface ExerciseRecord {
  exerciseId: string;
  answer: string;
  klgCode: string
}
export function saveExerciseRecordApi(data: ExerciseRecord) {
  return http.request({
    method: 'post',
    url: 'knowledgeExam/exercise/record/save',
    data
  });
}

export function getQuestionListApi(exerciseId: string) {
  return http.request<{
    list: [
      {
        questionId: number;
        associatedWords: string;
        keyword:string,
        questionType:string,
        questionDescription:string,
        canDelete:boolean
      }
    ];
  }>({
    method: 'get',
    url: '/knowledgeExam/listExerciseQuestion',
    params: {
      exerciseId
    }
  });
}

export function saveExerciseQuestionApi(param: {}) {
  return http.request<{}>({
    method: 'post',
    url: '/question/exercise/markWords',
    data: param
  });
}



export function getQuestionquestionListApi(exerciseId: string) {
  return http.request<{
    list: [
      {
        questionId: number;
        associatedWords: string;
        keyword:string,
        questionType:string,
        questionDescription:string,
        canDelete:boolean
      }
    ];
  }>({
    method: 'get',
    url: '/knowledgeExam/listExerciseQuestion',
    params: {
      exerciseId
    }
  });
}
