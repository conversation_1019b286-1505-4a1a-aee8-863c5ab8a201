<template>
  <div class="ksg-map-wrapper">
    <KsgMap
      ref="ksgRef"
      :config="config"
      @load-more="handleLoadMore"
      :loading="loading"
      @click-label="handleClickLabel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { KsgMap } from '@endlessorigin/KsgMap';
import { MODE, type Options } from '@endlessorigin/KsgMap';

// 定义Props接口
interface Props {
  dataList: any[]; // 知识点数据数组
  total: string; // 总数据量
  rootID: string; // 根节点ID
  mode?: 'Single_ROOT' | 'MULTIPLE_ROOT'; // 配置模式
  pointsLevelPager?: {
    current: number;
    levelSize: number;
  };
  autoInit?: boolean; // 是否自动初始化
}

// 定义Emits
interface Emits {
  (e: 'load-more', rootId: string, crt: number, levelSize: number): void;
  (e: 'click-label', id: string): void;
  (e: 'loading-change', loading: 'loading' | 'loaded' | 'error'): void;
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  mode: 'Single_ROOT',
  pointsLevelPager: () => ({
    current: 1,
    levelSize: 2
  }),
  autoInit: true
});

// Emits定义
const emit = defineEmits<Emits>();

// 组件引用和状态
const ksgRef = ref<any>();
const loading = ref<'loading' | 'loaded' | 'error'>('loading');

// 计算配置项
const config = computed<Options>(() => ({
  model: props.mode === 'Single_ROOT' ? MODE.Single_ROOT : MODE.MULTIPLE_ROOT,
  pointsLevelPager: props.pointsLevelPager
}));

/**
 * 初始化加载数据
 */
async function init() {
  loading.value = 'loading';
  emit('loading-change', loading.value);

  try {
    // 调用KsgMap的firstLoadPointsData方法
    ksgRef.value?.firstLoadPointsData(props.dataList, props.total, props.rootID);
    loading.value = 'loaded';
  } catch (error) {
    console.error('KsgMap初始化失败:', error);
    loading.value = 'error';
  }

  emit('loading-change', loading.value);
}

/**
 * 场景上拉到阈值时，加载更多知识节点回调函数
 * @param rootId 根节点id
 * @param crt 当前第几层
 * @param levelSize 每次加载的层数
 */
async function handleLoadMore(rootId: string, crt: number, levelSize: number) {
  loading.value = 'loading';
  emit('loading-change', loading.value);

  // 触发父组件的load-more事件
  emit('load-more', rootId, crt, levelSize);
}

/**
 * 点击知识点title后触发的回调
 * @param id 知识点ID
 */
function handleClickLabel(id: string) {
  emit('click-label', id);
}

/**
 * 加载更多数据（供父组件调用）
 * @param newDataList 新的数据列表
 */
function loadMorePointsData(newDataList: any[]) {
  try {
    ksgRef.value?.loadMorePointsData(newDataList);
    loading.value = 'loaded';
    emit('loading-change', loading.value);
  } catch (error) {
    console.error('加载更多数据失败:', error);
    loading.value = 'error';
    emit('loading-change', loading.value);
  }
}

// 监听数据变化，自动重新初始化
watch(
  () => [props.dataList, props.total, props.rootID],
  () => {
    if (props.autoInit && props.dataList.length > 0) {
      init();
    }
  },
  { deep: true }
);

// 组件挂载后初始化
onMounted(() => {
  if (props.autoInit) {
    init();
  }
});

// 暴露方法给父组件
defineExpose({
  init,
  loadMorePointsData,
  ksgRef
});
</script>

<style scoped lang="less">
.ksg-map-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.ksg-map {
  width: 100%;
  height: 100%;
}
</style>
