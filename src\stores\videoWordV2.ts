import { defineStore } from 'pinia';
import type { RenderInfo } from '@/types/word';

export const useVideoWordStoreV2 = defineStore('videoWordV2', {
  state: () => ({
    regString: '',
    renderInfoListList: [] as RenderInfo[][],
    renderInfoIndexes: [] as Array<{
      listIndex: number;
      index: number;
    }>
  }),

  actions: {
    resetRenderInfoList(clearQid: boolean = true, clearSearch: boolean = true) {
      const renderInfoList = toRaw(this.renderInfoListList).flat();
      // const tempRenderList = this.renderInfoList.filter((item) => {
      //   return item.qids.length > 0 || item.search;
      // });
      // tempRenderList.forEach((item) => {
      //   if (clearQid) {
      //     item.qids = [];
      //   }
      //   if (clearSearch) {
      //     item.search = false;
      //   }
      // });
      renderInfoList.forEach((item) => {
        if (clearQid) {
          item.questionCountMap = {};
        }
        if (clearSearch) {
          item.searchCount = 0;
        }
      });
    },
    getAskModeVideoStrings(): string[] {
      const start = Date.now();
      const renderInfoListList = toRaw(this.renderInfoListList);
      const res = renderInfoListList.map((renderInfoList) => {
        return renderInfoList
          .map((item) => {
            return item.content;
          })
          .join('');
      });
      console.log('getAskModeVideoStrings', Date.now() - start);
      return res;
    },
    getReadModeVideoStrings(): string[] {
      const renderInfoListList = toRaw(this.renderInfoListList);
      const start = Date.now();
      let index = 0;
      let searchIndex = 0;
      let searchFlag = false;
      const res = renderInfoListList.map((renderInfoList) => {
        let tempQid = null;
        let tempContent = null;
        let tempSearch = null;
        let unMatchTempText = '';
        let str = '';
        for (let i = 0; i < renderInfoList.length; ++i) {
          const renderInfo = renderInfoList[i];
          const matchQids = getMatchQids(renderInfo);
          if (matchQids.length || renderInfo.searchCount >= renderInfo.relatedSearchCount) {
            if (unMatchTempText.length > 0) {
              str += unMatchTempText;
              // if (trim(unMatchTempText).length > 0) {
              index++;
              // }
              unMatchTempText = '';
              if (searchFlag) {
                searchIndex++;
                searchFlag = false;
              }
            }
            if (tempQid == null) {
              tempQid = matchQids.join(',');
              tempContent = renderInfo.content;
              tempSearch = renderInfo.searchCount >= renderInfo.relatedSearchCount;
            } else if (
              tempQid == matchQids.join(',') &&
              tempSearch == renderInfo.searchCount >= renderInfo.relatedSearchCount
            ) {
              tempContent += renderInfo.content;
            } else {
              if (tempQid.length > 0) {
                if (tempSearch) {
                  str += `<span data-index="${index++}" data-index2="${searchIndex}" class="highlight highlight2" data-qid="${tempQid}">${tempContent}</span>`;
                  searchFlag = true;
                } else {
                  str += `<span data-index="${index++}" class="highlight" data-qid="${tempQid}">${tempContent}</span>`;
                  if (searchFlag) {
                    searchIndex++;
                    searchFlag = false;
                  }
                }
              } else {
                str += `<span class="highlight2" data-index2="${searchIndex}">${tempContent}</span>`;
                searchFlag = true;
              }
              tempQid = matchQids.join(',');
              tempContent = renderInfo.content;
              tempSearch = renderInfo.searchCount >= renderInfo.relatedSearchCount;
            }
          } else {
            if (tempQid != null) {
              if (tempQid.length > 0) {
                if (tempSearch) {
                  str += `<span data-index="${index++}" data-index2="${searchIndex}" class="highlight highlight2" data-qid="${tempQid}">${tempContent}</span>`;
                  searchFlag = true;
                } else {
                  str += `<span data-index="${index++}" class="highlight" data-qid="${tempQid}">${tempContent}</span>`;
                  if (searchFlag) {
                    searchIndex++;
                    searchFlag = false;
                  }
                }
              } else {
                str += `<span class="highlight2" data-index2="${searchIndex}">${tempContent}</span>`;
                searchFlag = true;
              }
            }
            if (renderInfo.isTag) {
              if (unMatchTempText.length > 0) {
                str += unMatchTempText;
                // if (trim(unMatchTempText).length > 0) {
                index++;
                // }
                unMatchTempText = '';
                if (searchFlag) {
                  searchIndex++;
                  searchFlag = false;
                }
              }
              str += renderInfo.content;
            } else {
              unMatchTempText += renderInfo.content;
            }
            tempQid = null;
            tempContent = null;
            tempSearch = null;
          }
        }
        if (tempQid != null) {
          if (tempQid.length > 0) {
            if (tempSearch) {
              str += `<span data-index="${index++}" data-index2="${searchIndex}" class="highlight highlight2" data-qid="${tempQid}">${tempContent}</span>`;
              searchFlag = true;
            } else {
              str += `<span data-index="${index++}" class="highlight" data-qid="${tempQid}">${tempContent}</span>`;
              if (searchFlag) {
                searchIndex++;
                searchFlag = false;
              }
            }
          } else {
            str += `<span class="highlight2" data-index2="${searchIndex}">${tempContent}</span>`;
            searchFlag = true;
          }
        }
        if (unMatchTempText.length > 0) {
          str += unMatchTempText;
          // if (trim(unMatchTempText).length > 0) {
          index++;
          // }
          unMatchTempText = '';
          if (searchFlag) {
            searchIndex++;
            searchFlag = false;
          }
          // str += `<span data-index="${index++}">${unMatchTempText}</span>`;
        }
        return str;
      });
      console.log('getReadModeVideoStrings', Date.now() - start);
      return res;
    }
  }
});

function getMatchQids(renderInfo: RenderInfo) {
  const keys: (number | string)[] = [];
  for (const key in renderInfo.questionCountMap) {
    if (renderInfo.questionCountMap[key] >= renderInfo.relatedQuestionCount) {
      keys.push(key);
    }
  }
  return keys;
}
