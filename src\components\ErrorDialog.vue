<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import CmpButton from '@/components/CmpButton.vue';
import { ref } from 'vue';

defineProps({
  errorMessage: {
    type: String,
    default: '加载数据失败'
  }
});

const route = useRoute();
const router = useRouter();
const dialogVisible = ref(true);

const handleCloseTab = () => {
  console.log('关闭浏览器窗口按钮被点击');
  // 关闭对话框
  dialogVisible.value = false;
  
  // 延迟执行窗口关闭，确保对话框关闭动画完成
  setTimeout(() => {
    try {
      // 关闭当前浏览器窗口
      window.close();
    } catch (error) {
      console.error('关闭窗口失败:', error);
      // 如果window.close()失败，尝试其他方法
      try {
        // 尝试使用window.open关闭
        const win = window.open('', '_self');
        if (win) {
          win.close();
        }
      } catch (error2) {
        console.error('备用关闭方法也失败:', error2);
        // 如果都失败了，至少返回上一页
        router.go(-1);
      }
    }
  }, 300);
};
</script>

<template>
  <div class="error-dialog-wrapper">
    <el-dialog
      v-model="dialogVisible"
      width="500px"
      :show-close="false"
      class="dialog-container"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <el-card class="error-dialog" shadow="never">
        <div class="warn">{{ errorMessage }}</div>
        <div class="btn-container">
          <CmpButton
            class="btn"
            type="primary"
            @click="handleCloseTab"
          >关闭窗口</CmpButton>
        </div>
      </el-card>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.error-dialog-wrapper {
  &:deep(.el-dialog) {
    border-radius: 5px;
    padding: 0;
    margin-top: 30vh !important; /* 调整dialog位置居中 */
    background-color: #fff;
  }

  &:deep(.el-dialog__header) {
    padding: 0;
  }

  &:deep(.el-dialog__body) {
    padding: 0;
    color: #333333;
    font-size: 12px;
    font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
      '阿里巴巴普惠体 3.0';
    font-weight: 400;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .dialog-container {
    width: 500px;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .error-dialog {
    width: 100%;
    background-color: #f2f2f2;
    border: unset;
    font-size: 12px;
    font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
      '阿里巴巴普惠体 3.0';
    font-weight: 400;

    .title {
      align-content: center;
      color: black;
      font-size: 18px;
      font-weight: 700;
    }

    .divider {
      margin: 10px 0;
    }

    .warn {
      color: #333333;
      margin: 50px 0 50px 0;
      font-size: 16px;
      font-weight: 400;
      text-align: center;
    }

    .btn-container {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;

      .btn {
        width: 200px;
        height: 35px;
        font-size: var(--fontsize-middle-project);
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 