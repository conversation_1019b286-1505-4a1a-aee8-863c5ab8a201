<template>
  <div class="card-container">
    <div class="card-content">
      <div class="text-container">
        购买 <span v-html="props.title" class="drop-line"></span> 学习资料，学习周期为
        {{ props.studyTime }} 天，支付价格
        {{ props.actualPaidAmount }}
        元，购买后，学习资料会在我的“正在学”页面找到。
      </div>
      <div class="agreement">
        <input type="checkbox" v-model="checked" style="margin-right: 10px" />我已登录并同意
        <span @click="gotoAgreement" class="agreement-content">《无尽本源支付协议》</span>
      </div>
      <div class="payfor">
        <div class="foot-btns">
          <!-- <CmpButton class="btn" type="info" @click="model = !model">我知道了</CmpButton> -->
          <CmpButton class="btn" type="primary" @click="handleBuy" v-if="buttonVisible">
            确认购买
          </CmpButton>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import SuccessAnimation from './SuccessAnimation.vue';
import { useProjectStore } from '@/stores/project';
//import { BuyStatus } from '@/types/goods';
import { getPaymentcodeApi, payFreeApi } from '@/apis/payment';
import { getPrjIntroduceApi } from '@/apis/case';

const router = useRouter();
const route = useRoute();

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const orderNo = ref();
const props = defineProps<{
  title: string;
  skuId: string;
  studyTime: number;
  actualPaidAmount: number;
}>();
// 默认同意协议，不能修改
const checked = ref(false);
const buttonVisible = ref(false);
// 跳转到同意协议
const gotoAgreement = () => {
  const { href } = router.resolve({
    path: '/agreement'
  });
  window.open(href, '_blank');
};
watch(
  () => checked.value,
  (newVal) => {
    if (newVal) {
      buttonVisible.value = true;
    } else {
      buttonVisible.value = false;
    }
  }
);

// 创建免费商品订单
const createFreeOrder = async () => {
  if (props.actualPaidAmount !== 0) return;
  const res = await getPaymentcodeApi({
    skuId: props.skuId,
    channel: 0
  });
  if(res.success) {
    orderNo.value = res.data.orderNo
  }
};

//const paySuccess = ref(false);
const emit = defineEmits(['paySuccess']);
const handleBuy = async () => {
  let res;
  if (props.actualPaidAmount === 0) {
    console.log("order", orderNo.value)
    res = await payFreeApi(orderNo.value);
  } else {
    res = await getPaymentcodeApi({
      skuId: props.skuId,
      channel: 0
    });
  }
  if (res.success) {
    emit('paySuccess');
  } else {
    alert(res.message);
  }
};

onMounted(() => {
  createFreeOrder();
});
</script>
<style scoped lang="less">
.card-container {
  background-color: #fff9eb;
  width: 352px;
  height: 543px;

  .card-content {
    margin-top: 95px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .text-container {
      margin-top: 60px;
      margin-bottom: 20px;
      width: 200px;
      height: 200px;
      overflow: hidden;
      font-size: 14px;

      .drop-line {
        :deep(p) {
          display: inline-block;
        }
      }

      .error-overlay {
        position: absolute;
        // top: 50%;
        // left: 50%;
        // transform: translate(-50%, -50%);
        background-color: #ccccccc9;
        width: 200px;
        height: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
        .error-content {
          font-size: 20px;
          font-weight: 400;
          color: var(--color-theme-project);
          &:hover {
            cursor: pointer;
            font-weight: 700;
          }
        }
      }
    }

    .payfor {
      margin-bottom: 20px;
      display: flex;

      span {
        margin-right: 5px;
      }
      .btn {
        margin-top: 16px;
        width: 130px;
        height: 35px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        cursor: pointer;
        border: 1px solid var(--color-theme-project);
        background-color: var(--color-theme-project);
      }
      .activeBtn {
        background-color: var(--color-theme-project);
        color: white;
      }
    }

    .price {
      margin-bottom: 20px;
      width: 271px;
      height: 35px;
      background-color: #ffd37a;
      border-radius: 5px;
      color: #4d0819;
      font-weight: 700;
      font-size: 16px;
      text-align: right;
      line-height: 35px;
      padding-right: 24px;
    }

    .agreement {
      font-size: 14px;
      font-weight: 400;
      display: flex;
      align-items: center;

      .agreement-content:hover {
        cursor: pointer;
      }
    }
    .message {
      padding-top: 180px;
      text-align: center;
      color: #4d0819;
      background-image: url('@/assets/images/project/code.png');
      font-size: 12px;
      font-weight: 600;
      height: 200px;
      width: 200px !important;
      float: left !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: normal !important;
    }
  }
}
</style>
