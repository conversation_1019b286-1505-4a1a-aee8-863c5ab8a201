<template>
  <!-- 
    浏览历史单条信息组件
    实现浏览历史信息的展示，包括日期的显示和跳转功能
   -->
  <div class="browse-history">
    <div class="left-date">
      <div class="date" v-if="showDate">{{ history.date }}</div>
      <div class="triggle" v-if="showDate">
        <img style="width: 5px; height: 7px" src="@/assets/images/learn/u3878.svg" alt="" />
      </div>
    </div>
    <div class="time">
      <img src="@/assets/images/learn/u3882.svg" alt="" />
      <span style="margin-left: 10px">{{ history.startTime }}</span>
    </div>
    <div class="main-info">
      <div class="left-img">
        <img
          style="width: 160px; height: 100px; border-radius: 5px"
          :src="history.coverPic"
          alt=""
        />
      </div>
      <div class="right-detail">
        <div class="title">
          <div @click="goPage" v-html="processAllLatexEquations(history.title)"></div>
          <img
            @click="delHistory(history.oid)"
            style="cursor: pointer"
            src="@/assets/images/learn/delete.svg"
            alt=""
          />
        </div>
        <div class="info">
          <div class="left-info">
            <div style="margin-right: 10px">看到：</div>
            <!-- 讲解与测试没有小结标题,显示正文即可,此处不显示领域，故只有案例有小结标题 -->
            <div
              v-if="history.goodsType == GoodsType.common && history.prjType == PrjType.case"
              class="left-info-see"
              @click="godetailPage"
            >
              {{ history.currentLearning.sectionTitle }}
            </div>
            <div v-else class="left-info-see" @click="godetailPage">正文</div>
            <div style="margin-left: 10px">
              <div v-if="history.currentLearning.prjForm == PrjForm.video">
                {{ history.currentLearning.endTimeTag }}
              </div>
            </div>
          </div>
          <div class="right-info">
            <div class="logo-info">
              <img :src="history.userCoverPic" class="logo" style="height: 12px" />
              <span class="wjby">{{ history.username }}</span>
            </div>
            <div class="ver-line"></div>
            <div>{{ prjTypeInfo[history.prjType] }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { deleteHistoryApi } from '@/apis/learnStatistics';
import { useLearnStore } from '@/stores/learnintro';
import { PrjForm, PrjType } from '@/types/project';
import { GoodsType } from '@/types/goods';
import { processAllLatexEquations } from '@/utils/latexUtils';

const learnStore = useLearnStore();
const router = useRouter();
type History = {
  oid: string;
  userCoverPic: string;
  username: string;
  coverPic: string;
  title: string;
  startTime: string;
  date: string;
  isLast: number;
  currentLearning: {
    chapterId: number;
    prjForm: PrjForm;
    endTimeTag: string;
    sectionTitle: string;
  };
  // 需要传递的数据
  prjType: PrjType;
  spuId: string;
  goodsType: GoodsType;
};
const prjTypeInfo = ['', '知识讲解', '案例学习', '知识测评'];
// 传入完整的数据结构后不用此参数
const props = defineProps<{
  history: History;
  showDate: boolean;
}>();
const emits = defineEmits(['afterDelete']);

const onDelete = () => {
  console.log('de');
};

// 删除历史记录接口
const delHistory = (oid: string) => {
  deleteHistoryApi(oid)
    .then((res) => {
      console.log(res);
      emits('afterDelete');
      window.location.reload();
    })
    .catch((error) => {
      console.log(error);
    });
};

defineExpose({
  delHistory
});

const goPage = async () => {
  try {
    let path: string;
    path = '/goodIntroduce';

    const { href } = router.resolve({
      path: path,
      query: {
        spuId: props.history.spuId
      }
    });
    window.open(href, '_blank');
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
const godetailPage = async () => {
  try {
    const { href } = router.resolve({
      path: 'goodIntroduce',
      query: {
        spuId: props.history.spuId
      }
    });
    window.open(href, '_blank');
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
</script>

<style lang="less" scoped>
.browse-history {
  font-family: var(--text-family);
  color: var(--color-black);
  width: 1300px;
  height: 130px;
  display: flex;
  flex-direction: row;
  align-items: center;

  .left-date {
    width: 140px;
    height: 100%;
    display: flex;
    flex-direction: row;
    padding-left: 20px;

    .date {
      height: 25px;
      width: 100px;
      color: white;
      background-color: var(--color-theme-project);
      border-radius: 5px;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .triggle {
    }
  }

  .time {
    width: 100px;
    height: 100%;
    display: flex;
    align-items: center;
    font-size: var(--fontsize-small-project);
    font-weight: 400;
    color: #797979;
    border-left-style: solid;
    border-left-width: 1px;
    border-left-color: var(--color-second);
  }

  .main-info {
    width: 1060px;
    height: 100px;
    display: flex;
    flex-direction: row;

    .left-img {
      width: 20%;
      height: 100%;
    }

    .right-detail {
      width: 80%;
      display: flex;
      height: 100%;
      flex-direction: column;
      padding-top: 18px;
      border-bottom-style: solid;
      border-bottom-color: var(--color-second);
      border-bottom-width: 1px;

      .title {
        width: 100%;
        display: flex;
        flex-direction: row;
        height: 20px;
        justify-content: space-between;
        font-weight: 700;
        font-size: 18px;
        font-family: var(--title-family);
        color: var(--color-black);
        margin-bottom: 27px;
        cursor: pointer;
      }

      .title:hover {
        color: var(--color-theme-project);
      }

      .info {
        width: 100%;
        height: 20px;
        display: flex;
        flex-direction: row;

        justify-content: space-between;

        .left-info {
          width: 44%;
          height: 100%;
          display: flex;
          flex-direction: row;
          font-size: var(--fontsize-middle-project);
          // justify-content: space-between;

          .left-info-see {
            font-size: var(--fontsize-middle-project);
            cursor: pointer;
            width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .left-info-see:hover {
            color: var(--color-theme-project);
          }
        }

        .right-info {
          width: 20%;
          height: 100%;
          display: flex;
          flex-direction: row;
          font-size: var(--fontsize-small-project);
          font-weight: 400;
          color: var(--color-deep);
          align-items: center;
          justify-content: space-between;

          .ver-line {
            height: 100%;
            width: 2px;
            background-color: var(--color-second);
          }

          .logo-info {
            display: flex;
            align-items: center;
            height: 100%;

            .wjby {
              margin-left: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
