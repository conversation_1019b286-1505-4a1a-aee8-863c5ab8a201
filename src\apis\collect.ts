import { http } from '@/apis';

/**waq */
//获取收藏夹title接口参数
interface getCollectDataParams {
  limit: number;
  current: number;
  keyword?: string;
}
//获取收藏夹title接口
export function getCollectDataApi(params: getCollectDataParams) {
  return http.get<{
    current: number;
    limit: number;
    total: number;
    records: any[];
  }>('/favorites-file/page', params);
}

/**waq */
//获取收藏夹详情接口参数
interface getCollectDetailDataParams {
  favoriteId: string | number;
}

import type { ExamData } from '@/types/exam';
import type { exerciseItem } from '@/types/exercise';
import type { ExerciseType, contentType } from '@/types/exercise';
//获取收藏夹详情接口
export function getCollectDetailDataApi(params: getCollectDetailDataParams) {
  return http.get<{
    preId: number | null;
    nextId: number | null;
    documentData: ExamData;
  }>('/favorites-file/info', params);
}

/**waq */
//移除收藏夹接口参数
interface removeCollectDetailDataParams {
  examId: string | number;
}
//移除收藏夹接口
export function removeCollectDetailDataApi(params: removeCollectDetailDataParams) {
  return http.get('/favorites-file/cancel', params);
}

//添加收藏夹接口
export function addFavoritesApi(exerciseId: string) {
  return http.request({
    method: 'post',
    url: '/favorites-file/create',
    data: { exerciseId }
  });
}
export function cancelFavoritesApi(exerciseId: string) {
  return http.request({
    method: 'post',
    url: '/favorites-file/cancel',
    data: { exerciseId }
  });
}

interface getCollectListParams {
  current: number;
  keyword?: string;
}
interface exerciseStemPage {
  current: number;

  limit: number;

  total: number;
  records: [
    {
      exerciseId: string;
      stem: string;
    }
  ];
}
export function getCollectListApi(params: getCollectListParams) {
  return http.get<{
    exerciseStemPage: exerciseStemPage;
    exerciseIdList: string[];
  }>('/favorites-file/page', params);
}

export function getExerciseApi(exerciseId: string) {
  return http.request<{
    exerciseId: string;
    stem: string;
    type: ExerciseType;
    content: contentType[];
    answer: string[];
    explanation: string;
  }>({
    method: 'get',
    url: '/favorites-file/query',
    params: {
      exerciseId
    }
  });
}
