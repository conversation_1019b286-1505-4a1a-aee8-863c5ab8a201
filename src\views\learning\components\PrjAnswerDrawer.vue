<template>
  <div class="warpper">
    <el-drawer v-model="model" size="50%" direction="ltr" class="drawer">
      <template #header>
        <div class="header">查看问题</div>
      </template>
      <el-scrollbar class="drawer-body">
        <div class="question">
          <div class="title">
            <div class="creator-name">{{ question.userName }}</div>
            的提问
          </div>
          <div class="description">
            "<span class="key-words" v-html="question.keyword"></span>"
            <span
              v-if="question.questionType != '开放性问题'"
              v-html="question.questionType"
            ></span>
            <span v-else v-html="question.questionDescription"></span>
          </div>
          <div class="bottom">
            <div class="date">
              {{ question.createTime }}
            </div>
            <div class="operate">
              <div
                v-if="question.canDelete"
                class="click-delete"
                @click="handleDeleteQuestion(questionId)"
              >
                删除
              </div>
              <!-- <div v-if="!showMyAnswer" class="click-answer" @click="showMyAnswer = true">回答</div> -->
            </div>
          </div>
        </div>
        <!-- <div v-if="showMyAnswer" class="my-answer">
          <el-divider class="divider" />
          <div class="title">
            <span class="name">
              {{ userInfo.username }}
            </span>
            的回答
          </div>
          <div class="add-klg">
            <el-button class="button" plain @click="showAddKlg = true">+ 添加知识</el-button>
          </div>
          <div class="klg-list">
            <div
              class="klg-item"
              v-for="item in klgList"
              :key="item.id"
              @click="goToDetail(item.id)"
            >
              <el-tooltip placement="top" effect="customized">
                <template #content>
                  <span v-html="item.name"></span>
                </template>
                <el-tag
                  class="klg-tag"
                  closable
                  @close="klgList = klgList.filter((klg) => klg.id != item.id)"
                >
                  <div class="klg-tag-text" v-html="item.name"></div>
                </el-tag>
              </el-tooltip>
            </div>
          </div>
          <el-input v-model="myanswer" type="textarea" :rows="4" class="description" />
          <div class="bottom">
            <el-button @click="handleAnswer" class="button"> 提交回答 </el-button>
          </div>
        </div> -->
        <div class="all-answer">
          <div v-for="answer in question.answers" :key="answer.answerId" class="answer-list">
            <el-card class="answer-card">
              <div class="title">
                <span class="name">
                  {{ answer.userName }}
                </span>
                的回答
              </div>
              <div class="klg-list">
                <div
                  class="klg-item"
                  v-for="item in answer.knowledgeList"
                  :key="item.code"
                  @click="goToDetail(item.code, item.type)"
                >
                  <el-tooltip placement="top" effect="customized">
                    <template #content>
                      <span v-html="item.title"></span>
                    </template>
                    <el-tag class="klg-tag">
                      <div class="klg-tag-text" v-html="item.title"></div>
                    </el-tag>
                  </el-tooltip>
                </div>
              </div>
              <div class="description">
                {{ answer.answerExplanation }}
              </div>
              <div class="bottom">
                <div class="date">
                  {{ answer.createTime }}
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </el-scrollbar>
    </el-drawer>
  </div>
  <!-- <add-klg
    v-if="showAddKlg"
    :showAddKlg="showAddKlg"
    @close="showAddKlg = false"
    @submit="handleSubmit"
  >
  </add-klg> -->
</template>

<script setup lang="ts">
import type { QuestionData } from '@/types/learning';
import AddKlg from './AddKlg.vue';
import { saveAnswerApi, deleteQuestionApi, getQuestionDetailApi } from '@/apis/learning';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const route = useRoute();

interface Klg {
  id: string;
  name: string;
}

const props = defineProps<{
  question: QuestionData;
  showqList: Boolean;
  questionId: string;
}>();
const emits = defineEmits(['close', 'refresh', 'deleteQuestion']);
const show = ref(false);
const model = defineModel({ type: Boolean });
const showMyAnswer = ref(false);
const myanswer = ref<string>('');
const showAddKlg = ref(false);
const klgList = ref<Klg[]>([]);
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

const handleDelete = (aimId: string) => {
  console.log(aimId);
  klgList.value = klgList.value.filter((item) => {
    console.log(item.id != aimId);
    return item.id != aimId;
  });
};

const handleDeleteQuestion = async (questionId: string) => {
  emits('deleteQuestion', questionId);
  const res = await deleteQuestionApi(questionId);
  if (res.success) {
    ElMessage.success('删除成功');
  }
  show.value = false;
  model.value = false;
};

function handleAnswer() {
  const answerKlgs = klgList.value.map((item) => item.id).join('@@');
  const params = {
    questionId: props.question.questionId,
    answerKlgs: answerKlgs,
    answerExplanation: myanswer.value
  };
  saveAnswerApi(params).then((res) => {
    // @ts-ignore
    if (res.success) {
      myanswer.value = '';
      klgList.value = [];
      emits('refresh', props.question.questionId);
      showMyAnswer.value = false;
      ElMessage({
        type: 'success',
        message: '保存成功'
      });
    } else {
      // @ts-ignore
      ElMessage.error(res.message);
    }
  });
}

function handleSubmit(list: Klg[]) {
  klgList.value.push(
    ...list.filter((item) => !klgList.value.map((klg) => klg.id).includes(item.id))
  );
}

const goToDetail = (code: string, type: number) => {
  if (type == 1) {
    const { href } = router.resolve({
      path: '/klgdetail',
      query: {
        klgCode: code
      }
    });
    window.open(href, '_blank');
  } else {
    // const { href } = router.resolve({
    //   path: '/areadetail',
    //   query: {
    //     areaCode: code
    //   }
    // });
    const { href } = router.resolve({
      path: '/area',
      query: {
        areaCode: code
      }
    });
    window.open(href, '_blank');
  }
};

const questionDetail = ref();
onMounted(async () => {
  if (props.questionId) {
    const res = await getQuestionDetailApi(props.questionId);
    questionDetail.value = res.data;
  }
  watch(
    () => props.questionId,
    async (newVal) => {
      const res = await getQuestionDetailApi(newVal);
      questionDetail.value = res.data;
    }
  );
  watch(show, (newVal) => {
    if (!newVal) {
      emits('close');
      showMyAnswer.value = false;
      myanswer.value = '';
      klgList.value = [];
    }
  });
});
</script>

<style scoped>
.warpper {
  &:deep(.el-drawer__header) {
    padding: 10px 10px 10px 10px;
    margin: 0;
    border-bottom: 1px solid #dddddd;
    color: #333333;
    font-family:
      '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
    font-size: 16px;
    font-weight: 700;
  }

  &:deep(.el-drawer__body) {
    padding: 0;
  }

  .drawer-body {
    height: 100%;
    color: #333333;
    display: flex;
    flex-direction: column;
    font-family:
      '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
    font-size: 12px;
    font-weight: 400;
    padding-left: 10px;
    padding-right: 10px;

    .question {
      display: flex;
      flex-direction: column;
      align-items: left;

      .title {
        margin-top: 10px;
        display: flex;
        justify-content: left;
        font-size: 12px;

        .creator-name {
          font-weight: 700;
          margin-right: 10px;
        }
      }

      .description {
        margin-top: 10px;
        font-size: 14px;
        .key-words {
          font-weight: 700;
        }
      }

      .bottom {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        .date {
          font-size: 12px;
          color: '#999999';
        }

        .operate {
          display: flex;
          flex-direction: row;

          .click-delete {
            color: var(--color-deep);
            &:hover {
              font-weight: 700;
              cursor: pointer;
            }
            margin: 0 10px;
          }

          .click-answer {
            color: var(--color-theme-project);
            &:hover {
              font-weight: 700;
              cursor: pointer;
            }
          }
        }
      }
    }
    .my-answer {
      .divider {
        margin: 22px 0 20px 0;
      }

      .title {
        margin-left: 20px;

        .name {
          margin-right: 10px;
          font-weight: 700;
        }
      }

      .add-klg {
        padding-left: 20px;
        margin-top: 10px;
      }

      .klg-list {
        padding-left: 20px;
        margin-top: 10px;
      }

      .description {
        padding-left: 20px;
        margin-top: 10px;
        margin-bottom: 10px;
      }

      .bottom {
        display: flex;
        flex-direction: row-reverse;
      }
    }
    .all-answer {
      .answer-list {
        &:deep(.el-card__body) {
          padding-left: 10px;
        }

        .answer-card {
          margin: 10px 0px 10px 20px;
          background-color: rgba(214, 233, 246, 0.996078431372549);

          .title {
            .name {
              font-weight: 700;
            }
          }

          .klg-list {
            margin-top: 10px;
          }

          .description {
            font-size: 16px;
            margin-top: 10px;
            margin-bottom: 10px;
          }

          .bottom {
            .date {
              color: '#999999';
            }
          }
        }
      }
    }

    .klg-item {
      display: inline;
      &:hover {
        cursor: pointer;
      }

      .klg-tag {
        border-color: #1661ab;
        border-radius: 0;
        margin: 0 8px 3px 0px;
        background-color: white;
        border-radius: 3px;
        .klg-tag-text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 20ch;
          color: #1661ab;
          font-size: 12px;
          font-family: '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
          font-weight: 400;
        }
      }
    }
    .button {
      width: 90px;
      height: 27px;
      font-size: 12px;
      font-family: '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
      font-weight: 400;
      font-variant: small-caps;
      color: white;
      background-color: var(--color-theme-project);
    }
  }
}
</style>
