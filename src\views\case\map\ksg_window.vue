<template>
  <div style="display: none">
    <span id="marker_template" class="marker"><span class="name"> </span></span>

    <span id="legacy_marker_template" class="legacymarker"><span class="name"> </span></span>
  </div>

  <div id="layout">
    <div id="visualization">
      <div id="css-container">
        <div id="css-world">
          <div id="css-camera">
            <!-- 2D overlay elements go in here -->
          </div>
        </div>
      </div>
      <div id="glContainer">
        <!-- 3D webgl canvas here -->
      </div>
    </div>

    <div id="icon-nav"></div>

    <div id="detailContainer" style="padding-top: 10%">
      <div id="detailTitle">
        <span>Sun</span>
        <div id="detailClose">
          <p id="zoom-back"></p>
          <p id="ex-out">&times;</p>
        </div>
      </div>
      <div id="detailBody">
        <p>
          The <b>Sun</b> is the <a href="http://en.wikipedia.org/wiki/Star" title="Star">star</a> at
          the center of the
          <a href="http://en.wikipedia.org/wiki/Solar_System" title="Solar System">Solar System</a>.
          It is almost perfectly
          <a href="http://en.wikipedia.org/wiki/Sphere" title="Sphere">spherical</a>
          and consists of hot
          <a href="http://en.wikipedia.org/wiki/Plasma_(physics)" title="Plasma (physics)"
            >plasma</a
          >
          interwoven with
          <a href="http://en.wikipedia.org/wiki/Magnetic_field" title="Magnetic field"
            >magnetic fields</a
          >.<sup id="cite_ref-11" class="reference"
            ><a href="http://en.wikipedia.org/wiki/Sun#cite_note-11"
              ><span>[</span>12<span>]</span></a
            ></sup
          ><sup id="cite_ref-12" class="reference"
            ><a href="http://en.wikipedia.org/wiki/Sun#cite_note-12"
              ><span>[</span>13<span>]</span></a
            ></sup
          >
          It has a
          <a href="http://en.wikipedia.org/wiki/Diameter" title="Diameter">diameter</a>
          of about 1,392,684 km,<sup id="cite_ref-arxiv1203_4898_4-2" class="reference"
            ><a href="http://en.wikipedia.org/wiki/Sun#cite_note-arxiv1203_4898-4"
              ><span>[</span>5<span>]</span></a
            ></sup
          >
          about 109&nbsp;times that of
          <a href="http://en.wikipedia.org/wiki/Earth" title="Earth">Earth</a>, and its mass (about
          2<span style="margin: 0 0.15em 0 0.25em">×</span>10<sup>30</sup>&nbsp;kilograms,
          330,000&nbsp;times that of Earth) accounts for about 99.86% of the total mass of the Solar
          System.<sup id="cite_ref-Woolfson00_13-0" class="reference"
            ><a href="http://en.wikipedia.org/wiki/Sun#cite_note-Woolfson00-13"
              ><span>[</span>14<span>]</span></a
            ></sup
          >
          Chemically, about three quarters of the Sun's mass consists of
          <a href="http://en.wikipedia.org/wiki/Hydrogen" title="Hydrogen">hydrogen</a>, while the
          rest is mostly <a href="http://en.wikipedia.org/wiki/Helium" title="Helium">helium</a>.
          The remainder (1.69%, which nonetheless equals 5,628&nbsp;times the mass of Earth)
          consists of heavier elements, including
          <a href="http://en.wikipedia.org/wiki/Oxygen" title="Oxygen">oxygen</a>,
          <a href="http://en.wikipedia.org/wiki/Carbon" title="Carbon">carbon</a>,
          <a href="http://en.wikipedia.org/wiki/Neon" title="Neon">neon</a>
          and
          <a href="http://en.wikipedia.org/wiki/Iron" title="Iron">iron</a>, among others.<sup
            id="cite_ref-basu2008_14-0"
            class="reference"
            ><a href="http://en.wikipedia.org/wiki/Sun#cite_note-basu2008-14"
              ><span>[</span>15<span>]</span></a
            ></sup
          ><span id="why_the_sun_is_yellow"></span>
        </p>
        <p>
          Excerpt from
          <a href="http://en.wikipedia.org/wiki/Sun" id="excerpt-link">Wikipedia.</a>
        </p>
      </div>
    </div>
  </div>

  <div id="star-name">
    <span>Sun</span>
  </div>

  <div id="meta">
    <p>
      <!-- Stuff info here! -->
    </p>
  </div>

  <div id="minimap">
    <div id="volume">
      <!-- 				<img src="images/icons/sound-on.svg" alt="" /> -->
    </div>
    <div id="zoom-levels">
      <div id="zoom-backdrop"></div>
      <div id="zoom-cursor"></div>
    </div>
  </div>

  <!--    <div id="loader">
      <div>
        <img
          src="images/loading6.gif"
          width="50"
          height="50"
          alt=""
          border="0"
        />
        <p id="loadtext">Loading the galaxy, please wait&hellip;</p>
      </div>
    </div> -->

  <img id="image" />
</template>

<script>
/* eslint-disable */
//import * as THREEx from 'threex'
import * as dat from 'dat.gui';
import * as THREE from 'three';
//tmp import { Gyroscope } from 'three/examples/jsm/misc/Gyroscope.js';
import $ from 'jquery';
import * as TWEEN from 'tween';
//import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
//import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
//import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
//import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';
//import * as dagre from '@dagrejs/dagre'
//import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass.js';

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

let screenWidth;
let screenHeight;
let screenWhalf, screenHhalf;
let divCSSWorld, divCSSCamera;
let fovValue;
let shaderList = [
  '/shaders/starsurface',
  '/shaders/starhalo',
  '/shaders/starflare',
  '/shaders/galacticstars',
  '/shaders/galacticdust',
  '/shaders/datastars',
  '/shaders/cubemapcustom',
  '/shaders/corona'
];
let camera;
let renderer;
let scene;

let starModel;
/************************************************************************/
/* Initialized some variables for CSS, and also it computes the initial

position for the CSS cube based on the Three Cube */
/************************************************************************/
function initCSS3D() {
  //    screenWidth = window.innerWidth;
  //    screenHeight = window.innerHeight;

  screenWidth = 732;
  screenHeight = 477;
  screenWhalf = screenWidth / 2;
  screenHhalf = screenHeight / 2;

  divCSSWorld = document.getElementById('css-world');
  divCSSCamera = document.getElementById('css-camera');
  //tmp    divCube = document.getElementById('shape');

  fovValue = (0.5 / Math.tan((camera.fov * Math.PI) / 360)) * screenHeight;

  setCSSWorld();
  // setDivPosition(divCube, glCube);
}

/************************************************************************/
/* Applies CSS3 styles to the css-world div                             */
/************************************************************************/
function setCSSWorld() {
  divCSSWorld.style.WebkitPerspective = fovValue + 'px';
  divCSSWorld.style.WebkitPerspectiveOrigin = '50% 50%';
  divCSSWorld.style.MozPerspective = fovValue + 'px';
  divCSSWorld.style.MozPerspectiveOrigin = '50% 50%';
  divCSSWorld.style.overflow = 'hidden';
}

/************************************************************************/
/*  Applies CSS3 styles to css-camera div                               */
/************************************************************************/
function setCSSCamera(camera, fovValue) {
  //tmp    console.log("camera====hello");
  var cameraStyle = getCSS3D_cameraStyle(camera, fovValue);
  //tmp    console.log("cameraStyle:", cameraStyle);
  divCSSCamera.style.WebkitTransform = cameraStyle;
  divCSSCamera.style.MozTransform = cameraStyle;
}

/************************************************************************/
/* Return the CSS3D transformations from the Three camera               */
/************************************************************************/
function getCSS3D_cameraStyle(camera, fov) {
  //tmp    console.log("camera++++hello");
  camera.updateProjectionMatrix();
  var cssStyle = '';
  cssStyle += 'translate3d(0,0,' + epsilon(fov) + 'px) ';
  cssStyle += toCSSMatrix(camera.matrixWorldInverse, true);
  cssStyle += ' translate3d(' + screenWhalf + 'px,' + screenHhalf + 'px, 0)';
  return cssStyle;
}

/************************************************************************/
/* Fixes the difference between WebGL coordinates to CSS coordinates    */
/************************************************************************/
function toCSSMatrix(threeMat4, b, offsetX, offsetY) {
  var a = threeMat4,
    f;
  if (b) {
    f = [
      a.elements[0],
      -a.elements[1],
      a.elements[2],
      a.elements[3],
      a.elements[4],
      -a.elements[5],
      a.elements[6],
      a.elements[7],
      a.elements[8],
      -a.elements[9],
      a.elements[10],
      a.elements[11],
      a.elements[12],
      -a.elements[13],
      a.elements[14],
      a.elements[15]
    ];
  } else {
    f = [
      a.elements[0],
      a.elements[1],
      a.elements[2],
      a.elements[3],
      a.elements[4],
      a.elements[5],
      a.elements[6],
      a.elements[7],
      a.elements[8],
      a.elements[9],
      a.elements[10],
      a.elements[11],
      a.elements[12] + (offsetX || 0),
      a.elements[13] + (offsetY || 0),
      a.elements[14],
      a.elements[15]
    ]; // Hack to make sure the text is centered
  }
  for (var e in f) {
    f[e] = epsilon(f[e]);
  }
  return 'matrix3d(' + f.join(',') + ')';
}

/************************************************************************/
/* Computes CSS3D transformations based on a Three Object                */
/************************************************************************/
function setDivPosition(cssObject, glObject, scale) {
  glObject.updateMatrix();
  cssObject.style.position = 'absolute';
  //tmp    var transformation = CSStransform(1.0, 3.5, glObject.matrixWorld, scale);
  var transformation = CSStransform(0, 0, glObject.matrixWorld, scale);
  //tmp    console.log("====transformation = ", transformation);
  //tmp    console.log("====scale==== ", scale);
  //tmp    console.log("====glObject.matrixWorld==== ", glObject.matrixWorld);
  //Webkit:
  cssObject.style.WebkitTransformOrigin = '0% 0%';
  cssObject.style.WebkitTransform = transformation;
  //Mozilla:
  cssObject.style.MozTransformOrigin = '0% 0%';
  cssObject.style.MozTransform = transformation;
}

/************************************************************************/
/* Helper function to convert to CSS3D transformations                  */
/************************************************************************/
function CSStransform(width, height, matrix, scale) {
  return [
    toCSSMatrix(matrix, false, width, height),
    'scale3d(' + scale + ', -' + scale + ', ' + scale + ')',
    'translate3d(0,0,0)'
  ].join(' ');
}

/************************************************************************/
/* Rounding error                                                       */
/************************************************************************/
function epsilon(a) {
  if (Math.abs(a) < 0.000001) {
    return 0;
  }
  return a;
}

//tmp  import * as THREEx from './THREEx_namespace.js'
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/** @namespace */
let THREEx = {};

/**
 * - NOTE: it would be quite easy to push event-driven too
 *   - microevent.js for events handling
 *   - in this._onkeyChange, generate a string from the DOM event
 *   - use this as event name
 */
THREEx.KeyboardState = function () {
  // to store the current state
  //tmp	this.keyCodes	= {};
  //tmp	this.modifiers	= {};
  this.KeyboardState.keyCodes = {};
  this.KeyboardState.modifiers = {};

  // create callback to bind/unbind keyboard events
  var self = this.KeyboardState;
  //tmp     	this._onKeyDown	= (event) => { self._onKeyChange(event, true); };
  //tmp     	this._onKeyUp	= (event) => { self._onKeyChange(event, false);};
  this.KeyboardState._onKeyDown = function (event) {
    self._onKeyChange(event, true);
  };
  this.KeyboardState._onKeyUp = function (event) {
    self._onKeyChange(event, false);
  };

  console.log(self);
  console.log(this);
  // bind keyEvents
  document.addEventListener('keydown', this.KeyboardState._onKeyDown, false);
  document.addEventListener('keyup', this.KeyboardState._onKeyUp, false);
};

/**
 * to process the keyboard dom event
 */
// 原先在prototype存的函数，回头得仔细看一下有没有性能问题
//tmp  THREEx.KeyboardState.prototype._onKeyChange	= function(event, pressed)
THREEx.KeyboardState._onKeyChange = function (event, pressed) {
  // log to debug
  //console.log("onKeyChange", event, pressed, event.keyCode, event.shiftKey, event.ctrlKey, event.altKey, event.metaKey)

  // update this.keyCodes
  var keyCode = event.keyCode;
  this.keyCodes[keyCode] = pressed;

  // update this.modifiers
  this.modifiers['shift'] = event.shiftKey;
  this.modifiers['ctrl'] = event.ctrlKey;
  this.modifiers['alt'] = event.altKey;
  this.modifiers['meta'] = event.metaKey;
};

/**
 * query keyboard state to know if a key is pressed of not
 *
 * @param {String} keyDesc the description of the key. format : modifiers+key e.g shift+A
 * @returns {Boolean} true if the key is pressed, false otherwise
 */
// 原先在prototype存的函数，回头得仔细看一下有没有性能问题
//tmp THREEx.KeyboardState.prototype.pressed	= function(keyDesc)
THREEx.KeyboardState.pressed = function (keyDesc) {
  var keys = keyDesc.split('+');
  for (var i = 0; i < keys.length; i++) {
    var key = keys[i];
    var pressed;
    if (THREEx.KeyboardState.MODIFIERS.indexOf(key) !== -1) {
      pressed = this.modifiers[key];
    } else if (Object.keys(THREEx.KeyboardState.ALIAS).indexOf(key) != -1) {
      pressed = this.keyCodes[THREEx.KeyboardState.ALIAS[key]];
    } else {
      pressed = this.keyCodes[key.toUpperCase().charCodeAt(0)];
    }
    if (!pressed) return false;
  }
  return true;
};
/**
 * To stop listening of the keyboard events
 */
//tmp THREEx.KeyboardState.prototype.destroy	= function()
THREEx.KeyboardState.destroy = function () {
  // unbind keyEvents
  document.removeEventListener('keydown', this._onKeyDown, false);
  document.removeEventListener('keyup', this._onKeyUp, false);
};

THREEx.KeyboardState.MODIFIERS = ['shift', 'ctrl', 'alt', 'meta'];
THREEx.KeyboardState.ALIAS = {
  left: 37,
  up: 38,
  right: 39,
  down: 40,
  space: 32,
  pageup: 33,
  pagedown: 34,
  tab: 9
};

//tmp var pageZoom = 1.0;
THREEx.WindowResize = function (renderer, camera) {
  var callback = function () {
    //		var w = window.innerWidth;
    //		var h = window.innerHeight;
    var w = 732;
    var h = 477;

    //tmp		pageZoom = document.documentElement.clientWidth / w;

    // used for css3d placement
    screenWidth = w;
    screenHeight = h;

    var devicePixelRatio = window.devicePixelRatio || 1;

    // notify the renderer of the size change
    renderer.setSize(w * devicePixelRatio, h * devicePixelRatio);
    renderer.domElement.style.width = w + 'px';
    renderer.domElement.style.height = h + 'px';

    // update the camera
    camera.aspect = w / h;
    camera.updateProjectionMatrix();
    initCSS3D();
  };
  // bind the resize event
  window.addEventListener('resize', callback, false);
  // return .stop() the function to stop watching window resize
  return {
    /**
     * Stop watching window resize
     */
    stop: function () {
      window.removeEventListener('resize', callback);
    }
  };
};
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

export default {
  props: ['ksg_window_width', 'ksg_window_height'],
  name: 'ksg_window',
  mounted() {
    this.initThree();
  },
  methods: {
    initThree() {
      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      //	list of shaders we'll load

      start();

      //	a small util to pre-fetch all shaders and put them in a data structure (replacing the list above)
      function loadShaders(list, callback) {
        var shaders = {};

        var expectedFiles = list.length * 2;
        var loadedFiles = 0;

        function makeCallback(name, type) {
          //这里头用到了闭包，data应该是$(document).load函数加载得到的data数据
          return function (data) {
            if (shaders[name] === undefined) {
              shaders[name] = {};
            }

            shaders[name][type] = data;

            //	check if done
            loadedFiles++;
            if (loadedFiles == expectedFiles) {
              callback(shaders);
            }
          };
        }

        for (var i = 0; i < list.length; i++) {
          var vertexShaderFile = list[i] + '.vsh';
          var fragmentShaderFile = list[i] + '.fsh';

          //	find the filename, use it as the identifier
          var splitted = list[i].split('/');
          var shaderName = splitted[splitted.length - 1];
          $(document).load(vertexShaderFile, makeCallback(shaderName, 'vertex'));
          $(document).load(fragmentShaderFile, makeCallback(shaderName, 'fragment'));
        }
      }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      //tmp      var $loadText = $("#loadtext");
      //tmp      function setLoadMessage(msg) {
      //tmp        $loadText.html(msg + "&hellip;");
      //tmp      }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      var masterContainer = document.getElementById('visualization');
      //        Graphic Settings
      //        Clear cross origin flags
      THREE.ImageUtils.crossOrigin = null;

      //        Graphic Settings
      var maxAniso = 1;
      var enableDataStar = true;
      //tmp var enableSkybox = true;
      //tmp var enableGalaxy = true;
      //tmp var enableDust = false;
      //tmp var enableSolarSystem = true;
      //tmp var enableSpacePlane = true;
      //tmp var enableStarModel = true;
      //tmp var enableTour = true;
      //tmp var enableDirector = true;

      var galacticCentering;

      var enableSkybox = false;
      var enableGalaxy = true;
      var enableDust = false;
      var enableSolarSystem = false;
      var enableSpacePlane = true;
      var enableStarModel = false;

      var firstTime = localStorage ? localStorage.getItem('first') == null : true;

      //tmp // Tour
      //tmp var tour = new Tour(GALAXY_TOUR);

      //tmp var initialAutoRotate = true;
      var initialAutoRotate = false;

      //        animation timing
      var startTime = Date.now();
      var clock = new THREE.Clock();
      var shaderTiming = 0;

      var $starName = $('#star-name');

      var $iconNav = $('#icon-nav');

      var $detailContainer = $('#detailContainer');
      var $cssContainer = $('#css-container');

      //tmp var $spectralGraph = $('#spectral-graph');

      //        world transform
      var rotating;
      var translating;

      var lastRotateY = 0;
      var rotateYAccumulate = 0;

      //        global objects
      var starData;
      var pSystem;
      var pGalacticSystem;
      var pDustSystem;
      //tmp var earth;
      var spacePlane;
      // var glCube;

      var gradientImage;
      var gradientCanvas;

      //tmp var rtparam = { minFilter: THREE.LinearFilter, magFilter: THREE.LinearFilter, format: THREE.RGBFormat, stencilBufer: false };
      //tmp var rt;

      //tmp var antialias = gup('antialias') == 1 ? true : false;
      var antialias = true;

      //        called from body onload
      //        页面加载时onload加载的函数
      //tmp function start( e ){
      function start() {
        // detect for webgl and reject everything else
        //tmp        if ( ! Detector.webgl ) {
        //tmp          if ( Detector.Chrome ) {
        //tmp            Detector.addGetWebGLMessage([
        //tmp         'Your graphics card does not support WebGL. Please try again on a different Windows, Mac, or Linux computer using <a href="http://www.google.com/chrome/" style="color:#ffffff; text-decoration:underline; text-transform:capitalize">Google Chrome</a><br>',
        //tmp         'or another <a href="http://www.khronos.org/webgl/wiki_1_15/index.php/Getting_a_WebGL_Implementation" style="color:#ffffff; text-decoration:underline; text-transform:none"> WebGL-Compatible browser</a>. You can watch a video preview of the experiment below:',
        //tmp         '<p><iframe style="margin-top:4em;" id="trailer" width=800 height=600 src="http://www.youtube.com/embed/TU6RAjABX40" frameborder="0" allowfullscreen></iframe></p>',
        //tmp       ].join( '\n' ));
        //tmp            return;
        //tmp          }
        //tmp                Detector.addGetWebGLMessage();
        //tmp                return;
        //tmp        }

        //tmp        gradientImage = document.createElement('img');
        // image创建完成后加载postStarGradientLoaded函数
        gradientImage = document.getElementById('image');
        //tmp        postStarGradientLoaded;
        gradientCanvas = document.createElement('canvas');
        gradientCanvas.width = gradientImage.width;
        gradientCanvas.height = gradientImage.height;
        gradientCanvas
          .getContext('2d')
          .drawImage(gradientImage, 0, 0, gradientImage.width, gradientImage.height);
        gradientCanvas.getColor = function (percentage) {
          return this.getContext('2d').getImageData(0, percentage * gradientImage.height, 1, 1)
            .data;
        };

        //        load all the shaders first before doing anything
        loadShaders(shaderList, function (e) {
          //        we have the shaders loaded now...
          shaderList = e;
          postShadersLoaded();
        });

        //tmp        gradientImage.src = 'images/star_color_modified.png';
      }

      var postStarGradientLoaded = function () {
        console.log('hello3');
        gradientCanvas = document.createElement('canvas');
        gradientCanvas.width = gradientImage.width;
        gradientCanvas.height = gradientImage.height;
        gradientCanvas
          .getContext('2d')
          .drawImage(gradientImage, 0, 0, gradientImage.width, gradientImage.height);
        gradientCanvas.getColor = function (percentage) {
          return this.getContext('2d').getImageData(0, percentage * gradientImage.height, 1, 1)
            .data;
        };

        //        load all the shaders first before doing anything
        loadShaders(shaderList, function (e) {
          //        we have the shaders loaded now...
          shaderList = e;
          postShadersLoaded();
        });
      };

      var postShadersLoaded = function () {
        if (enableDataStar) {
          loadStarData('/data/stars_all.json', function (loadedData) {
            starData = loadedData.stars;
            console.log(starData);
            initScene();
            animate();
          });
        } else {
          initScene();
          animate();
        }
      };

      var controllers = {
        viewSize: 0.6,
        datastarSize: 1.0,
        sceneSize: 1000.0,
        sol: function () {
          camera.position.z = 1.1;
        },
        solarsystem: function () {
          camera.position.z = 18;
        },
        hipparcos: function () {
          camera.position.z = 1840;
        },
        milkyway: function () {
          camera.position.z = 40000;
        }
      };

      var gui;

      //对应于页面里头的这个dom element，{domElement: div.dg.main.a, __ul: ul, __folders: {…}, __controllers: Array(14), __rememberedObjects: Array(0), …}
      //可以通过修改它的display属性来使得它可见
      function buildGUI() {
        var gui = new dat.GUI();
        gui.domElement.style.display = 'none';
        // gui.domElement.style.display = 'none';

        var c = gui.add(controllers, 'viewSize', 0.01, 4.0);
        c.onChange(function (v) {
          camera.scale.z = v;
        });

        c = gui.add(controllers, 'datastarSize', 0.01, 10.0);
        c = gui.add(controllers, 'sceneSize', 1, 50000);

        c = gui.add(controllers, 'sol');
        c = gui.add(controllers, 'solarsystem');
        c = gui.add(controllers, 'hipparcos');
        c = gui.add(controllers, 'milkyway');

        // c = gui.add(camera, 'fov', 1.0, 200.0 );
        initializeMinimap();
      }

      //        -----------------------------------------------------------------------------
      //        All the initialization stuff for THREE
      function initScene() {
        //        -----------------------------------------------------------------------------
        //        Let's make a scene
        scene = new THREE.Scene();

        scene.add(new THREE.AmbientLight(0x505050));

        rotating = new THREE.Object3D();

        translating = new THREE.Object3D();

        galacticCentering = new THREE.Object3D();
        galacticCentering.add(translating);
        rotating.add(galacticCentering);
        //层次关系是：
        // scene => rotation => galacticCentering => translating
        scene.add(rotating);

        translating.targetPosition = new THREE.Vector3();
        translating.update = function () {
          //首先检查 easePanning 属性（如果存在），如果它为 true，则
          //不执行移动（这可能是一个用于暂停移动的控制标志）。
          if (this.easePanning) return;
          //使用 lerp 方法（线性插值）将物体的当前位置 this.position 向
          //目标位置 this.targetPosition 移动。0.1 是插值因子，表示每一
          //帧物体位置更新的比例。较小的值会使移动更平滑，但速度更慢；较大
          //的值会使移动更快，但可能不够平滑。
          this.position.lerp(this.targetPosition, 0.1);
          if (this.position.distanceTo(this.targetPosition) < 0.01)
            this.position.copy(this.targetPosition);
        };

        //        -----------------------------------------------------------------------------
        //        Setup our renderer
        //        screenWidth = window.innerWidth;
        //        screenHeight = window.innerHeight
        screenWidth = 732;
        screenHeight = 477;
        screenWhalf = screenWidth / 2;
        screenHhalf = screenHeight / 2;

        //renderer每次新建的时候就会自动创建一个canvas dom元素作为它的domElement，也可以直接指定一个已经提前创建好的canvas dom元素
        renderer = new THREE.WebGLRenderer({ antialias: antialias });

        //这行代码设置了一个名为 devicePixelRatio 的变量，其值默认为1。如果浏览器支持 window.devicePixelRatio 属性（通常在高DPI设备上），
        //则使用该属性的值，否则保持为1。这个值用于调整渲染器的尺寸，以适应不同的屏幕像素密度。
        // The devicePixelRatio caused odd alignment and behavior in retina displays
        // that were not "tablets", so I took it out.
        var devicePixelRatio = 1; //window.devicePixelRatio || 1;

        renderer.setSize(screenWidth * devicePixelRatio, screenHeight * devicePixelRatio);
        renderer.domElement.style.width = screenWidth + 'px';
        renderer.domElement.style.height = screenHeight + 'px';

        //autoClear 设置为 false 表示在每次渲染之前不会自动清除渲染器的颜色和深度缓冲区。这在某些情况下可以提高性能，但你需要手动清除缓冲区。
        //sortObjects 设置为 false 表示不对场景中的对象进行排序。这通常可以提高性能，但可能导致渲染顺序不正确，特别是当使用混合模式时。
        //generateMipmaps 设置为 false 表示不生成纹理的Mipmap级别。这可以节省内存和提高性能，但可能降低纹理的渲染质量。
        renderer.autoClear = false;
        renderer.sortObjects = false;
        renderer.generateMipmaps = false;

        //使用 renderer.getMaxAnisotropy 方法获取当前渲染器支持的最大各向异性过滤级别，
        //并将其存储在变量 maxAniso 中。各向异性过滤是一种用于提高远距离物体纹理质量的技
        //术，通过在不同的观察角度使用不同的纹理采样率来实现。

        maxAniso = renderer.getMaxAnisotropy();

        document.getElementById('glContainer').appendChild(renderer.domElement);

        //        -----------------------------------------------------------------------------
        //        Event listeners
        window.addEventListener('mousemove', onDocumentMouseMove, true);
        masterContainer.addEventListener('windowResize', onDocumentResize, true);
        masterContainer.addEventListener('mousedown', onDocumentMouseDown, true);
        window.addEventListener('mouseup', onDocumentMouseUp, false);
        masterContainer.addEventListener('click', onClick, true);
        masterContainer.addEventListener('mousewheel', onMouseWheel, false);
        masterContainer.addEventListener('keydown', onKeyDown, false);

        masterContainer.addEventListener('touchstart', touchStart, false);
        window.addEventListener('touchend', touchEnd, false);
        window.addEventListener('touchmove', touchMove, false);

        //        -----------------------------------------------------------------------------
        //        Setup our camera
        //tmp        camera = new THREE.PerspectiveCamera( 30, window.innerWidth / window.innerHeight, 0.5, 10000000 );
        camera = new THREE.PerspectiveCamera(30, 800 / 450, 0.5, 10000000);
        camera.position.z = 2000;
        camera.rotation.vx = 0;
        camera.rotation.vy = 0;
        camera.position.target = { x: 0, z: 1450, pz: 2000 };

        if (enableSkybox) {
          //tmp                setupSkyboxScene();
        }

        camera.update = function () {
          if (this.__tour) {
            return;
          }

          if (this.easeZooming) return;

          //        cam shake
          //        except it's horrible when zoomed in
          //        let's not use it

          // camera.rotation.vx += (0 - camera.rotation.x) * 0.005 * camera.position.z / 100;
          // camera.rotation.vy += (0 - camera.rotation.y) * 0.005 * camera.position.z / 100;

          // camera.rotation.x += camera.rotation.vx;// + Math.cos( (Date.now() + Math.random()) * 0.004 ) * 0.000015 * camera.position.z / 1000000;
          // camera.rotation.y += camera.rotation.vy;// + Math.sin( (Date.now() + Math.random()) * 0.004 ) * 0.000015 * camera.position.z / 1000000;

          // camera.rotation.vx *= 0.98 * camera.position.z / 1000;
          // camera.rotation.vy *= 0.98 * camera.position.z / 1000;

          // camera.rotation.x *= constrain(camera.position.z / 100, 0, 1);
          // camera.rotation.y *= constrain(camera.position.z / 100, 0, 1);

          camera.position.z += (camera.position.target.z - camera.position.z) * 0.125;
        };

        camera.position.y = 0;
        camera.scale.z = 0.83;

        scene.add(camera);

        //tmp        var windowResize = THREEx.WindowResize(renderer, camera);
        var windowResize = THREEx.WindowResize(renderer, camera);
        if (enableSkybox)
          //tmp                windowResize = THREEx.WindowResize(renderer, cameraCube);
          windowResize = THREEx.WindowResize(renderer, cameraCube);

        //        turn it 90 deg
        rotateY = Math.PI / 2;
        rotateX = Math.PI * 0.05;

        buildGUI();

        sceneSetup();

        initCSS3D();

        // Close Button

        var $exout = $('#ex-out').click(function (e) {
          e.preventDefault();
          $detailContainer.fadeOut();
          $('#css-container').css('display', 'block');
          if ($detailContainer.hasClass('about')) {
            $detailContainer.removeClass('about');
          }
        });

        var $zoomback = $('#zoom-back').click(function (e) {
          e.preventDefault();
          $exout.click();
          zoomOut(750);
        });

        $.get('/images/icons/zoom-out.svg', function (resp) {
          $(resp).find('svg').addClass('icon').appendTo($zoomback);
        });

        setTimeout(function () {
          var s = 'scale(1.0)';

          $('#layout').css({
            webkitTransform: s,
            mozTransform: s,
            msTransform: s,
            oTransform: s,
            transform: s
          });

          //tmp                $('#loader')
          //tmp                        .fadeOut(250);

          $iconNav.fadeIn();
          $iconNav.isReady = true;

          //tmp                if ( firstTime ) {
          //tmp                        displayIntroMessage();
          //tmp                        if( localStorage )
          //tmp                                localStorage.setItem('first', 0);
          //tmp                } else {
          //tmp                        _.delay(function() {
          //tmp                                $iconNav.find('#tour-button').trigger('mouseover');
          //tmp                        }, 500);
          //tmp                }

          if (markers.length > 0) markers[0].select();
        }, 500);

        //tmp        document.getElementById('bgmusicA').addEventListener('ended', function(){
        //tmp                this.currentTime = 0;
        //tmp                this.pause();
        //tmp                var playB = function(){
        //tmp                        document.getElementById('bgmusicB').play();
        //tmp                }
        //tmp                setTimeout( playB, 15000 );
        //tmp        }, false);
        //tmp
        //tmp        document.getElementById('bgmusicB').addEventListener('ended', function(){
        //tmp                this.currentTime = 0;
        //tmp                this.pause();
        //tmp                var playA = function(){
        //tmp                        document.getElementById('bgmusicA').play();
        //tmp                }
        //tmp                setTimeout( playA, 15000 );
        //tmp        }, false);
        //tmp
        //tmp        document.getElementById('bgmusicA').play();
        //tmp
        //tmp        if( localStorage && localStorage.getItem('sound') == 0 ){
        //tmp                // console.log('localstorage sound is off');
        //tmp              // $('#soundoff').show();
        //tmp              // $('#sound').hide();
        //tmp                muteSound();
        //tmp        }
      }

      function sceneSetup() {
        if (enableStarModel) {
          // console.time("make star models");
          starModel = makeStarModels();
          starModel.setSpectralIndex(0.9);
          starModel.setScale(1.0);
          translating.add(starModel);
          // console.timeEnd("make star models");
        }

        if (enableDataStar) {
          pSystem = generateHipparcosStars();
          translating.add(pSystem);
        }

        if (enableGalaxy) {
          pGalacticSystem = generateGalaxy();
          translating.add(pGalacticSystem);
          if (enableDust) {
            pDustSystem = generateDust();
            pGalacticSystem.add(pDustSystem);
          }
        }

        if (enableSolarSystem) {
          var solarSystem = makeSolarSystem();
          translating.add(solarSystem);
        }

        if (enableSpacePlane) {
          spacePlane = createSpacePlane();
          translating.add(spacePlane);
        }

        if (enableSkybox) {
          initSkybox(false);
        }
      }

      function animate() {
        // Make sure the document doesn't scroll
        // 为什么要保证document body不能scroll呢？
        document.body.scrollTop = document.body.scrollLeft = 0;

        camera.update();
        camera.markersVisible =
          camera.position.z < markerThreshold.max && camera.position.z > markerThreshold.min;

        lastRotateY = rotateY;

        // Tween the camera if we're not touring.
        if (!camera.__tour) {
          rotateX += rotateVX;
          rotateY += rotateVY;

          rotateVX *= 0.9;
          rotateVY *= 0.9;

          if (dragging) {
            rotateVX *= 0.6;
            rotateVY *= 0.6;
          }

          if (initialAutoRotate) rotateVY = 0.0015;

          //        treat the solar system a bit differently
          //        since we are at 0,0,0 floating point percision won't be as big of a problem
          var spinCutoff = 100;
          if (translating.position.length() < 0.0001) {
            spinCutoff = 2;
          }

          if (camera.position.z < spinCutoff) {
            if (starModel) {
              starModel.rotation.x = rotateX;
              starModel.rotation.y = rotateY;
            }
            rotating.rotation.x = 0;
            rotating.rotation.y = 0;
          } else {
            rotating.rotation.x = rotateX;
            rotating.rotation.y = rotateY;
            if (starModel) {
              starModel.rotation.x = rotateX;
              starModel.rotation.y = rotateY;
            }
          }

          var isZoomedIn = camera.position.target.z < markerThreshold.min;
          var isZoomedToSolarSystem = camera.position.target.z > markerThreshold.min;

          if (
            isZoomedIn &&
            camera.position.z < markerThreshold.min &&
            $detailContainer.css('display') == 'none' &&
            $starName.css('display') == 'none'
          ) {
            $starName.fadeIn();
          } else if (
            (isZoomedToSolarSystem || $detailContainer.css('display') != 'none') &&
            $starName.css('opacity') == 1.0
          ) {
            $starName.fadeOut();
          }

          // console.log(isZoomedIn);
          // console.log("display=", $cssContainer.css('display'));
          // console.log("height=", $cssContainer.css('height'));
          // console.log("width = ", $cssContainer.css('width'));
          if (isZoomedIn && $cssContainer.css('display') != 'none') {
            $cssContainer.css({ display: 'none' });
          } else if (!isZoomedIn && $cssContainer.css('display') == 'block') {
            //tmp                } else if (!isZoomedIn && $cssContainer.css('display') == 'none') {
            //tmp                        $cssContainer.css({ display: 'block' });
            // console.log("hello==============");
            $cssContainer.css({ display: 'block', height: '477px', width: '732px' });
          }

          if (
            isZoomedToSolarSystem &&
            $detailContainer.css('display') != 'none' &&
            !$detailContainer.hasClass('about')
          ) {
            $detailContainer.fadeOut();
          }

          if ($detailContainer.css('display') == 'none' /* && starModel.scale.length() < 10 */) {
            camera.position.x *= 0.95;
          } else {
            camera.position.x += (camera.position.target.x - camera.position.x) * 0.95;
          }
        }

        var targetFov = constrain(Math.pow(camera.position.z, 2) / 100000, 0.000001, 40);
        camera.fov = targetFov;
        fovValue = (0.5 / Math.tan((camera.fov * Math.PI) / 360)) * screenHeight;
        camera.updateProjectionMatrix();

        shaderTiming = (Date.now() - startTime) / 1000;

        rotateYAccumulate += Math.abs(rotateY - lastRotateY) * 5;

        rotating.traverse(function (mesh) {
          if (mesh.update !== undefined) {
            mesh.update();
          }
        });

        if (enableSkybox) {
          updateSkybox();
        }

        render();

        setCSSWorld();
        setCSSCamera(camera, fovValue);

        updateMarkers();
        //tmp          updateLegacyMarkers();

        requestAnimationFrame(animate);

        //tmp        if ( tour.touring || camera.easeZooming || translating.easePanning ) {
        if (camera.easeZooming || translating.easePanning) {
          updateMinimap();

          TWEEN.update();
        }
      }

      function render() {
        renderer.clear();

        if (enableSkybox) renderSkybox();

        renderer.render(scene, camera);
      }

      //tmp function muteSound(){
      //tmp         document.getElementById('bgmusicA').volume = 0;
      //tmp         document.getElementById('bgmusicB').volume = 0;
      //tmp         if ( localStorage )
      //tmp                 localStorage.setItem('sound', 0);
      //tmp }

      //tmp function unmuteSound(){
      //tmp         document.getElementById('bgmusicA').volume = 1;
      //tmp         document.getElementById('bgmusicB').volume = 1;
      //tmp         if ( localStorage )
      //tmp                 localStorage.setItem('sound', 1);
      //tmp }

      //tmp function displayIntroMessage(){
      //tmp         Tour.meta.fadeIn();
      //tmp         tour.showMessage('Welcome to the stellar neighborhood.', 5000 )
      //tmp         .showMessage('This is a visualization of over 100,000 nearby stars.', 5000 )
      //tmp         .showMessage('Scroll and zoom to explore.', 4000, function(){
      //tmp                 firstTime = false;
      //tmp                 $(window).trigger('resize');
      //tmp                 $iconNav.find('#tour-button').trigger('mouseover');
      //tmp         } )
      //tmp         .endMessages();
      //tmp }
      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      // TODO, 这块的代码需要修改成跟后端连接，现在是用的本地的json文件
      function loadStarData(dataFile, callback) {
        var xhr = new XMLHttpRequest();
        //tmp        setLoadMessage("Fetching stellar data");
        xhr.addEventListener(
          'load',
          function () {
            var parsed = JSON.parse(xhr.responseText);
            // console.log(parsed);
            if (callback) {
              //tmp                        setLoadMessage("Parsing knowledge data");
              callback(parsed);
            }
          },
          false
        );
        xhr.open('GET', dataFile, true);
        xhr.send(null);
      }

      //        points in the sky in HIPPARCOS star cluster
      var datastarTexture0 = new THREE.TextureLoader().load('/images/p_0.png');
      var datastarTexture1 = new THREE.TextureLoader().load('/images/p_2.png');
      var datastarHeatVisionTexture = new THREE.TextureLoader().load('/images/sharppoint.png');

      //        bright flashy named stars graphic
      //tmp var starPreviewTexture = THREE.ImageUtils.loadTexture( 'images/star_preview.png', undefined, setLoadMessage("Focusing optics")        );
      var starPreviewTexture = THREE.ImageUtils.loadTexture(
        '/images/star_preview.png',
        undefined,
        undefined
      );
      //try var starPreviewTexture = new THREE.TextureLoader().load( 'images/star_preview.png', undefined, undefined        );
      var starColorGraph = new THREE.TextureLoader().load('/images/star_color_modified.png');

      var datastarUniforms = {
        color: { type: 'c', value: new THREE.Color(0xffffff) },
        texture0: { type: 't', value: datastarTexture0 },
        texture1: { type: 't', value: datastarTexture1 },
        heatVisionTexture: { type: 't', value: datastarHeatVisionTexture },
        spectralLookup: { type: 't', value: starColorGraph },
        idealDepth: { type: 'f', value: 1.0 },
        blurPower: { type: 'f', value: 1.0 },
        blurDivisor: { type: 'f', value: 2.0 },
        sceneSize: { type: 'f', value: 120.0 },
        cameraDistance: { type: 'f', value: 800.0 },
        zoomSize: { type: 'f', value: 1.0 },
        scale: { type: 'f', value: 1.0 },
        brightnessScale: { type: 'f', value: 1.0 },
        heatVision: { type: 'f', value: 0.0 }
      };

      var datastarAttributes = {
        size: { type: 'f', value: [] },
        customColor: { type: 'c', value: [] },
        colorIndex: { type: 'f', value: [] }
      };

      function generateHipparcosStars() {
        var container = new THREE.Object3D();

        var pGeo = new THREE.Geometry();
        //tmp        var pGeo = new THREE.BufferGeometry();
        var count = starData.length;

        var starPreviews = new THREE.Object3D();
        container.add(starPreviews);

        var starPreviewMaterial = new THREE.MeshBasicMaterial({
          map: starPreviewTexture,
          blending: THREE.AdditiveBlending,
          transparent: true,
          depthTest: false,
          depthWrite: false
        });

        var starPreview = new THREE.Mesh(new THREE.PlaneGeometry(40, 40), starPreviewMaterial);

        var pLineGeo = new THREE.Geometry();
        //tmp        var pLineGeo = new THREE.BufferGeometry();

        for (var i = 0; i < count; i++) {
          var star = starData[i];

          //        original data is in parsecs
          //        we need to convert these into light years
          //        in this case 1.0 GL unit is a light year...

          //        data comes in parsecs
          //        need to convert this to LY
          var distance = star.d * 3.26156;

          //        stars with error data?
          if (distance >= 10000000) {
            // console.log( star );
            star.position = new THREE.Vector3();
            continue;
          }

          //        this sucks but we have to make a special case for the sun
          if (i == 0) {
            //tmp_for_undefined_warning                        lat = 0;
            //tmp_for_undefined_warning                        lon = 0;
            distance = 0;
          }

          var p = new THREE.Vector3(0, 0, 0);

          var ra = star.ra;
          var dec = star.dec;

          //        using this method
          //        http://math.stackexchange.com/questions/52936/plotting-a-stars-position-on-a-2d-map
          var phi = ((ra + 90) * 15 * Math.PI) / 180;
          var theta = (dec * Math.PI) / 180;
          var rho = distance;
          var rvect = rho * Math.cos(theta);
          var x = rvect * Math.cos(phi);
          var y = rvect * Math.sin(phi);
          var z = rho * Math.sin(theta);

          p.set(x, y, z);

          /*
                //        using galactic coordinates
                var latlon = EquatorialToGalactic( ra, dec );
                var lat = latlon.lat;
                var lon = latlon.lon;

                star.lat = lat;
                star.lon = lon;

        var phi = Math.PI/2 - lat;
        var theta = 2 * Math.PI - lon;

        p.x = Math.sin(phi) * Math.cos(theta) * distance;
        p.y = -Math.cos(phi) * distance;
        p.z = Math.sin(phi) * Math.sin(theta) * distance;

        star.position = p;
        */

          //        using astronexus coordinates
          /*
                var x = star.x * 3.26156;
                var y = star.y * 3.26156;
                var z = star.z * 3.26156;
                */

          // console.log( star.position );

          // p.size = 0.16;
          p.size = 20.0;
          p.name = star.name;
          p.spectralIndex = star.c;

          //        what to do with stars that have bad spectral data?
          if (star.c <= -1) star.c = 0;

          p.spectralLookup = map(star.c, -0.3, 1.52, 0, 1);
          // console.log( star.c + " --> " + p.spectralLookup );

          if (i == 0) p.size = 0;

          pGeo.vertices.push(p);

          var r = 1,
            g = 1,
            b = 1;
          var c = new THREE.Color();
          c.r = r;
          c.g = g;
          c.b = b;
          pGeo.colors.push(c);
        }

        var matrix = new THREE.Matrix4();
        var angle = new THREE.Euler(Math.PI / 2, Math.PI, -Math.PI / 2);
        var quat = new THREE.Quaternion();
        quat.setFromEuler(angle);
        matrix.makeRotationFromQuaternion(quat);

        // matrix.scale( new THREE.Vector3(1,-1,1) );
        pGeo.applyMatrix(matrix);

        for (var i in pGeo.vertices) {
          var p = pGeo.vertices[i];
          if (p.name === undefined) continue;
          if (p.name.length > 0) {
            //        make a line from base plane to star
            pLineGeo.vertices.push(p.clone());
            var base = p.clone();
            base.y = 0;
            pLineGeo.vertices.push(base);

            //        create a star sprite highlighting it
            var preview = starPreview.clone();
            var gyroStar = new THREE.Gyroscope();
            //tmp                        var gyroStar = new Gyroscope();
            gyroStar.position.copy(p);
            gyroStar.add(preview);

            //        give it an update based on camera...
            //        这一段代码比较重要，starPreview是材质模板，拷贝到preview里头，然后前面把preview
            //        材质赋给了gyroStar，下面这段是针对这个preview材质添加一些透明度、可见性和scale大小随相机坐标Z轴变换的变换
            preview.update = function () {
              this.material.opacity = constrain(Math.pow(camera.position.z * 0.002, 2), 0, 1);
              if (this.material.opacity < 0.1) this.material.opacity = 0.0;
              if (this.material <= 0.0) this.visibile = false;
              else this.visible = true;
              this.scale.setLength(constrain(Math.pow(camera.position.z * 0.001, 2), 0, 1));
            };

            //        create a self contained gyroscope for the star marker
            var g = new THREE.Gyroscope();
            //tmp                        var g = new Gyroscope();
            container.add(g);

            // starPreview.name = star.name;
            g.name = p.name;
            // 这个地方有点奇怪，为什么不把spectralIndex赋给gyroStar，而是赋给了g，而且为什么要针对同一个named星球创建两个Gyroscope类型的对象
            g.spectralIndex = p.spectralIndex;
            // console.log(g.name);
            g.position.copy(p);
            g.scale.setLength(0.2);
            // attachMarker是用来给这个知识点添加一个名字文本dom元素
            attachMarker(g);

            starPreviews.add(gyroStar);

            console.log(p.name);
            console.log(p.x, p.y, p.z);
          }
        }

        var shaderMaterial = new THREE.ShaderMaterial({
          uniforms: datastarUniforms,
          attributes: datastarAttributes,
          vertexShader: shaderList.datastars.vertex,
          fragmentShader: shaderList.datastars.fragment,

          blending: THREE.AdditiveBlending,
          depthTest: false,
          depthWrite: false,
          transparent: true

          // blending:                 THREE.NormalBlending,
          // depthTest:                 true,
          // depthWrite:         true,
          // transparent:        false,
          // sizeAttenuation: false,

          // blending:                 THREE.NormalBlending,
          // depthTest:                 true,
          // depthWrite:         true,
          // transparent:        false,
        });

        container.heatVision = false;
        container.shaderMaterial = shaderMaterial;

        // 这段代码不需要
        //tmp        container.toggleHeatVision = function( desired ){
        //tmp
        //tmp                if( desired !== undefined )
        //tmp                        container.heatVision = !desired;
        //tmp
        //tmp                if( container.heatVision == false ){
        //tmp                        container.shaderMaterial.blending = THREE.NormalBlending;
        //tmp                        container.shaderMaterial.depthTest = true;
        //tmp                        container.shaderMaterial.depthWrite = true;
        //tmp                        container.shaderMaterial.transparent = false;
        //tmp                }
        //tmp                else{
        //tmp                        container.shaderMaterial.blending = THREE.AdditiveBlending;
        //tmp                        container.shaderMaterial.depthTest = false;
        //tmp                        container.shaderMaterial.depthWrite = false;
        //tmp                        container.shaderMaterial.transparent = true;
        //tmp                }
        //tmp
        //tmp                container.heatVision = !container.heatVision;
        //tmp
        //tmp                if( container.heatVision ){
        //tmp                        // $spectralGraph.css({opacity:1});
        //tmp                        $spectralGraph.addClass('heatvision').fadeIn();
        //tmp                        $iconNav.addClass('heatvision');
        //tmp                }
        //tmp                else{
        //tmp                        // $spectralGraph.css({opacity:0});
        //tmp                        $spectralGraph.removeClass('heatvision').fadeOut();
        //tmp                        $iconNav.removeClass('heatvision');
        //tmp                }
        //tmp        }
        //tmp
        //tmp        window.toggleHeatVision = container.toggleHeatVision;

        var pSystem = new THREE.ParticleSystem(pGeo, shaderMaterial);
        pSystem.dynamic = false;

        //        set the values to the shader
        var values_size = datastarAttributes.size.value;
        var values_color = datastarAttributes.customColor.value;
        var values_spectral = datastarAttributes.colorIndex.value;

        for (var v = 0; v < pGeo.vertices.length; v++) {
          values_size[v] = pGeo.vertices[v].size;
          values_color[v] = pGeo.colors[v];
          values_spectral[v] = pGeo.vertices[v].spectralLookup;
        }

        //        -----------------------------------------------------------------------------
        //        attach lines from star to plane base
        var lineMesh = new THREE.Line(
          pLineGeo,
          new THREE.LineBasicMaterial({
            color: 0x333333,
            blending: THREE.AdditiveBlending,
            depthTest: false,
            depthWrite: false,
            transparent: true
          }),
          THREE.LinePieces
        );
        pSystem.add(lineMesh);

        //        -----------------------------------------------------------------------------
        //        create a ring of degree marks around the plane
        var degCounter = 12;
        var radius = 600;
        for (var i = 0; i < degCounter; i++) {
          var degrees = (i / degCounter) * 360;
          var zerodeg = new THREE.Gyroscope();
          //tmp                var zerodeg = new Gyroscope();
          zerodeg.scale.setLength(0.8);
          //tmp                var angle = i / degCounter * Math.TWO_PI;
          var angle = (i / degCounter) * Math.PI * 2;
          //tmp		console.log("angle = ", angle)
          //tmp		console.log("Math.PI = ", Math.PI)
          var x = Math.cos(angle) * radius;
          var y = Math.sin(angle) * radius;
          zerodeg.position.x = x;
          zerodeg.position.z = -y;
          //tmp		console.log("zerodeg.position=",zerodeg.position)
          zerodeg.name = degrees + '°';
          attachMarker(zerodeg, 1);
          container.add(zerodeg);
        }

        //        -----------------------------------------------------------------------------
        //        create base circles for each named star on the plane
        var starBaseTexture = new THREE.TextureLoader().load('/images/starbase.png');
        var starBaseMaterial = new THREE.MeshBasicMaterial({
          map: starBaseTexture,
          blending: THREE.AdditiveBlending,
          transparent: true,
          depthTest: false,
          depthWrite: false,
          side: THREE.DoubleSide
        });
        var starBaseGeometry = new THREE.PlaneGeometry(10, 10);
        var matrix = new THREE.Matrix4();
        //        seriously?
        var euler = new THREE.Euler(Math.PI / 2, 0, 0);
        var quat = new THREE.Quaternion();
        quat.setFromEuler(euler);
        matrix.makeRotationFromQuaternion(quat);

        starBaseGeometry.applyMatrix(matrix);
        var baseGeometryCombined = new THREE.Geometry();
        //tmp        var baseGeometryCombined = new THREE.BufferGeometry();
        var circles = new THREE.Object3D();
        for (var i in pGeo.vertices) {
          var p = pGeo.vertices[i];
          if (p.name !== undefined && p.name.length > 0) {
            var geo = starBaseGeometry.clone();
            var geoMatrix = new THREE.Matrix4();
            geoMatrix.setPosition(p.x, 0, p.z);
            geo.applyMatrix(geoMatrix);
            THREE.GeometryUtils.merge(baseGeometryCombined, geo);
          }
        }
        var starBases = new THREE.Mesh(baseGeometryCombined, starBaseMaterial);
        starBases.update = function () {
          this.material.opacity = constrain((camera.position.z - 400.0) * 0.002, 0, 1);
          if (this.material.opacity <= 0) this.visible = false;
          else this.visible = true;
        };
        pSystem.add(starBases);

        //        -----------------------------------------------------------------------------
        //        add everything to the container
        container.add(pSystem);

        //        -----------------------------------------------------------------------------
        //        give it an update function to handle transitions
        container.update = function () {
          var blueshift = (camera.position.z + 5000.0) / 60000.0;
          blueshift = constrain(blueshift, 0.0, 0.2);

          var brightnessScale = constrain(10 / Math.sqrt(camera.position.z), 0, 1);

          // console.log(blueshift);
          if (container.heatVision) {
            datastarUniforms.cameraDistance.value = 0.0;
            datastarUniforms.brightnessScale.value = 1.0;
            datastarUniforms.heatVision.value += (1.0 - datastarUniforms.heatVision.value) * 0.2;
          } else {
            datastarUniforms.brightnessScale.value = brightnessScale;
            datastarUniforms.heatVision.value += (0.0 - datastarUniforms.heatVision.value) * 0.2;
          }

          if (datastarUniforms.heatVision.value < 0.01) datastarUniforms.heatVision.value = 0.0;

          datastarUniforms.cameraDistance.value = blueshift;
          datastarUniforms.zoomSize.value = constrain(camera.position.z / 4000, 0, 1);

          var areaOfWindow = window.innerWidth * window.innerHeight;
          datastarUniforms.scale.value = Math.sqrt(areaOfWindow) * 1.5;

          if (camera.position.z < 1500) {
            // controllers.datastarSize += (0.8 - controllers.datastarSize) * 0.02;
            // controllers.sceneSize += (10000 - controllers.sceneSize) * 0.06;
          } else {
            // controllers.datastarSize += (3.0 - controllers.datastarSize) * 0.02;
            // controllers.datastarSize += (3.0 - controllers.datastarSize) * 0.02;
            // controllers.sceneSize += (5000.0 - controllers.sceneSize) * 0.06;
          }

          //        some basic LOD
          // if( camera.position.z > 400 && camera.position.z < 40000 )
          //         pSystem.visible = true;
          // else
          //         pSystem.visible = false;

          // datastarUniforms.sceneSize.value = controllers.sceneSize
          // datastarUniforms.scale.value = controllers.datastarSize;

          datastarUniforms.sceneSize.value = 10000;
        };

        lineMesh.update = function () {
          if (camera.position.z < 1500) {
            this.material.opacity = constrain((camera.position.z - 400.0) * 0.002, 0, 1);
          } else {
            this.material.opacity += (0.0 - this.material.opacity) * 0.1;
          }

          //        some basic LOD
          if (camera.position.z < 250) this.visible = false;
          else this.visible = true;
        };

        return container;
      }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      //tmp (function() {

      // reference to window
      var root = window;

      // globals taken from scss
      var border_width = 1;
      var padding = 30;

      // Loading variables
      var ready = false;
      var count = 0;
      var timer = null;
      var dragged = false;

      // SVG loadables
      var $soundOn, $soundOff, $heatvision, $tour, $home;

      // jQuery elements
      var $domElement = $('#minimap');
      var $minimap = $domElement.find('#zoom-levels');
      //tmp  var $volume = $domElement.find('#volume').load(updateCount);
      //tmp  var $about = $domElement.find('#about').load(updateCount);
      var $volume = $domElement.find('#volume').ready(updateCount);
      //tmp  var $about = $domElement.find('#about').ready(updateCount);
      // var $tour = $domElement.find('#tour').load(updateCount);
      // var $heatvision = $domElement.find('#heatvision').load(updateCount);
      // var $sound = $domElement.find('#sound').load(updateCount);
      // var $soundoff = $domElement.find('#soundoff').load(updateCount);
      // var $backdrop = $domElement.find("#zoom-backdrop");
      var $cursor = $domElement.find('#zoom-cursor');

      // Calculation variables
      var POWER = 3;
      var position = 0; // The default position of the cursor, as a pct (%).
      var curve = function (t) {
        return Math.pow(t, 1 / POWER);
      };
      var curve_inverse = function (t) {
        return Math.pow(t, POWER);
      };

      var $window = $(window);

      function clickEvent(e) {
        // console.log('touched outside');
        var id = $(e.target).attr('id');

        if (dragged || (id !== 'css-world' && id !== 'css-camera' && id !== 'glContainer')) {
          dragged = false;
          return;
        }

        unfocus();
      }

      function touchEvent(e) {
        var event = e.originalEvent;
        var id = $(event.target).attr('id');

        if (dragged || (id !== 'css-world' && id !== 'css-camera' && id !== 'glContainer')) {
          dragged = false;
          return;
        }

        unfocus();
      }

      $window.click(clickEvent);

      $window
        .resize(function () {
          if (!ready) {
            // If not all images are loaded then we need to halt this procedure
            // and time it out.
            if (timer) {
              updateCount();
              clearTimeout(timer);
            }
            timer = setTimeout(function () {
              if (firstTime == false) $window.trigger('resize');
            }, 500);

            return;
          }

          //tmp      var offset = $volume.outerHeight() + $about.outerHeight() + padding;
          var offset = $volume.outerHeight() + padding;
          var h = $domElement.height() - offset;

          $minimap.height(h - border_width * 2);

          // // Now that we're ready fade in the entire minimap.
          if (!$domElement.hasClass('ready')) {
            $domElement.addClass('ready');
          }
        })
        .bind('mouseup', onWindowMouseUp)
        .bind('touchend', onWindowMouseUp)
        .trigger('resize');

      // Exports

      // Create an initializer

      //tmp  var initializeMinimap = root.initializeMinimap = function() {
      function initializeMinimap() {
        //tmp    this.updateMinimap();
        updateMinimap();
      }

      // A means to update the minimap

      //tmp  var updateMinimap = root.updateMinimap = function() {
      function updateMinimap() {
        //tmp    if (!root.camera) {
        //tmp    if (!camera) {
        //tmp      return;
        //tmp    }

        //tmp    var normal = cmap(root.camera.position.target.z, 1.1, 40000, 0, 1);
        var normal = cmap(camera.position.target.z, 400, 10000, 0, 1);
        position = cmap(curve(normal), 0, 1, 0, 100);
        updateCursorPosition(true);
      }

      var setScrollPosition = (root.setScrollPositionFromTouch = function (touch) {
        var y = touch.pageY - $minimap.offset().top;
        position = cmap(y, 0, $minimap.height(), 0, 100);
        updateCursorPosition();
      });

      var setMinimap = (root.setMinimap = function (b) {
        dragged = !!b;
      });

      var showSunButton = (root.showSunButton = function () {
        // $home.css({
        //   height: 25 + 'px'
        // });
        // setTimeout(function() {
        //   $window.trigger('resize');
        // }, 250);

        if ($home) {
          // console.log("show home button");
          // $home.fadeIn();
          $home.css({ opacity: 1.0, display: 'inline' });
        }
      });

      var hideSunButton = (root.hideSunButton = function () {
        // $home.fadeOut();
        // $home.css({
        //   height: 0
        // });
        // setTimeout(function() {
        //   $window.trigger('resize');
        // }, 250);
        if ($home) {
          $home.fadeOut();
        }
      });

      /**
       * Setup the dragging functionality for the minimap
       */

      $minimap.bind('mousedown', onElementMouseDown);

      $minimap.bind('touchstart', onElementTouchStart);

      // $home
      //   .css({
      //     height: 0,
      //     overflow: 'hidden'
      //   })
      //   // .tip('Center camera on the sun.')
      //   .click(function(e) {
      //     // markers[0].$.trigger('click');
      //     unfocus(true);
      //   });

      //tmp  $about
      //tmp    .click(function(e) {
      //tmp
      //tmp      var line_height = 20;
      //tmp      e.preventDefault();
      //tmp
      //tmp      $detailContainer.addClass('about');
      //tmp
      //tmp      $('#css-container').css('display', 'none');
      //tmp
      //tmp      $.get('detail/about.html', function(data) {
      //tmp        $('#detailBody').html(data);
      //tmp      });
      //tmp
      //tmp      $('#detailTitle').find('span').html('100,000 Stars');
      //tmp
      //tmp      $detailContainer.css({
      //tmp        paddingTop: line_height * 3 + 'px'
      //tmp      });
      //tmp
      //tmp      $detailContainer.fadeIn();
      //tmp
      //tmp    });
      //tmp
      //tmp  var muted = localStorage.getItem('sound') === '0';
      //tmp
      //tmp  $.get('./images/icons/sound-on.svg', function(resp) {
      //tmp    $soundOn = $(resp).find('svg').addClass('icon')
      //tmp      .css({
      //tmp        display: muted ? 'none' : 'block'
      //tmp      })
      //tmp      .click(function(e) {
      //tmp        e.preventDefault();
      //tmp        $soundOn.css({ display: 'none' });
      //tmp        muteSound();
      //tmp        if ($soundOff) {
      //tmp          $soundOff.css({ display: 'inline-block' });
      //tmp        }
      //tmp      });
      //tmp    $volume.append($soundOn);
      //tmp  });
      //tmp
      //tmp  $.get('./images/icons/sound-off.svg', function(resp) {
      //tmp    $soundOff = $(resp).find('svg').addClass('icon')
      //tmp      .css({
      //tmp        display: !muted ? 'none' : 'block'
      //tmp      })
      //tmp      .click(function(e) {
      //tmp        e.preventDefault();
      //tmp        $soundOff.css({ display: 'none' });
      //tmp        unmuteSound();
      //tmp        if ($soundOn) {
      //tmp          $soundOn.css({ display: 'inline-block' });
      //tmp        }
      //tmp      });
      //tmp      $volume.append($soundOff);
      //tmp  });
      //tmp
      //tmp  $.get('./images/icons/big-tour.svg', function(resp) {
      //tmp
      //tmp    $tour = $(resp).find('svg').addClass('icon')
      //tmp      .attr('id', 'tour-button')
      //tmp      .tip('Take a tour.')
      //tmp      .click(function(e) {
      //tmp        e.preventDefault();
      //tmp        tour.start();
      //tmp      });
      //tmp    $iconNav.append($tour);
      //tmp
      //tmp    $.get('./images/icons/heat-vision.svg', function(resp) {
      //tmp
      //tmp      $heatvision = $(resp).find('svg').addClass('icon')
      //tmp        .click(function(e) {
      //tmp          e.preventDefault();
      //tmp          toggleHeatVision();
      //tmp        })
      //tmp        .hover(function(e) {
      //tmp          $tour.trigger('mouseleave', [true]);
      //tmp        }, function(e) {
      //tmp          $tour.trigger('mouseenter');
      //tmp        })
      //tmp        .tip('Toggle Spectral Index.');
      //tmp      $iconNav.append($heatvision);
      //tmp
      //tmp      $.get('./images/icons/center-sun.svg', function(resp) {
      //tmp        $home = $(resp).find('svg').addClass('icon')
      //tmp          .tip('Center camera position to the Sun.')
      //tmp          .hover(function(e) {
      //tmp            $tour.trigger('mouseleave', [true]);
      //tmp          }, function(e) {
      //tmp            $tour.trigger('mouseenter');
      //tmp          })
      //tmp          .click(function(e) {
      //tmp            e.preventDefault();
      //tmp            unfocus(true);
      //tmp          })
      //tmp          .css({
      //tmp            display: 'none'
      //tmp          });
      //tmp        $iconNav.append($home);
      //tmp      });
      //tmp
      //tmp    });
      //tmp
      //tmp  });

      function onElementMouseDown(e) {
        var y = e.pageY - $minimap.offset().top;
        position = cmap(y, 0, $minimap.height(), 0, 100);

        updateCursorPosition();

        $window.bind('mousemove', drag);
      }

      function onElementTouchStart(e) {
        var event = e.originalEvent;
        var touch = event.touches[0];

        var y = touch.pageY - $minimap.offset().top;
        position = cmap(y, 0, $minimap.height(), 0, 100);

        updateCursorPosition();
        root.scrollbaring = true;
        // $window
        //   .bind('touchmove', dragTouch);
      }

      function drag(e) {
        var y = e.pageY - $minimap.offset().top;
        position = cmap(y, 0, $minimap.height(), 0, 100);

        updateCursorPosition();
      }

      function dragTouch(e) {
        var event = e.originalEvent;
        if (event.touches.length != 1) return;

        // event.preventDefault();
        // event.stopImmediatePropagation();
        // Make sure the document doesn't scroll
        // document.body.scrollTop = document.body.scrollLeft = 0;
      }

      function onWindowMouseUp(e) {
        // console.log('minimap end');
        $window.unbind('mousemove', drag);
        $window.unbind('touchmove', dragTouch);
      }

      function updateCursorPosition(silent) {
        $cursor.css({
          top: position + '%'
        });
        if (!silent) {
          updateCameraPosition();
        }
      }

      function updateCameraPosition() {
        //tmp    if (root.camera) {
        //tmp      var normal = position / 100;
        //tmp      root.camera.position.target.z = cmap(curve_inverse(normal), 0, 1, 1.1, 40000);
        //tmp      root.camera.position.target.pz = root.camera.position.target.z;
        //tmp    }

        if (camera) {
          var normal = position / 100;
          //tmp_pos      camera.position.target.z = cmap(curve_inverse(normal), 0, 1, 1.1, 40000);
          camera.position.target.z = cmap(curve_inverse(normal), 0, 1, 410, 5000);
          camera.position.target.pz = camera.position.target.z;
          console.log(camera.position.target.z);
        }
      }
      function map(v, i1, i2, o1, o2) {
        return o1 + (o2 - o1) * ((v - i1) / (i2 - i1));
      }

      function cmap(v, i1, i2, o1, o2) {
        return Math.max(Math.min(map(v, i1, i2, o1, o2), o2), o1);
      }

      function unfocus(home) {
        $('#detailContainer').fadeOut();
        $('#css-container').css('display', 'block');
        if (!!home) {
          centerOnSun();
          setTimeout(hideSunButton, 500);
          zoomOut(555);
        } else {
          // zoomOut();
        }
      }

      function updateCount() {
        ready = true;
        //tmp    if (count < 3) {
        //tmp      count++;
        //tmp    } else if (!ready) {
        //tmp      ready = true;
        //tmp    }
      }

      //tmp })();

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      var markers = [];
      var markerThreshold = {
        min: 400,
        max: 1500
      };

      //	called by animate per frame
      function updateMarkers() {
        for (var i in markers) {
          var marker = markers[i];
          marker.update();
        }
      }

      function attachMarker(obj, size) {
        var padding = 3;
        var line_height = 20;
        var title, extraData;
        var container = document.getElementById('css-camera');
        var template = document.getElementById('marker_template');
        var marker = template.cloneNode(true);
        marker.$ = $(marker); // jQuery reference

        //	strip out the apostrophe in names first before looking them up
        //	the same is done in the details data file already, as well as the star system lookup
        obj.name = obj.name.replace("'", '');

        marker.obj = obj;
        marker.absPosition = obj.position;

        //tmp	console.log(obj);

        marker.size = size !== undefined ? size : 1.0;
        marker.id = obj.name;
        //tmp	console.log(marker.id);
        marker.style.fontSize = 24 + 'px';

        marker.spectralIndex = obj.spectralIndex;

        setDivPosition(marker, obj);

        var nameLayer = marker.children[0];

        //	get the formal name of the star
        //tmp	var system = starSystems[obj.name];
        //tmp
        //tmp	if( system !== undefined ){
        //tmp		var systemName = system.name;
        //tmp		nameLayer.innerHTML = systemName;
        //tmp	}
        //tmp	else{
        nameLayer.innerHTML = obj.name;
        //tmp	}

        //	because these stars appear in the db as named stars and they are so closely packed together
        //	we need to force them to be smaller font
        //	totally sucks :|
        if (
          obj.name === 'Proxima Centauri' ||
          obj.name === 'Rigel Kentaurus A' ||
          obj.name === 'Rigel Kentaurus B'
        )
          marker.style.fontSize = 10 + 'px';

        //	let's not even do Alpha Centauri B here because it just leads to the same description
        if (obj.name === 'Rigel Kentaurus B') return;

        if (obj.name === 'Rigel Kentaurus A') nameLayer.innerHTML = 'Alpha Centauri';

        marker.defaultSize = marker.style.fontSize;

        var name = marker.id.toLowerCase();
        title = nameLayer.innerHTML;

        // console.log(name);
        var fileName = name.replace(/ /g, '_');
        // fileName = fileName.replace(/\'/g, "%27");
        var pathToDetail = encodeURI('detail/' + fileName + '.html');

        // console.log(pathToDetail);

        var obj_name = obj.name.match('°');

        if (obj_name && obj_name[0] == '°') {
          marker.$.addClass('label');
        } else {
          var extraData = function () {
            $.get(pathToDetail, function (data) {
              var $body = $('#detailBody').html(data);

              $body.find('a').each(function () {
                var $this = $(this);
                ahref = $this.attr('href');
                var finalLink = 'http://en.wikipedia.org' + ahref;
                $this.attr('href', finalLink);
                $this.attr('target', 'blank');
              });

              var $title = $('#detailTitle');
              $title.find('span').html(title);
              var $foot = $('#detailFooter');

              $('#detailContainer').fadeIn();
              $('#css-container').css('display', 'none');
              setTimeout(function () {
                var offset = $title.outerHeight() + $body.outerHeight() + $foot.outerHeight();
                $('#detailContainer').css({
                  paddingTop: Math.max(($(window).height() - offset) / 2, line_height * 3) + 'px'
                });
              }, 0);
            });
          };

          //鼠标移动到各个知识点标签上时的字体的变化
          marker.$.hover(
            function (e) {
              var ideal = 20;
              var posAvgRange = 200;
              marker.style.fontSize = 10 + ideal * (camera.position.z / posAvgRange) + 'px';
            },
            function (e) {
              marker.style.fontSize = marker.defaultSize;
            }
          );

          var markerClick = function (e) {
            // $iconNav.css({
            // 	display: 'none'
            // });

            var vec = marker.absPosition.clone();

            if (vec.length() !== 0) {
              console.log('show sun button');
              window.showSunButton();
            } else {
              window.hideSunButton();
            }

            window.setMinimap(true);

            $starName.find('span').html(title);
            $starName[0].onclick = extraData;

            extraData();

            //tmp			var isStarSystem = (system !== undefined);
            //tmp
            //tmp			if( isStarSystem ){
            //tmp				//	this also sets the scale, and thus the star radius
            //tmp				setStarModel( vec, marker.id );
            //tmp				var modelScale = starModel.scale.length();
            //tmp
            //tmp				// we use the radius to determine how much we're going to zoom and offset
            //tmp				var zoomByStarRadius = getZoomByStarRadius( modelScale );
            //tmp				zoomIn( zoomByStarRadius );
            //tmp
            //tmp				var offset = getOffsetByStarRadius( modelScale );
            //tmp				vec.add( offset );
            //tmp			}

            //	set the star
            centerOn(vec);
          };

          var markerTouch = function (e) {
            if (e.originalEvent.touches.length > 1) return;
            markerClick(e);
          };

          marker.$.bind('click', markerClick);
          marker.$.bind('touchstart', markerTouch);
        }

        container.appendChild(marker);

        marker.setVisible = function (vis) {
          if (vis) {
            this.style.opacity = 1.0;
          } else {
            this.style.opacity = 0.0;
          }
          // var isVisible = this.$.css('display') == 'none';
          // if ( !vis && isVisible ){
          // 	this.$.fadeOut();
          // } else if ( vis && !isVisible ) {
          // 	this.$.fadeIn();
          // }
          return this;
        };

        marker.select = function () {
          var vec = marker.absPosition.clone();

          if (enableStarModel == false) return;

          setStarModel(vec, marker.id);
          var modelScale = starModel.scale.length();

          // we use the radius to determine how much we're going to zoom and offset
          var zoomByStarRadius = getZoomByStarRadius(modelScale);
          // zoomIn( zoomByStarRadius );

          var title = nameLayer.innerHTML;
          var offset = getOffsetByStarRadius(modelScale);

          $starName.find('span').html(title);

          $starName[0].onclick = extraData;

          vec.add(offset);
          snapTo(vec);
        };

        marker.setSize = function (s) {
          this.style.fontSize = s + 'px';
          this.style.lineHeight = s + 'px';
          this.style.marginTop = -(s + padding) / 2 + 'px';
        };

        var countryLayer = marker.querySelector('#startText');
        marker.countryLayer = countryLayer;

        marker.update = function () {
          var s = (0.05 + camera.position.z / 2000) * this.size;
          s = constrain(s, 0, 1);

          setDivPosition(this, this.obj, s);
          this.setVisible(camera.markersVisible);
        };

        // nameLayer.innerHTML = "TESTING TESTING TESTING";

        markers.push(marker);
      }
      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

      var mouseX = 0,
        mouseY = 0,
        pmouseX = 0,
        pmouseY = 0;
      var pressX = 0,
        pressY = 0;

      var dragging = false;
      var histogramPressed = false;
      var scrollbaring = false;

      var rotateX = 0,
        rotateY = 0;
      var rotateVX = 0,
        rotateVY = 0;
      var rotateXMax = (90 * Math.PI) / 180;

      var rotateTargetX = undefined;
      var rotateTargetY = undefined;

      //tmp var keyboard = new THREEx.KeyboardState();
      //tmp var keyboard = new THREEx_KeyboardState();
      console.log('hello1==');
      var keyboard = THREEx.KeyboardState();
      console.log('hello2==');

      var TOUCHMODES = {
        NONE: 0,
        SINGLE: 1,
        DOUBLE: 2
      };
      var touchMode = TOUCHMODES.NONE;
      var previousTouchDelta = 0;
      var touchDelta = 0;

      function onDocumentMouseMove(event) {
        if (touchMode != TOUCHMODES.NONE) {
          event.preventDefault();
          return;
        }

        pmouseX = mouseX;
        pmouseY = mouseY;

        mouseX = event.clientX - window.innerWidth * 0.5;
        mouseY = event.clientY - window.innerHeight * 0.5;

        if (dragging) {
          doCameraRotationFromInteraction();
          window.setMinimap(dragging);
        }
      }

      function onDocumentMouseDown(event) {
        dragging = true;
        pressX = mouseX;
        pressY = mouseY;
        rotateTargetX = undefined;
        rotateTargetX = undefined;

        if (initialAutoRotate) {
          initialAutoRotate = false;
        }
      }

      function onDocumentMouseUp(event) {
        dragging = false;
        histogramPressed = false;
        // window.setMinimap(dragging);
      }

      function onClick(event) {
        //	make the rest not work if the event was actually a drag style click
        if (Math.abs(pressX - mouseX) > 3 || Math.abs(pressY - mouseY) > 3) return;
      }

      function onKeyDown(event) {}

      function handleMWheel(delta) {
        // camera.scale.z += delta * 0.1;
        camera.position.target.z += delta * camera.position.target.z * 0.01;
        //tmp_pos	camera.position.target.z = constrain( camera.position.target.z, 0.8, 80000 );
        camera.position.target.z = constrain(camera.position.target.z, 405, 5000);
        camera.position.target.pz = camera.position.target.z;
        // console.log( camera.position.z );

        camera.rotation.vx += ((-0.0001 + Math.random() * 0.0002) * camera.position.z) / 1000;
        camera.rotation.vy += ((-0.0001 + Math.random() * 0.0002) * camera.position.z) / 1000;

        if (window.updateMinimap) {
          window.updateMinimap();
        }

        if (initialAutoRotate) {
          initialAutoRotate = false;
        }
      }

      function onMouseWheel(event) {
        var delta = 0;

        if (event.wheelDelta) {
          /* IE/Opera. */
          delta = event.wheelDelta / 120;
        }
        //	firefox
        else if (event.detail) {
          delta = -event.detail / 3;
        }

        if (delta) handleMWheel(delta);

        event.returnValue = false;
      }

      function onDocumentResize(e) {}

      function determineTouchMode(event) {
        if (event.touches.length <= 0 || event.touches.length > 2) {
          touchMode = TOUCHMODES.NONE;
          return;
        }

        if (event.touches.length == 1) {
          touchMode = TOUCHMODES.SINGLE;
          return;
        }

        if (event.touches.length == 2) {
          touchMode = TOUCHMODES.DOUBLE;
          return;
        }
      }

      function equalizeTouchTracking(event) {
        if (event.touches.length == 2) {
          var touchA = event.touches[0];
          var touchB = event.touches[1];
          touchDelta = calculateTouchDistance(touchA, touchB);
          previousTouchDelta = touchDelta;
        }

        if (event.touches.length < 1) return;

        var touch = event.touches[0];
        pmouseX = mouseX = touch.pageX - window.innerWidth * 0.5;
        pmouseX = mouseY = touch.pageY - window.innerHeight * 0.5;
      }

      function touchStart(event) {
        onDocumentMouseDown(event);
        // console.log('touchstart');

        determineTouchMode(event);
        equalizeTouchTracking(event);
        event.preventDefault();
      }

      function touchEnd(event) {
        scrollbaring = false;

        onDocumentMouseUp(event);
        // console.log('touchend');
        determineTouchMode(event);
        equalizeTouchTracking(event);
        // event.preventDefault();
      }

      function touchMove(event) {
        if (scrollbaring) {
          var touch = event.touches[0];
          setScrollPositionFromTouch(touch);
          event.preventDefault();
          return;
        }

        determineTouchMode(event);

        //	single touch
        if (touchMode == TOUCHMODES.SINGLE) {
          pmouseX = mouseX;
          pmouseY = mouseY;

          // console.log('swiping');
          // console.log('touches: ' + event.touches.length );
          var touch = event.touches[0];

          mouseX = touch.pageX - window.innerWidth * 0.5;
          mouseY = touch.pageY - window.innerHeight * 0.5;

          // console.log( mouseX, pmouseX );
          // console.log(mouseX - pmouseX);

          if (dragging) {
            doCameraRotationFromInteraction();
            window.setMinimap(dragging);
          }
        } else if (touchMode == TOUCHMODES.DOUBLE) {
          // console.log('pinching');
          var touchA = event.touches[0];
          var touchB = event.touches[1];

          previousTouchDelta = touchDelta;
          touchDelta = calculateTouchDistance(touchA, touchB);

          var pinchAmount = touchDelta - previousTouchDelta;
          // console.log('pinch amount ' + pinchAmount );
          handleMWheel(-pinchAmount * 0.25);
        }

        // event.stopImmediatePropagation();
        // event.preventDefault();
      }

      function calculateTouchDistance(touchA, touchB) {
        var taX = touchA.pageX;
        var taY = touchA.pageY;
        var tbX = touchB.pageX;
        var tbY = touchB.pageY;
        var dist = Math.sqrt(Math.pow(tbX - taX, 2) + Math.pow(tbY - taY, 2));
        return dist;
      }

      function doCameraRotationFromInteraction() {
        rotateVY += ((((mouseX - pmouseX) / 2) * Math.PI) / 180) * 0.2;
        rotateVX += ((((mouseY - pmouseY) / 2) * Math.PI) / 180) * 0.2;

        camera.rotation.vy += ((mouseX - pmouseX) * 0.00005 * camera.position.z) / 10000;
        camera.rotation.vx += ((mouseY - pmouseY) * 0.00005 * camera.position.z) / 10000;
      }
      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      function toTHREEColor(colorString) {
        return new THREE.Color(parseInt(colorString.substr(1), 16));
      }

      var monthNames = new Array(12);
      monthNames[0] = 'January';
      monthNames[1] = 'February';
      monthNames[2] = 'March';
      monthNames[3] = 'April';
      monthNames[4] = 'May';
      monthNames[5] = 'June';
      monthNames[6] = 'July';
      monthNames[7] = 'August';
      monthNames[8] = 'September';
      monthNames[9] = 'October';
      monthNames[10] = 'November';
      monthNames[11] = 'December';

      function toMonthName(monthNumber) {
        return monthNames[monthNumber];
      }

      function componentToHex(c) {
        var hex = c.toString(16);
        return hex.length == 1 ? '0' + hex : hex;
      }

      function rgbToHex(r, g, b) {
        return '#' + componentToHex(r) + componentToHex(g) + componentToHex(b);
      }

      function gup(name) {
        name = name.replace(/[\[]/, '\\\[').replace(/[\]]/, '\\\]');
        var regexS = '[\\?&]' + name + '=([^&#]*)';
        var regex = new RegExp(regexS);
        var results = regex.exec(window.location.href);
        if (results == null) return '';
        else return results[1];
      }

      //tmp function wrap(value, min, rangeSize) {
      //tmp 	rangeSize-=min;
      //tmp     while (value < min) {
      //tmp     	value += rangeSize;
      //tmp 	}
      //tmp 	return value % rangeSize;
      //tmp }

      THREE.Curve.Utils.createLineGeometry = function (points) {
        var geometry = new THREE.Geometry();
        for (var i = 0; i < points.length; i++) {
          geometry.vertices.push(points[i]);
        }
        return geometry;
      };

      function getAbsOrigin(object3D) {
        var mat = object3D.matrixWorld;
        var worldpos = new THREE.Vector3();
        worldpos.x = mat.n14;
        worldpos.y = mat.n24;
        worldpos.z = mat.n34;
        return worldpos;
      }

      var projector = new THREE.Projector();
      function screenXY(object) {
        var vector = projector.projectVector(
          new THREE.Vector3().getPositionFromMatrix(object.matrixWorld),
          camera
        );
        var result = new Object();
        var windowWidth = window.innerWidth;
        var minWidth = 1280;
        if (windowWidth < minWidth) {
          windowWidth = minWidth;
        }
        result.x = Math.round(vector.x * (windowWidth / 2)) + windowWidth / 2;
        result.y = Math.round((0 - vector.y) * (window.innerHeight / 2)) + window.innerHeight / 2;
        return result;
      }

      function buildHexColumnGeo(rad, height) {
        var points = [];
        var ang = 0;
        var sixth = (2 * Math.PI) / 6;
        for (var i = 0; i < 7; i++) {
          var x = Math.cos(ang) * rad;
          var y = -Math.sin(ang) * rad;
          points.push(new THREE.Vector2(x, y));
          ang += sixth;
        }
        var shape = new THREE.Shape(points);

        var options = {
          size: 0,
          amount: height,
          steps: 1,
          bevelEnabled: false
        };
        var extrudedGeo = new THREE.ExtrudeGeometry(shape, options);
        return extrudedGeo;
      }

      function map(v, i1, i2, o1, o2) {
        return o1 + ((o2 - o1) * (v - i1)) / (i2 - i1);
      }

      function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }

      function roundNumber(num, dec) {
        var result = Math.round(num * Math.pow(10, dec)) / Math.pow(10, dec);
        return result;
      }

      //tmp function save(data, filename, mime) {
      //tmp
      //tmp     window.webkitRequestFileSystem(window.TEMPORARY, 1024 * 1024, initRecord, errorHandler("Error getting file system"));
      //tmp
      //tmp     function initRecord(fs) {
      //tmp       var create = function() {
      //tmp         fs.root.getFile("data.tar", {create: true}, function(fileEntry) {
      //tmp
      //tmp           // Create a FileWriter object for our FileEntry (log.txt).
      //tmp           fileEntry.createWriter(function(fileWriter) {
      //tmp
      //tmp
      //tmp             var bb = new window.WebKitBlobBuilder();
      //tmp
      //tmp             data = dataURItoBlob(data);
      //tmp             var header = createHeader(filename, data.byteLength, mime);
      //tmp             bb.append(header);
      //tmp             bb.append(data);
      //tmp
      //tmp //
      //tmp             fileWriter.write(bb.getBlob('tar/archive'));
      //tmp             window.open(fileEntry.toURL(), "_blank", "width=400,height=10");
      //tmp
      //tmp
      //tmp           }, errorHandler("Error creating writer"));
      //tmp
      //tmp         }, errorHandler("Error getting file"));
      //tmp       };
      //tmp       // delete any previous
      //tmp       fs.root.getFile("data.tar", {create: false}, function(fileEntry) {
      //tmp         fileEntry.remove(create, errorHandler("Error deleting file"));
      //tmp       }, create);
      //tmp     }
      //tmp
      //tmp   function dumpString(value, ia, off, size) {
      //tmp     var i,x;
      //tmp     var sum = 0;
      //tmp     var len = Math.min(value.length, size);
      //tmp     for (i = 0; i < len; i++) {
      //tmp       x = value.charCodeAt(i);
      //tmp       ia[off] = x;
      //tmp       sum += x;
      //tmp       off += 1;
      //tmp     }
      //tmp     return sum;
      //tmp   }
      //tmp
      //tmp   function padLeft(value, size) {
      //tmp     if (size < value.length) {
      //tmp       throw new Error("Incompatible size");
      //tmp     }
      //tmp     var l = size-value.length;
      //tmp     for (var i = 0; i < l; i++) {
      //tmp       value = "0" + value;
      //tmp     }
      //tmp     return value;
      //tmp   }
      //tmp
      //tmp   function createHeader( name, size, type ){
      //tmp     var ab = new ArrayBuffer(512);
      //tmp     var ia = new Uint8Array(ab);
      //tmp     var sum = 0;
      //tmp     sum += dumpString(name, ia, 0, 99);
      //tmp     sum += dumpString(size.toString(8), ia, 124, 12);
      //tmp     sum += dumpString(padLeft("644 \0", 8), ia, 100, 8)
      //tmp       // timestamp
      //tmp       var ts = new Date().getTime();
      //tmp     ts = Math.floor(ts/1000);
      //tmp     sum += dumpString(ts.toString(8), ia, 136, 12);
      //tmp
      //tmp     // extra header info
      //tmp     sum += dumpString("0", ia, 156, 1);
      //tmp     sum += dumpString("ustar ", ia, 257, 6);
      //tmp     sum += dumpString("00", ia, 263, 2);
      //tmp
      //tmp     // assume checksum to be 8 spaces
      //tmp     sum += 8*32;
      //tmp     //checksum 6 digit octal followed by null and space
      //tmp     dumpString(padLeft(sum.toString(8)+"\0 ", 8), ia, 148, 8)
      //tmp       return ab;
      //tmp   }
      //tmp
      //tmp   function dataURItoBlob(byteString) {
      //tmp
      //tmp     // write the bytes of the string to an ArrayBuffer
      //tmp     var padding = 512 - (byteString.length % 512);
      //tmp     var ab = new ArrayBuffer(byteString.length + padding);
      //tmp     var ia = new Uint8Array(ab);
      //tmp     for (var i = 0; i < byteString.length; i++) {
      //tmp       ia[i] = byteString.charCodeAt(i);
      //tmp     }
      //tmp
      //tmp     return ab;
      //tmp   }
      //tmp
      //tmp
      //tmp     function errorHandler(msg) {
      //tmp 		return function(e) {
      //tmp       // console.log(msg, e);
      //tmp 		}
      //tmp     }
      //tmp }

      function wrap(value, min, rangeSize) {
        rangeSize -= min;
        while (value < min) {
          value += rangeSize;
        }
        return value % rangeSize;
      }

      function constrain(v, min, max) {
        if (v < min) v = min;
        else if (v > max) v = max;
        return v;
      }

      function random(low, high) {
        if (low >= high) return low;
        var diff = high - low;
        return Math.random() * diff + low;
      }

      function map(value, istart, istop, ostart, ostop) {
        return ostart + (ostop - ostart) * ((value - istart) / (istop - istart));
      }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      var glowSpanTexture = THREE.ImageUtils.loadTexture('/images/glowspan.png');

      function createSpacePlane() {
        var cylinderMaterial = new THREE.MeshBasicMaterial({
          map: glowSpanTexture,
          blending: THREE.AdditiveBlending,
          transparent: true,
          depthTest: false,
          depthWrite: false,
          wireframe: true,
          opacity: 0
        });
        var cylinderGeo = new THREE.CylinderGeometry(600, 0, 0, 360 / 8 - 1, 100);
        // var cylinderGeo = new THREE.IcosahedronGeometry( 600, 6 );
        var matrix = new THREE.Matrix4();
        matrix.scale(new THREE.Vector3(1, 0, 1));
        cylinderGeo.applyMatrix(matrix);
        var mesh = new THREE.Mesh(cylinderGeo, cylinderMaterial);
        mesh.material.map.wrapS = THREE.RepeatWrapping;
        mesh.material.map.wrapT = THREE.RepeatWrapping;
        mesh.material.map.needsUpdate = true;
        mesh.material.map.onUpdate = function () {
          this.offset.y -= 0.001;
          this.needsUpdate = true;
        };

        var updatePlaneMaterial = function () {
          if (camera.position.z < 1500) {
            this.material.opacity = constrain((camera.position.z - 400.0) * 0.002, 0, 0.5);
            if (this.material.map !== undefined && this.material.opacity <= 0.001) {
              this.material.map.offset.y = 0.0;
              this.material.map.needsUpdate = true;
            }

            if (this.material.opacity <= 0) this.visible = false;
            else this.visible = true;
          } else {
            this.material.opacity += (0.0 - this.material.opacity) * 0.1;
          }

          //	some basic LOD
          if (camera.position.z < 400) this.visible = false;
          else this.visible = true;
        };

        mesh.update = updatePlaneMaterial;
        translating.add(mesh);

        var lines = new THREE.Geometry();
        lines.vertices.push(new THREE.Vector3(0, 0, -600));
        lines.vertices.push(new THREE.Vector3(0, 0, 600));
        lines.vertices.push(new THREE.Vector3(-600, 0, 0));
        lines.vertices.push(new THREE.Vector3(600, 0, 0));
        mesh = new THREE.Line(
          lines,
          new THREE.LineBasicMaterial({
            color: 0x111144,
            blending: THREE.AdditiveBlending,
            transparent: true,
            depthTest: false,
            depthWrite: false,
            wireframe: true,
            linewidth: 2
          }),
          THREE.LinePieces
        );
        mesh.update = updatePlaneMaterial;
        return mesh;
      }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      //tmp var starSystems = {
      //tmp     "268 G. Cet": {
      //tmp         "id": 12082,
      //tmp         "name": "HR 753",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.918,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.79
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.97,
      //tmp                 "offset": 1200,
      //tmp                 "radius": 0.28
      //tmp             },
      //tmp 			{
      //tmp                 "c": 1.6,
      //tmp                 "offset": 24,
      //tmp                 "radius": 0.1
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "33 G. Lib": {
      //tmp         "id": 72958,
      //tmp         "name": "Gliese 570",
      //tmp         "sep": 0.000,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.024,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.77
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.51,
      //tmp                 "offset": 50,
      //tmp                 "radius": 0.65
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.52,
      //tmp                 "offset": 66,
      //tmp                 "radius": 0.6
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "82 G. Eri": {
      //tmp         "id": 15471,
      //tmp         "name": "82 G. Eridani",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.711,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.92
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "96 G. Psc": {
      //tmp         "id": 3759,
      //tmp         "name": "96 G. Piscium",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.89,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.69
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Achernar": {
      //tmp         "id": 7574,
      //tmp         "name": "Achernar",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.158,
      //tmp                 "offset": 0,
      //tmp                 "radius": 7.3
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.158,
      //tmp                 "offset": 40.0,  // need to adjust this from 12.3 since it would be too close in the visualization
      //tmp                 "radius": 1.5
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Acrux": {
      //tmp         "id": 60531,
      //tmp         "name": "Alpha Crucis",
      //tmp         "sep": 430.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.243,
      //tmp                 "offset": 0,
      //tmp                 "radius": 14
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.24,
      //tmp                 "offset": 0,
      //tmp                 "radius": 14
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.24,
      //tmp                 "offset": 0,
      //tmp                 "radius": 14
      //tmp             },
      //tmp         ]
      //tmp     },
      //tmp     "Adhara": {
      //tmp         "id": 33492,
      //tmp         "name": "Epsilon Canis Majoris",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.211,
      //tmp                 "offset": 0,
      //tmp                 "radius": 13.9
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alcyone": {
      //tmp         "id": 17661,
      //tmp         "name": "Alcyone",
      //tmp         "sep": 5.2,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.086,
      //tmp                 "offset": 0,
      //tmp                 "radius": 8.2
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.02,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.2
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.02,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.2
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.3,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.2
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Aldebaran": {
      //tmp         "id": 21368,
      //tmp         "name": "Aldebaran",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.538,
      //tmp                 "offset": 0,
      //tmp                 "radius": 44.2
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alderamin": {
      //tmp         "id": 104862,
      //tmp         "name": "Alpha Cephei",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.257,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.3
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Algenib": {
      //tmp         "id": 1065,
      //tmp         "name": "Gamma Pegasi",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.19,
      //tmp                 "offset": 0,
      //tmp                 "radius": 4.8
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Algieba": {
      //tmp         "id": 50440,
      //tmp         "name": "Gamma Leonis",
      //tmp         "sep": 170.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.128,
      //tmp                 "offset": 0,
      //tmp                 "radius": 31.8
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.7,
      //tmp                 "offset": 0,
      //tmp                 "radius": 10.0
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Algol": {
      //tmp         "id": 14540,
      //tmp         "name": "Algol",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.003,
      //tmp                 "offset": 0,
      //tmp                 "radius": 4.13
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.0,
      //tmp                 "offset": 50.0, // adjusted these values by scaling 1000
      //tmp                 "radius": 3.0
      //tmp             },
      //tmp 			{
      //tmp                 "c": 0.01,
      //tmp                 "offset": 2500.0, // adjusted these values by scaling 1000
      //tmp                 "radius": 0.9
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alhena": {
      //tmp         "id": 31601,
      //tmp         "name": "Gamma Geminorum",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.001,
      //tmp                 "offset": 0,
      //tmp                 "radius": 3.3
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alioth": {
      //tmp         "id": 62758,
      //tmp         "name": "Epsilon Ursae Majoris",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.022,
      //tmp                 "offset": 0,
      //tmp                 "radius": 3.7
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alkaid": {
      //tmp         "id": 67089,
      //tmp         "name": "Eta Ursae Majoris",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.099,
      //tmp                 "offset": 0,
      //tmp                 "radius": 3.4
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alnair": {
      //tmp         "id": 108923,
      //tmp         "name": "Alpha Gruis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.07,
      //tmp                 "offset": 0,
      //tmp                 "radius": 3.4
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alnath": {
      //tmp         "id": 25364,
      //tmp         "name": "Beta Tauri",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.13,
      //tmp                 "offset": 0,
      //tmp                 "radius": 4.2
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alnilam": {
      //tmp         "id": 26246,
      //tmp         "name": "Alnilam",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.184,
      //tmp                 "offset": 0,
      //tmp                 "radius": 24
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alnitak": {
      //tmp         "id": 26662,
      //tmp         "name": "Alnitak",
      //tmp         "sep": 50.0, // complete guess
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.199,
      //tmp                 "offset": 0,
      //tmp                 "radius": 20
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.33,
      //tmp                 "offset": 0,
      //tmp                 "radius": 18 // complete guess
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alphard": {
      //tmp         "id": 46259,
      //tmp         "name": "Alphard",
      //tmp         "sep": 0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.44,
      //tmp                 "offset": 0,
      //tmp                 "radius": 50.5
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alphekka": {
      //tmp         "id": 76036,
      //tmp         "name": "Alpha Coronae Borealis",
      //tmp         "sep": 100.0, // complete guess
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.032,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.89
      //tmp             },
      //tmp 			{
      //tmp                 "c": 0.4,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.9
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Alpheratz": {
      //tmp         "id": 676,
      //tmp         "name": "Alpha Andromedae",
      //tmp         "sep": 100.0, // complete guess
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.038,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.7
      //tmp             },
      //tmp 			{
      //tmp                 "c": -0.01,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1.65
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Altair": {
      //tmp         "id": 97339,
      //tmp         "name": "Altair",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.221,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1.63
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Ankaa": {
      //tmp         "id": 2076,
      //tmp         "name": "Alpha Phoenicis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.083,
      //tmp                 "offset": 0,
      //tmp                 "radius": 15
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Antares": {
      //tmp         "id": 80520,
      //tmp         "name": "Antares",
      //tmp         "sep": 529.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.865,
      //tmp                 "offset": 0,
      //tmp                 "radius": 883
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.3,
      //tmp                 "offset": 0,
      //tmp                 "radius": 10 // complete guess
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Arcturus": {
      //tmp         "id": 69452,
      //tmp         "name": "Arcturus",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.239,
      //tmp                 "offset": 0,
      //tmp                 "radius": 25.7
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Arneb": {
      //tmp         "id": 25920,
      //tmp         "name": "Alpha Leporis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.211,
      //tmp                 "offset": 0,
      //tmp                 "radius": 129
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Barnards Star": {
      //tmp         "id": 87666,
      //tmp         "name": "Barnard's Star",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.57,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.196
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Bellatrix": {
      //tmp         "id": 25273,
      //tmp         "name": "Bellatrix",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.224,
      //tmp                 "offset": 0,
      //tmp                 "radius": 6
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Betelgeuse": {
      //tmp         "id": 27919,
      //tmp         "name": "Betelgeuse",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.5,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1050
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Canopus": {
      //tmp         "id": 30365,
      //tmp         "name": "Canopus",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.164,
      //tmp                 "offset": 0,
      //tmp                 "radius": 65
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Capella": {
      //tmp         "id": 24549,
      //tmp         "name": "Capella",
      //tmp         "sep": 100.0, // complete guess
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.795,
      //tmp                 "offset": 0,
      //tmp                 "radius": 12.2
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.6,
      //tmp                 "offset": 0,
      //tmp                 "radius": 9.2
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Caph": {
      //tmp         "id": 744,
      //tmp         "name": "Beta Cassiopeiae",
      //tmp         "sep": 50.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.38,
      //tmp                 "offset": 0,
      //tmp                 "radius": 3.5
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.15,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1.5 // complete guess
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Castor": {
      //tmp         "id": 36744,
      //tmp         "name": "Castor",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.034,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.3
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.1,
      //tmp                 "offset": 90, // complete guess
      //tmp                 "radius": 2.0 // complete guess
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.02,
      //tmp                 "offset": 20, // complete guess
      //tmp                 "radius": 1.6
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.2,
      //tmp                 "offset": 50, // complete guess
      //tmp                 "radius": 1.9 // complete guess
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.4,
      //tmp                 "offset": 40, // complete guess
      //tmp                 "radius": 0.76
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.4,
      //tmp                 "offset": 30, // complete guess
      //tmp                 "radius": 0.68 // complete guess
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Deneb": {
      //tmp         "id": 101768,
      //tmp         "name": "Deneb",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.092,
      //tmp                 "offset": 0,
      //tmp                 "radius": 203
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Denebola": {
      //tmp         "id": 57460,
      //tmp         "name": "Denebola",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.09,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1.728
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Diphda": {
      //tmp         "id": 3413,
      //tmp         "name": "Beta Ceti",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.019,
      //tmp                 "offset": 0,
      //tmp                 "radius": 16.78
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Dubhe": {
      //tmp         "id": 53905,
      //tmp         "name": "Alpha Ursae Majoris",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.061,
      //tmp                 "offset": 0,
      //tmp                 "radius": 30
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Enif": {
      //tmp         "id": 106973,
      //tmp         "name": "Epsilon Pegasi",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.52,
      //tmp                 "offset": 0,
      //tmp                 "radius": 185
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Etamin": {
      //tmp         "id": 87562,
      //tmp         "name": "Gamma Draconis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.521,
      //tmp                 "offset": 0,
      //tmp                 "radius": 48.15
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Fomalhaut": {
      //tmp         "id": 113009,
      //tmp         "name": "Fomalhaut",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.145,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1.842
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Groombridge 1618": {
      //tmp         "id": 49767,
      //tmp         "name": "Groombridge 1618",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.326,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.605
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Groombridge 1830": {
      //tmp         "id": 57768,
      //tmp         "name": "Groombridge 1830",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.754,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.681
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Hadar": {
      //tmp         "id": 68484,
      //tmp         "name": "Beta Centauri",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.231,
      //tmp                 "offset": 0,
      //tmp                 "radius": 8
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Hamal": {
      //tmp         "id": 9861,
      //tmp         "name": "Alpha Arietis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.151,
      //tmp                 "offset": 0,
      //tmp                 "radius": 14.9
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Izar": {
      //tmp         "id": 71880,
      //tmp         "name": "Epsilon Boötis",
      //tmp         "sep": 50.0, // complete guess
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.966,
      //tmp                 "offset": 0,
      //tmp                 "radius": 33
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.05,
      //tmp                 "offset": 0,
      //tmp                 "radius": 8 // complete guess
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Kapteyns Star": {
      //tmp         "id": 24129,
      //tmp         "name": "Kapteyn's Star",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.543,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.291
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Kaus Australis": {
      //tmp         "id": 89907,
      //tmp         "name": "Epsilon Sagittarii",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.031,
      //tmp                 "offset": 0,
      //tmp                 "radius": 6.8
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.58,
      //tmp                 "offset": 30, // complete guess
      //tmp                 "radius": 0.9
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Kochab": {
      //tmp         "id": 72381,
      //tmp         "name": "Beta Ursae Minoris",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.465,
      //tmp                 "offset": 0,
      //tmp                 "radius": 42.06
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Kruger 60": {
      //tmp         "id": 110548,
      //tmp         "name": "Kruger 60",
      //tmp         "sep": 9.5,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.613,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.35
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.8,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.24
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Lacaille 8760": {
      //tmp         "id": 104752,
      //tmp         "name": "Lacaille 8760",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.397,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.51
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Lacaille 9352": {
      //tmp         "id": 113688,
      //tmp         "name": "Lacaille 9352",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.483,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.459
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Lalande 21185": {
      //tmp         "id": 53879,
      //tmp         "name": "Lalande 21185",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.502,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.393
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Luytens Star": {
      //tmp         "id": 36107,
      //tmp         "name": "Luyten's Star",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.573,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.35
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Markab": {
      //tmp         "id": 113604,
      //tmp         "name": "Alpha Pegasi",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.002,
      //tmp                 "offset": 0,
      //tmp                 "radius": 4.72
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Menkar": {
      //tmp         "id": 14100,
      //tmp         "name": "Alpha Ceti",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.63,
      //tmp                 "offset": 0,
      //tmp                 "radius": 89
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Merak": {
      //tmp         "id": 53754,
      //tmp         "name": "Beta Ursae Majoris",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.033,
      //tmp                 "offset": 0,
      //tmp                 "radius": 3.021
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Mirach": {
      //tmp         "id": 5436,
      //tmp         "name": "Beta Andromedae",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.576,
      //tmp                 "offset": 0,
      //tmp                 "radius": 100
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Mirphak": {
      //tmp         "id": 15824,
      //tmp         "name": "Alpha Persei",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.481,
      //tmp                 "offset": 0,
      //tmp                 "radius": 31
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     // Lack of detailing info on Mizar
      //tmp     //	Actually a quadruple star system?
      //tmp     "Mizar": {
      //tmp         "id": 65174,
      //tmp         "name": "Mizar",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.057,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Nihal": {
      //tmp         "id": 25542,
      //tmp         "name": "Beta Leporis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.807,
      //tmp                 "offset": 0,
      //tmp                 "radius": 16
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Nunki": {
      //tmp         "id": 92565,
      //tmp         "name": "Sigma Sagittarii",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.134,
      //tmp                 "offset": 0,
      //tmp                 "radius": 4.5
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Phad": {
      //tmp         "id": 57829,
      //tmp         "name": "Gamma Ursae Majoris",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.044,
      //tmp                 "offset": 0,
      //tmp                 "radius": 3.04
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Polaris": {
      //tmp         "id": 11734,
      //tmp         "name": "Polaris",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.636,
      //tmp                 "offset": 0,
      //tmp                 "radius": 46
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.30,
      //tmp                 "offset": 2400,
      //tmp                 "radius": 1
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.25,
      //tmp                 "offset": 18.8,
      //tmp                 "radius": 2
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Pollux": {
      //tmp         "id": 37718,
      //tmp         "name": "Pollux",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.991,
      //tmp                 "offset": 0,
      //tmp                 "radius": 8.8
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Procyon": {
      //tmp         "id": 37173,
      //tmp         "name": "Procyon",
      //tmp         "sep": 15.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.432,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.048
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.65,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1.2 // complete guess
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Proxima Centauri": {
      //tmp         "id": 70667,
      //tmp         "name": "Proxima Centauri",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.807,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.141
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Rasalgethi": {
      //tmp         "id": 84087,
      //tmp         "name": "Alpha Herculis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.164,
      //tmp                 "offset": 0,
      //tmp                 "radius": 387
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Rasalhague": {
      //tmp         "id": 85770,
      //tmp         "name": "Alpha Ophiuchi",
      //tmp         "sep": 20.0, // complete guess
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.155,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.6
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.8,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.8 // complete guess
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Regulus": {
      //tmp         "id": 49528,
      //tmp         "name": "Regulus",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.11,
      //tmp                 "offset": 0,
      //tmp                 "radius": 3.6
      //tmp             },
      //tmp             {
      //tmp                 "c": 0.87,
      //tmp                 "offset": 50,
      //tmp                 "radius": 0.5
      //tmp             },
      //tmp             {
      //tmp                 "c": 1.4,
      //tmp                 "offset": 50,
      //tmp                 "radius": 0.5
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.05, // complete guess
      //tmp                 "offset": 120,
      //tmp                 "radius": 0.2
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Rigel": {
      //tmp         "id": 24378,
      //tmp         "name": "Rigel",
      //tmp         "sep": 2200.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.03,
      //tmp                 "offset": 0,
      //tmp                 "radius": 74
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.03,
      //tmp                 "offset": 0,
      //tmp                 "radius": 50
      //tmp             },
      //tmp         ]
      //tmp     },
      //tmp     "Rigel Kentaurus A": {
      //tmp         "id": 71457,
      //tmp         "name": "Alpha Centauri A",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.71,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1.227
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Rigel Kentaurus B": {
      //tmp         "id": 71454,
      //tmp         "name": "Alpha Centauri B",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.9,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.865
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Saiph": {
      //tmp         "id": 27298,
      //tmp         "name": "Saiph",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.168,
      //tmp                 "offset": 0,
      //tmp                 "radius": 22.2
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Scheat": {
      //tmp         "id": 113522,
      //tmp         "name": "Beta Pegasi",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.655,
      //tmp                 "offset": 0,
      //tmp                 "radius": 95
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Shaula": {
      //tmp         "id": 85666,
      //tmp         "name": "Lambda Scorpii",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.231,
      //tmp                 "offset": 0,
      //tmp                 "radius": 6.5
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.3,
      //tmp                 "offset": 7500,
      //tmp                 "radius": 0.9
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.3,
      //tmp                 "offset": 17000,
      //tmp                 "radius": 6.0
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Shedir": {
      //tmp         "id": 3172,
      //tmp         "name": "Shedir",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.17,
      //tmp                 "offset": 0,
      //tmp                 "radius": 42
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Sirius": {
      //tmp         "id": 32263,
      //tmp         "name": "Sirius",
      //tmp         "sep": 80.0,     //  complete guess
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.009,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1.711
      //tmp             },
      //tmp             {
      //tmp                 "c": -0.03,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.0084
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Sol": {
      //tmp         "id": 0,
      //tmp         "name": "Sun",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.656,
      //tmp                 "offset": 0,
      //tmp                 "radius": 1
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Spica": {
      //tmp         "id": 65270,
      //tmp         "name": "Spica",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.235,
      //tmp                 "offset": 0,
      //tmp                 "radius": 7.40
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Tarazed": {
      //tmp         "id": 96971,
      //tmp         "name": "Gamma Aquilae",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.507,
      //tmp                 "offset": 0,
      //tmp                 "radius": 95
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Unukalhai": {
      //tmp         "id": 76836,
      //tmp         "name": "Alpha Serpentis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 1.167,
      //tmp                 "offset": 0,
      //tmp                 "radius": 12
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Van Maanens Star": {
      //tmp         "id": 3820,
      //tmp         "name": "Van Maanen's Star",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.554,
      //tmp                 "offset": 0,
      //tmp                 "radius": 0.013
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Vega": {
      //tmp         "id": 90980,
      //tmp         "name": "Vega",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": -0.001,
      //tmp                 "offset": 0,
      //tmp                 "radius": 2.5
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "Vindemiatrix": {
      //tmp         "id": 63406,
      //tmp         "name": "Epsilon Virginis",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.934,
      //tmp                 "offset": 0,
      //tmp                 "radius": 10.6
      //tmp             }
      //tmp         ]
      //tmp     },
      //tmp     "p Eridani": {
      //tmp         "id": 7736,
      //tmp         "name": "p Eridani",
      //tmp         "sep": 0.0,
      //tmp         "sub": [
      //tmp             {
      //tmp                 "c": 0.88,
      //tmp                 "offset": 0,
      //tmp                 "radius": 10.6
      //tmp             }
      //tmp         ]
      //tmp     }
      //tmp }
      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      //	These markers use screenspace by doing fancy 3D to 2D calculations
      //	Much slower, use with caution!

      //tmp var legacyMarkers = [];
      //tmp
      //tmp //	called by animate per frame
      //tmp function updateLegacyMarkers(){
      //tmp 	for( var i in legacyMarkers ){
      //tmp 		var marker = legacyMarkers[i];
      //tmp 		marker.update();
      //tmp 	}
      //tmp }

      //tmp function attachLegacyMarker( text, obj, size, visibleRange ){
      //tmp 	var template = document.getElementById( 'legacy_marker_template' );
      //tmp
      //tmp 	var marker = template.cloneNode(true);
      //tmp
      //tmp 	marker.obj = obj;
      //tmp 	marker.absPosition = obj.position;
      //tmp 	marker.size = size !== undefined ? size : 1.0;
      //tmp
      //tmp 	marker.visMin = visibleRange === undefined ? 0 : visibleRange.min;
      //tmp 	marker.visMax = visibleRange === undefined ? 10000000 : visibleRange.max;
      //tmp
      //tmp 	marker.$ = $(marker);	// jQuery reference
      //tmp
      //tmp 	var container = document.getElementById('visualization');
      //tmp 	container.appendChild( marker );
      //tmp
      //tmp 	marker.setVisible = function ( vis ){
      //tmp 		if (vis) {
      //tmp 			this.style.opacity = 1.0;
      //tmp 		} else {
      //tmp 			this.style.opacity = 0.0;
      //tmp 		}
      //tmp
      //tmp 		if( vis )
      //tmp 			this.style.visibility = 'visible';
      //tmp 		else
      //tmp 			this.style.visibility = 'hidden';
      //tmp 		return this;
      //tmp 	};
      //tmp
      //tmp 	marker.setSize = function( s ) {
      //tmp 		this.style.fontSize = s + 'px';
      //tmp 	};
      //tmp
      //tmp 	marker.setPosition = function(x,y){
      //tmp 		x -= this.markerWidth * 0.5;
      //tmp 		this.style.left = x + 'px';
      //tmp 		this.style.top = y + 'px';
      //tmp 		// this.style.zIndex = z;
      //tmp 	};
      //tmp
      //tmp 	var nameLayer = marker.children[0];
      //tmp 	marker.nameLayer = nameLayer;
      //tmp 	nameLayer.innerHTML = text;
      //tmp 	marker.markerWidth = marker.$.outerWidth();
      //tmp
      //tmp 	marker.zero = new THREE.Vector3();
      //tmp 	marker.update = function() {
      //tmp
      //tmp 		var screenPos = screenXY(this.obj);
      //tmp
      //tmp 		var inCamRange = (camera.position.z > this.visMin && camera.position.z < this.visMax);
      //tmp 		var inCamFrame = (screenPos.x > 0 && screenPos.x < screenWidth && screenPos.y > 0 && screenPos.y < screenHeight);
      //tmp 		var isParentVisible = this.obj.visible;
      //tmp
      //tmp 		if( isParentVisible && inCamRange )
      //tmp 			this.setPosition( screenPos.x, screenPos.y );
      //tmp
      //tmp 		if( inCamRange && inCamFrame && isParentVisible )
      //tmp 			this.setVisible( true );
      //tmp 		else
      //tmp 			this.setVisible( false );
      //tmp 	};
      //tmp
      //tmp 	legacyMarkers.push( marker );
      //tmp
      //tmp }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      function centerOn(vec3) {
        var target = vec3.clone().negate();
        translating.easePanning = new TWEEN.Tween(translating.position)
          .to(
            {
              x: target.x,
              y: target.y,
              z: target.z
            },
            1200
          )
          //tmp      .easing(Tour.Easing)
          .easing(TWEEN.Easing.Sinusoidal.InOut)
          .start()
          .onComplete(function () {
            translating.easePanning = undefined;
          });

        translating.targetPosition.copy(target);
        updateMinimap();
      }

      function snapTo(vec3) {
        translating.targetPosition.copy(vec3.clone().negate());
        translating.position.copy(vec3.clone().negate());
        updateMinimap();
      }

      function zoomIn(v) {
        camera.easeZooming = new TWEEN.Tween(camera.position)
          .to(
            {
              z: v
              //tmp      }, 3000)
              //tmp      .easing(Tour.Easing)
            },
            1200
          )
          .easing(TWEEN.Easing.Sinusoidal.InOut)
          .start()
          .onComplete(function () {
            camera.easeZooming = undefined;
          });

        camera.position.target.pz = camera.position.z;
        camera.position.target.z = v;
        updateMinimap();
      }

      function zoomOut(v) {
        camera.position.target.z = v || camera.position.target.pz;
        updateMinimap();
      }

      function centerOnSun() {
        // zoomOut();
        // translating.targetPosition.set(0, 0, 0);
        markers[0].select();
      }

      function KMToLY(kilometers) {
        return kilometers * 1.05702341 * Math.pow(10, -13);
      }

      function LYToKM(LY) {
        return (LY / 1.05702341) * Math.pow(10, -13);
      }

      function AUToLY(AU) {
        return AU * 1.58128451 * Math.pow(10, -5);
      }

      Math.TWO_PI = Math.PI * 2.0;

      //	from
      //	Ryan Scranton
      //	http://code.google.com/p/astro-stomp/source/browse/trunk/stomp/stomp_angular_coordinate.cc#688
      function EquatorialToGalactic(ra, dec) {
        var g_psi = 0.574770433;
        var sTheta = 0.88998808748;
        var ctheta = 0.45598377618;
        var g_phi = 4.9368292465;

        var a = ra - g_phi;
        // var b = dec;

        var sb = Math.sin(dec);
        var cb = Math.cos(dec);
        var cbsa = cb * Math.sin(a);

        var b = -1.0 * sTheta * cbsa + ctheta * sb;
        if (b > 1.0) b = 1.0;

        var bo = Math.asin(b);

        a = Math.atan2(ctheta * cbsa + sTheta * sb, cb * Math.cos(a));
        var ao = a + g_psi + 4.0 * Math.PI;

        while (ao > Math.TWO_PI) ao -= Math.TWO_PI;

        var gal_lon = ao;
        if (gal_lon < 0.0) gal_lon += Math.TWO_PI;
        if (gal_lon > Math.TWO_PI) gal_lon -= Math.TWO_PI;

        var gal_lat = bo;

        return {
          lat: gal_lat,
          lon: gal_lon
        };
      }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      var galacticTexture0 = THREE.ImageUtils.loadTexture('/images/galactic_sharp.png');
      var galacticTexture1 = THREE.ImageUtils.loadTexture('/images/galactic_blur.png');

      var galacticUniforms = {
        color: { type: 'c', value: new THREE.Color(0xffffff) },
        texture0: { type: 't', value: galacticTexture0 },
        texture1: { type: 't', value: galacticTexture1 },
        idealDepth: { type: 'f', value: 1.0 },
        blurPower: { type: 'f', value: 1.0 },
        blurDivisor: { type: 'f', value: 2.0 },
        sceneSize: { type: 'f', value: 120.0 },
        cameraDistance: { type: 'f', value: 800.0 },
        zoomSize: { type: 'f', value: 1.0 },
        scale: { type: 'f', value: 1.0 },
        heatVision: { type: 'f', value: 0.0 }
      };

      var galacticAttributes = {
        size: { type: 'f', value: [] },
        customColor: { type: 'c', value: [] }
      };

      function generateGalaxy() {
        //tmp	setLoadMessage("Generating the galaxy");
        var galacticShaderMaterial = new THREE.ShaderMaterial({
          uniforms: galacticUniforms,
          attributes: galacticAttributes,
          vertexShader: shaderList.galacticstars.vertex,
          fragmentShader: shaderList.galacticstars.fragment,

          blending: THREE.AdditiveBlending,
          depthTest: false,
          depthWrite: false,
          transparent: true,
          sizeAttenuation: true,
          opacity: 0.0
        });

        var pGalaxy = new THREE.Geometry();

        //tmp  	var count = 100000;
        //tmp  	var numArms = 5;
        //tmp  	var arm = 0;
        //tmp  	var countPerArm = count / numArms;
        //tmp  	var ang = 0;
        //tmp  	var dist = 0;
        //tmp  	for( var i=0; i<count; i++ ){
        //tmp  		var x = Math.cos(ang) * dist;
        //tmp  		var y = 0;
        //tmp  		var z = Math.sin(ang) * dist;
        //tmp
        //tmp  		//	scatter
        //tmp  		var sa = 100 - Math.sqrt(dist);				//	scatter amt
        //tmp  		if( Math.random() > 0.3)
        //tmp  			sa *= ( 1 + Math.random() ) * 4;
        //tmp  		x += random(-sa, sa);
        //tmp  		z += random(-sa, sa);
        //tmp
        //tmp  		var distanceToCenter = Math.sqrt( x*x + z*z);
        //tmp  		var thickness = constrain( Math.pow( constrain(90-distanceToCenter*0.1,0,100000),2) * 0.02,2,10000) + Math.random() * 120;
        //tmp  		y += random( -thickness, thickness);
        //tmp
        //tmp  		// x -= 100;
        //tmp  		// z -= 1500;
        //tmp
        //tmp  		x *= 20;
        //tmp  		y *= 20;
        //tmp  		z *= 20;
        //tmp
        //tmp  		var p = new THREE.Vector3(x,y,z);
        //tmp  		p.size = 200 + constrain( 600/dist,0,32000);
        //tmp  		if( Math.random() > 0.99 )
        //tmp  			p.size *= Math.pow(1 + Math.random(), 3 + Math.random() * 3) * .9;
        //tmp  		else
        //tmp  			if( Math.random() > 0.7 )
        //tmp  				p.size *= 1 + Math.pow(1 + Math.random(), 2) * .04;
        //tmp
        //tmp  		if( i == 0 ){
        //tmp  			p.size = 100;
        //tmp  			// p.x = -100 * 20;
        //tmp  			// p.y = 0;
        //tmp  			// p.z = -1500 * 20;;
        //tmp  		}
        //tmp 		if ( 0 < i && i <= 100 ) {
        //tmp 		p.size = 2000;
        //tmp 		console.log("the position of =="+i+"== planet is: {"+ p.x+", "+ p.y+", "+ p.z+"}");
        //tmp 		console.log("the size of ==", i,"== planet is: ", p.size);
        //tmp 	 	pGalaxy.vertices.push( p );
        //tmp 		}
        //tmp
        //tmp  		var r = constrain(1 - (Math.pow(dist,3)*0.00000002),.3,1) + random(-.1,.1);
        //tmp  		var g = constrain(0.7 - (Math.pow(dist,2)*0.000001),.41,1) + random(-.1,.1);
        //tmp  		var b = constrain(0.1 + dist * 0.004,.3,.6) + random(-.1,.1);
        //tmp  		var c = new THREE.Color();
        //tmp  		c.r = r; c.g = g; c.b = b;
        //tmp 		if ( 0 < i && i <= 100 ) {
        //tmp 		console.log("the color of =="+i+"== planet is: {"+ c.r+", "+ c.g+", "+ c.b+"}");
        //tmp  		c.r = 0.3; c.g = 0.4; c.b = 0.6;
        //tmp  		pGalaxy.colors.push( c );
        //tmp 		}
        //tmp
        //tmp  		ang += 0.0002;
        //tmp  		dist += .08;
        //tmp
        //tmp  		if( i % countPerArm == 0 ){
        //tmp  			ang = Math.PI * 2 / numArms * arm;
        //tmp  			dist = 0;
        //tmp  			arm++;
        //tmp  		}
        //tmp  	}
        //tmp
        //tmp 		var p = new THREE.Vector3(0,0,5000);
        //tmp 		p.size = 200000;
        //tmp 		pGalaxy.vertices.push( p );
        //tmp 		var c = new THREE.Color();
        //tmp 		c.r = 1; c.g = 0.7; c.b = 0.1;
        //tmp 		pGalaxy.colors.push( c );

        var count = 3;
        for (var i = 0; i < count; i++) {
          var x = 0 + 100 * (i - 1);
          var y = 0 + 100 * (i - 1);
          var z = 0 + 100 * (i - 1);
          var p = new THREE.Vector3(x, y, z);
          p.size = 2500;
          pGalaxy.vertices.push(p);

          var c = new THREE.Color();
          c.r = 0.3;
          c.g = 0.4;
          c.b = 0.6;
          pGalaxy.colors.push(c);
        }
        var pGalacticSystem = new THREE.ParticleSystem(pGalaxy, galacticShaderMaterial);

        //	set the values to the shader
        var values_size = galacticAttributes.size.value;
        var values_color = galacticAttributes.customColor.value;

        for (var v = 0; v < pGalaxy.vertices.length; v++) {
          values_size[v] = pGalaxy.vertices[v].size;
          values_color[v] = pGalaxy.colors[v];
        }

        //	galactic core is 27000 ly from earth
        //tmp	pGalacticSystem.position.x = 27000;

        //tmp	pGalacticSystem.add( addLensFlare(0,0,0) );

        //	make a top down image
        var galacticTopMaterial = new THREE.MeshBasicMaterial({
          map: THREE.ImageUtils.loadTexture('/images/galactictop.png'),
          blending: THREE.AdditiveBlending,
          depthTest: false,
          depthWrite: false,
          side: THREE.DoubleSide,
          transparent: true
        });

        //	the milky way is about 100,000 to 120,000 LY across
        //	however the texture of the milky way is smaller than this on the plane
        //	we'll make it roughly 10% bigger than it really will be to arrive at the correct size

        //	note:
        //	normally we want to do this with one poly quad
        //	however on certain GPUs this seems to break because the quad is looking like it's attempted to be culled
        //	throwing my polygons at the problem apparently fixes this

        //tmp	var plane = new THREE.Mesh( new THREE.PlaneGeometry(150000,150000, 30, 30), galacticTopMaterial );
        //tmp	plane.rotation.x = Math.PI/2;
        //tmp	plane.material.map.anisotropy = maxAniso;
        //tmp	pGalacticSystem.add( plane );
        //tmp
        //tmp	//	a measurement of the galactic plane
        //tmp	var measurement = createDistanceMeasurement( new THREE.Vector3( 0,0,-55000 ), new THREE.Vector3( 0,0,55000 ) );
        //tmp	measurement.position.y = -1000;
        //tmp	measurement.visible = false;
        //tmp//tmp	attachLegacyMarker( "Milky Way ~110,000 Light Years", measurement, 1.0, {min:6000, max: 120000} );
        //tmp	pGalacticSystem.add( measurement );
        //tmp	measurement.rotation.x = Math.PI;
        //tmp
        //tmp	pGalacticSystem.measurement = measurement;
        //tmp	window.toggleGalacticMeasurement = function( desired ){
        //tmp		if( desired == undefined )
        //tmp			pGalacticSystem.measurement.visible = !this.measurement.visible;
        //tmp		else
        //tmp			pGalacticSystem.measurement.visible = desired;
        //tmp	}
        //tmp
        //tmp	//	a heat-vision skeleton of the galactic plane
        //tmp	var cylinderMaterial = new THREE.MeshBasicMaterial({
        //tmp		map: glowSpanTexture,
        //tmp		blending: THREE.AdditiveBlending,
        //tmp		transparent: true,
        //tmp		depthTest: false,
        //tmp		depthWrite: false,
        //tmp		wireframe: true,
        //tmp		opacity: 1.0,
        //tmp	})
        //tmp	var isogeo = new THREE.IcosahedronGeometry( 40000, 4 );
        //tmp	var matrix = new THREE.Matrix4();
        //tmp	matrix.scale( new THREE.Vector3(1,0,1) );
        //tmp	isogeo.applyMatrix( matrix );
        //tmp	var isoball = new THREE.Mesh( isogeo, cylinderMaterial );
        //tmp	isoball.material.map.wrapS = THREE.RepeatWrapping;
        //tmp	isoball.material.map.wrapT = THREE.RepeatWrapping;
        //tmp	isoball.material.map.needsUpdate = true;
        //tmp	isoball.update = function(){
        //tmp		var heatVisionValue = pSystem.shaderMaterial.uniforms.heatVision.value;
        //tmp		//this.material.opacity = (1.0 - heatVisionValue);
        //tmp	}
        //tmp	isoball.material.map.onUpdate = function(){
        //tmp		this.offset.y -= 0.0001;
        //tmp		this.needsUpdate = true;
        //tmp	}
        //tmp	//pGalacticSystem.add( isoball );

        pGalacticSystem.update = function () {
          //	reduce the galactic particle sizes when zooming way in (otherwise massive overdraw, drop in fps, too bright..)
          galacticUniforms.zoomSize.value = 1.0 + 10000 / camera.position.z;

          //	scale the particles based off of screen size
          var areaOfWindow = window.innerWidth * window.innerHeight;

          galacticUniforms.scale.value = Math.sqrt(areaOfWindow) * 1.5;

          galacticTopMaterial.opacity = galacticShaderMaterial.opacity;

          //	for heat vision...
          if (pSystem) {
            var heatVisionValue = pSystem.shaderMaterial.uniforms.heatVision.value;

            if (heatVisionValue > 0) {
              galacticTopMaterial.opacity = 1.0 - heatVisionValue;
            }

            galacticUniforms.heatVision.value = heatVisionValue;

            if (pDustSystem) {
              if (heatVisionValue > 0) pDustSystem.visible = false;
              else pDustSystem.visible = true;
            }
          }

          // console.log( galacticUniforms.zoomSize.value);
          if (camera.position.z < 2500) {
            if (galacticShaderMaterial.opacity > 0) galacticShaderMaterial.opacity -= 0.05;
          } else {
            if (galacticShaderMaterial.opacity < 1) galacticShaderMaterial.opacity += 0.05;
          }

          if (galacticShaderMaterial.opacity <= 0.0) {
            pGalacticSystem.visible = false;
            //tmp			plane.visible = false;
          } else {
            pGalacticSystem.visible = true;
            //tmp			plane.visible = true;
          }

          var targetLerp = constrain(Math.pow(camera.position.z / 80000, 3), 0.0, 1.0);
          if (targetLerp < 0.00001) targetLerp = 0.0;

          galacticCentering.position.set(0, 0, 0);
          galacticCentering.position.lerp(this.position.clone().negate(), targetLerp);
        };

        //	position it as if the disc visible in the star data were the actual galactic disc
        //tmp_for_debug pGalacticSystem.position.x = 11404;
        //tmp_for_debug pGalacticSystem.position.y = 14000;
        //tmp_for_debug pGalacticSystem.position.z = 10000;
        pGalacticSystem.position.x = 0;
        pGalacticSystem.position.y = 0;
        pGalacticSystem.position.z = 0;

        //tmp_for_debug pGalacticSystem.rotation.x = 2.775557;
        //tmp_for_debug pGalacticSystem.rotation.y = -0.4;
        //tmp_for_debug pGalacticSystem.rotation.z = -1.099999;

        // pGalacticSystem.targetPosition = pGalacticSystem.position.clone();
        // pGalacticSystem.zeroRotation = new THREE.Vector3();
        // pGalacticSystem.targetRotation = pGalacticSystem.rotation.clone();

        // pGalacticSystem.scale.x = pGalacticSystem.scale.y = pGalacticSystem.scale.z = 100;
        return pGalacticSystem;
      }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      THREE.ImageUtils.crossOrigin = null;
      var guidePointTexture = THREE.ImageUtils.loadTexture('/images/p_1.png');

      function createSpaceRadius(radius, color, representationScale) {
        color = color ? color : 0xffffff;
        representationScale = representationScale ? representationScale : 1;

        var width = Math.sqrt(radius) * 0.00001 * representationScale;
        var thickness = radius * 0.0005;
        var textureRepeat = 30;

        var resolution = 180;
        var twoPI = Math.PI * 2;
        var angPerRes = twoPI / resolution;
        var verts = [];
        for (var i = 0; i < twoPI; i += angPerRes) {
          var x = Math.cos(i) * radius;
          var y = Math.sin(i) * radius;
          var v = new THREE.Vector3(x, y, 0);
          verts.push(v);
        }

        var geometry = new THREE.Geometry();
        geometry.vertices = verts;

        var areaOfWindow = window.innerWidth * window.innerHeight;

        var pointSize = 0.000004 * areaOfWindow;

        var particleMaterial = new THREE.ParticleBasicMaterial({
          color: color,
          size: pointSize,
          sizeAttenuation: false,
          map: guidePointTexture,
          blending: THREE.AdditiveBlending,
          depthTest: false,
          depthWrite: false
        });

        var mesh = new THREE.ParticleSystem(geometry, particleMaterial);

        mesh.update = function () {
          if (camera.position.z < 2.0) this.visible = false;
          else if (camera.position.z < 800) this.visible = true;
          else this.visible = false;
        };

        mesh.rotation.x = Math.PI / 2;
        return mesh;
      }

      function createDistanceMeasurement(vecA, vecB) {
        var geometry = new THREE.Geometry();
        var distance = vecA.distanceTo(vecB);
        var height = distance * 0.04;
        var bufferSpace = 0.38;

        /*
		vecA--------vecAMargin .....bufferspace	..... vecBMargin--------vecB		
		|																   |
		clamperA													clamperB
	*/

        var upwards = new THREE.Vector3(0, 0, 0);
        var downwards = new THREE.Vector3(0, -height, 0);
        var clamperA = vecA.clone().add(downwards);
        var clamperB = vecB.clone().add(downwards);
        vecA.add(upwards);
        vecB.add(upwards);

        var center = vecA.clone().lerp(vecB, 0.5);
        //tmp	vecAMargin = vecA.clone().lerp( vecB, bufferSpace );
        //tmp	vecBMargin = vecB.clone().lerp( vecA, bufferSpace );
        var vecAMargin = vecA.clone().lerp(vecB, bufferSpace);
        var vecBMargin = vecB.clone().lerp(vecA, bufferSpace);

        geometry.vertices.push(clamperA);
        geometry.vertices.push(vecA);
        geometry.vertices.push(vecAMargin); //	double down on the margin vertex to create a solid to transparent separation
        geometry.vertices.push(vecAMargin);
        geometry.vertices.push(vecBMargin);
        geometry.vertices.push(vecBMargin);
        geometry.vertices.push(vecB);
        geometry.vertices.push(clamperB);

        var solid = new THREE.Color(0x888888);
        var nonsolid = new THREE.Color(0x000000);

        geometry.colors.push(solid);
        geometry.colors.push(solid);
        geometry.colors.push(solid);
        geometry.colors.push(nonsolid);
        geometry.colors.push(nonsolid);
        geometry.colors.push(solid);
        geometry.colors.push(solid);
        geometry.colors.push(solid);

        var material = new THREE.LineBasicMaterial({
          color: 0xffffff,
          depthTest: false,
          depthWrite: false,
          blending: THREE.AdditiveBlending,
          vertexColors: true
        });
        var mesh = new THREE.Line(geometry, material);
        return mesh;
      }

      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
      ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    }
  }
};
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
}

/*
#loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: #000; }
  #loader div {
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -80px;
    margin-left: -250px;
    text-align: center; }
  #loader p {
    color: #fff;
    width: 500px;
    font-size: 21px;
    line-height: 40px;
    font-style: italic;
    font-weight: normal;
    padding-bottom: 20px;
    text-shadow: 0 0 3px #fff, 0 0 10px #000; }*/

.marker {
  font-family: 'Lekton', monospace;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  /*
   Introduced in IE 10.
   See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
  position: absolute;
  padding: 0px 0px;
  cursor: pointer;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  -ms-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  transition: opacity 0.5s;
}
.marker span.name {
  color: #fff;
  text-shadow:
    0 0 3px #fff,
    0 0 10px #000;
  -webkit-transition: text-shadow 0.25s;
  -moz-transition: text-shadow 0.25s;
  -ms-transition: text-shadow 0.25s;
  -o-transition: text-shadow 0.25s;
  transition: text-shadow 0.25s;
}
.marker span.name:hover {
  text-shadow:
    0 0 3px #fff,
    0 0 5px #fff,
    0 0 10px #fff,
    0 0 20px #fff;
}
.marker.label span.name {
  cursor: default !important;
  font-size-adjust: none;
}
.marker.label span.name:hover {
  text-shadow:
    0 0 3px #fff,
    0 0 10px #000 !important;
}
.marker span.name {
  color: #fff;
  text-shadow:
    0 0 3px #fff,
    0 0 10px #000;
  -webkit-transition: text-shadow 0.25s;
  -moz-transition: text-shadow 0.25s;
  -ms-transition: text-shadow 0.25s;
  -o-transition: text-shadow 0.25s;
  transition: text-shadow 0.25s;
}
.marker span.name:hover {
  text-shadow:
    0 0 3px #fff,
    0 0 5px #fff,
    0 0 10px #fff,
    0 0 20px #fff;
}

.legacymarker {
  font-family: 'Lekton', monospace;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  /*
   Introduced in IE 10.
   See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
  position: absolute;
  padding: 6px 8px;
  min-width: 140px;
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  -ms-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  transition: opacity 0.5s;
  display: inline;
}
.legacymarker span.name {
  display: inline-block;
  width: 100%;
  color: #fff;
  text-shadow: 0.1em 0.1em 4px #000;
  -webkit-transition: text-shadow 0.25s;
  -moz-transition: text-shadow 0.25s;
  -ms-transition: text-shadow 0.25s;
  -o-transition: text-shadow 0.25s;
  transition: text-shadow 0.25s;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}

@keyframes aura {
  from {
    -webkit-box-shadow: inset 0 0 1px white;
    -moz-box-shadow: inset 0 0 1px white;
    -ms-box-shadow: inset 0 0 1px white;
    -o-box-shadow: inset 0 0 1px white;
    box-shadow: inset 0 0 1px white;
  }

  to {
    -webkit-box-shadow: inset 0 0 50px white;
    -moz-box-shadow: inset 0 0 50px white;
    -ms-box-shadow: inset 0 0 50px white;
    -o-box-shadow: inset 0 0 50px white;
    box-shadow: inset 0 0 50px white;
  }
}

@-moz-keyframes aura {
  from {
    -webkit-box-shadow: inset 0 0 1px white;
    -moz-box-shadow: inset 0 0 1px white;
    -ms-box-shadow: inset 0 0 1px white;
    -o-box-shadow: inset 0 0 1px white;
    box-shadow: inset 0 0 1px white;
  }

  to {
    -webkit-box-shadow: inset 0 0 50px white;
    -moz-box-shadow: inset 0 0 50px white;
    -ms-box-shadow: inset 0 0 50px white;
    -o-box-shadow: inset 0 0 50px white;
    box-shadow: inset 0 0 50px white;
  }
}

@-webkit-keyframes aura {
  from {
    -webkit-box-shadow: inset 0 0 1px white;
    -moz-box-shadow: inset 0 0 1px white;
    -ms-box-shadow: inset 0 0 1px white;
    -o-box-shadow: inset 0 0 1px white;
    box-shadow: inset 0 0 1px white;
  }

  to {
    -webkit-box-shadow: inset 0 0 50px white;
    -moz-box-shadow: inset 0 0 50px white;
    -ms-box-shadow: inset 0 0 50px white;
    -o-box-shadow: inset 0 0 50px white;
    box-shadow: inset 0 0 50px white;
  }
}

@-o-keyframes aura {
  from {
    -webkit-box-shadow: inset 0 0 1px white;
    -moz-box-shadow: inset 0 0 1px white;
    -ms-box-shadow: inset 0 0 1px white;
    -o-box-shadow: inset 0 0 1px white;
    box-shadow: inset 0 0 1px white;
  }

  to {
    -webkit-box-shadow: inset 0 0 50px white;
    -moz-box-shadow: inset 0 0 50px white;
    -ms-box-shadow: inset 0 0 50px white;
    -o-box-shadow: inset 0 0 50px white;
    box-shadow: inset 0 0 50px white;
  }
}

@-ms-keyframes aura {
  from {
    -webkit-box-shadow: inset 0 0 1px white;
    -moz-box-shadow: inset 0 0 1px white;
    -ms-box-shadow: inset 0 0 1px white;
    -o-box-shadow: inset 0 0 1px white;
    box-shadow: inset 0 0 1px white;
  }

  to {
    -webkit-box-shadow: inset 0 0 50px white;
    -moz-box-shadow: inset 0 0 50px white;
    -ms-box-shadow: inset 0 0 50px white;
    -o-box-shadow: inset 0 0 50px white;
    box-shadow: inset 0 0 50px white;
  }
}

#icon-nav {
  position: absolute;
  top: 0;
  left: 0;
  padding: 20px 20px 16px 20px;
}
#icon-nav #tour-button {
  margin-top: -3.33333px;
  opacity: 1;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  -webkit-animation-name: aura;
  -moz-animation-name: aura;
  animation-name: aura;
  -webkit-animation-duration: 2s;
  -moz-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  -moz-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-direction: alternate;
  -moz-animation-direction: alternate;
  animation-direction: alternate;
  -webkit-animation-timing-function: ease-in-out;
  -moz-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-transition: background 0.25s;
  -moz-transition: background 0.25s;
  -ms-transition: background 0.25s;
  -o-transition: background 0.25s;
  transition: background 0.25s;
}
#icon-nav #tour-button:hover {
  -webkit-animation-play-state: paused;
  -moz-animation-play-state: paused;
  animation-play-state: paused;
  background: rgba(255, 255, 255, 0.66);
}
#icon-nav > * {
  opacity: 0.8;
  vertical-align: top;
  cursor: pointer;
  display: inline-block;
}
#icon-nav > *:not(#tour-button) {
  -webkit-transition: opacity 0.25s;
  -moz-transition: opacity 0.25s;
  -ms-transition: opacity 0.25s;
  -o-transition: opacity 0.25s;
  transition: opacity 0.25s;
}
#icon-nav > *:hover {
  opacity: 1;
}
#icon-nav > *:not(:first-child) {
  margin-left: 10px;
}

#spectral-graph {
  position: absolute;
  left: 0;
  top: 67px;
  color: #fff;
  font-family: 'Lekton', monospace;
  display: none;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  /*
   Introduced in IE 10.
   See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
  padding: 24px 20px 20px 20px;
  -webkit-transition: opacity 0.25s;
  -moz-transition: opacity 0.25s;
  -ms-transition: opacity 0.25s;
  -o-transition: opacity 0.25s;
  transition: opacity 0.25s;
  opacity: 0.8;
}
#spectral-graph:hover,
#spectral-graph.heatvision {
  opacity: 1;
}
#spectral-graph p {
  position: absolute;
  width: 300px;
  top: 42px;
  left: 20px;
  font-size: 12px;
}
#spectral-graph p.left {
  text-align: left;
}
#spectral-graph p.center {
  text-align: center;
}
#spectral-graph p.right {
  text-align: right;
}
#spectral-graph h5 {
  margin-top: 30px;
}
#spectral-graph #heat-map {
  width: 300px;
  height: 20px;
  background: #c8420a;
  /* Old browsers */
  background: -moz-linear-gradient(
    left,
    #c8420a 0%,
    #f8e141 25%,
    white 50%,
    #444fe3 98%,
    #be78ef 100%
  );
  /* FF3.6+ */
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(0%, #c8420a),
    color-stop(25%, #f8e141),
    color-stop(50%, white),
    color-stop(98%, #444fe3),
    color-stop(100%, #be78ef)
  );
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(
    left,
    #c8420a 0%,
    #f8e141 25%,
    white 50%,
    #444fe3 98%,
    #be78ef 100%
  );
  /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(
    left,
    #c8420a 0%,
    #f8e141 25%,
    white 50%,
    #444fe3 98%,
    #be78ef 100%
  );
  /* Opera 11.10+ */
  background: -ms-linear-gradient(
    left,
    #c8420a 0%,
    #f8e141 25%,
    white 50%,
    #444fe3 98%,
    #be78ef 100%
  );
  /* IE10+ */
  background: linear-gradient(
    to right,
    #c8420a 0%,
    #f8e141 25%,
    white 50%,
    #444fe3 98%,
    #be78ef 100%
  );
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#c8420a', endColorstr='#be78ef',GradientType=1 );
  /* IE6-9 */
}

#theater .top-bar {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 67px;
  background: #000;
  margin-top: -67px;
}
#theater .bottom-bar {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 67px;
  background: #000;
  margin-bottom: -67px;
}
#theater .message {
  position: absolute;
  top: 75%;
  left: 0;
  right: 0;
  text-align: center;
}
#theater .message p {
  padding: 0 100px;
  height: 40px;
  font-size: 28px;
  line-height: 40px;
  margin-top: -30px;
  color: #fff;
  font-style: italic;
  text-shadow:
    0 0 3px #2c47dc,
    0 0 10px #eeeeff;
}
#theater .message p span {
  padding: 10px 20px;
  background: rgba(0, 0, 0, 0.9);
}

#controlshelp {
  background-color: rgba(0, 0, 0, 0.65);
  padding: 15px;
  color: #ffffff;
  font-size: 32px;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  /*
   Introduced in IE 10.
   See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
}

#layout {
  -webkit-transform: scale(1.25);
  -moz-transform: scale(1.25);
  -ms-transform: scale(1.25);
  -o-transform: scale(1.25);
  transform: scale(1.25);
  -webkit-transition: all 0.25s;
  -moz-transition: all 0.25s;
  -ms-transition: all 0.25s;
  -o-transition: all 0.25s;
  transition: all 0.25s;
}

.unselectable {
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  /*
   Introduced in IE 10.
   See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
}

#star-name,
#detailTitle {
  color: #fff;
  font-size: 42px;
  line-height: 60px;
  margin: 0 0 20px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.75);
  position: relative;
  display: block;
  font-style: italic;
  text-shadow:
    0 0 3px #2c47dc,
    0 0 10px #eeeeff;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  /*
   Introduced in IE 10.
   See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
}

#star-name {
  position: fixed;
  left: 0;
  bottom: 0;
  margin: 0 20px 25px 20px;
  width: 560px;
  display: none;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  /*
   Introduced in IE 10.
   See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
}
#star-name span {
  cursor: pointer;
}

#detailBody {
  color: white;
  z-index: 9999;
  display: block;
  text-shadow: 0 0 7px rgba(255, 255, 255, 0.7);
}
#detailBody p:first-child {
  text-indent: 10px;
}
#detailBody p + p {
  margin-top: 20px;
}

#detailContainer {
  display: none;
  background-color: rgba(0, 0, 0, 0.75);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  padding: 0 20px 60px 20px;
  overflow: auto;
  /*tmp  width: 560px;*/
  width: 450px;
  z-index: 9999 !important;
}
#detailContainer ul {
  margin-left: 20px;
}
#detailContainer ul li {
  margin-bottom: 10px;
}

#detailFooter {
  margin-top: 20px;
  color: #ccc;
  font-size: 12px;
}

#detailClose {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  color: #fff;
  padding: 10px;
  font-size: 28px;
}
#detailClose > * {
  text-shadow: 0 0 transparent;
  display: inline-block;
  margin-left: 10px;
  opacity: 0.8;
}
#detailClose > *:hover {
  opacity: 1;
}

#meta {
  display: none;
  position: fixed;
  bottom: 0;
  left: 50%;
  text-align: center;
  z-index: 9999;
  color: #fff;
  -webkit-transition: text-shadow 0.25s;
  -moz-transition: text-shadow 0.25s;
  -ms-transition: text-shadow 0.25s;
  -o-transition: text-shadow 0.25s;
  transition: text-shadow 0.25s;
  text-shadow:
    0 0 3px #2c47dc,
    0 0 10px #eeeeff;
}
#meta p {
  vertical-align: bottom;
  padding: 20px 40px;
  background: rgba(0, 0, 0, 0.9);
}
#meta p a {
  text-decoration: none;
  font-style: italic;
  border: 0;
}
#meta:hover {
  text-shadow:
    0 0 5px #fff,
    0 0 10px #eeeeff;
}

div.tip {
  font-size: 14px;
  line-height: 14px;
  display: inline-block;
  font-style: italic;
  padding: 0;
  margin-top: 0;
  margin-left: 0;
  color: #fff;
}
div.tip > div {
  display: inline-block;
  vertical-align: top;
}

#minimap {
  opacity: 0;
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  color: #fff;
  text-align: center;
  padding: 20px;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  /*
   Introduced in IE 10.
   See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: opacity 0.25s;
  -moz-transition: opacity 0.25s;
  -ms-transition: opacity 0.25s;
  -o-transition: opacity 0.25s;
  transition: opacity 0.25s;
  z-index: 9999;
  /**
   * Zoom variables
   */
}
#minimap.ready {
  opacity: 0.8;
}
#minimap.ready:hover {
  opacity: 1;
}
#minimap #home {
  cursor: pointer;
  -webkit-transition: all 0.25s;
  -moz-transition: all 0.25s;
  -ms-transition: all 0.25s;
  -o-transition: all 0.25s;
  transition: all 0.25s;
}
#minimap #zoom-levels {
  left: 0;
  right: 0;
  position: relative;
  cursor: ns-resize;
  border-bottom: 2px solid white;
  border-top: 2px solid white;
  -webkit-transition: all 0.25s;
  -moz-transition: all 0.25s;
  -ms-transition: all 0.25s;
  -o-transition: all 0.25s;
  transition: all 0.25s;
  margin-top: 10px;
}
#minimap #zoom-backdrop {
  position: relative;
  width: 0;
  height: 100%;
  margin: 0 auto;
  border-left: 2px solid white;
}
#minimap #zoom-cursor {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 0;
  border-top: 2px solid white;
  border-bottom: 2px solid white;
}
#minimap #about {
  position: absolute;
  font-family: 'Lekton', monospace;
  font-size: 24px;
  width: 24px;
  line-height: 27px;
  text-align: center;
  height: 24px;
  -webkit-border-radius: 24px;
  -moz-border-radius: 24px;
  -ms-border-radius: 24px;
  -o-border-radius: 24px;
  border-radius: 24px;
  -webkit-transition: all 0.25s;
  -moz-transition: all 0.25s;
  -ms-transition: all 0.25s;
  -o-transition: all 0.25s;
  transition: all 0.25s;
  font-weight: bold;
  cursor: pointer;
  bottom: 20px;
  margin-left: -2px;
}
#minimap #volume {
  cursor: pointer;
  width: 24px;
  height: 24px;
}
#minimap #sound {
  cursor: pointer;
  -webkit-transition: all 0.25s;
  -moz-transition: all 0.25s;
  -ms-transition: all 0.25s;
  -o-transition: all 0.25s;
  transition: all 0.25s;
  bottom: 13px;
  right: 54px;
  position: absolute;
  visibility: visible;
}
#minimap #soundoff {
  cursor: pointer;
  -webkit-transition: all 0.25s;
  -moz-transition: all 0.25s;
  -ms-transition: all 0.25s;
  -o-transition: all 0.25s;
  transition: all 0.25s;
  bottom: 13px;
  right: 54px;
  position: absolute;
  display: none;
}

sup {
  vertical-align: super;
  font-size: 9.24px;
  line-height: 0;
}

.reference {
  display: none;
  visibility: hidden;
}

a {
  -webkit-transition: border 0.25s;
  -moz-transition: border 0.25s;
  -ms-transition: border 0.25s;
  -o-transition: border 0.25s;
  transition: border 0.25s;
}
a:link,
a:active,
a:visited {
  color: inherit;
  text-decoration: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}
a:hover {
  border-bottom: 1px solid rgba(0, 0, 0, 0);
  text-decoration: none;
}

::selection,
::-moz-selection {
  background: #2c47dc;
}

#css-world {
  width: 100%;
  height: 100%;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  position: absolute;
}

#css-camera {
  width: 100%;
  height: 100%;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  position: absolute;
}

#css-container {
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  position: absolute;
}

canvas {
  pointer-events: none;
  z-index: 10;
}

#chrome-experiments {
  width: 141px;
  height: 72px;
}
#chrome-experiments a {
  border-bottom: 0px;
}
</style>
