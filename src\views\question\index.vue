<template>
  <div class="question-layout">
    <template v-if="ready">
      <template v-if="!NoPermisson">
        <!-- 左侧边栏：项目信息和问题列表 -->
        <div class="left-sidebar">
          <QuestionPrjInfo
            ref="questionPrjInfoRef"
            @question-change="handleQuestionChange"
            @chapter-change="handleChapterChange"
          />
        </div>

        <!-- 主内容区：问题详情和回答（包含右侧文档内容） -->
        <div class="main-content">
          <DraftQuestion ref="draftQuestionRef" />
        </div>
      </template>
      <OutPermission v-else :errorMessage="errorInfo"></OutPermission>
    </template>
  </div>
</template>

<script setup lang="ts">
import { PrjForm, PrjType } from '@/types/project';
import type { PrjinfoItf } from '@/types/learning';
import { getPartProApi, getPrjDetailApi } from '@/apis/learning';
import { useDraftWordStoreV2 } from '@/stores/draftWordV2';
import { useVideoWordStoreV2 } from '@/stores/videoWordV2';
import QuestionPrjInfo from './components/PrjInfo.vue';
import DraftQuestion from './components/DraftQuestion.vue';
import OutPermission from '@/components/OutPermission.vue';
import { PermissionCode } from '@/types/goods';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
const draftWordStore = useDraftWordStoreV2();
const videoWordStore = useVideoWordStoreV2();

const ready = ref(false);
const useWideScreen = inject('useWideScreen') as Ref<boolean>;

const route = useRoute();
const router = useRouter();
const spuId = route.query.spuId as string;
const chapterId = route.query.chapterId as string;

// 组件引用
const questionPrjInfoRef = ref();
const draftQuestionRef = ref();

const prjInfo = ref<PrjinfoItf>({});
const prjForm = ref<PrjForm>();
const prjType = ref<PrjType>();

provide('prjInfo', prjInfo);
provide('prjForm', prjForm);
provide('prjType', prjType);
const errorInfo = ref('');
const NoPermisson = ref(false);

// 事件处理函数
const handleQuestionChange = (questionId: string, chapterId: string) => {
  // 更新路由参数
  router.replace({
    query: {
      spuId,
      chapterId,
      questionId
    }
  });

  // 通知DraftQuestion组件更新（现在统一处理文稿和视频项目）
  // 注意：这里只调用changeQuestion，让它内部处理章节变化检测和内容更新
  if (draftQuestionRef.value) {
    draftQuestionRef.value.changeQuestion?.(questionId, chapterId);
  }
};

const handleChapterChange = (chapterId: string) => {
  // 通知组件更新内容
  if (draftQuestionRef.value) {
    draftQuestionRef.value.updateContent?.(chapterId);
  }
};

// 处理删除问题事件
const handleRemoveQuestion = async (eventData: any) => {
  let questionId: string;
  let chapterId: string;
  let needNavigation = false;

  // 兼容旧的事件格式（只传questionId）和新的事件格式（传对象）
  if (typeof eventData === 'string') {
    questionId = eventData;
    chapterId = route.query.chapterId as string;
  } else {
    questionId = eventData.questionId;
    chapterId = eventData.chapterId;
    needNavigation = eventData.needNavigation || false;
  }

  // 如果需要导航，先获取导航结果（在刷新列表之前）
  let navigationResult = null;
  if (needNavigation && questionPrjInfoRef.value?.handleQuestionDeleted) {
    navigationResult = questionPrjInfoRef.value.handleQuestionDeleted(questionId, chapterId);
  }

  // 刷新左侧问题列表（这会重新从API获取数据）
  if (questionPrjInfoRef.value) {
    await questionPrjInfoRef.value.refreshQuestionList?.();
  }

  // 执行导航
  if (navigationResult) {
    if (
      navigationResult.type === 'question' &&
      navigationResult.questionId &&
      navigationResult.chapterId
    ) {
      // 跳转到下一个问题
      handleQuestionChange(navigationResult.questionId, navigationResult.chapterId);
    } else if (navigationResult.type === 'project-detail') {
      // 返回项目详情页
      router.push({
        path: '/goodIntroduce',
        query: { spuId: spuId, tab: 'questionList' }
      });
    }
  }
};

onMounted(async () => {
  useWideScreen.value = true;

  // 监听删除问题事件
  emitter.on(Event.REMOVE_QUESTION, handleRemoveQuestion);

  try {
    // 获取项目基本信息
    const res = await getPartProApi(spuId);
    prjInfo.value = res.data.list[0];
    prjForm.value = prjInfo.value.prjForm;
    prjType.value = prjInfo.value.prjType;

    if (prjForm.value == PrjForm.draft) {
      draftWordStore.$reset();
    } else if (prjForm.value == PrjForm.video) {
      videoWordStore.$reset();
    }

    // 获取项目详情信息
    const detailRes = await getPrjDetailApi(spuId, chapterId);

    if (detailRes.code === PermissionCode.nobuy) {
      NoPermisson.value = true;
      errorInfo.value = detailRes.message || '获取项目详情失败';
    } else {
      // 先设置ready为true，让组件渲染
      ready.value = true;

      // 等待组件渲染完成
      await nextTick();

      // 初始化左侧组件数据
      if (questionPrjInfoRef.value && detailRes.data) {
        const chapters = detailRes.data.chapterList || [];
        const currentChapterId = route.query.chapterId as string;
        const currentQuestionId = route.query.questionId as string;

        await questionPrjInfoRef.value.setChapterAndQuestionData(
          chapters,
          [],
          currentChapterId,
          currentQuestionId
        );
      }

      // 初始化主组件数据（包含右侧内容）- 统一使用DraftQuestion组件处理文稿和视频项目
      if (draftQuestionRef.value) {
        const chapters = detailRes.data.chapterList || [];
        const currentChapterId = route.query.chapterId as string;
        const currentQuestionId = route.query.questionId as string;

        await draftQuestionRef.value.setInitialData(
          chapters,
          currentChapterId,
          currentQuestionId,
          detailRes.data
        );
      }

      // 等待DOM更新完成
      await nextTick();
      await nextTick(); // 确保所有子组件都已渲染完成

      return; // 提前返回，避免重复设置ready
    }

    ready.value = true;
  } catch (error) {
    errorInfo.value = '加载页面数据失败，请刷新重试';
    NoPermisson.value = true;
    ready.value = true;
  }
});

onBeforeUnmount(() => {
  useWideScreen.value = false;

  // 清理事件监听器
  emitter.off(Event.REMOVE_QUESTION, handleRemoveQuestion);
});
</script>

<style scoped lang="less">
.question-layout {
  display: flex;
  height: calc(100vh - 70px);
  background-color: white;
  font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;

  .left-sidebar {
    width: 345px;
    flex-shrink: 0;
    border-right: 1px solid #f0f0f0;
    background-color: white;
  }

  .main-content {
    flex: 1;
    min-width: 0;
    background-color: white;
  }
}



@media screen and (max-width: 1440px) {
  .question-layout {
    .left-sidebar {
      width: 300px;
    }
  }
}
</style>
