<template>
  <div class="project">
    <div v-if="isLayerVisible" class="overlay"></div>
    <div class="left">
      <el-scrollbar style="height: calc(120%)" class="scroll" :class="{ show: isShow }">
        <div class="layer" :class="{ show: isShow }">
          <div
            style="
              display: flex;
              border-bottom: 2px solid #333;
              margin-bottom: 5px;
              padding-bottom: 5px;
            "
          >
            <div style="font-size: 20px">{{ '主题' }}</div>
            <div class="close-btn" @click="closeLayer" style="margin-left: auto">
              <el-icon :size="18"><Close /></el-icon>
            </div>
          </div>

          <el-radio-group v-model="selectedTitle">
            <div class="layer-line">
              <div class="layer-line-text">全部</div>
              <el-radio :label="'all'" @change="handleSelectAll">
                <template></template>
              </el-radio>
            </div>
            <div style="margin-top: 10px">
              <el-input
                v-model="searchTerm"
                placeholder="搜索主题"
                prefix-icon="el-icon-search"
                clearable
                style="margin-bottom: 10px; width: 228px; margin-right: 10px"
              /><el-icon :size="16"><Search /></el-icon>
            </div>

            <template v-for="theme in filteredThemes" :key="theme.areaCode">
              <div class="layer-line">
                <div class="layer-line-text">{{ theme.title }}</div>
                <el-radio :label="theme.areaCode" @change="() => handleSelect(theme)">
                  <template></template>
                </el-radio>
              </div>
            </template>
          </el-radio-group>        
        </div>
      </el-scrollbar>

      <div class="card" style="margin-top: 0;margin-bottom: 40px">
        <div class="card-title-line" @click="handleFold(1)">
          <h3 class="card-title">类型</h3>
          <div class="card-icon">
            <el-icon class="fold">
              <ArrowDown :class="isMenuShow[1] ? 'rotate-icon' : 'origin-icon'" />
            </el-icon>
          </div>
        </div>
        <div class="card-select" v-show="isMenuShow[1]">
          <div class="select">
            <span>案例学习</span>
            <el-radio :label="2" v-model="params.prjType[0]" @change="handlePrjTypeChange">
              <template></template>
            </el-radio>
          </div>
          <div class="select">
            <span>领域学习</span>
            <el-radio :label="5" v-model="params.prjType[0]" @change="handlePrjTypeChange">
              <template></template>
            </el-radio>
          </div>
        </div>
      </div>
      <div class="card theme">
        <div
          class="card-title-line"
          :style="isShow ? 'cursor: default' : ''"
          @click="handleFold(0)"
        >
          <h3 class="card-title">主题</h3>
          <div class="card-icon">
            <span class="more" @click.stop @click="handleFoldMoreTheme" v-show="isMenuShow[0] && params.prjType[0] !== 5"
              >更多</span
            >
            <el-icon class="fold">
              <ArrowDown :class="isMenuShow[0] ? 'rotate-icon' : 'origin-icon'" />
            </el-icon>
          </div>
        </div>
        <div class="card-select" v-show="isMenuShow[0]">
          <div class="select">
            <span>全部</span>
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="outSelectAll">
              <template></template
            ></el-checkbox>
          </div>
          <el-checkbox-group v-model="params.themes">
            <div class="select" v-if="isSelect">
              <template v-for="theme in selectedThemes" :key="theme.areaCode"
                ><span>{{ theme.title }}</span>
                <el-checkbox :label="theme.areaCode" @change="handleCheckboxChange(theme)">
                  <template></template> </el-checkbox
              ></template>
            </div>
          </el-checkbox-group>
        </div>
      </div>
      <div class="card" v-if="params.prjType[0] == 2">
        <div class="card-title-line" @click="handleFold(2)">
          <h3 class="card-title">形式</h3>
          <div class="card-icon">
            <el-icon class="fold">
              <ArrowDown :class="isMenuShow[2] ? 'rotate-icon' : 'origin-icon'" />
            </el-icon>
          </div>
        </div>
        <div class="card-select" v-show="isMenuShow[2]">
          <el-checkbox-group v-model="params.formType" :min="1">
            <div class="select">
              <span>视频 </span>
              <el-checkbox :label="1" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
            <div class="select">
              <span>文稿</span>
              <el-checkbox :label="2" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>
      <div class="card">
        <div class="card-title-line" @click="handleFold(3)">
          <h3 class="card-title">价格</h3>
          <div class="card-icon">
            <el-icon class="fold">
              <ArrowDown :class="isMenuShow[3] ? 'rotate-icon' : 'origin-icon'" />
            </el-icon>
          </div>
        </div>
        <div class="card-select" v-show="isMenuShow[3]">
          <el-checkbox-group v-model="params.price" :min="1">
            <div class="select">
              <span>付费</span>
              <el-checkbox :label="1" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
            <div class="select">
              <span>免费</span>
              <el-checkbox :label="2" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>
      <!-- <div class="card">
        <div class="card-title-line" @click="handleFold(4)">
          <h3 class="card-title">排序</h3>
          <div class="card-icon">
            <el-icon class="fold">
              <ArrowDown :class="isMenuShow[4] ? 'rotate-icon' : 'origin-icon'" />
            </el-icon>
          </div>
        </div>
        <div class="card-select" v-show="isMenuShow[4]">
          <div class="select">
            <span>难度由高到低</span>
            <el-radio v-model="params.order" :label="1" @change="getNewPrjList">
              <template></template>
            </el-radio>
          </div>
          <div class="select">
            <span>难度由低到高</span>
            <el-radio v-model="params.order" :label="2" @change="getNewPrjList">
              <template></template>
            </el-radio>
          </div>
        </div>
      </div> -->
    </div>
    <div class="right">
      <div class="search-result">
        <span v-show="keyword">您筛选的关键字 : "{{ keyword }}"，</span>
        已为您搜索到了 {{ total }} 个结果。
      </div>
      <el-skeleton style="width: 300px" :rows="5" :loading="loading" :throttle="100" animated>
        <template #default>
          <div class="list">
            <GoodsCard
              v-for="(video, idx) in goodsList"
              :key="'_' + idx"
              v-bind="video"
              :isFree="video.isFree"
            ></GoodsCard>
          </div>
        </template>
        <template #template>
          <div class="list">
            <div v-for="i in 9" :key="'_' + i">
              <el-skeleton-item variant="image" style="width: 300px; height: 225px" />
              <div>
                <el-skeleton-item variant="p" style="width: 50%" />
                <el-skeleton-item variant="p" style="width: 100%" />
                <el-skeleton-item variant="p" style="width: 100%" />
                <el-skeleton-item variant="p" style="width: 50%" />
                <el-skeleton-item variant="p" style="width: 100%" />
                <div style="display: flex; align-items: center; justify-items: space-between">
                  <el-skeleton-item variant="text" style="margin-right: 16px" />
                  <el-skeleton-item variant="text" style="width: 30%" />
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>

      <div class="pagination-block" v-if="showMore">
        <span class="footerBtn" @click="getMoreFn" id="getMoreFn">
          <span class="myicon"></span>
          加载更多
        </span>
      </div>
      <div class="pagination-block" v-else v-if="params.current != 1">
        <span>暂无更多内容</span>
      </div>
    </div>
  </div>
  <BackTop></BackTop>
</template>

<script setup lang="ts">
import GoodsCard from '@/components/GoodsCard.vue';
import BackTop from '@/components/Backtop.vue';
import { getPrjThemeListApi, getPrjListApi } from '@/apis/classification';
import type { ParamsItf } from '@/apis/classification';
import { PrjType, PrjForm } from '@/types/project';
import { ArrowDown, Close, Search } from '@element-plus/icons-vue';
import { ElRadioGroup, ElRadio } from 'element-plus';

import 'element-plus/dist/index.css';

const route = useRoute();
const isShow = ref(false);
const loading = ref(true); // 骨架屏
interface theme {
  areaCode: string;
  title: string;
}
const keyword = ref();
const themeList = ref<theme[]>([]); // 主题列表
const showThemeList = ref<theme[]>([]);
const selectedThemes = ref<theme[]>([]);
const isSelect = ref(false);
const goodsList = ref([]); // 项目列表
const total = ref(0);
const checkAll = ref(true);
const isIndeterminate = ref(false);
const topicShow = ref(false);
const showMore = ref(false);
// const current = ref(1); // 当前第几页
// const limit = ref(9); // 一页几个数据
const params = ref<ParamsItf>({
  themes: [], // 里面放的是项目的areaCode
  formType: [PrjForm.video, PrjForm.draft],
  price: [1, 2], // 付费1，免费2
  order: 1, // descend降序1， ascend升序2
  //prjType: [PrjType.klgExplain, PrjType.case, PrjType.exam, PrjType.area],
  prjType: [2],//案例学习2，领域学习5
  current: 1,
  limit: 9,
  keyword: keyword.value
});
// 转换params
// @ts-ignore
const convertParamsFn = (data) => {
  const { themes, formType, price, prjType, keyword } = data;

  //...data,
  return {
    limit: data.limit,
    current: data.current,
    theme: themes.length == 1 ? themes[0] : '',
    form: formType.length == 2 ? "" : Number(formType),
    price: price.length == 2 ? "" : price[0] == 1 ? "1" : "0",
    type: prjType[0] == 2 ? 2 : 5,
    keyword: keyword
  };
};

const isLayerVisible = ref(false);

const closeLayer = () => {
  //todo
  if (params.value.themes.length == 0) {
    ElMessage({
      message: '至少选择一个主题',
      type: 'warning'
    });
    return;
  }
  isLayerVisible.value = false;
  isShow.value = false;
};

//浮窗默认选择“全选”
const selectedTitle = ref('all');
const searchTerm = ref<string>('');
const filteredThemes = computed(() => {
  return showThemeList.value.filter((theme) => theme.title.includes(searchTerm.value));
});

// 重置状态
const resetParams = () => {
  params.value.current = 1;
  params.value.limit = 9;
  params.value.keyword = keyword.value;
  totalPage.value = 0;
  total.value = 0;
};
//全选
const selectAll = () => {
  params.value.themes = showThemeList.value.map((t) => t.areaCode);
  isIndeterminate.value = false;
};
const handleSelectAll = async () => {
  if (selectedTitle.value == 'all') {
    selectedThemes.value = [];
    selectAll();
    isSelect.value = false;
    isIndeterminate.value = false;
    checkAll.value = true;
    closeLayer();
    await getNewPrjList();
  } else {
    params.value.themes = [];
  }
};
//菜单下的全部选项变化控制
const outSelectAll = () => {
  if (params.value.themes.length == showThemeList.value.length) {
    checkAll.value = true;
    isIndeterminate.value = false;
    return;
  } else {
    isSelect.value = false;
    checkAll.value = true;
    selectedTitle.value = 'all';
    isIndeterminate.value = false;
    selectedThemes.value = [];
    selectAll();
    getNewPrjList();
  }
};
//根据用户选择主题更新全选、半选状态
const handleSelect = async (theme: theme) => {
  checkAll.value = false;
  isIndeterminate.value = true;

  selectedThemes.value = [];
  selectedThemes.value.push(theme);
  isSelect.value = true;

  params.value.themes = [];
  params.value.themes[0] = theme.areaCode;
  
  // 当选中主题时，取消领域学习的选择
  params.value.prjType = params.value.prjType.filter(type => type !== 5); // 移除领域学习(5)
  if (params.value.prjType.length === 0) {
    params.value.prjType = [2]; // 至少保留案例学习
  }

  closeLayer();
  await getNewPrjList();
};

const handleCheckboxChange = async (theme: theme) => {
  // 如果取消选中已选择的主题
  if (!params.value.themes.includes(theme.areaCode)) {
    checkAll.value = true;
    selectedTitle.value = 'all';
    isIndeterminate.value = false;
    isSelect.value = false;
    selectedThemes.value = [];
    selectAll();
  }
  await getNewPrjList();
};

const handleFoldMoreTheme = () => {
  //params.value.themes = [];

  if (checkAll.value == false && isIndeterminate.value == false) {
    ElMessage({
      message: '至少选择一个主题',
      type: 'warning'
    });
    return;
  }
  if (isShow.value) {
    //收起状态
    getNewPrjList();
    topicShow.value = true;
    if (checkAll.value == true) topicShow.value = false;
  } else {
    topicShow.value = false;
  }
  isShow.value = !isShow.value;
  isLayerVisible.value = !isLayerVisible.value;
};
// 刷新列表
const totalPage = ref(0);
const getNewPrjList = async () => {
  resetParams();
  loading.value = true;
  // alert("k is " + keyword.value)
  // alert("pk is " + params.value.keyword)
  const data = convertParamsFn(params.value);
  const res = await getPrjListApi(data);
  loading.value = false;
  // 映射字段名称以匹配组件期望的属性
  goodsList.value = res.data.records.map(item => ({
    ...item,
    countWord: item.wordsCount
  }));
  total.value = res.data.total;
  totalPage.value = Math.ceil(res.data.total / res.data.limit);
  if (params.value.current < totalPage.value) {
    showMore.value = true;
  } else {
    showMore.value = false;
  }
  // if (res.data.total == total.value) {
  //   noMoreContent();
  // }
};
// 加载更多
const getMoreFn = async () => {
  params.value.current += 1;
  const data = convertParamsFn(params.value);
  const res = await getPrjListApi(data);
  // 映射字段名称以匹配组件期望的属性
  const mappedRecords = res.data.records.map(item => ({
    ...item,
    countWord: item.wordsCount
  }));
  // @ts-ignore
  goodsList.value.push(...mappedRecords);
  total.value = res.data.total;
  totalPage.value = Math.ceil(res.data.total / res.data.limit);
  if (params.value.current < totalPage.value) {
    showMore.value = true;
  } else {
    showMore.value = false;
  }
};

const isMenuShow = ref([true, true, true, true, true]);

const handleFold = (index: number) => {
  if (index == 0) {
    if (isShow.value) return; // 如果更多菜单被打开了，不许折叠主题菜单
    showThemeList.value = showThemeList.value == themeList.value ? [] : themeList.value;
  }
  isMenuShow.value[index] = !isMenuShow.value[index];
};

const handlePrjTypeChange = () => {
  if (params.value.prjType[0] === 5) {
    // 选择领域学习时，清空主题选择
    params.value.themes = [];
    selectedThemes.value = [];
    isSelect.value = false;
    checkAll.value = true;
    isIndeterminate.value = false;
    selectedTitle.value = 'all';
    topicShow.value = false;
  } else {
    // 选择案例学习时，恢复主题选择
    selectAll();
  }
  getNewPrjList();
};

onMounted(async () => {
  keyword.value = route.query.keyword;
  // alert("rqk is " + keyword.value);
  const res = await getPrjThemeListApi();
  console.log(res);
  // @ts-ignore
  params.value.themes = res.data.list.map((t) => t.areaCode);
  themeList.value = res.data.list;
  showThemeList.value = res.data.list;
  checkAll.value = true;
  params.value.themes[0] = '';
  selectedTitle.value = 'all';
  await getNewPrjList();
});
watch(
  () => route.query.keyword,
  async (newValue, oldValue) => {
    keyword.value = newValue;
    await getNewPrjList();
  }
);
watch(
  () => checkAll.value,
  async (newValue, oldValue) => {
    console.log('ca:', newValue);
  }
);
</script>

<style scoped lang="less">
.el-checkbox {
  /* 重写element */
  // --el-color-primary: var(--color-theme-project);
  --el-color-primary: white;
  --el-color-white: var(--color-black);
  --el-checkbox-checked-input-border-color: var(--color-black);
}
.project {
  /* 重写element */
  --el-color-primary: var(--color-theme-project);

  width: var(--width-fixed--project);
  padding: var(--padding-box);
  margin: 0 auto 0;
  display: flex;
  align-items: flex-start;

  .rotate-icon {
    transition: transform 0.5s ease-in-out;
    transform: rotate(180deg);
  }
  .origin-icon {
    transition: transform 0.5s ease-in-out;
    transform: rotate(0deg);
  }
  .left {
    width: 310px;
    padding-right: 10px;
    position: relative;

    // card 标题
    .card-title-line {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 5px;
      border-bottom: 1px solid rgb(121, 121, 121);

      .card-title {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        font-family: var(--title-family);
      }
      //it is so ironic to inaugurate for such a maligant people
      //don't be so rash

      .card-icon {
        display: flex;
        align-items: center;

        .more {
          font-size: 12px;
          cursor: pointer;

          &:hover {
            font-weight: 600;
          }
        }

        .el-icon {
          margin-left: 5px;
        }
      }
    }

    .card-select {
      .select {
        font-size: 16px;
        font-family: var(--text-family);
        display: flex;
        align-items: center;
        justify-content: space-between;
        // margin-top: 15px;
      }
    }

    .card {
      margin-top: 40px;
    }

    .card:first-of-type {
      margin-top: 0px;
    }

    .theme {
      position: relative;
      margin-top: 0px;
    }

    .layer {
      //position: absolute;
      background-color: #fff;
      padding: 9px 0px 0px 9px;
      margin-right: 10px;
      width: calc(100% - 10px);
      height: calc(150% - 20px);
      //transform: translateY(30px);

      //display: none;
      //z-index: 1000;

      .layer-line {
        display: flex;
        align-items: center;

        // line-height: 25px;
        // height: 25px;
        .layer-line-text {
          width: 228px;
          height: 25px;
          line-height: 25px;
          color: var(--color-black);
          background-color: #f3f3f3;
          padding-left: 10px;
          border-radius: 10px;
          margin-right: 10px;
          cursor: pointer;
          border: 1px solid transparent;
          display: flex;
          align-items: center;
          font-size: 14px;

          &:hover {
            color: var(--color-theme-project);
            background-color: var(--color-second);
          }
        }
      }
    }

    .layer.show {
      display: block;
      //overflow: auto;
      //   opacity: 1;
    }
    .scroll {
      z-index: 1000;
      position: absolute;
      display: none;
      background-color: #fff;
      padding: 0;
      margin: 0;
    }
    .scroll.show {
      display: block;
      padding: 0;
      margin: 0;
    }
  }

  .right {
    flex-grow: 1;
    margin-left: 20px;

    .search-result {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }

    .list {
      margin-top: 20px;
      display: grid;
      grid-template-columns: repeat(3, 300px); // 重复4次300px
      grid-column-gap: 34px; // 列之间的距离 34px
      grid-row-gap: 10px; // 行之间的距离 10px
    }

    .pagination-block {
      display: flex;
      text-align: center;
      justify-content: center;
      color: var(--color-theme-project);
      font-family: var(--text-family);
      font-weight: 400;
      font-size: 14px;
      height: 20px;
      line-height: 20px;
      margin: 10px 0;

      .footerBtn {
        width: 90px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        .myicon {
          width: 14px;
          height: 12px;
          margin-right: 5px;
          background-image: url('@/assets/images/project/u3964.svg');
        }
        &:hover {
          font-weight: bold;
        }
      }
    }
  }
  .fold {
    cursor: pointer;
  }
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
}
</style>
