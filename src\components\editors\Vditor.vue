<script setup lang="ts">
// 1.1 引入Vditor 构造函数
import Vditor from 'vditor';
// 1.2 引入样式
import 'vditor/dist/index.css';
import { ref, onMounted, defineProps, defineExpose, defineModel, watch, withDefaults } from 'vue';

interface VeditorProps {
  disabled?: boolean;
  height?: number;
  mode?: 'ir' | 'wysiwyg' | 'sv';
  placeholder?: string;
}
const props = withDefaults(defineProps<VeditorProps>(), {
  disabled: false,
  height: 300,
  mode: 'ir',
  placeholder: '请输入'
});

// 2. 获取DOM引用
const vditor = ref<Vditor>();
const model = defineModel<string>(); // 指定model的类型为string
const isReady = ref(false);

// 监听model变化
watch(
  () => model.value,
  (newVal) => {
    if (vditor.value && isReady.value && newVal !== vditor.value.getValue()) {
      vditor.value.setValue(newVal || '');
    }
  },
  { deep: true }
);

// 3. 在组件初始化时，就创建Vditor对象，并引用
onMounted(() => {
  vditor.value = new Vditor('vditor', {
    cdn: '',
    height: props.height,
    width: '100%',
    mode: props.mode,
    cache: {
      enable: false
    },
    minHeight: props.height,
    toolbar: [
      'headings',
      'bold',
      'italic',
      'strike',
      'link',
      '|',
      'list',
      'ordered-list',
      'check',
      'outdent',
      'indent',
      '|',
      'quote',
      'line',
      'code',
      'inline-code',
      'table',
      '|',
      // 'upload',
      'undo',
      'redo',
      '|',
      'fullscreen',
      // 'edit-mode'
      'outline'
      // 'code-theme',
      // 'content-theme',
      // 'export'
      // 'help',
      // 'emoji'
    ],
    hint: {
      extend: []
    },
    after: () => {
      isReady.value = true;
      if (model.value) {
        vditor.value?.setValue(model.value);
      }
    },
    input: (value: string) => {
      model.value = value;
    },
    placeholder: props.placeholder,
    preview: {
      delay: 500,
      mode: 'both',
      actions: []
    }
  });

  // 设置禁用状态
  if (props.disabled) {
    vditor.value.disabled();
  }
});

// 实现与 classicCKEditor 相同的方法
const getData = (): string => {
  if (vditor.value) {
    return vditor.value.getValue();
  }
  return '';
};

const setData = (value: string, dis?: boolean) => {
  if (vditor.value && isReady.value) {
    vditor.value.setValue(value);
    if (dis !== undefined) {
      if (dis) {
        vditor.value.disabled();
      } else {
        vditor.value.enable();
      }
    }
  }
};

// 暴露与 classicCKEditor 相同的方法
defineExpose({
  getData,
  setData
});
</script>

<template>
  <!-- 指定一个容器 -->
  <div id="vditor" class="vditor-container"></div>
  <!-- 添加一个按钮来获取内容 -->
  <!-- <button @click="getContent">获取内容</button> -->
</template>

<style scoped>
.vditor-container {
  border: 1px solid rgb(220, 223, 230);

  border-radius: 5px;
  box-sizing: border-box;
  height: v-bind('props.height + "px"');
  display: flex; /* 添加flex布局 */
  flex-direction: column; /* 垂直方向排列 */
}

/* 移除固定高度计算 */
:deep(.vditor-content) {
  flex: 1; /* 自动填充剩余空间 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 防止内容溢出 */
}

:deep(.vditor-reset) {
  padding: 0 10px !important;
  flex: 1; /* 自动填充剩余空间 */
  overflow: auto; /* 内容过多时显示滚动条 */
  width: 100% !important;
  /* 修复列表样式 */
  & ul {
    list-style-type: disc !important;
    padding-left: 20px !important;
  }

  & ol {
    list-style-type: decimal !important;
    padding-left: 20px !important;
  }

  /* 修复列表项标记 */
  & li::marker {
    content: initial !important;
    color: initial !important;
  }
  & img {
    width: 100%;
  }
}
/* 编辑区域也使用flex布局 */
:deep(.vditor-ir),
:deep(.vditor-wysiwyg),
:deep(.vditor-sv) {
  flex: 1; /* 自动填充剩余空间 */
  min-height: 0; /* 防止内容溢出 */
  overflow: auto; /* 内容过多时显示滚动条 */
}

/* 确保所有子元素都使用border-box盒模型 */
:deep(*) {
  box-sizing: border-box;
}
</style>
