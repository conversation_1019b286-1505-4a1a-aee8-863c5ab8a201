<template>
  <div class="main-wrapper" :class="{ big: isBig, map: !isMap }">
    <div class="left-video-wrapper">
      <!-- 左侧的文稿 -->
      <div class="left-main">
        <div class="header-wrapper" v-if="wordContent">
          <div class="left-header" ref="header">
            <div class="title-row" style="display: flex; align-items: center; justify-content: space-between;">
              <div class="title">{{ prjInfo.title }}</div>
              <el-button class="ask-btn" type="info" size="small" plain @click="askDialogVisible = true" style="border: unset; margin-left: 10px;">
                <el-icon style="margin-right: 4px; border: unset;"><QuestionFilled /></el-icon>
                有看不懂的地方点我提问题
              </el-button>
            </div>
<!--            <div class="base-info">-->
<!--              <div class="creater">-->
<!--                <img :src="prjInfo.userCoverPic" class="avatar" />-->
<!--                <div class="name">{{ prjInfo.userName }}</div>-->
<!--              </div>-->
<!--              <div class="time">{{ prjInfo.createTime }}</div>-->
<!--              <el-popover-->
<!--                v-model:visible="descriptionVisible"-->
<!--                placement="bottom-start"-->
<!--                trigger="click"-->
<!--                width="200"-->
<!--                class="custom-popover"-->
<!--              >-->
<!--                &lt;!&ndash; 气泡卡片内容 &ndash;&gt;-->
<!--                <div class="popover-content">-->
<!--                  <span v-html="prjTargetObj.description"></span>-->
<!--                  <span v-html="prjTargetObj.purpose"></span>-->
<!--                </div>-->

<!--                &lt;!&ndash; 触发内容 &ndash;&gt;-->
<!--                <template #reference>-->
<!--                  <div class="function-tag">项目介绍</div>-->
<!--                </template>-->
<!--              </el-popover>-->
<!--            </div>-->
          </div>
        </div>

        <div>
          <template v-if="wordContent">
            <draftWrapperScript
              :wordContent="wordContent"
              ref="manuScript"
              class="lineWordContent"
            ></draftWrapperScript>
          </template>
        </div>
      </div>
    </div>
    <AskQuestionDialog
      v-if="askDialogVisible"
      :prj-info="prjInfo"
      :transmit-spuId="props.transmitSpuId || ''"
      :transmit-chapterId="props.transmitChapterId || ''"
      :transmit-uniqueCode="props.transmitUniqueCode"
      :type="1"
      @close="askDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import draftWrapperScript from './draftWrapperScript.vue'
import { getManuProjectSectionApi, getPrjDetailApi } from '@/apis/learning';
//import PrjManuscript from '@/views/klgdetail/PrjManuscript.vue';
import { defineExpose } from 'vue';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { useLearningStore } from '@/stores/learning';
import { PrjType } from '@/types/project';
import PrjTag from '@/views/learning/components/PrjTag.vue';
import anime from 'animejs/lib/anime.es.js';
import type { PrjTag as PrjTagType } from '@/types/learning';
import { useRouter } from 'vue-router';
import PrjManuScript from './draftWrapperScript.vue';
import AskQuestionDialog from '../AskQuestionDialog.vue';
import { QuestionFilled } from '@element-plus/icons-vue';

const learningStore = useLearningStore();
const router = useRouter();

const isSectionMenuShow = ref(false);
const manuScript = ref<InstanceType<typeof PrjManuscript>>();

const route = useRoute();
const spuId = route.query.spuId as string;
const chapterId = route.query.chapterId as string;

const isMap = ref<boolean>(true);
const isBig = ref<boolean>(false);

const wordContent = ref();

const prjId = ref();
const uniqueCode = ref();
const curChapterId = ref();
const projectDetailData = shallowRef<any>();
const chapterList = shallowRef<any[]>();
const activeIndex = ref();
const contentId = ref<string | number>();

const { handleChapterChange } = userBehaviour(
  PrjForm.draft,
  activeIndex,
  curChapterId
);

const klg = inject('klg') as Ref;
const prjType = inject('prjType') as Ref;
const learnedPct = ref();
const graspKlgPct = ref();
const targetKlgs = ref();

const saveType = inject('saveType') as Ref;

const askDialogVisible = ref(false);

const props = defineProps(['prjInfo', 'targetKlgs', 'prjTargetObj', 'wordContent', 'transmitSpuId', 'transmitChapterId', 'transmitUniqueCode']);

const tagsVisible = ref(false);
const descriptionVisible = ref(false);

const prjTargetObj = computed(() => props.prjTargetObj);
const tags = computed(() => props.prjInfo?.prjTags || []);

const header = ref();

const assessmentId = ref();

function handleClose(event: MouseEvent) {
  if (header.value && header.value.contains(event.target as HTMLElement)) {
    return;
  }
  descriptionVisible.value = false;
  tagsVisible.value = false;
}

function handleExpandTag() {
  tagsVisible.value = !tagsVisible.value;
}

onMounted(() => {
  document.addEventListener('click', handleClose);
  watch(
    () => descriptionVisible.value || tagsVisible.value,
    () => {
      if (descriptionVisible.value || tagsVisible.value) {
        anime({
          targets: header.value,
          backgroundColor: {
            value: '#F2F2F2',
            duration: 300,
            easing: 'linear'
          }
        });
      } else {
        anime({
          targets: header.value,
          backgroundColor: {
            value: '#FFFFFF',
            duration: 300,
            easing: 'linear'
          }
        });
      }
    }
  );
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClose);
});

onMounted(() => {
  wordContent.value = props.wordContent;
});

watch(
  () => props.wordContent,
  (newVal) => {
    wordContent.value = newVal;
  },
  { immediate: true }
);

provide(
  'isBig',
  computed(() => toValue(isBig))
);

provide(
  'prjSectionInfo',
  computed<{ chapterId: string; prjId: string; contentId: string }>(() => ({
    chapterId: curChapterId.value ?? '',
    uniqueCode: uniqueCode.value ?? '',
    contentId: String(contentId.value ?? ''),
    prjId: prjId.value ?? ''
  }))
);

const transmitUniqueCode = computed(() => props.prjInfo?.uniqueCode || '');
</script>

<style scoped lang="less">
:deep(.sectionMenu) {
  margin: 10px;
  cursor: pointer;
  transform: scale(2);
}
.sectionMenuContent {
  position: absolute;
  transition: width 0.5s ease-in-out;
  left: 90px;
  border-radius: 5px;
  z-index: 10;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;

  border: 1px solid rgb(220, 223, 230);
  border-radius: 5px;
  box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 5px;
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background: var(--color-grey);
  }
  .section {
    height: 41px;
    font-size: 14px;
    padding: 0 10px;
    background-color: white;
    display: flex;
    align-items: center;
    font-family: var(--text-family);
    color: var(--color-black);
    cursor: pointer;
    &:hover {
      background-color: #f2f2f2;
    }
    .sectionTitle {
      margin-left: 5px;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
.main-wrapper {
  display: block;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

.left-video-wrapper {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  //height: calc(100vh - 60px - 70px - 50px);
  scroll-snap-align: start;
  transition: width 1s;
  overflow: hidden;

  .left-title-list {
    width: 25px;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title-item {
      width: 90%;
      cursor: pointer;
      height: 40px;
      display: flex;
      align-items: center;
      padding-left: 20px;
      font-size: 14px;
      &:hover {
        cursor: pointer;
        font-weight: 700;
        background-color: #f2f2f2;
      }
      margin-top: 4px;
    }

    .title {
      font-size: 12px;
      font-weight: 400;
      color: #797979;
    }

    .title-num {
      width: 25px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      background-color: #f2f2f2;
      border-radius: 5px 0px 0px 5px;
      font-size: 13px;
      font-weight: 400;
      color: #333333;
      margin-bottom: 2px;
      cursor: pointer;

      &.active {
        background-color: var(--color-theme-project);
        color: rgb(254, 254, 246);
        border-radius: 5px 0px 0px 5px;
        box-shadow: rgba(0, 85, 121, 0.376) 0px 3px 3px 0px;
      }
    }
  }

  .left-main {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block !important;
    flex: 1;
    height: 100%;
    overflow: hidden;
    .video-title {
      height: 25px;
      font-size: 18px;
      font-weight: 400;
      color: var(--color-black);
      font-family: var(--text-family);
      margin-bottom: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .mapBtn {
        height: 20px;
        border: 1px solid var(--color-theme-project);
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 10px;
        .text {
          font-size: 12px;
          color: var(--color-theme-project);
          margin-right: 5px;
          cursor: pointer;
          white-space: nowrap;
        }
        &:hover {
          cursor: pointer;
          background-color: var(--color-second);
        }
      }
      .icon {
        cursor: pointer;
      }

      .icon-wrapper {
        width: 16px;
        height: 12px;
        margin-right: 5px;
        background: url('@/assets/svgs/u2985.svg') no-repeat center/contain;
        cursor: pointer;

        &:hover {
          background-image: url('@/assets/svgs/u4176.svg');
        }
      }
    }

    .video {
      flex: 1;
      position: relative;

      .coverPic-wrapper {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: var(--color-black);
        background-repeat: no-repeat;
        background-size: cover;
      }

      .video-btn-wrapper {
        width: 50px;
        height: 50px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
      }

      .expand-logo {
        position: absolute;
        right: 10px;
        bottom: 10px;
        cursor: pointer;

        &:hover {
          font-weight: 500;
        }
      }
    }

    .video-footer-info {
      height: 40px;
      display: flex;
      align-items: center;
      background-color: #ffffff;
      box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
      border: 1px solid rgb(242, 242, 242);
      padding-left: 10px;
      width: 100%;
      position: relative;

      .video-footer {
        vertical-align: middle;
        transform: translateY(-3px);
      }

      .footer-logo-wrapper {
        width: 90%;
        display: flex;
        align-items: center;
        position: absolute;
      }

      .footer-title {
        font-size: 18px;
        font-weight: 300;
        color: #333333;
        margin-left: 17px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    
    .lineWordContent {
      width: 100%;
      max-width: 100%;
      overflow: hidden;
      flex: 1;
    }
  }
  .header-wrapper,
  .left-header {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block !important;
  }
  .left-header {
    //background-color: #f2f2f2;
    .title {
      font-size: 18px;
      font-weight: 700;
      margin: 10px 0px 10px 0;
    }
    .base-info {
      font-size: 12px;
      display: flex;
      align-items: center;
      color: var(--color-deep);
      .creater {
        display: flex;
        align-items: center;
        .avatar {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
        .name {
        }
      }
      .time {
        margin: 0 10px;
      }
      .function-tag {
        margin: 0 10px;
        padding: 0 10px;
        border-radius: 10px;
        border: 1px solid var(--color-deep);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;

        &:hover {
          background-color: var(--color-inactive-project);
          cursor: pointer;
        }
      }
    }
  }
  .right {
    .prj-study-info {
    }
    .tags {
      align-items: center;
    }
  }
  .tags {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    margin-left: 15px;
    .prj-tag {
      width: 80px;
      height: 20px;
      margin: 5px 5px;
      border-radius: 10px;
      color: var(--color-black);
      background-color: var(--color-inactive-project);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-sizing: border-box;
      transition: border 0.3s ease;

      .tag-content {
        max-width: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:hover {
        border: 1px solid var(--color-deep);
      }
    }
    .more {
      width: 18px;
      height: 18px;
      margin: 5px 5px;
      border-radius: 10px;
      color: var(--color-black);
      background-color: var(--color-inactive-project);
      &:hover {
        border: 1px solid var(--color-deep);
      }
    }
  }
}

.right-content {
  height: calc(100vh - 60px - 70px - 50px);
  flex-basis: 36%;
  scroll-snap-align: start;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .right-map {
    width: 100%;
    height: 58%;
    position: relative;

    .close-icon {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
    }
  }
  .questionList {
    width: 100%;
    height: 40%;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 2%;
    overflow: auto;
    .question-item {
      font-size: 13px;
      width: 100%;
      min-height: 50px;
      background-color: white;
      border-radius: 4px;
      padding-left: 6px;
      margin-top: 8px;
      box-sizing: border-box;
      padding: 5px;
      display: flex;
      justify-content: space-between;
      &:hover {
        cursor: pointer;
      }
      .title-line {
        display: flex;
        align-items: center;
      }
    }
  }

  .content-wrapper {
    background-color: #f2f2f2;
    width: 100%;
    height: 100%;
  }
}

.main-wrapper.big {
  display: block;

  .left-video-wrapper {
    width: 100%;
    min-height: 100vh;
  }

  .right-content {
    box-sizing: border-box;
    width: calc(100% - 25px);
    margin-top: 10px;
    min-height: 100vh;
    margin-left: 25px;
    height: 100%;

    &:deep(.text-wrapper) {
      height: 800px !important;
    }
  }
}
.main-wrapper.map {
  justify-content: center;
  .left-video-wrapper {
    width: 1350px;
  }
}
.custom-popover {
  .el-popper {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #dcdfe6;
    padding: 10px;
  }

  .el-popper__arrow {
    border-color: transparent transparent #f4f4f9 transparent;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
