<template>
  <!-- 学习信息组件二次封装 -->
  <div class="study-info">
    <div class="learn-footer-info">
      <img style="width: 20px; height: 20px" :src="info.userCoverPic" class="logo" />
      <span class="wjby">{{ info.userName }}</span>
    </div>
    <PrjStudyInfo
      :knowledgeSum="info.klgCount"
      :studyPercent="computePercent(info.learned, info.klgCount)"
      :acquaintePercent="computePercent(info.graspKlg, info.klgCount)"
    ></PrjStudyInfo>
    <div class="btn-wrapper">
      <CmpButton v-if="!isTest" class="btn" type="primary" @click="toDetail()">{{
        computePercent(info.learned, info.klgCount) == 0 ? '开始学习' : '继续学习'
      }}</CmpButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import PrjStudyInfo from '@/components/PrjStudyInfo.vue';
import { useLearnStore } from '@/stores/learnintro';
import CmpButton from '@/components/CmpButton.vue';
import { reactive } from 'vue';
import { computePercent } from '@/utils/computeNumber';
import type { BoughtInfoType } from '@/types/case';
import { useProjectStore } from '@/stores/project';
import router from '@/router';

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const emits = defineEmits(['watch']);
const props = defineProps<{
  isTest: boolean;
}>();

const learnStore = useLearnStore();
// 已购买组件传参
const boughtInfo = inject('boughtInfo') as BoughtInfoType;

const latestChapterId = inject('latestChapterId') as number;
// const learnInfo = reactive({
//   klgSum: 12,
//   studyPercent: 0,
//   acquaintePercent: 0
// });
const toDetail = () => {
  // 既然进入了这个组件，其实已经可以理解为这个用户有充分权限学习了吧
  emits('watch');
};
</script>

<style lang="less" scoped>
.study-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .learn-footer-info {
    display: flex;
    align-items: center;

    .wjby {
      font-size: var(--fontsize-small-project);
      font-weight: 400;
      font-family: var(--text-family);
      color: var(--color-deep);
      margin-left: 10px;
    }

    .el-progress--line {
      margin-left: 5px;
      width: 135px;
    }

    &:deep(.el-progress-bar__outer) {
      background-color: rgb(211, 222, 227);
    }

    &:deep(.el-progress__text) {
      color: rgb(0, 85, 121);
    }
  }

  .btn-wrapper {
    width: 105px;
    height: 40px;

    .btn {
      width: 105px;
      height: 40px;
      font-size: var(--fontsize-middle-project);
    }
  }
}
</style>
