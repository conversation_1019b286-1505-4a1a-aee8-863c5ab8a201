// import 'amfe-flexible';
import './assets/main.less';
import piniaPluginPersist from 'pinia-plugin-persist'; //不引入这个插件persist:enable会报错
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import vClickOutside from 'click-outside-vue3';
import hljsVuePlugin from '@highlightjs/vue-plugin';
import VueLazyload from 'vue-lazyload';
import KsgMap from '@endlessorigin/KsgMap';
import '@endlessorigin/KsgMap/css/ksgMap.css';
import '@endlessorigin/select_to_ask/dist/index.css';
import hljs from 'highlight.js';

//引入依赖和语言
import 'highlight.js/styles/agate.min.css';
// import loadingImage from '@/assets/imgs/default1.png';
// import errorImage from '@/assets/imgs/default2.png';

const app = createApp(App);
// for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
//   app.component(key, component);
// }

const store = createPinia();
store.use(piniaPluginPersist); //不引入这个插件persist:enable会报错
app.use(store);
app.use(router);
app.use(vClickOutside);
// app.use(VueLazyload, {
//   preLoad: 1.3,
//   error: errorImage,
//   loading: loadingImage,
//   attempt: 3
// });

app.use(VueLazyload, {
  loading: '/src/assets/myStudyBgDef.png',
  error: '/src/assets/myStudyBgDef.png'
});
app.use(hljsVuePlugin);
app.use(KsgMap);
app.mount('#app');
