<template>
  <!-- 
    学习卡片
    负责显示用户正在学习的课程的信息，可点击跳转
    跳转时要记得初始化仓库的变量，保证后续页面的正常展示
    目前此处只显示案例和领域，后续可根据需求进行相应的判断修改
   -->
  <div class="learing-card">
    <div class="left-img">
      <img style="width: 160px; height: 110px" :src="LCinfo.coverPic" alt="视频封面" />
    </div>
    <div class="mid-info">
      <div class="start-time">学习启动时间：{{ LCinfo.startTime }}</div>
      <h1 class="learn-title" @click="goIntroPage">{{ LCinfo.title }}</h1>
      <div class="learn-process">
        <!-- <div class="knowledge-num">
          <img src="@/assets/images/learn/knowledgenum.svg" />
          {{ LCinfo.klgNumbers }}
        </div> -->
        <div style="font-size: var(--fontsize-small-project); font-weight: 400; color: #797979">
          <span>已掌握/全掌握/目标知识点数: </span>
          <span> {{ LCinfo.masteredKlgCount}} / {{ LCinfo.fullyMasteredKlgCount }} / {{ LCinfo.klgCount }}</span>
        </div>
<!--        <div class="process">-->
<!--          <div>已学习</div>-->
<!--          <el-progress-->
<!--            :color="`&#45;&#45;color-theme-project`"-->
<!--            style="width: 150px; margin-left: 15px"-->
<!--            :percentage="computePercent(LCinfo.learned, LCinfo.klgNumbers)"-->
<!--          />-->
<!--        </div>-->
      </div>

      <div v-if="!isObjectEmpty(LCinfo.currentLearning) && LCinfo.currentLearning.chapterId" class="current-learn">
        <div style="font-size: var(--fontsize-small-project); font-weight: 400; color: #797979; margin-top: 10px">
          当前学习：
        </div>
        <div class="current-learn-point">
          <img
            v-if="LCinfo.currentLearning.prjForm == PrjForm.video"
            style="width: 12px; height: 12px; margin-right: 5px; margin-top: 10px"
            src="@/assets/images/learn/vedioplay.svg"
            alt=""
          />
          <img
            v-else
            style="width: 12px; height: 12px; margin-right: 5px; margin-top: 10px"
            src="@/assets/images/learn/doc.svg"
            alt=""
          />
          <div
            @click="godetailPage"
            style="
              font-size: var(--fontsize-small-project);
              font-weight: 700;
              width: 180px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-top: 10px;
            "
          >
            {{ LCinfo.currentLearning.chapterTitle }}
          </div>
          <div style="margin-left: 10px; font-size: 12px; font-weight: 700">
            <div v-if="LCinfo.currentLearning.prjForm == PrjForm.video">
              {{ LCinfo.currentLearning.endTimeTag }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="right-icon">
      <el-dropdown trigger="click">
        <img style="cursor: pointer" src="@/assets/images/learn/learnmenu.svg" alt="" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleCancel(LCinfo.spuId)"> 取消学习 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { cancelLearnApi } from '@/apis/learnStatistics';
import { computePercent } from '@/utils/computeNumber';
import { useLearnStore } from '@/stores/learnintro';
import { PrjForm, PrjType } from '@/types/project';
import { GoodsType } from '@/types/goods';
const router = useRouter();
const learnStore = useLearnStore();
// 卡片参数
const props = defineProps<{
  LCinfo: {
    coverPic: string;
    graspKlg: number;
    klgNumbers: number; //?
    klgCount: number;
    // lastLearnedTime: string;
    learned: number;
    processEndTime: string;
    // processSectionName: string;
    startTime: string;
    title: string;
    userCoverPic: string;
    userName: string;
    // 传递到下一页的参数
    goodsType: GoodsType;
    //跳转下一页需要用到的参数!!
    //其中
    //    测评跳转至介绍页不跳转至详情页，也需要用learntype进行判断
    prjType: PrjType;
    spuId: string;
    masteredKlgCount: number,
    fullyMasteredKlgCount: number,
    klgCount: number,
    currentLearning: {
      spuId: string; //标志小结的Id
      prjForm: PrjForm;
      endTimeTag: string;
      chapterTitle: string;
      chapterId: number;
    };
  };
}>();

const isObjectEmpty = (obj: any) => {
  return Object.keys(obj).length == 0 && obj?.constructor == Object;
};
// 后续封装跳转介绍页的函数 完成的操作是 1.根据参数请求数据 2. 将数据入库
// 明天学习await逻辑
const goIntroPage = async () => {
  try {
    const { href } = router.resolve({
      path: '/goodIntroduce',
      query: {
        spuId: props.LCinfo.spuId
      }
    });
    window.open(href, '_blank');
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};

const godetailPage = () => {
  try {
    learnStore.setintroData(props.LCinfo.currentLearning.spuId);
    const { href } = router.resolve({
      path: '/learning',
      query: {
        spuId: props.LCinfo.currentLearning.spuId,
        chapterId: props.LCinfo.currentLearning.chapterId
      }
    });
    window.open(href, '_blank');
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};

// 项目信息
const prjInfo = ref({
  title: '',
  description: '',
  coverPic: ''
});
// 定义取消事件  用于更新
const emits = defineEmits(['cancel']);
const handleCancel = (spuId: string) => {
  const params = {
    spuId: spuId
  };
  cancelLearnApi(params)
    .then((res) => {
      //@ts-ignore
      if (res.code == 20000) {
        emits('cancel');
        ElMessage.success('取消学习成功');
      }
    })
    .catch((error) => {
      console.log(error);
      ElMessage.error('取消学习失败');
    });
};
</script>

<style lang="less" scoped>
.learing-card {
  background-color: white;
  margin-top: 5px;
  width: 530px;
  height: 134px;
  display: flex;
  border-radius: 6px;
  box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
  font-size: 13px;

  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 15px;
  padding-right: 17px;

  .left-img {
    top: 0px;
    left:0px;
    width: 170px;
    height: 124px;
    border-radius: 5px;
  }

  .mid-info {
    width: 350px;

    .start-time {
      font-size: var(--fontsize-small-project);
      color: rgb(121, 121, 121);
    }

    .learn-title {
      width: 320px;
      margin-top: 7px;
      font-size: 20px;
      font-weight: 700;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }

    .learn-title:hover {
      color: var(--color-theme-project);
    }

    .learn-process {
      display: flex;
      flex-direction: row;
      width: 100%;
      justify-content: space-between;
      margin-top: 10px;

      .process {
        display: flex;
        flex-direction: row;
      }
    }

    .current-learn {
      width: 100%;
      margin-top: 3px;
      align-items: center;
      display: flex;
      flex-direction: row;

      // justify-content: space-between;
      .current-learn-point {
        cursor: pointer;
        align-items: center;
        display: flex;
        flex-direction: row;
      }

      .current-learn-point:hover {
        color: var(--color-theme-project);
      }
    }

    margin-left: 5px;
  }
}

// 修改element 样式
:global(.el-dropdown-menu__item) {
  --el-dropdown-menuItem-hover-color: var(--color-black);
  --el-dropdown-menuItem-hover-fill: #f2f2f2;
}
</style>
