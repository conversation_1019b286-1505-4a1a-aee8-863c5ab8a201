<template>
  <div ref="warpper" class="warpper hover-scrollbar">
    <!-- 学习进度卡片 -->
    <div class="progress-card">
      <!-- 卡片头部 - 始终显示 -->
      <div class="card-header">
        <div class="header-left">
          <div class="project-info">
            <div>
              <img class="backBtn" @click="handleBack()" src="@/assets/images/prjlearn/u4508.svg" />
              <span class="titlefont title" v-html="displayTitle"></span>
            </div>
            <div class="meta-info">
              <div class="author-info">
                <img :src="displayAuthorPic" class="avatar" />
                <span class="author-name">{{ displayAuthorName }}</span>
              </div>
              <el-icon
                class="expand-arrow"
                :class="{ expanded: progressExpanded }"
                @click="toggleProgressExpand"
              >
                <ArrowDown />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 可折叠的进度内容 -->
      <el-collapse-transition>
        <div v-show="progressExpanded" class="progress-content textfont">
          <!-- 进度圆环区域 -->
          <div class="progress-circles">
            <div class="progress-item">
              <el-progress
                type="circle"
                :percentage="graspKlgPct"
                :width="56"
                :stroke-width="3"
                color="#67c23a"
              />
              <div class="progress-label">
                <span class="label-text textfont">已掌握 {{ displayMasteredKlg }}</span>
              </div>
            </div>
            <div class="progress-item">
              <el-progress
                type="circle"
                :percentage="fullyGraspKlgPct"
                :width="56"
                :stroke-width="3"
                color="#67c23a"
              />
              <div class="progress-label">
                <span class="label-text textfont">全掌握 {{ displayFullyMasteredKlg }} </span>
              </div>
            </div>
          </div>

          <!-- 标签区域 -->
          <div class="tags-section">
            <div class="tags-grid">
              <div v-for="tag in displayTags" :key="tag.id" class="tag-item">
                {{ tag.content }}
              </div>
              <div v-if="tags.length > 4" class="tag-item more-tag" @click="handleExpandTag">
                <el-icon><More /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <div class="divider"></div>

    <!-- 课程列表 -->
    <div class="chapter-section">
      <div class="chapter-list" v-if="displayList && displayList.length > 0">
        <div
          v-for="(item, index) in displayList"
          :key="item.id"
          class="chapter-item"
          :class="{
            active: item.id == currentItemId
          }"
          @click="handleItemClick(item)"
        >
          <div class="chapter-content">
            <div class="textfont chapter-name" :title="item.title">
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-chapters">暂无课程内容</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getPrjMoreInfoApi } from '@/apis/learning';
import type { PrjinfoItf, PrjTag as PrjTagType, Chapter } from '@/types/learning';
import { PrjType } from '@/types/project';
import { useRouter } from 'vue-router';
import { useLearningStore } from '@/stores/learning';
import {
  Lock,
  VideoPlay,
  ArrowDown,
  ArrowLeft,
  ArrowUp,
  More,
  InfoFilled
} from '@element-plus/icons-vue';

// 定义props
const props = defineProps<{
  buyStatus: boolean;
  // 领域相关数据
  areaInfo?: {
    title?: string;
    editorName?: string;
    editorPic?: string;
    masteredKlgCount?: number;
    fullyMasteredKlgCount?: number;
    klgCount?: number;
  };
  // 返回路径信息
  backPath?: string;
  backQuery?: Record<string, any>;
  // 课程列表数据
  courseList?: Array<{
    spuId: string;
    title: string;
    coverPic?: string;
    userName?: string;
    userCoverPic?: string;
  }>;
  // 当前选中的课程ID
  currentCourseId?: string;
}>();

// 定义emits
const emit = defineEmits<{
  chapterChange: [chapterId: number, preview: boolean];
  courseChange: [courseId: string];
  titleChange: [title: string];
}>();

const prjInfo = inject('prjInfo') as Ref<PrjinfoItf>;
const route = useRoute();
const router = useRouter();
const learningStore = useLearningStore();

const spuId = route.query.spuId as string;
const targetKlgs = ref<any>([]);
const klg = reactive({
  klgCount: 0,
  masteredKlgCount: 0,
  fullyMasteredKlgCount: 0
});
const prjTargetObj = ref({
  description: '',
  purpose: ''
});
const graspKlgPct = computed(() => {
  // 优先使用领域数据
  if (props.areaInfo?.masteredKlgCount !== undefined && props.areaInfo?.klgCount) {
    return Math.floor((props.areaInfo.masteredKlgCount / props.areaInfo.klgCount) * 100);
  }
  // 回退到项目数据
  if (!klg.klgCount) return 0;
  return Math.floor((klg.masteredKlgCount / klg.klgCount) * 100);
});
const fullyGraspKlgPct = computed(() => {
  // 优先使用领域数据
  if (props.areaInfo?.fullyMasteredKlgCount !== undefined && props.areaInfo?.klgCount) {
    return Math.floor((props.areaInfo.fullyMasteredKlgCount / props.areaInfo.klgCount) * 100);
  }
  // 回退到项目数据
  if (!klg.klgCount) return 0;
  return Math.floor((klg.fullyMasteredKlgCount / klg.klgCount) * 100);
});
const descriptionVisible = ref(false);
const tagsVisible = ref(false);
const warpper = ref();
const more = ref();
const prjType = inject('prjType') as Ref;
const tags = prjInfo.value.prjTags as Array<PrjTagType>;

// 章节相关数据
const chapterList = ref<Chapter[]>([]);
const activeChapterId = ref<number>();
const hasPermission = ref(false);

// 显示列表数据 - 优先显示课程列表，回退到章节列表
const displayList = computed(() => {
  if (props.courseList && props.courseList.length > 0) {
    return props.courseList.map((course) => ({
      id: course.spuId,
      title: course.title,
      meta: course.userName || ''
    }));
  } else if (chapterList.value && chapterList.value.length > 0) {
    return chapterList.value.map((chapter) => ({
      id: chapter.chapterId.toString(),
      title: chapter.chapterName,
      meta: chapter.duration ? formatDuration(chapter.duration) : ''
    }));
  }
  return [];
});

// 当前选中项ID
const currentItemId = computed(() => {
  if (props.courseList && props.courseList.length > 0) {
    return props.currentCourseId || '';
  } else {
    return activeChapterId.value?.toString() || '';
  }
});

// 当前选中项标题
const currentItemTitle = computed(() => {
  const currentId = currentItemId.value;
  if (!currentId || !displayList.value) return '';

  const currentItem = displayList.value.find((item) => item.id === currentId);
  return currentItem?.title || '';
});

// 监听标题变化并通知父组件
watch(
  currentItemTitle,
  (newTitle) => {
    if (newTitle) {
      emit('titleChange', newTitle);
    }
  },
  { immediate: true }
);

// 监听 currentCourseId 变化，确保选中状态正确更新
watch(
  () => props.currentCourseId,
  (newCourseId) => {
    console.log('PrjInfo - currentCourseId 变化:', newCourseId);
  },
  { immediate: true }
);
// 进度卡片相关数据
const progressExpanded = ref(false);

// 计算显示的标签
const displayTags = computed(() => {
  return tagsVisible.value ? tags : tags.slice(0, 4);
});

// 计算显示的标题 - 优先显示领域名称
const displayTitle = computed(() => {
  return props.areaInfo?.title || prjInfo.value.title || '';
});

// 计算显示的作者名称 - 优先显示领域作者
const displayAuthorName = computed(() => {
  return props.areaInfo?.editorName || prjInfo.value.userName || '';
});

// 计算显示的作者头像 - 优先显示领域作者头像
const displayAuthorPic = computed(() => {
  return props.areaInfo?.editorPic || prjInfo.value.userCoverPic || '';
});

// 计算显示的已掌握知识点 - 优先显示领域统计
const displayMasteredKlg = computed(() => {
  if (props.areaInfo?.masteredKlgCount !== undefined && props.areaInfo?.klgCount !== undefined) {
    return `${props.areaInfo.masteredKlgCount}/${props.areaInfo.klgCount}`;
  }
  return `${klg.masteredKlgCount || 0}/${klg.klgCount || 0}`;
});

// 计算显示的全掌握知识点 - 优先显示领域统计
const displayFullyMasteredKlg = computed(() => {
  if (
    props.areaInfo?.fullyMasteredKlgCount !== undefined &&
    props.areaInfo?.klgCount !== undefined
  ) {
    return `${props.areaInfo.fullyMasteredKlgCount}/${props.areaInfo.klgCount}`;
  }
  return `${klg.fullyMasteredKlgCount || 0}/${klg.klgCount || 0}`;
});

function handleClose(event: MouseEvent) {
  if (warpper.value.contains(event.target as HTMLElement)) {
    return;
  }
  descriptionVisible.value = false;
  tagsVisible.value = false;
}

function handleExpandTag() {
  tagsVisible.value = !tagsVisible.value;
}

// 切换进度卡片展开状态
const toggleProgressExpand = () => {
  progressExpanded.value = !progressExpanded.value;
};
// 处理返回
const handleBack = () => {
  if (props.backPath) {
    router.push({
      path: props.backPath,
      query: props.backQuery || {}
    });
  } else {
    // 默认返回逻辑
    router.push({
      path: '/goodIntroduce',
      query: { spuId: spuId }
    });
  }
};
// 格式化时长
const formatDuration = (duration: string | number) => {
  if (!duration) return '';
  if (typeof duration === 'string') return duration;
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

// 判断是否可以访问章节
const canAccessChapter = (chapter: Chapter) => {
  // 预览章节可以访问
  if (chapter.preview) return true;
  // 已购买可以访问
  if (props.buyStatus) return true;
  // 有权限可以访问
  if (hasPermission.value) return true;
  // 其他情况不能访问
  return false;
};

// 处理项目点击
const handleItemClick = (item: any) => {
  // 如果点击的是当前已选中的项目，不做任何操作
  if (item.id === currentItemId.value) {
    return;
  }

  console.log('PrjInfo - 项目点击:', item);

  // 判断是课程列表还是章节列表
  if (props.courseList && props.courseList.length > 0) {
    // 课程切换
    console.log('触发课程切换事件:', item.id);
    emit('courseChange', item.id);
  } else if (chapterList.value && chapterList.value.length > 0) {
    // 章节切换
    const chapterId = parseInt(item.id);
    const chapter = chapterList.value.find((c) => c.chapterId === chapterId);

    if (chapter) {
      console.log('触发章节切换事件:', chapterId, chapter.preview);
      emit('chapterChange', chapterId, chapter.preview || false);
    }
  }
};

// 监听购买状态变化，实现局部更新
watch(
  () => props.buyStatus,
  (newBuyStatus) => {
    // 当购买状态变化时，更新hasPermission
    hasPermission.value = newBuyStatus;
  },
  { immediate: true }
);

// 监听learningStore变化，当项目切换时更新章节信息
watch(
  () => learningStore.chapterList,
  (newChapterList) => {
    if (newChapterList && newChapterList.length > 0) {
      console.log('PrjInfo - 检测到项目切换，更新章节信息');
      // 获取章节列表
      chapterList.value = newChapterList;
      const validChapter = newChapterList[learningStore.validIndex];
      if (validChapter && typeof validChapter === 'object' && 'chapterId' in validChapter) {
        activeChapterId.value = (validChapter as any).chapterId;
        console.log('PrjInfo - 更新activeChapterId:', activeChapterId.value);
      }
    }
  },
  { deep: true }
);

// 监听prjInfo变化，实现项目信息的实时更新
watch(
  () => prjInfo.value,
  async (newPrjInfo) => {
    if (newPrjInfo && newPrjInfo.spuId) {
      // 当项目信息更新时，重新获取详细信息
      try {
        const res = await getPrjMoreInfoApi(newPrjInfo.spuId);
        const data = res.data as any;

        // 更新知识点统计
        if (data.klg) {
          Object.assign(klg, {
            klgCount: data.klg.klgCount || 0,
            masteredKlgCount: data.klg.masteredKlgCount || 0,
            fullyMasteredKlgCount: data.klg.fullyMasteredKlgCount || 0
          });
        }

        // 更新项目目标信息
        prjTargetObj.value = data.prj || { description: '', purpose: '' };
      } catch (error) {
        console.error('更新项目详细信息失败:', error);
      }
    }
  },
  { deep: true }
);

onMounted(async () => {
  // 获取项目详细信息
  const res = await getPrjMoreInfoApi(spuId);

  const data = res.data as any;
  targetKlgs.value = data.targetKlgs || [];

  // 正确更新响应式对象的属性
  if (data.klg) {
    Object.assign(klg, {
      klgCount: data.klg.klgCount || 0,
      masteredKlgCount: data.klg.masteredKlgCount || 0,
      fullyMasteredKlgCount: data.klg.fullyMasteredKlgCount || 0
    });
  }

  prjTargetObj.value = data.prj || { description: '', purpose: '' };

  // 获取章节列表
  chapterList.value = learningStore.chapterList || [];
  const validChapter = learningStore.chapterList?.[learningStore.validIndex];
  if (validChapter && typeof validChapter === 'object' && 'chapterId' in validChapter) {
    activeChapterId.value = (validChapter as any).chapterId;
  }

  // 权限信息主要通过props.buyStatus来判断，hasPermission作为备用
  hasPermission.value = props.buyStatus; // 初始化时使用props.buyStatus
});

onMounted(() => {
  document.addEventListener('click', handleClose);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClose);
});
</script>

<style scoped src="./css/prjInfo.less"></style>
