<template>
  <div class="video-wrapper">
    <div id="videoPlayer" class="video"></div>
    <!-- <div @click="handleClick()">点击</div> -->
  </div>
</template>

<script setup lang="ts">
import { throttle } from 'lodash-es';
import { getVideoSrc } from '@/utils/getStoreAuth';
import Player, { Events } from 'xgplayer';
// import HlsPlugin, { EVENT } from 'xgplayer-hls';
import HlsJsPlugin from 'xgplayer-hls.js';
import Danmu from 'xgplayer/es/plugins/danmu';
import 'xgplayer/es/plugins/danmu/index.css';
import 'xgplayer/dist/index.min.css';
import createXgPlayerWiderPlugin from '@/utils/createXgPlayerWiderPlugin';
import { useUserStore } from '@/stores/user';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { sample } from 'lodash-es';
import { usePlayerStore } from '@/stores/player';
import { userInfoStore } from '@/stores/userInfo';
import favicon from '@/assets/favicon.ico';

const playerStore = usePlayerStore();
const { player } = storeToRefs(playerStore);
const userStore = useUserStore();
const infoStore = userInfoStore();
const { userInfo } = storeToRefs(userStore);
console.log('user', userInfo.value);
const drawerControllerStore = useDrawerControllerStore();
const { mode } = storeToRefs(drawerControllerStore);
import xhook from 'xhook';
import { Mode } from '@/types/word';
import { timeToSeconds } from '@/utils/Video';

xhook.after(function (request, response) {
  if (String.prototype.includes.call(request.url, 'key')) {
    //response.data = 'xxx';
  }
});
// 水印
const sign = {
  txt: userInfo.value.username + '@' + userInfo.value.phone
};

const props = defineProps<{
  videoSrc: string;
  canBeWiden: boolean;
  questionTimelineMatches?: Array<{
    startTime: string;
    keyword: string;
    questionType: string;
    questionId: number;
  }>;
}>();

const emits = defineEmits(['timeupdate', 'wider', 'endTrigger', 'closePip']);

const exitPIP = () => {
  const plugin = player.value!.getPlugin('pip');
  plugin.exitPIP();
};

const requestPIP = () => {
  const plugin = player.value!.getPlugin('pip');
  plugin.requestPIP();
};

const setTime = (time: number) => {
  player.value!.currentTime = time;
};
const pause = () => {
  player.value!.pause();
};

const play = () => {
  player.value!.play();
};

watch(
  () => props.videoSrc,
  async (newVal) => {
    const res = await getVideoSrc(newVal);
    player.value!.playNext({
      url: res.url + '&ci-process=pm3u8&expires=3600'
    });
  }
);

watch(
  () => mode.value,
  (newVal) => {
    if (newVal == Mode.ask) {
      pause();
    }
  }
);

let timer: NodeJS.Timeout | null = null;

// 问题弹幕相关状态
const displayedDanmus = ref(new Set<string>()); // 记录已显示的弹幕（使用时间点+questionId作为唯一标识）
const lastCheckTime = ref(-1); // 上次检查的时间点，用于优化性能

/**
 * 清理HTML标签，只保留纯文本
 */
const stripHtml = (html: string): string => {
  if (!html) return '';
  // 创建临时DOM元素来解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  return tempDiv.textContent || tempDiv.innerText || '';
};

/**
 * 发送问题弹幕
 */
const sendQuestionDanmu = (question: any) => {
  const danmu = player.value?.getPlugin('danmu');
  if (!danmu) return;
  
  // 🎯 清理HTML标签，只保留纯文本
  const cleanKeyword = stripHtml(question.keyword);
  
  // 🎯 修复开放性问题格式：开放性问题只显示关键词，不显示questionType
  let questionText: string;
  if (question.questionType === '开放性问题') {
    questionText = `【${cleanKeyword}】`;
  } else {
    questionText = `【${cleanKeyword}】${question.questionType}？`;
  }

  danmu.sendComment({
    duration: 8000, // 问题弹幕显示8秒
    id: `question_${question.questionId}_${question.startTime}_${Date.now()}`, // 使用时间点确保唯一性
    txt: questionText,
    style: {
      color: '#ffffff', // 区别于水印弹幕
      fontSize: 12,
      fontFamily: 'var(--text-family)',
      fontWeight: '400',
      opacity: 0.8
    },
    mode: 'scroll' // 滚动模式，更显眼
  });

  // console.log(`📝 发送问题弹幕: ${questionText} (时间: ${question.startTime})`);
};

/**
 * 检查并发送问题弹幕
 */
const checkAndSendQuestionDanmu = (currentTime: number) => {
  if (!props.questionTimelineMatches || props.questionTimelineMatches.length === 0) {
    return;
  }

  // 性能优化：避免频繁检查相同时间点
  const currentTimeInt = Math.floor(currentTime);
  if (currentTimeInt === lastCheckTime.value) {
    return;
  }
  lastCheckTime.value = currentTimeInt;

  // 检查是否有问题需要在当前时间显示
  props.questionTimelineMatches.forEach((question) => {
    const questionTime = timeToSeconds(question.startTime);

    // 🎯 使用时间点+questionId作为唯一标识，允许相同问题在不同时间点显示
    const danmuKey = `${question.startTime}_${question.questionId}`;

    // 时间匹配检查（允许1秒的误差范围）
    if (Math.abs(currentTime - questionTime) <= 1 && !displayedDanmus.value.has(danmuKey)) {
      sendQuestionDanmu(question);
      displayedDanmus.value.add(danmuKey);
    }
  });
};

/**
 * 重置问题弹幕状态（当视频重新开始或切换时调用）
 */
const resetQuestionDanmuState = () => {
  displayedDanmus.value.clear();
  lastCheckTime.value = -1;
  console.log('🔄 重置问题弹幕状态');
};

// 监听questionTimelineMatches变化，重置状态
watch(
  () => props.questionTimelineMatches,
  () => {
    resetQuestionDanmuState();
  },
  { deep: true }
);

onMounted(async () => {
  const res = await getVideoSrc(props.videoSrc);
  const m3u8Url = res.url + '&ci-process=pm3u8&expires=3600';
  if (props.canBeWiden) {
    const myPlugin = createXgPlayerWiderPlugin(function (event) {
      emits('wider');
    });
    player.value = new Player({
      id: 'videoPlayer',
      url: m3u8Url,
      // plugins: [HlsJsPlugin, myPlugin, Danmu],
      plugins: [HlsJsPlugin, Danmu],

      definitionActive: 'click',
      fluid: true,
      ignores: ['cssfullscreen'],
      videoFillMode: 'contain',
      miniprogress: true,
      danmu: {
        closeDefaultBtn: false, // 显示弹幕控制按钮
        fontSize: 30,
        channelSize: 50,
        open: true, // 默认开启弹幕
        opacity: 1 // 设置弹幕不透明度
      },
      commonStyle: {
        // 进度条底色
        progressColor: '',
        // 播放完成部分进度条底色
        playedColor: '#1973CB',
        // 缓存部分进度条底色
        cachedColor: '',
        // 进度条滑块样式
        sliderBtnStyle: { backgroundColor: '#1973CB' },
        // 音量颜色
        volumeColor: '#1973CB'
      }
    });
    // player.value.registerPlugin(myPlugin);
    //此处是因为xgplayer渲染播放器的默认机制是设置height为0
    player.value.on(Events.READY, () => {
      (document.getElementById('videoPlayer') as HTMLElement).style.height = '100%';
      (document.getElementById('videoPlayer') as HTMLElement).style['padding-top'] = 0;
    });
    player.value.on(Events.PIP_CHANGE, (isPip) => {
      if (!isPip) {
        emits('closePip');
      }
    });
  } else {
    player.value = new Player({
      id: 'videoPlayer',
      url: m3u8Url,
      plugins: [HlsJsPlugin, Danmu],
      definitionActive: 'click',
      fluid: true,
      ignores: ['cssfullscreen'],
      videoFillMode: 'contain',
      miniProgress: true,
      danmu: {
        closeDefaultBtn: false, // 显示弹幕控制按钮
        fontSize: 30,
        channelSize: 50,
        open: true, // 默认开启弹幕
        opacity: 1 // 设置弹幕不透明度
      },
      commonStyle: {
        // 进度条底色
        progressColor: '',
        // 播放完成部分进度条底色
        playedColor: '#1973CB',
        // 缓存部分进度条底色
        cachedColor: '',
        // 进度条滑块样式
        sliderBtnStyle: { backgroundColor: '#1973CB' },
        // 音量颜色
        volumeColor: '#1973CB'
      }
    });
  }
  // player.value!.on('core_event', (e) => {
  //   // if (e.eventName === EVENT.HLS_LEVEL_LOADED) {
  //   //   console.log(e.playlist);
  //   //   e.playlist.segments.forEach((segment, index) => {
  //   //     segment.url = 'xxx';
  //   //     console.log(index);
  //   //   });
  //   // }
  //   if (e.eventName === EVENT.METADATA_PARSED) {
  //     console.log(e);
  //   }
  // });
  // 此处不能用防抖，使用节流
  const func = throttle((val) => {
    // 🎯 检查并发送问题弹幕
    checkAndSendQuestionDanmu(val.currentTime);

    emits('timeupdate', val.currentTime);
  }, 300);
  player.value.on('timeupdate', func);
  const areas = [
    {
      start: 0,
      end: 1
    },
    {
      start: 0.1,
      end: 1
    },
    {
      start: 0.2,
      end: 1
    },
    {
      start: 0.3,
      end: 1
    },
    {
      start: 0.4,
      end: 1
    },
    {
      start: 0.5,
      end: 1
    },
    {
      start: 0.6,
      end: 1
    },
    {
      start: 0.7,
      end: 1
    },
    {
      start: 0.8,
      end: 1
    },
    {
      start: 0.9,
      end: 1
    }
  ];
  const xOffsets = [
    '-60%',
    '-50%',
    '-40%',
    '-30%',
    '-20%',
    '-10%',
    '0',
    '10%',
    '20%',
    '30%',
    '40%',
    '50%',
    '60%'
  ];
  timer = setInterval(() => {
    const danmu = player.value!.getPlugin('danmu');
    if (!player.value?.paused) {
      danmu.setArea(sample(areas));
      danmu.sendComment({
        duration: 5000,
        id: Date.now(),
        ...sign,
        style: {
          color: 'rgba(0, 0, 0, .1)',
          padding: '5px 5px',
          transform: `rotate(-15deg) translateX(${sample(xOffsets)})`
        },
        mode: 'top'
      });
    }
  }, 5000);
});

onBeforeUnmount(() => {
  player.value!.destroy();
  player.value = null;
  clearInterval(timer);
});

// 重置弹幕状态的方法
const resetDanmu = () => {
  const danmu = player.value?.getPlugin('danmu');
  if (danmu) {
    danmu.clear();
    resetQuestionDanmuState();
    console.log('🔄 重置弹幕状态');
  }
};

defineExpose({ exitPIP, requestPIP, setTime, pause, play, resetDanmu });
</script>

<style scoped lang="less">
.video-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  .video {
    height: 100%;
    min-height: 100%;
    // background: #f2f2f2;
  }
}
</style>
