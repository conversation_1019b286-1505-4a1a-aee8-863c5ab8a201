import { DateTime } from 'luxon';

/**
 * 计算给定日期距离今天还有多少天
 * @param dateString 日期字符串，格式为 'YYYY-MM-DD' 或 'YYYY-MM-DDThh:mm:ss'
 * @returns 距离今天的天数，如果日期已过期则返回 undefined
 */
export function getDaysFromToday(dateString: string): number | undefined {
  if (!dateString) return undefined;

  // 处理日期字符串，确保格式正确
  const cleanDateString = dateString.split(' ')[0].split('T')[0];

  // 解析目标日期
  const targetDate = DateTime.fromISO(cleanDateString);

  // 获取今天的日期
  const today = DateTime.now().startOf('day');

  // 计算差异（天数）
  const diff = targetDate.diff(today, 'days').days;

  // 向下取整，得到整数天数
  const days = Math.floor(diff);
  // return `${days}`;
  return days;
}
