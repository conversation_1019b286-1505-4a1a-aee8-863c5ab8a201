<template>
  <div class="card-wrapper">
    <div class="title">
      <h1>{{ title }}</h1>
    </div>
    <div class="info">
      <div class="info-time-wrapper">
        视频时长: <span class="info-time">{{ duration }}</span>
      </div>
      <div class="info-test-percent">测试完成度: {{ completion }}/{{ total }}</div>
    </div>
    <div class="video-card-wrapper">
      <PrjVideoWrapper></PrjVideoWrapper>
    </div>
    <div class="result">
      <div class="my">我的成果</div>
      <div class="upload">
        <!-- TODO: 这个函数没有写 -->
        <el-upload
          v-model:file-list="fileList"
          ref="uploadRef"
          class="upload-btn"
          :auto-upload="false"
          :limit="1"
          :on-exceed="handleExceedFn"
          :on-success="handleSuccessFn"
        >
          <template #trigger>
            <el-button type="primary">上传成果</el-button>
          </template>
        </el-upload>
      </div>
    </div>
    <div class="footer">
      <CmpButton @click="returnFn" class="btn" type="info">返回上页</CmpButton>
      <CmpButton @click="submitFn" class="btn" type="primary">提交成果</CmpButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import PrjVideoWrapper from '@/views/learning/components/PrjVideoWrapper.vue';
import { useLearnStore } from '@/stores/learnintro';
import { getVideoExamApi, uploadVideoFileApi } from '@/apis/exam';
import type { UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus';
import { emitChangeFn, genFileId, progressProps } from 'element-plus';
import { ShowType } from '@/types/exam';
import { PrjForm } from '@/types/project';

const showType = inject('showType') as Ref<ShowType>;
const route = useRoute();
const spuId = route.query.spuId as string;
const uploadRef = ref<UploadInstance>();
const fileList = ref<UploadUserFile[]>([]);
const completion = ref(''); // 完成了多少
const duration = ref(''); // 持续时间
const total = ref(); // 总数
const title = ref(); // 标题

const prjForm = ref(PrjForm.video);
provide('prjForm', prjForm);
const router = useRouter();
const returnFn = () => {
  showType.value = ShowType.map;
};

const handleExceedFn: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

const handleSuccessFn = () => {};

const submitFn = async () => {
  console.log('文件文件', fileList.value);
  // console.log('文件文件', uploadRef.value?.submit());
  const formData = new FormData();
  formData.append('myfile', fileList.value[0].raw!);
  formData.append('projectId', spuId);
  const res = await uploadVideoFileApi(formData);
  if (res.success) {
    ElMessage.success('上传成功');
  } else {
    ElMessage.error(res.message);
  }
};
onMounted(async () => {
  // 跳转过来的时候可能还需要tprjParentId，之后页面跳转的时候需要改
  const res = await getVideoExamApi(spuId);
  completion.value = res.data.result.completionRate;
  duration.value = res.data.result.duration;
  total.value = res.data.result.total;
  title.value = res.data.result.title;
});
onBeforeUnmount(() => {
  console.log('hello world');
});
</script>

<style scoped lang="less">
.card-wrapper {
  padding: 20px 30px;
  width: 1100px;

  .title {
    font-size: 20px;
    font-weight: 700;
    color: #333333;
    margin-right: 65px;
  }

  .info {
    margin: 25px 0;
    display: flex;
    align-items: center;

    .info-time-wrapper {
      font-size: 12px;
      font-weight: 400;
      color: #999999;
      margin-right: 50px;
    }

    .info-test-percent {
      font-size: 14px;
      font-weight: 400;
      color: var(--color-theme-project);
    }
  }

  .video-card-wrapper:deep(.main-wrapper) {
    max-height: 374px;
    overflow: auto;
  }

  .video-card-wrapper:deep(.left-video-wrapper) {
    max-height: 374px;
    overflow: auto;
  }

  .video-card-wrapper:deep(.right-content) {
    max-height: 374px;
    overflow: auto;
  }

  .result {
    margin-top: 10px;
    background-color: #f2f2f2;
    border-radius: 4px;
    padding: 14px 17px;

    .my {
      margin-bottom: 5px;
    }

    .upload-btn {
      .el-button {
        background-color: var(--color-theme-project);
        outline: none;
        border: 0;
      }
    }
    .upload {
      display: flex;
      flex-direction: row;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;

    .btn {
      width: 160px;
      height: 40px;
    }

    .btn + .btn {
      margin-left: 30px;
    }
  }
}
</style>
