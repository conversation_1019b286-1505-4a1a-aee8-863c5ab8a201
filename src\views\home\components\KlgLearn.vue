<template>
  <!-- 知识讲解 -->
  <LayoutCard v-bind="data"> </LayoutCard>
</template>

<script setup lang="ts">
import LayoutCard from './LayoutCard.vue';
import { getPrjinfoApi } from '@/apis/home';
import { PrjType, PrjForm } from '@/types/project';
const data = ref({
  title: '热门领域',
  word: '从领域学习入手丰富自己的知识体系',
  tip: '提供完整直观的领域知识体系，配合生动的知识讲解，轻松构建起你的知识体系',
  //prjType: PrjType.klgExplain,
  prjType: PrjType.hotArea,
  //prjFormList: [PrjForm.video, PrjForm.draft, PrjForm.domain],
  prjFormList: [],
  big: false,
  getListApi: getPrjinfoApi
});
</script>

<style scoped lang="less"></style>
