<template>
  <div class="userspace-outer-container" v-if="topContentReady && imagesLoaded">
    <div class="userspace-inner-container">
      <div class="userspace-top">
        <div class="userspace-top-img">
          <img v-lazy="backgroundImgurl" alt="" style="width: 1200px; height: 200px" />
          <el-upload
            action="#"
            list-type="picture"
            :auto-upload="false"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            accept="image/png,image/gif,image/jpg,image/jpeg"
            :show-file-list="false"
          >
            <div class="edit-backgroundimg">
              <span class="iconfont icon-tupian" style="margin-right: 5px"></span>编辑背景图片
            </div>
          </el-upload>
          <el-dialog v-model="centerDialogVisible" title="裁剪封面" class="dialog" align-center>
            <div class="cropper-content">
              <div class="cropper" style="text-align: center">
                <vueCropper
                  ref="cropper"
                  :img="option.img"
                  :outputType="option.outputType"
                  :info="true"
                  :full="option.full"
                  :canMoveBox="option.canMoveBox"
                  :original="option.original"
                  :autoCrop="option.autoCrop"
                  :fixed="option.fixed"
                  :fixedNumber="option.fixedNumber"
                  :centerBox="option.centerBox"
                  :infoTrue="option.infoTrue"
                  :fixedBox="option.fixedBox"
                />
              </div>
            </div>
            <template #footer>
              <span class="dialog-footer">
                <el-button type="light" @click="centerDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="uploadImg"> 确认 </el-button>
              </span>
            </template>
          </el-dialog>
        </div>
        <div class="userspace-top-info">
          <div class="userspace-top-userimg">
            <img
              v-if="userinfo.coverPic"
              :src="userinfo.coverPic"
              alt=""
              class="avatar-img"
            />
          </div>
          <div class="userspace-top-userinfo">
            <span class="userspace-fontstyle0">{{ userinfo.username }}</span>
            <span class="userspace-fontstyle2 intro">{{ userinfo.selfIntroduction || '这里是个人简介' }}</span>
          </div>
        </div>
        <div class="userspace-top-detail" v-if="isExpand">
          <div class="userspace-top-detail1">
            <div class="item">
              <span class="userspace-top-detail-style1 right-align q">学习目标:</span>
              <span class="userspace-top-detail-style2">{{ userinfo.goal }}</span>
            </div>
            <div class="item">
              <span class="userspace-top-detail-style1 right-align q">向往的行业:</span>
              <span class="userspace-top-detail-style2">{{ userinfo.industry }}</span>
            </div>
            <div class="item">
              <span class="userspace-top-detail-style1 right-align q">目标职业:</span>
              <span class="userspace-top-detail-style2">{{ userinfo.career }}</span>
            </div>
          </div>
          <div class="userspace-top-detail2">
            <div class="userspace-top-detail-style1 right-align q">想学的技能:</div>
            <div class="userspace-top-detail-wrap">
              <div
                v-for="(item, index) in userinfo.skills"
                :key="index"
                class="userspace-top-detail-style3"
              >
                <el-tooltip effect="customized" :hide-after="0">
                  <div :data-descr="item.title">{{ item.title }}</div>
                  <template #content>
                    {{ item.title }}
                  </template>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="userspace-bottom">
          <div class="check-more">
            <span class="editinfo-style" @click="changExpand">
              <!-- <el-icon style="color: #c0c4cc">
                <CaretTop v-if="isExpand" />
                <CaretBottom v-else />
              </el-icon> -->
              <span class="span-more">
                <el-icon>
                  <CaretBottom v-if="!isExpand" />
                  <CaretTop v-if="isExpand" />
                </el-icon>
                {{ isExpand ? '收起资料' : '查看更多资料' }}
              </span>
            </span>
          </div>
          <div class="userspace-top-work">
            <div class="userspace-button">
              <el-button class="el-button-style1" v-if="showKnowledge" @click="toKlg"
                >创建知识</el-button
              >
              <el-button class="el-button-style1" v-if="showProject" @click="toPRJ"
                >创建项目</el-button
              >
              <el-button class="el-button-style1" v-if="showExercise" @click="toExer"
                >创建习题</el-button
              >
              <el-button class="el-button-style2" @click="toUserInfo">
                <el-icon style="margin-right: 5px"><Edit /></el-icon>
                编辑个人资料
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="userspace-content">
        <div class="userspace-content-left">
          <div class="userspace-content-leftInner">
            <span class="userspace-fontstyle1">我的会员状态</span>
            <p class="userspace-content-leftInner-title">
              成为领域会员后，您可以看到该领域内的全部知识点。同时，可以学习全部领域知识的讲解内容，点亮自己的知识地图。
            </p>

            <!-- <div
              class="userspace-content-viphonor"
              v-show="userKlgInfo.myviplist && userKlgInfo.myviplist.length > 0"
            >
              <div
                class="userspace-content-viphonor-item"
                v-for="(item, index) in userKlgInfo.myviplist"
                :key="index"
              >
                <span class="iconfont icon-xunzhang userspace-content-viphonor-pic"></span>
                <p class="userspace-content-viphonor-p1">{{ cardTypeMap.get(item.cardType) }}</p>
                <p class="userspace-content-viphonor-p2">{{ item.fieldName }}</p>
                <p class="userspace-content-viphonor-p3">
                  有效期:{{ getDaysFromToday(item.dealTime) }}天
                </p>
              </div>
              <div class="userspace-content-viphonor-item-add" @click="dialogAddVipVisible = true">
                <el-icon class="add"><Plus /></el-icon>
                <p class="add-p">成为新会员</p>
              </div>
            </div> -->

            <el-skeleton :loading="vipLoading" animated>
              <template #default>
                <div class="vip-area-list">
                  <GoodsCard v-for="(item, idx) in myVipLists" :key="idx + '_'" v-bind="item" :startTime="item.startTime" :expirationTime="item.expirationTime" />
                  <div
                    class="userspace-content-viphonor-item-add vip-area-add"
                    @click="dialogAddVipVisible = true"
                  >
                    <el-icon class="add"><Plus /></el-icon>
                    <p class="add-p">成为新会员</p>
                  </div>
                </div>
              </template>
              <template #template>
                <div class="vip-area-list">
                  <div v-for="i in 3" :key="'_' + i" class="vip-skeleton-item">
                    <el-skeleton-item variant="image" style="width: 300px; height: 215px" />
                    <div style="padding: 12px;">
                      <el-skeleton-item variant="p" style="width: 50%" />
                      <el-skeleton-item variant="p" style="width: 100%" />
                      <el-skeleton-item variant="p" style="width: 100%" />
                      <el-skeleton-item variant="p" style="width: 50%" />
                      <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 8px;">
                        <el-skeleton-item variant="text" style="width: 60px" />
                        <el-skeleton-item variant="text" style="width: 40px" />
                      </div>
                    </div>
                  </div>
                  <div class="vip-skeleton-add">
                    <div style="width: 300px; height: 354px; border-radius: 5px; background: #f5f5f5; display: flex; flex-direction: column; align-items: center; justify-content: center; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                      <el-skeleton-item variant="image" style="width: 40px; height: 40px; border-radius: 50%;" />
                    </div>
                  </div>
                </div>
              </template>
            </el-skeleton>
            <div class="pagination-block" v-if="showMore">
              <span class="footerBtn" @click="getMoreFn" id="getMoreFn">
                <span class="myicon"></span>
                加载更多
              </span>
            </div>
            <div class="pagination-block" v-else v-if="current != 1">
              <span>暂无更多内容</span>
            </div>
          </div>
        </div>
        <div class="userspace-content-right">
          <div class="userspace-content-right-item">
            <p class="userspace-content-right-itemtitle">我的知识</p>
            <div class="userspace-content-right-bg">
              <span class="userspace-fontstyle2"
                >我已经学习了<span class="importanr-number-style">{{ userKlgInfo.learnedKlg }}</span
                >个知识点</span
              >
            </div>
            <div class="userspace-content-right-bg">
              <span class="userspace-fontstyle2"
                >我已经掌握了<span class="importanr-number-style">{{ userKlgInfo.graspKlg }}</span
                >个知识点</span
              >
            </div>
            <div class="checkall-style" @click="$router.push('/mystudy/map')">查看全部</div>
          </div>
          <div class="userspace-content-right-item">
            <p class="userspace-content-right-itemtitle">我的提问</p>
            <div class="userspace-content-right-bg">
              <span class="userspace-fontstyle2"
                >学习中我提了<span class="importanr-number-style">{{
                  userKlgInfo.myQuestions
                }}</span
                >个问题</span
              >
            </div>
            <div class="userspace-content-right-bg">
              <span class="userspace-fontstyle2"
                >已经有<span class="importanr-number-style">{{ userKlgInfo.repliedQuestions }}</span
                >个问题被回复</span
              >
            </div>
            <div class="checkall-style" @click="$router.push('/mystudy/question')">查看全部</div>
          </div>
          <div class="userspace-content-right-item">
            <p class="userspace-content-right-itemtitle">收藏夹</p>
            <div class="userspace-content-right-bg">
              <span class="userspace-fontstyle2"
                >我收藏了<span class="importanr-number-style">{{ userKlgInfo.favoriteCount }}</span
                >个题目</span
              >
            </div>
            <div class="checkall-style" @click="$router.push('/mystudy/collect')">进入收藏夹</div>
          </div>
        </div>
      </div>
      <div>
        <!-- <DialogAddVip
          v-if="dialogAddVipVisible"
          :dialogAddVisible="dialogAddVipVisible"
          @closeDialog="closeDialog"
          :userInfo="userinfo"
          :userKlgInfo="userKlgInfo"
        ></DialogAddVip> -->
        <PayDialog
          v-model="dialogAddVipVisible"
          :isUserAdd="true"
          @paySuccess="closeDialog"
        ></PayDialog>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import dialogAdd from './dialogaddvip/index.vue';
import { cardTypeMap } from '@/types/constant'; // 转换卡的类型
import axios from 'axios';
import { docCookies } from '@/utils/cookieop';
import 'vue-cropper/dist/index.css';
import { VueCropper } from 'vue-cropper';
import { reactive, onMounted, watch } from 'vue';
import { Plus, Edit } from '@element-plus/icons-vue';
import type { UploadInstance, UploadProps, UploadRawFile, UploadFile } from 'element-plus';
import { toKlg, toPRJ, toUserInfo, toExer } from '@/utils/gopage';
import { getUserDetailApi } from '@/apis/learnStatistics';
import { getBgimgApi, getPermissionApi, getUserInfoApi, saveBgimgApi, getBoughtVip } from '@/apis/userspace';
import { genFileId } from 'element-plus';
import DialogAddVip from './dialogaddvip/index.vue';
import { CaretBottom, CaretTop } from '@element-plus/icons-vue';
import PayDialog from '@/components/PayDialog.vue';
import { userInfoStore } from '@/stores/userInfo';
import { getDaysFromToday } from '@/utils/dateUtils';
//import vipArea from './components/vipArea.vue';
import GoodCard from '@/components/GoodsCard.vue';
import { getAreaArea } from '@/apis/area';

const showKnowledge = ref(false);
const showProject = ref(false);
const showExercise = ref(false);
const router = useRouter();
// 新增会员对话框的开关
const dialogAddVipVisible = ref(false);
const closeDialog = (val: any) => {
  //dialogAddVipVisible.value = val;
  getUserKlgInfoList();
};
watch(
  () => dialogAddVipVisible.value,
  (newVal) => {
    if (!newVal) {
      getUserKlgInfoList();
    }
  }
);
// 获取个人信息
const userKlgInfo = ref({
  // uniqueCode: '',
  // username: '',
  // coverPic: '',
  // selfIntroduction: '',
  // phone: '',
  // goal: {oid: null, title: "暂无"},
  // industry: {oid: null, title: "暂无"},
  // career: {oid: null, title: "暂无"},
  // skills: [{oid: null, title: "暂无"}],
  learnedKlg: 0,
  graspKlg: 0,
  myQuestions: 0,
  repliedQuestions: 0,
  myviplist: [
    {
      dealTime: '',
      fieldName: '',
      cardType: 0
    }
  ],
  favoriteCount: 0
});

interface PrjPermission {
  name: string;
  isAble: boolean;
  children: any[];
}

interface UserinfoItf {
  career?: string;
  goal?: string;
  industry?: string;
  skills?: [{ oid: Number; title: string }];
  uniqueCode?: string;
  selfIntroduction?: '';
  username?: '';
  coverPic?: '';
}
const userinfo = ref<UserinfoItf>({});
userinfo.value = {
  career: '暂无',
  goal: '暂无',
  industry: '暂无',
  skills: [
    {
      oid: 1,
      title: '暂无'
    }
  ]
};

interface MyviplistItem {
  coverPic: string;
  title: string;
  description: string;
  spuId: string;
  goodsType: number;
  isFree: number;
  buyStatus: number;
  startTime: string;
  expirationTime: string;
}
const myVipLists = ref<MyviplistItem[]>([]);
const total = ref(0);
const totalPage = ref(0);
const showMore = ref(false);
const current = ref(1);
const limit = ref(3);
const vipLoading = ref(true);
const topContentReady = ref(false);
const imagesLoaded = ref(false);

// 重置参数
const resetParams = () => {
  current.value = 1;
  totalPage.value = 0;
  total.value = 0;
};

// 预加载图片
const preloadImages = async (imageUrls: string[]) => {
  const loadPromises = imageUrls.map(url => {
    return new Promise((resolve, reject) => {
      if (!url) {
        resolve(null);
        return;
      }
      const img = new Image();
      img.onload = () => resolve(url);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    });
  });
  
  try {
    await Promise.all(loadPromises);
    imagesLoaded.value = true;
  } catch (error) {
    console.warn('部分图片加载失败:', error);
    // 即使部分图片加载失败也要显示内容
    imagesLoaded.value = true;
  }
};

// 获取列表数据
const getUserKlgInfoList = async () => {
  vipLoading.value = true;
  resetParams();
  const res = await getBoughtVip(current.value, limit.value);
  myVipLists.value = res.data.records || [];
  total.value = res.data.total;
  totalPage.value = Math.ceil(res.data.total / res.data.limit);
  if (current.value < totalPage.value) {
    showMore.value = true;
  } else {
    showMore.value = false;
  }
  // 延迟0.5秒再设置loading为false
  vipLoading.value = false;
};

// 加载更多
const getMoreFn = async () => {
  current.value += 1;
  const res = await getBoughtVip(current.value, limit.value);
  myVipLists.value.push(...(res.data.records || []));
  total.value = res.data.total;
  totalPage.value = Math.ceil(res.data.total / res.data.limit);
  if (current.value < totalPage.value) {
    showMore.value = true;
  } else {
    showMore.value = false;
  }
};

// 展开查看更多资料
let isExpand = ref(true);
function changExpand() {
  isExpand.value = !isExpand.value;
}

// 更换背景图片
const backgroundImgurl = ref('');
onBeforeMount(async () => {
  try {
    // 并行加载所有数据
    const [userDetailRes, bgimgRes] = await Promise.all([
      getUserDetailApi(),
      getBgimgApi()
    ]);
    
    if (userDetailRes.data) {
      userinfo.value = userDetailRes.data;
    }
    
    if (bgimgRes.data) {
      backgroundImgurl.value = bgimgRes.data.backgroundImgurl;
    }
    
    // 获取权限信息
    const permissions = userInfoStore().getPermission();
    showKnowledge.value = permissions.find((item) => item.service == 'blackpearl')?.access || false;
    showProject.value = permissions.find((item) => item.service == 'wellerman')?.access || false;
    showExercise.value = permissions.find((item) => item.service == 'mayflower')?.access || false;
    
    // 预加载图片
    const imageUrls = [];
    if (backgroundImgurl.value) {
      imageUrls.push(backgroundImgurl.value);
    }
    if (userinfo.value.coverPic) {
      imageUrls.push(userinfo.value.coverPic);
    }
    
    // 并行预加载图片
    await preloadImages(imageUrls);
    
    // 所有数据加载完成，设置topContentReady为true
    topContentReady.value = true;
  } catch (error) {
    console.error('加载用户空间数据失败:', error);
    // 即使出错也要显示内容
    topContentReady.value = true;
    imagesLoaded.value = true;
  }
  
  // 独立加载vip列表数据
  getUserKlgInfoList();
});
const imgSaveToUrl = (file: any) => {
  const fileSize = file.size;
  const fileType = file.raw.type;
  if (!fileSize) {
    // 是否为空
    ElMessage({
      type: 'error',
      showClose: true,
      message: '无效的文件，请重新选择！'
    });
    return;
  }
  if (fileSize / 1024 / 1024 > 10) {
    // 图片大小
    ElMessage({
      type: 'error',
      showClose: true,
      message: '请上传小于10M的图片！ '
    });
    return;
  }
  if (fileType.indexOf('image') == -1) {
    // 如果不是图片格式
    ElMessage({
      type: 'error',
      showClose: true,
      message: '不是有效的图片文件，或格式不支持，请重新选择!'
    });
    return;
  }
  console.log('回显', URL.createObjectURL(file.raw));
  backgroundImgurl.value = URL.createObjectURL(file.raw);
  imageUpload(file);
};

const imageUpload = (file: any) => {
  let formData = new FormData();
  formData.append('file', file.raw);
  saveBgimgApi(formData).then((res) => {
    // @ts-ignore
    if (res.code == 20000) {
      console.log('上传成功');
      backgroundImgurl.value = res.data.backgroundImgurl;
    }
  });
};

//图片裁剪
const emit = defineEmits(['sendImgUrl', 'sendPrjId']);
const longUrl = ref(''); //回显
const shortUrl = ref(''); //发送给后端
const prjId = ref();
const sectionId = ref();
const upload = ref();
const prjForm = ref();
const props = defineProps({
  LongUrl: {
    type: String,
    required: true
  }
});
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  upload.value!.handleStart(file);
};

let isLoading = ref(false);
// 图片裁剪
const centerDialogVisible = ref(false);
//获取裁剪图片组件
const cropper = ref();
// 图片裁剪各项参数
const option = reactive({
  img: '', // 裁剪图片的地址
  info: true, // 裁剪框的大小信息
  outputSize: 1, // 裁剪生成图片的质量
  outputType: 'jpg', // 裁剪生成图片的格式
  canScale: true, // 图片是否允许滚轮缩放
  autoCrop: true, // 是否默认生成截图框
  canMoveBox: true, // 截图框能否拖动
  canMove: true, // 上传图片是否可以移动
  fixedBox: false, // 固定截图框大小 不允许改变
  fixed: true, // 是否开启截图框宽高固定比例
  fixedNumber: [6, 1], // 截图框的宽高比例
  full: false, // 是否输出原图比例的截图
  original: false, // 上传图片按照原始比例渲染
  centerBox: true, // 截图框是否被限制在图片里面
  infoTrue: true // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
});
// 点击确认
const uploadImg = () => {
  isLoading.value = true;
  //上传图片并且点击了确认按钮 异步才能获取数据
  cropper.value.getCropBlob(async (data: Blob) => {
    let formData = new FormData();
    formData.append('file', data);
    saveBgimgApi(formData).then((res) => {
      // @ts-ignore
      if (res.code == 20000) {
        console.log('上传成功');
        backgroundImgurl.value = res.data.backgroundImgurl;
      }
    });
    isLoading.value = false;
  });
  centerDialogVisible.value = false;
};
const handleChange = (UploadFile: UploadFile) => {
  console.log(UploadFile);
  const fileSize = UploadFile.size;
  const fileType = UploadFile.raw?.type;
  if (!fileSize) {
    // 是否为空
    ElMessage({
      type: 'error',
      showClose: true,
      message: '无效的文件，请重新选择！'
    });
    return;
  }
  if (fileSize / 1024 / 1024 > 10) {
    // 图片大小
    ElMessage({
      type: 'error',
      showClose: true,
      message: '请上传小于10M的图片！ '
    });
    return;
  }
  if (fileType.indexOf('image') == -1) {
    // 如果不是图片格式
    ElMessage({
      type: 'error',
      showClose: true,
      message: '不是有效的图片文件，或格式不支持，请重新选择!'
    });
    return;
  }
  centerDialogVisible.value = true;
  //这里将图片转化为base64位格式为图片进行裁剪
  const reader = new FileReader();
  reader.onload = (e) => {
    //将图片绑定到裁剪组件的img中
    option.img = e.target.result;
  };
  //拿到图片的base64
  reader.readAsDataURL(UploadFile.raw as UploadRawFile);
};
</script>

<style scoped lang="less">
.right-align {
  display: inline-block;
  width: 78px;
  text-align: right;
}
.q {
  font-size: var(--fontsize-middle-project);
  font-weight: 600;
  color: var(--color-black);
  font-family: var(--title-family);
  margin-right: 20px;
}
// 按钮样式
.el-button {
  width: 118px;
  height: 30px;
  font-family: var(--text-family);
}

.el-button-style1 {
  background-color: var(--color-theme-project);
  color: #fff;
}

.el-button-style2 {
  background-color: #fff;
  border-color: var(--color-theme-project);
  color: var(--color-theme-project);
}

.el-buttonstyle3 {
  width: 148px;
  height: 53px;
  color: var(--color-theme-project);
  border-color: var(--color-theme-project);
}

.el-button:hover {
  background-color: var(--color-second);
  color: var(--color-theme-project);
  // font-weight: 700;
  border: 1px solid var(--color-theme-project);
}

// 字体样式
.userspace-fontstyle0 {
  font-size: 18px;
  font-weight: 600;
  font-family:
    'Alimama FangYuanTi VF SemiBold', 'Alimama FangYuanTi VF Regular', 'Alimama FangYuanTi VF',
    sans-serif;
  color: var(--color-black);
}
.userspace-fontstyle1 {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-family);
  color: var(--color-black);
}

.userspace-fontstyle2 {
  font-size: 14px;
  font-weight: 400;
  font-family: var(--text-family);
  color: var(--color-black);
}

.userspace-fontstyle3 {
  font-size: 12px;
  font-weight: 400;
  color: var(--color-black);
}

.importanr-number-style {
  font-size: 20px;
  color: var(--color-theme-project);
  font-weight: 600;
  margin: 10px;
}

.userspace-outer-container {
  // 重写element样式
  --el-color-primary: var(--color-theme-project);

  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--text-family);

  .userspace-inner-container {
    width: 1200px;
    position: relative;

    .userspace-top-img {
      width: 1200px;
      position: relative;

      .edit-backgroundimg {
        position: absolute;
        right: 30px;
        top: 10px;
        width: 120px;
        height: 30px;
        line-height: 30px;
        border-radius: 5px;
        font-size: 14px;
        color: #eee;
        border: 1px solid #eee;
        text-align: center;
        visibility: hidden;
      }

      .showImg {
        position: absolute;
        top: 0;
        .miniPic {
          width: 100%;
          height: 100%;
          border-radius: 5px;
        }
      }
      .cropper-content {
        .cropper {
          width: auto;
          height: 350px;
          .handle_btn {
            display: flex;
            display: -webkit-flex;
            justify-content: space-between;
            padding: 10px 300px;
            box-sizing: border-box;
          }
        }
      }
      .dialog {
        width: 900px;
      }
      .dialog-footer {
        display: flex;
        justify-content: space-between;
        padding: 0 220px;
      }
    }

    .userspace-top-img:hover {
      .edit-backgroundimg {
        visibility: visible;
      }
    }

    .userspace-top {
      box-shadow: 0px 5px 5px #eee;
      padding-bottom: 10px;
      background-color: white;

      .userspace-top-info {
        height: 100px;
        display: flex;
        align-items: flex-end;
        position: relative;
        margin-top: -50px; // 头像上移悬浮
        margin-left: 60px;

        .userspace-top-userimg {
          border: 2px solid #fff;
          border-radius: 8px;
          overflow: hidden;
          width: 120px;
          height: 120px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.08);
          background: #fff;
          position: relative;
          z-index: 2;
        }
        .avatar-img {
          width: 120px;
          height: 120px;
          object-fit: cover;
          border-radius: 8px;
          display: block;
        }
        .userspace-top-userinfo {
          margin-left: 12px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          min-height: 80px;
          .userspace-fontstyle0 {
            font-size: 22px;
            font-weight: 600;
            color: #222;
            margin-bottom: 8px;
          }
          .intro {
            font-size: 14px;
            color: #888;
            margin-top: 2px;
            max-width: 800px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: var(--text-family);
            font-weight: 400;
            font-style: normal;
            color: #666666;
          }
        }
      }
      .userspace-top-detail {
        position: relative;
        // bottom: -90px;
        // height: 85px;
        width: 1200px;
        background-color: #fff;
        // background-color: aqua;
        // z-index: 999;
        margin-top: 10px;

        .userspace-top-detail1 {
          margin-left: 195px;
          display: flex;
          flex-direction: row;
          .item {
            min-width: 200px;
            // margin-left: 30px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
          }
        }

        .userspace-top-detail-style1 {
          font-size: 14px;
          font-weight: 700;
          margin-right: 20px;
          white-space: nowrap;
        }

        .userspace-top-detail-style2 {
          margin-right: 20px;

          font-size: var(--fontsize-middle-project);
          font-weight: 400;
          max-width: 100px;
          color: #333333;
          font-family: var(--text-family);
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .userspace-top-detail-style3 {
          position: relative;
        }

        .userspace-top-detail-wrap {
          display: flex;
          width: 100%;
          flex-wrap: wrap;
          gap: 0 24px;
          margin-top: 4px;
        }
        div[data-descr] {
          background: #f2f2f2;
          border: none;
          border-radius: 16px;
          color: #555;
          font-size: 15px;
          font-weight: 400;
          padding: 0 16px;
          height: 20px;
          //line-height: 32px;
          margin-right: 0;
          margin-bottom: 0;
          margin-top: 0;
          box-shadow: none;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: background 0.2s;
          font-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
          font-size: 12px;
          font-weight: 400;
          font-style: normal;
         color: #666666;
        }
        div[data-descr]:hover {
          background: #e0e0e0;
        }

        .userspace-top-detail2 {
          display: flex;
          margin-left: 195px;
          margin-top: 10px;
        }
      }
      // 查看更多资料

      .userspace-bottom {
        display: flex;
        flex-direction: row;
        margin-top: 10px;
        margin-left: 195px;
        margin-right: 20px;
        justify-content: space-between;
        .check-more {
          display: flex;

          .editinfo-style {
            .span-more {
              color: #c0c4cc;
            }
            .userspace-fontstyle2();
            .iconfont-reset {
              transform: rotate(90deg);
              -webkit-transform: rotate(180deg);
              transition: transform 0.5s;
            }
            .icon-rotate {
              transform: rotate(0deg);
              -webkit-transform: rotate(0deg);
              transition: transform 0.5s;
            }
          }

          .editinfo-style:hover {
            font-weight: bold;
            cursor: pointer;
          }
        }
        .userspace-top-work {
          display: flex;
          // flex-direction: column-reverse;
          align-items: flex-end;
          justify-content: flex-end;
          width: 520px;
        }
      }
    }

    .userspace-content {
      display: flex;
      flex-direction: row;
      min-height: 570px;
      margin-top: 10px;
      margin-bottom: 20px;

      .userspace-content-left {
        width: 797px;
        //height: 772px;
        box-sizing: border-box;
        box-shadow: 0px 5px 5px #ddd;
        background-color: white;

        .userspace-content-leftInner {
          margin: 20px 86px auto 86px;
          //position: absolute;

          display: flex;
          flex-direction: column;
          //align-items: flex-start;

          .userspace-content-leftInner-title {
            margin-top: 10px;
            font-size: 12px;
            font-weight: 400;
            color: #666;
            font-family:
              '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0',
              sans-serif;
          }

          .userspace-content-viphonor {
            display: flex;
            width: 750px;
            flex-direction: row;
            margin-top: 37px;
            flex-wrap: wrap;

            .userspace-content-viphonor-item {
              width: 320px;
              height: 175px;
              border: 1px solid var(--color-second);
              margin-top: 40px;
              border-radius: 5px;
              margin-right: 40px;
              margin-bottom: 10px;
              display: flex;
              flex-direction: column;
              align-items: center;
              box-shadow: 0px 5px 5px #f2f2f2;
              overflow: hidden;

              .userspace-content-viphonor-pic {
                color: var(--color-theme-project);
                font-size: 70px;
                margin-bottom: 15px;
                margin-top: 10px;
              }

              .userspace-content-viphonor-p1 {
                font-size: 15px;
                color: var(--color-theme-project);
                font-family: var(--title-family);
                margin-bottom: 7px;
              }

              .userspace-content-viphonor-p2 {
                .userspace-fontstyle1();
                margin-bottom: 3px;
              }

              .userspace-content-viphonor-p3 {
                .userspace-fontstyle2();
              }
            }
          }

          .userspace-content-viphonor-item:hover {
            background-color: rgba(250, 250, 250, 1);
            cursor: pointer;
          }

          .userspace-content-viphonor-item-add {
            width: 320px;
            height: 175px;
            border: 1px solid var(--color-second);
            background-color: rgba(255, 255, 255, 1);
            margin-top: 40px;
            box-sizing: border-box;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: rgba(220, 223, 230, 1);
            border-color: rgba(220, 223, 230, 1);
            font-weight: 400px;
            box-shadow: 0px 5px 5px #f2f2f2;

            .add {
              font-size: 20px;
              font-weight: 600;
            }
            .add-p {
              margin-top: 10px;
            }
          }
          .userspace-content-viphonor-item-add:hover {
            background-color: rgba(250, 250, 250, 1);
            cursor: pointer;
          }
          .userspace-content-addbutton {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 30px;
            margin-bottom: 150px;
          }

          .vip-area-list {
            display: flex;
            flex-wrap: wrap;
            gap: 32px 24px; /* 行间距32px，列间距24px */
            margin-top: 20px;
            margin-bottom: 32px;
            justify-content: flex-start;

            .vip-skeleton-item {
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            .vip-area-add {
              width: 300px;
              height: 354px;
              border: 1px solid var(--color-second);
              background-color: rgba(255, 255, 255, 1);
              border-radius: 5px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              color: rgba(220, 223, 230, 1);
              font-weight: 400;
              box-shadow: 0px 5px 5px #f2f2f2;
              margin-top: 0;
              margin-bottom: 0;
              cursor: pointer;
            }
            .vip-area-add .add {
              font-size: 20px;
              font-weight: 600;
            }
            .vip-area-add .add-p {
              margin-top: 10px;
            }
            .vip-area-add:hover {
              background-color: rgba(250, 250, 250, 1);
            }
          }
        }
      }

      .userspace-content-right {
        width: 393px;
        margin-left: 10px;
        height: 772px;
        background-color: white;

        .userspace-content-right-item {
          &:nth-child(1) {
            height: 280px;
          }
          &:nth-child(2) {
            height: 280px;
          }
          &:nth-child(3) {
            height: 192px;
          }
          box-shadow: 0px 5px 5px #ddd;
          margin-bottom: 10px;
          padding: 20px;
          position: relative;

          .userspace-content-right-itemtitle {
            .userspace-fontstyle1();
            margin-bottom: 33px;
          }

          .userspace-content-right-bg {
            width: 353px;
            height: 50px;
            background-color: #f2f2f2;
            margin-bottom: 10px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            padding-left: 20px;
          }

          .checkall-style {
            font-size: 14px;
            font-weight: 400;
            color: var(--color-theme-project);
            position: absolute;
            bottom: 20px;
            left: 168px;
          }

          .checkall-style:hover {
            font-weight: 700;
            cursor: pointer;
          }
        }
      }
    }
  }
}

// 分页按钮样式
.pagination-block {
  display: flex;
  text-align: center;
  justify-content: center;
  color: var(--color-theme-project);
  font-family: var(--text-family);
  font-weight: 400;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  margin: 10px 0;
}
.footerBtn {
  width: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.footerBtn .myicon {
  width: 14px;
  height: 12px;
  margin-right: 5px;
  background-image: url('@/assets/images/project/u3964.svg');
}
.footerBtn:hover {
  font-weight: bold;
}

// 新增会员对话框样式
</style>
