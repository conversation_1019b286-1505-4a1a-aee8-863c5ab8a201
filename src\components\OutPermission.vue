<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import CmpButton from '@/components/CmpButton.vue';
import { ref } from 'vue';

defineProps({
  errorMessage: {
    type: String,
    default: '获取项目详情失败'
  }
});

const route = useRoute();
const router = useRouter();
const spuId = route.query.spuId as string;
const dialogVisible = ref(true);
const handleGoToIntroduce = () => {
  router.push({
    path: '/goodIntroduce',
    query: {
      spuId: spuId
    }
  });
};
</script>

<template>
  <div class="warpper-error">
    <el-dialog
      v-model="dialogVisible"
      width="500px"
      :show-close="false"
      class="dialog-container"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <el-card class="error-dialog" shadow="never">
        <div class="warn">{{ errorMessage }}</div>
        <div class="btn-container">
          <CmpButton
            class="btn"
            type="primary"
            @click="handleGoToIntroduce"
          >返回项目介绍页</CmpButton>
        </div>
      </el-card>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.warpper-error {
  &:deep(.el-dialog) {
    border-radius: 5px;
    padding: 0;
    margin-top: 30vh !important; /* 调整dialog位置居中 */
    background-color: #fff;
  }

  &:deep(.el-dialog__header) {
    padding: 0;
  }

  &:deep(.el-dialog__body) {
    padding: 0;
    color: #333333;
    font-size: 12px;
    font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
      '阿里巴巴普惠体 3.0';
    font-weight: 400;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .dialog-container {
    width: 500px;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .error-dialog {
    width: 100%;
    background-color: #f2f2f2;
    border: unset;
    font-size: 12px;
    font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
      '阿里巴巴普惠体 3.0';
    font-weight: 400;

    .title {
      align-content: center;
      color: black;
      font-size: 18px;
      font-weight: 700;
    }

    .divider {
      margin: 10px 0;
    }

    .warn {
      color: #333333;
      margin: 50px 0 50px 0;
      font-size: 16px;
      font-weight: 400;
      text-align: center;
    }

    .btn-container {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;

      .btn {
        width: 200px;
        height: 35px;
        font-size: var(--fontsize-middle-project);
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>