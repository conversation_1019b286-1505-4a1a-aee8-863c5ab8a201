/**
 * 生产环境版本：associatedWords匹配算法（优化版）
 *
 * 特点：
 * 1. 兼容一维和三维数组结构
 * 2. 为每个时间点创建独立的数组元素（不去重）
 * 3. 跨段落匹配时返回第一个caption的startTime
 * 4. 优先选择最短、最紧凑的匹配
 * 5. 动态调整匹配范围，支持长文本匹配
 * 6. 只返回必要的四个字段：startTime, keyword, questionType, questionId
 *
 * @param {Array} questionList - 问题列表
 * @param {Array} videoCaptionList - 视频字幕列表（支持一维或三维数组）
 * @returns {Array} 匹配结果数组
 */
function matchQuestionsWithTimeline(questionList, videoCaptionList) {
  const matches = [];

  // 时间转换辅助函数
  function timeToSeconds(timeStr) {
    const parts = timeStr.split(':');
    if (parts.length === 3) {
      return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    } else if (parts.length === 2) {
      return parseInt(parts[0]) * 60 + parseInt(parts[1]);
    }
    return parseInt(timeStr);
  }

  // 智能扁平化处理，兼容多种数据结构
  function flattenCaptions(captionList) {
    const allCaptions = [];

    // 检测数据结构类型
    if (!Array.isArray(captionList) || captionList.length === 0) {
      return allCaptions;
    }

    // 如果第一个元素是数组，说明是三维结构
    if (Array.isArray(captionList[0])) {
      captionList.forEach((paragraphList, paragraphIndex) => {
        if (Array.isArray(paragraphList)) {
          paragraphList.forEach((captionItem) => {
            if (captionItem && captionItem.caption) {
              allCaptions.push({
                caption: captionItem.caption,
                startTime: captionItem.startTime,
                paragraphIndex
              });
            }
          });
        }
      });
    }
    // 如果第一个元素有caption属性，说明是一维结构
    else if (captionList[0] && captionList[0].caption) {
      captionList.forEach((captionItem) => {
        if (captionItem && captionItem.caption) {
          allCaptions.push({
            caption: captionItem.caption,
            startTime: captionItem.startTime,
            paragraphIndex: 0 // 一维结构统一设为段落0
          });
        }
      });
    }

    return allCaptions;
  }

  questionList.forEach((question) => {
    if (!question.associatedWords) return;

    const associatedWords = question.associatedWords.trim();

    // 使用优化的扁平化处理
    const allCaptions = flattenCaptions(videoCaptionList);

    if (allCaptions.length === 0) {
      console.warn('No valid captions found for matching');
      return;
    }

    // 找到所有可能的匹配
    const allPossibleMatches = [];

    // 动态计算最大检查范围，基于associatedWords长度
    const estimatedCaptionCount = Math.ceil(associatedWords.length / 10); // 假设每个caption平均10个字符
    const maxCaptionsToCheck = Math.min(Math.max(8, estimatedCaptionCount * 2), allCaptions.length);

    for (let i = 0; i < allCaptions.length; i++) {
      const actualMaxCheck = Math.min(maxCaptionsToCheck, allCaptions.length - i);

      for (let length = 1; length <= actualMaxCheck; length++) {
        let combinedText = '';

        // 构建连续的文本组合
        for (let k = 0; k < length; k++) {
          combinedText += allCaptions[i + k].caption;
        }

        // 检查是否包含associatedWords
        if (combinedText.includes(associatedWords)) {
          const paragraphSpan =
            allCaptions[i + length - 1].paragraphIndex - allCaptions[i].paragraphIndex;

          // 计算匹配质量分数（越小越好）
          // 长度权重×100 + 跨段落权重×10 + 时间权重×1
          const score = length * 100 + paragraphSpan * 10 + timeToSeconds(allCaptions[i].startTime);

          allPossibleMatches.push({
            startIndex: i,
            endIndex: i + length - 1,
            startTime: allCaptions[i].startTime,
            score: score,
            matchedText: combinedText,
            captionCount: length
          });

          break; // 找到最短匹配后停止扩展
        }
      }
    }

    // 按质量分数排序并选择最优匹配
    allPossibleMatches.sort((a, b) => a.score - b.score);

    const usedCaptions = new Set();

    for (const match of allPossibleMatches) {
      // 检查是否与已选择的匹配重叠
      let hasOverlap = false;
      for (let i = match.startIndex; i <= match.endIndex; i++) {
        if (usedCaptions.has(i)) {
          hasOverlap = true;
          break;
        }
      }

      if (!hasOverlap) {
        // 标记所有参与的caption为已使用
        for (let i = match.startIndex; i <= match.endIndex; i++) {
          usedCaptions.add(i);
        }

        // 创建最终匹配记录 - 只包含必要的四个字段
        matches.push({
          startTime: match.startTime,
          keyword: question.keyword,
          questionType: question.questionType,
          questionId: question.questionId
        });

        // 添加调试信息（生产环境可移除）
        console.log(
          `匹配成功: ${question.keyword} -> ${match.startTime} (使用${match.captionCount}个字幕)`
        );
      }
    }

    // 如果没有找到匹配，输出调试信息
    if (allPossibleMatches.length === 0) {
      console.warn(`未找到匹配: ${question.keyword} -> "${associatedWords}"`);
      console.warn(`可用字幕数量: ${allCaptions.length}`);
      if (allCaptions.length > 0) {
        console.warn(
          `字幕文本示例: "${allCaptions
            .slice(0, 3)
            .map((c) => c.caption)
            .join('')}"`
        );
      }
    }
  });

  // 按时间排序
  matches.sort((a, b) => {
    const timeA = timeToSeconds(a.startTime);
    const timeB = timeToSeconds(b.startTime);
    return timeA - timeB;
  });

  return matches;
}

// 导出函数（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { matchQuestionsWithTimeline };
}

// 示例用法和测试

// 三维数组示例（原始格式）
// const videoCaptionList3D = [
//     [
//         {
//             oid: 26447,
//             startTime: "00:00:43",
//             endTime: "00:00:44",
//             caption: "这就属于另外的知识了",
//             beginning: 0
//         },
//         {
//             oid: 26448,
//             startTime: "00:00:46",
//             endTime: "00:00:49",
//             caption: "只有单晶硅是造不了计算机的",
//             beginning: 0
//         }
//     ],
//     [
//         {
//             oid: 26449,
//             startTime: "00:00:49",
//             endTime: "00:00:51",
//             caption: "还需要对单晶硅做进一步处理",
//             beginning: 0
//         }
//     ]
// ];

// const questionList3D = [
//     {
//         questionId: 1,
//         keyword: "属于",
//         questionType: "是什么",
//         associatedWords: "属于"
//     },
//     {
//         questionId: 2,
//         keyword: "知识了只有",
//         questionType: "是什么",
//         associatedWords: "知识了只有"
//     }
// ];
// 一维数组测试示例（当前实际数据格式）
const videoCaptionList = [
  [
    {
      oid: 26548,
      startTime: '00:04:53',
      endTime: '00:04:55',
      caption: '整个结构就不会形成回路',
      beginning: 1
    },
    {
      oid: 26549,
      startTime: '00:04:55',
      endTime: '00:04:57',
      caption: '也就是断开的状态',
      beginning: 0
    },
    {
      oid: 26550,
      startTime: '00:04:58',
      endTime: '00:04:59',
      caption: '而如果颠倒正负结',
      beginning: 0
    }
  ],
  [
    {
      oid: 26551,
      startTime: '00:04:59',
      endTime: '00:05:00',
      caption: '也是一样的',
      beginning: 0
    },
    {
      oid: 26552,
      startTime: '00:05:01',
      endTime: '00:05:03',
      caption: '这边的拼音结就会被加宽',
      beginning: 0
    },
    {
      oid: 26553,
      startTime: '00:05:03',
      endTime: '00:05:05',
      caption: '整个结构也不会形成回路',
      beginning: 0
    },
    {
      oid: 26554,
      startTime: '00:05:05',
      endTime: '00:05:07',
      caption: '也是断开的状态',
      beginning: 0
    },
    {
      oid: 26555,
      startTime: '00:05:08',
      endTime: '00:05:10',
      caption: '那么有没有一种可能',
      beginning: 0
    },
    {
      oid: 26556,
      startTime: '00:05:10',
      endTime: '00:05:11',
      caption: '让我们动一些手脚',
      beginning: 0
    }
  ],
  [
    {
      oid: 26557,
      startTime: '00:05:11',
      endTime: '00:05:14',
      caption: '让这个结构变成可以导通的状态的呢',
      beginning: 0
    },
    {
      oid: 26558,
      startTime: '00:05:15',
      endTime: '00:05:15',
      caption: '这里',
      beginning: 0
    },
    {
      oid: 26559,
      startTime: '00:05:15',
      endTime: '00:05:18',
      caption: '我们来看一下另一种常见的电路元件',
      beginning: 0
    },
    {
      oid: 26560,
      startTime: '00:05:19',
      endTime: '00:05:19',
      caption: '电容',
      beginning: 0
    },
    {
      oid: 26561,
      startTime: '00:05:20',
      endTime: '00:05:22',
      caption: '我们准备两块金属板',
      beginning: 0
    },
    {
      oid: 26562,
      startTime: '00:05:22',
      endTime: '00:05:24',
      caption: '让它们相对摆放',
      beginning: 0
    },
    {
      oid: 26563,
      startTime: '00:05:24',
      endTime: '00:05:26',
      caption: '中间再夹一个绝缘层',
      beginning: 0
    }
  ]
];

// 测试用例1：完整长文本匹配
const questionList1 = [
  {
    questionId: 3,
    keyword: '长文本匹配测试',
    questionType: '是什么',
    associatedWords:
      '如果颠倒正负结也是一样的这边的拼音结就会被加宽整个结构也不会形成回路也是断开的状态那么有没有一种可能让我们动一些手脚让这个结构变成可以导通的状态的呢这里我们来看一下另一种常见的电路元件电容我们准备两块金属板让它们相对摆放中间再夹一个绝缘'
  }
];

// 测试用例2：短文本匹配
const questionList2 = [
  {
    questionId: 1,
    keyword: '断开状态',
    questionType: '是什么',
    associatedWords: '断开的状态'
  },
  {
    questionId: 2,
    keyword: '电路元件',
    questionType: '是什么',
    associatedWords: '电路元件'
  }
];

console.log('=== 测试长文本匹配 ===');
const results1 = matchQuestionsWithTimeline(questionList1, videoCaptionList);
console.log('结果1:', results1);

console.log('\n=== 测试短文本匹配 ===');
const results2 = matchQuestionsWithTimeline(questionList2, videoCaptionList);
console.log('结果2:', results2);
