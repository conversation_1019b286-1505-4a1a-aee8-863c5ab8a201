.main-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
:deep(.sectionMenu) {
  margin: 10px;
  cursor: pointer;
  transform: scale(2);
}
.sectionMenuContent {
  position: relative;
  transition: width 0.5s ease-in-out;
  left: 90px;
  top: -22px;
  z-index: 10;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
.sectionMenuContent .section {
  width: 100%;
  height: 41px;
  font-size: 14px;
  padding: 0 10px;
  background-color: white;
  display: flex;
  align-items: center;
  font-family: var(--text-family);
  color: var(--color-black);
  cursor: pointer;
}
.sectionMenuContent .section:hover {
  background-color: #f2f2f2;
}
.sectionMenuContent .section .sectionTitle {
  margin-left: 5px;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.sectionMenuContent .section .lock {
  margin-top: 4px;
}
.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}
.v-enter-from,
.v-leave-to {
  opacity: 0;
}
:deep(.divider) {
  margin: 0 !important;
}
:deep(.manuscript-container) {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}
.klgbtn {
  margin-right: 30px;
  position: relative;
  cursor: pointer;
}
.main-wrapper {
  flex: 1;
  margin: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  scroll-snap-align: start;
  transition: width 1s;
}
.main-wrapper .left-title-list {
  width: 25px;
  margin-right: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-wrapper .left-title-list .title-item {
  width: 90%;
  cursor: pointer;
  height: 40px;
  display: flex;
  align-items: center;
  padding-left: 20px;
  font-size: 14px;
  margin-top: 4px;
}
.main-wrapper .left-title-list .title-item:hover {
  cursor: pointer;
  font-weight: 700;
  background-color: #f2f2f2;
}
.main-wrapper .left-title-list .title {
  font-size: 12px;
  font-weight: 400;
  color: #797979;
}
.main-wrapper .left-title-list .title-num {
  width: 25px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  background-color: #f2f2f2;
  border-radius: 5px 0px 0px 5px;
  font-size: 13px;
  font-weight: 400;
  color: #333333;
  margin-bottom: 2px;
  cursor: pointer;
}
.main-wrapper .left-title-list .title-num.active {
  background-color: var(--color-theme-project);
  color: #fefef6;
  border-radius: 5px 0px 0px 5px;
  box-shadow: rgba(0, 85, 121, 0.376) 0px 3px 3px 0px;
}
.main-wrapper .main {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  min-height: 0;
  overflow: hidden;
  margin: 0;
  padding: 10px;
  height: 100%;
}
.main-wrapper .main .prjtitle {
  height: 25px;
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 10px;
}
.main-wrapper .main .prjtitle .titlefont {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main-wrapper .main .video {
  flex: 1;
  position: relative;
}
.main-wrapper .main .video .coverPic-wrapper {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-black);
  background-repeat: no-repeat;
  background-size: cover;
}
.main-wrapper .main .video .video-btn-wrapper {
  width: 50px;
  height: 50px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
}
.main-wrapper .main .video .expand-logo {
  position: absolute;
  right: 10px;
  bottom: 10px;
  cursor: pointer;
}
.main-wrapper .main .video .expand-logo:hover {
  font-weight: 500;
}
.main-wrapper .main .video-footer-info {
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
  border: 1px solid #f2f2f2;
  padding-left: 10px;
  width: 100%;
  position: relative;
}
.main-wrapper .main .video-footer-info .video-footer {
  vertical-align: middle;
  transform: translateY(-3px);
}
.main-wrapper .main .video-footer-info .footer-logo-wrapper {
  width: 90%;
  display: flex;
  align-items: center;
  position: absolute;
}
.main-wrapper .main .video-footer-info .footer-title {
  font-size: 18px;
  font-weight: 300;
  color: #333333;
  margin-left: 17px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.main-wrapper.big {
  display: flex;
  justify-content: center;
  width: 100%;
  overflow: hidden;
}
.main-wrapper.map {
  justify-content: center;
  width: 100%;
}
/* 问号图标淡入动画 */
@keyframes questionIconFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
