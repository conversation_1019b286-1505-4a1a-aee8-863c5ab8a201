<template>
  <!-- 项目文稿 -->
  <div class="layout textfont">
    <!-- 主内容区域 -->
    <div class="main-content-area" :class="{ 'with-ksg-map': isMap }">
      <!-- ksgMap覆盖层 -->
      <div class="ksg-map-overlay" v-show="isMap">
        <KsgMap
          ref="ksgRef"
          :config="config"
          @load-more="handleLoadMore"
          :loading="loading"
          @click-label="handleClickLabel"
          class="ksgmap"
        />
        <!-- <KsgMap ref="ksgRef" :config="config" :loading="loading" /> -->
        <el-icon class="close-icon" size="2em" @click="closeMap">
          <Close />
        </el-icon>
      </div>

      <div
        class="content-card"
        v-if="!isMap && (prjForm != PrjForm.video || props.videoCaptionList)"
      >
        <el-watermark :font="font" :content="userInfo.username + '@' + userInfo.phone">
          <div
            class="content-text-wrapper hover-scrollbar"
            :class="{ learning: $route.name == 'learning' }"
            ref="contentWrapperRef"
            id="script"
          >
            <div>
              <ContentRenderer class="mystem" :content="contentWithOutlineIds" />
            </div>
          </div>
        </el-watermark>
      </div>
    </div>

    <!-- 右侧大纲/目标知识区域 -->
    <div
      class="outline-sidebar hover-scrollbar"
      v-if="prjForm != PrjForm.video && isOutlineVisible"
      :class="{ 'has-drawer': showQuestionDrawer || showAnswerDrawer }"
    >
      <!-- 侧边栏标题 -->
      <div v-if="sidebarMode === 'knowledge'" class="sidebar-header">
        <div class="sidebar-title">
          {{ '目标知识' }}
        </div>
      </div>

      <!-- 大纲内容 -->
      <div class="outline-content" v-show="sidebarMode === 'outline'">
        <MarkdownOutline v-if="outline.length > 0" :outline="outline" />
        <div v-else class="no-outline">
          <p>暂无目录大纲</p>
        </div>
      </div>

      <!-- 目标知识内容 -->
      <div class="knowledge-content" v-show="sidebarMode === 'knowledge'">
        <div
          v-for="knowledge in targetKnowledgeList"
          :key="knowledge.klgCode"
          class="knowledge-item"
          @click="handleKnowledgeClick(knowledge)"
        >
          <span class="knowledge-title">{{ knowledge.klgTitle }}</span>
        </div>
        <div v-if="!targetKnowledgeList || targetKnowledgeList.length === 0" class="no-knowledge">
          <p>暂无目标知识</p>
        </div>
      </div>

      <!-- 弹窗组件 - 绝对定位覆盖在大纲上方 -->
      <QuestionDrawer
        :visible="showQuestionDrawer"
        :selectedText="selectedText"
        :zIndex="componentZIndex.question"
        :buyStatus="true"
        @close="handleCloseQuestionDrawer"
      />
      <AnswerDrawerSidebar
        :visible="showAnswerDrawer"
        :questionData="currentQuestionData"
        :projectAuthor="projectAuthor"
        :zIndex="componentZIndex.answer"
        @close="handleCloseAnswerDrawer"
        @show-question="handleShowQuestionFromFloating"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick, type Events } from 'vue';
import { STATE_FLAG } from '@/types/learning';
import type { VideoCaptionListObj } from '@/types/learning';
import { PrjForm } from '@/types/project';
import { useUserStore } from '@/stores/user';
import { getPrjIntroduceApi } from '@/apis/case';
import { extractOutline, addIdsToHeadings, type OutlineItem } from '@/utils/outlineUtils';
import MarkdownOutline from '@/views/learning/components/MarkdownOutline.vue';
import QuestionDrawer from '@/components/QuestionDrawer.vue';
import AnswerDrawerSidebar from '@/components/AnswerDrawerSidebar.vue';
import { Close } from '@element-plus/icons-vue';
import { useDrawerManager } from '@/composables/useDrawerManager';
// ksgMap相关导入
import { KsgMap, MODE, type Options } from '@endlessorigin/KsgMap'; //直接导入组件，局部使用方式
const ksgRef = ref<any>(); //获取组件实例
let loading = ref<'loading' | 'loaded' | 'error'>('loading'); //loading状态
// 场景配置项
const config: Options = {
  model: MODE.MULTIPLE_ROOT, //Single_ROOT单根节点模,多根知识点场景(MODE.MULTIPLE_ROOT)，
  // 配置加载更多参数
  pointsLevelPager: {
    current: 1, //当前层级（从1层开始，默认第一层）
    levelSize: 2 //每次加载层数
  }
};
// 处理加载更多事件
const handleLoadMore = async (params: any) => {
  // 这里可以根据需要实现加载更多逻辑
};

// 处理点击标签事件
const handleClickLabel = (label: any) => {
  // 这里可以根据需要实现点击标签逻辑
};
const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

interface Props {
  videoCaptionList?: [VideoCaptionListObj[]]; // 讲稿段落
  questionList?: any[]; // 问题列表
  renderContent?: any; // 文本项目文本
  // big?: false;
  isMap?: Boolean;
  ksgMapData?: any; // 知识图谱数据
  ksgMapLoading?: 'loading' | 'loaded' | 'error'; // 知识图谱加载状态
}
const emits = defineEmits([
  'returnInit',
  'scrollInTop',
  'refresh',
  'refreshContent',
  'deleteQuestion',
  'search',
  'outline-change',
  'close-map'
]);

const font = reactive({
  color: 'rgba(0, 0, 0, .07)'
});
// emit事件
const props = defineProps<Props>();
// const showContentWord=ref(false)

const prjForm = inject('prjForm') as Ref;

// 获取试学状态
const hasPermission = ref(0);

onMounted(async () => {
  try {
    const res = await getPrjIntroduceApi({
      spuId: spuId
    });
    hasPermission.value = res.data.hasPermission;
    projectAuthor.value = res.data.editorName || ''; // 获取项目作者
  } catch (error) {
    // console.error('获取试学状态失败', error);
  }
});

const myWordContent = ref();
const router = useRouter();
const route = useRoute();
const spuId = route.query.spuId as string;

// 大纲相关
const outline = ref<OutlineItem[]>([]);
const isOutlineVisible = ref(true); // 控制大纲显示/隐藏，默认显示

// 侧边栏显示模式：'outline' | 'knowledge'
const sidebarMode = ref<'outline' | 'knowledge'>('outline');
// 目标知识列表
const targetKnowledgeList = ref<Array<{ klgCode: string; klgTitle: string; choose: boolean }>>([]);

// 显示大纲 - 只能打开，会覆盖目标知识
const showOutline = () => {
  sidebarMode.value = 'outline';
  isOutlineVisible.value = true;
  console.log('显示大纲');
};

// 显示目标知识 - 只能打开，会覆盖大纲
const showTargetKnowledge = (
  knowledgeList: Array<{ klgCode: string; klgTitle: string; choose: boolean }>
) => {
  sidebarMode.value = 'knowledge';
  targetKnowledgeList.value = knowledgeList;
  isOutlineVisible.value = true;
  console.log('显示目标知识:', knowledgeList);
};

// 处理目标知识点击事件
const handleKnowledgeClick = (knowledge: {
  klgCode: string;
  klgTitle: string;
  choose: boolean;
}) => {
  // 跳转到知识详情页面
  const { href } = router.resolve({
    path: '/klgdetail',
    query: {
      klgCode: knowledge.klgCode
    }
  });
  window.open(href, '_blank');
  console.log('跳转到知识详情:', knowledge.klgCode, knowledge.klgTitle);
};

// 保留原有的toggleOutline方法以兼容现有调用
const toggleOutline = () => {
  showOutline();
};

// 使用抽屉管理 composable
const {
  showQuestionDrawer,
  showAnswerDrawer,
  selectedText,
  currentQuestionData,
  componentZIndex,
  componentStack,
  projectAuthor,
  updateComponentLayer,
  handleShowQuestionDrawer,
  handleShowAnswerDrawer,
  handleCloseQuestionDrawer,
  handleCloseAnswerDrawer,
  handleShowQuestionFromFloating,
  initializeEventListeners,
  cleanupEventListeners
} = useDrawerManager();

// 抽屉管理相关逻辑已移至 useDrawerManager composable

// ksgMap相关变量
const isMap = inject('isMap') as Ref<boolean>;

// 监听isMap变化和ksgMapData变化
watch(
  () => [isMap?.value, props.ksgMapData, props.ksgMapLoading],
  async ([newMapValue, newKsgMapData, newKsgMapLoading]) => {
    if (newMapValue && newKsgMapData && newKsgMapLoading === 'loaded') {
      // 当知识图谱打开且数据已加载时，初始化知识图谱
      console.log('PrjManuscript - 开始初始化知识图谱，数据:', newKsgMapData);
      await init();
    }
  },
  { immediate: false } // 改为false，避免初始化时就触发
);

// 关闭地图
const closeMap = () => {
  // 通知父组件关闭地图
  emits('close-map');
};

// 计算属性：处理后的内容（包含大纲ID）
const contentWithOutlineIds = computed(() => {
  if (!myWordContent.value || prjForm.value === PrjForm.video) {
    return myWordContent.value || '';
  }
  // console.log('contentWithOutlineIds:', addIdsToHeadings(myWordContent.value));
  return addIdsToHeadings(myWordContent.value);
});

// 提取并更新大纲
const updateOutline = (content: any) => {
  if (!content) {
    outline.value = [];
    emits('outline-change', []);
    return;
  }
  const contentWithIds = addIdsToHeadings(content);
  // 提取大纲
  const newOutline = extractOutline(contentWithIds);
  outline.value = newOutline;
  // 通知父组件大纲变化
  emits('outline-change', newOutline);
  return contentWithIds;
};

watch(
  () => props.renderContent,
  async (newVal) => {
    myWordContent.value = newVal;

    updateOutline(newVal);
  }
);
const stateFlag = ref(STATE_FLAG.init);

const changeStateFn = (state: STATE_FLAG) => {
  stateFlag.value = state;
};

const contentWrapperRef = ref();
// 阅读时滑动滚
// 阅读模式，双向绑定，提问模式单项绑定。而且滚动条在最中间
const scrollElement = ref<HTMLElement>();
watch(
  () => scrollElement.value,
  (newVal, oldVal) => {
    if (newVal) {
      newVal.style.backgroundColor = '#a6d0ea';
      newVal.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    if (oldVal) {
      oldVal.style.backgroundColor = '';
    }
  }
);

const scrollBar = async (idx: number) => {
  const containerHeight = contentWrapperRef.value.offsetHeight;
  // 找到元素并且滑动
  const allParagraph = Array.from(document.querySelectorAll('.paragraph-wrapper')) as HTMLElement[];
  const activeParagraph = allParagraph?.[idx];
  sethighlightColor(idx);
  // 小屏可以使用这个api
  await nextTick();
  scrollElement.value = document.querySelector(`[oid="${curoid.value}"]`) as HTMLElement;
};
const activeIdx = ref(0);
// 设置高亮色
const sethighlightColor = (idx: number) => {
  activeIdx.value = idx;
};

const curoid = ref();
const hoverPList = (_idx: Number) => {
  curoid.value = _idx;
};

// 使用从父组件传递的知识图谱数据，而不是自己获取
const dataList = ref('');
const total = ref('');
const rootid = ref('');
async function init() {
  // 使用父组件传递的loading状态
  if (props.ksgMapLoading !== 'loaded' || !props.ksgMapData) {
    console.warn('PrjManuscript - 知识图谱数据未准备好');
    return;
  }

  loading.value = 'loading'; //加载中状态

  //dataList - 后端响应的知识点数组
  //total - 总共多少个数据
  //root - 知识点id
  dataList.value = props.ksgMapData.records;
  console.log('PrjManuscript - dataList.value:', dataList.value);
  total.value = props.ksgMapData.total;
  rootid.value = '0'; // 修复类型错误，改为字符串
  // console.log('datalist', dataList.value);
  // console.log('total', total.value);
  // console.log('rootid', rootid.value);

  ksgRef.value?.firstLoadPointsData(dataList.value, total.value, rootid.value); //当多根知识点模式，id为邻域id，方便后期数据请求查询
  loading.value = 'loaded'; //加载完成状态
}

const getContentWithOutlineIds = () => {
  if (!contentWithOutlineIds.value) {
    return [];
  }
  return contentWithOutlineIds.value;
};
onMounted(async () => {
  // 初始化抽屉管理事件监听
  initializeEventListeners();
});

// 清理事件监听器
onUnmounted(() => {
  cleanupEventListeners();
});
defineExpose({
  changeStateFn,
  scrollBar,
  sethighlightColor,
  hoverPList,
  getContentWithOutlineIds,
  toggleOutline,
  showOutline,
  showTargetKnowledge
});
</script>

<style scoped src="./css/PrjManuscript.less"></style>
