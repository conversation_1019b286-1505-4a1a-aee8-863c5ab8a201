<template>
    <!-- {{ wordStore }} -->
    <!-- 这个组件还会用到exam页面 -->
    <div @wheel="handleWheel" class="main-wrapper" :class="{ big: isBig }">
      <span class="switchFloor" v-show="isBig">
        <span class="btn" :class="bigScreen == 0 ? 'light' : ''" @click="switchScreen(0)">
          <el-icon><ArrowUpBold /></el-icon>
        </span>
        <span class="btn" :class="bigScreen == 1 ? 'light' : ''" @click="switchScreen(1)">
          <el-icon><ArrowDownBold /></el-icon>
        </span>
      </span>
  
      <div
        v-show="(!isBig && !isMap) || (isBig && bigScreen == 0 && !isMap)"
        class="left-video-wrapper"
        :class="{ videoPage: $route.name == 'learning' }"
      >
        <!-- 左侧的title列表 -->
        <div class="left-title-list" v-if="$route.name == 'learning' && prjType == PrjType.case">
          <template v-if="prjType == PrjType.case">
            <img
              class="sectionMenu"
              v-if="!isSectionMenuShow"
              @click="isSectionMenuShow = !isSectionMenuShow"
              src="@/assets/images/prjlearn/u4448.svg"
            />
            <img
              class="sectionMenu"
              v-else
              @click="isSectionMenuShow = !isSectionMenuShow"
              src="@/assets/images/prjlearn/u4449.svg"
            />
            <span
              :style="isSectionMenuShow ? '' : 'width: 0px; height: 0px; border-weight: 0px'"
              class="sectionMenuContent"
            >
              <span
                v-show="isSectionMenuShow"
                v-for="(chapter, idx) in chapterList"
                :key="'_' + chapter.chapterId"
                class="section"
                @mousedown.prevent="handleChangeSectionFn(chapter.chapterId)"
                :class="{ active: activeIndex == chapter.chapterId }"
              >
                {{ idx + 1 }}
                <span class="sectionTitle" :title="chapter.chapterName">{{
                  chapter.chapterName
                }}</span>
              </span>
            </span>
          </template>
        </div>
        <!-- 左侧的video -->
        <div class="left-main" :class="{ big: isBig }">
          <template v-if="projectDetailData?.videoUrl">
            <XgPlayer
              :videoSrc="projectDetailData?.videoUrl"
              :canBeWiden="prjType == PrjType.exam ? false : true"
              @timeupdate="handleTimeupdateFn"
              @wider="toggleDisplay"
              @closePip="switchScreen(0)"
              ref="player"
            >
            </XgPlayer>
          </template>
  
          <div style="width: 100%; background-color: white; height: 5px"></div>
          <div class="video-title" v-if="$route.name == 'learning'">
            {{ projectDetailData?.chapterName }}
            <span class="btn" @click="toggleMap"> 节知识地图 </span>
          </div>
        </div>
      </div>
  
      <div class="map-wrapper" :class="{ videoPage: $route.name == 'learning' }" v-show="isMap">
        <div class="left-title-list" v-if="$route.name == 'learning' && prjType == PrjType.case">
          <template v-if="prjType == PrjType.case">
            <img
              class="sectionMenu"
              v-if="!isSectionMenuShow"
              @click="isSectionMenuShow = !isSectionMenuShow"
              src="@/assets/images/prjlearn/u4448.svg"
            />
            <img
              class="sectionMenu"
              v-else
              @click="isSectionMenuShow = !isSectionMenuShow"
              src="@/assets/images/prjlearn/u4449.svg"
            />
            <span
              :style="isSectionMenuShow ? '' : 'width: 0px; height: 0px; border-weight: 0px'"
              class="sectionMenuContent"
            >
              <span
                v-show="isSectionMenuShow"
                v-for="(chapter, idx) in chapterList"
                :key="'_' + chapter.chapterId"
                class="section"
                @mousedown.prevent="handleChangeSectionFn(chapter.chapterId)"
                :class="{ active: activeIndex == chapter.chapterId }"
              >
                {{ idx + 1 }}
                <span class="sectionTitle" :title="chapter.chapterName">{{
                  chapter.chapterName
                }}</span>
              </span>
            </span>
          </template>
        </div>
  
        <div class="left-main" :class="{ big: isBig }">
          <!-- todo:预留的地图组件 用isMap控制是否显示-->
          <div v-show="isMap" class="map-content">
            <div class="right-map" v-show="isMap" style="background-color: rgb(109, 81, 43)"></div>
          </div>
          <div style="width: 100%; background-color: white; height: 5px"></div>
          <div class="video-title" v-if="$route.name == 'learning'">
            {{ projectDetailData?.chapterName }}
            <span class="btn" @click="toggleMap"> 节知识地图 </span>
          </div>
        </div>
      </div>
  
      <!-- 右侧的内容区 -->
      <div
        v-show="!isBig || (isBig && bigScreen == 1)"
        class="right-content"
        :class="{ videoPage: $route.name == 'learning' }"
      >
        <!-- <div class="right-map" v-show="isMap">
          <KsgMap
            ref="ksgMap"
            v-model:config="config"
            :data="data"
            :target="target ?? undefined"
            :fetchAreaData="fetchAreaData"
            :fetchFocusData="fetchFocusData"
          />
  
          <el-icon class="close-icon" size="2em" color="#fff" @click="isMap = false">
            <CloseBold />
          </el-icon>
        </div> -->
        <!-- <div class="questionList" v-show="isMap">
          <div style="font-size: 13px">问题列表</div>
          <div
            class="question-item"
            v-for="question in questionList"
            :key="question.questionId"
            @click="clickQuestion(question.questionId)"
          >
            <div class="title-line">
              "
              <span v-html="question.keyword"></span>
              "
              <span v-if="question.questionType != '开放性问题'"> {{ question.questionType }}</span>
              <span v-html="question.questionDescription" v-else></span>
            </div> -->
        <!-- <div
              @click.stop="handleDeleteQuestion(question.questionId)"
              v-if="question.canDelete"
              style="cursor: pointer; z-index: 100000; margin-right: 20px"
            >
              ×
            </div> -->
        <!-- </div> -->
        <!-- </div> -->
  
        <div id="draft" @wheel.stop class="content-wrapper">
          <PrjManuscript
            @scrollInTop="handleScroll"
            ref="prjManuscript"
            :big="isBig ? true : false"
            :videoCaptionList="videoCaptionList"
            :questionList="questionList"
            @returnInit="handleReturnInitFn"
            @refresh="handleQuestionList"
            @delete-question="handleDeleteQuestion"
            :isMap="isMap"
            v-if="ready"
            @wheel="handleVideoWheel"
            @search="handleSearch"
            class="lineWordContent"
          >
          </PrjManuscript>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { getPrjDetailApi, getPrjSectionApi } from '@/apis/learning';
  import type { Chapter, VideoCaptionListObj, QuestionData } from '@/types/learning';
  import { STATE_FLAG } from '@/types/learning';
  import XgPlayer from '@/components/XgPlayer.vue';
  import { defineExpose } from 'vue';
  import { getQuestionListApi, deleteQuestionApi } from '@/apis/learning';
  import PrjManuscript from './PrjManuscript.vue';
  import ksg_window from './ksg_window.vue';
  import { PrjType } from '@/types/project';
  import userBehaviour from '@/utils/userBehaviour';
  import { PrjForm } from '@/types/project';
  import { useLearningStore } from '@/stores/learning';
  import { useDrawerControllerStore } from '@/stores/drawerController';
  import { useWordStore } from '@/stores/word';
  import { emitter } from '@/utils/emitter';
  import { ArrowUpBold, ArrowDownBold } from '@element-plus/icons-vue';
  
  const router = useRouter();
  const route = useRoute();
  const learningStore = useLearningStore();
  const drawerControllerStore = useDrawerControllerStore();
  const wordStore = useWordStore();
  const { mode } = storeToRefs(drawerControllerStore);
  watch(
    () => mode.value,
    (newVal) => {
      let index = 0;
      if (newVal) {
        if (wordStore.getOriginalVideoStringList.length > 0) {
          const originalVideoStringList = wordStore.getOriginalVideoStringList;
          videoCaptionList.value?.forEach((videoCaption) => {
            videoCaption.forEach((item) => {
              item.caption = originalVideoStringList[index++];
            });
          });
        }
      } else {
        if (wordStore.getVideoStringList.length > 0) {
          const videoStringList = wordStore.getVideoStringList;
          videoCaptionList.value?.forEach((videoCaption) => {
            videoCaption.forEach((item) => {
              item.caption = videoStringList[index++];
            });
          });
        }
      }
    }
  );
  
  const handleSearch = () => {
    let index = 0;
    if (mode.value) {
      const originalVideoStringList = wordStore.getOriginalVideoStringList;
      videoCaptionList.value?.forEach((videoCaption) => {
        videoCaption.forEach((item) => {
          item.caption = originalVideoStringList[index++];
        });
      });
    } else {
      const videoStringList = wordStore.getVideoStringList;
      videoCaptionList.value?.forEach((videoCaption) => {
        videoCaption.forEach((item) => {
          item.caption = videoStringList[index++];
        });
      });
    }
  };
  
  /**
   * 显示视频功能
   */
  const isBig = ref(false); // 大屏小屏
  const bigScreen = ref(0); // 0：大屏视频|1：大屏字幕
  const isSectionMenuShow = ref(false); //控制多节菜单的显示
  const player = ref<InstanceType<typeof XgPlayer> | null>(null);
  const prjManuscript = ref<InstanceType<typeof PrjManuscript>>();
  
  // if案例项目，小节号（if从开始学习|继续学习进来的then最新小节，else自选的节号）
  const initChapterId = route.query.chapterId as string;
  const spuId = route.query.spuId as string;
  const prjType = inject('prjType') as Ref; // 1讲解 2案例 3测评
  // 项目的prjId
  const prjId = ref<string>('');
  const uniqueCode = ref<string>('');
  // 项目的章节id
  const curChapterId = ref();
  // 项目详情
  const projectDetailData = ref<Chapter>();
  // 章节相关的数据
  const chapterList = ref<Chapter[]>();
  const activeIndex = ref();
  // 拿到章节信息
  const videoCaptionList = ref<[VideoCaptionListObj[]]>();
  const questionList = shallowRef<QuestionData[]>();
  const playerTime = ref<number>(0);
  const { initUserBehaviour, handleChapterChange } = userBehaviour(
    PrjForm.video,
    activeIndex,
    curChapterId,
    playerTime
  );
  
  // 提供isBig数据
  provide(
    'isBig',
    computed(() => toValue(isBig))
  );
  // const isBig = inject('isBig') as Ref;
  const isMap = ref(false); // 是否展示地图
  // 提供isMap数据
  provide(
    'isMap',
    computed(() => toValue(isMap.value))
  );
  // const emits = defineEmits('closeMap');
  const toggleDisplay = () => {
    if (!isBig.value) {
      //isMap.value = false;
      isBig.value = true;
      //
      // prjManuscript.value.changeStateFn(STATE_FLAG.init);
    } else {
      //isMap.value = true;
      isBig.value = false;
    }
  };
  const switchScreen = (direction) => {
    if (bigScreen.value == direction) {
      return;
    }
    bigScreen.value = direction;
    if (direction == 0) {
      //isMap.value = true;
      player.value?.exitPIP();
    } else {
      isMap.value = false;
      player.value?.requestPIP();
    }
  };
  
  //================================ksg-map================================
  import { getAreaData, getFocusData, getChapterGraph } from '@/apis/ksgmap';
  import KsgMap from 'ksg-map';
  import type {
    GlobalConfig,
    OriginalData,
    AreaData,
    PointData,
    FocusData
  } from 'ksg-map/dist/types';
  const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
  const target = ref<HTMLElement | null>(null);
  const config = ref<GlobalConfig>({
    fullScreen: false,
    showFullScreenButton: true,
    showArea: false,
    maxLevel: Infinity,
    targetArea: 'rootArea',
    allowFocus: false,
    autoLoadLevel: false
  });
  const data = ref<OriginalData>({
    topAreas: [],
    areas: [],
    focuses: [],
    points: []
  });
  async function fetchAreaData(id: string): Promise<AreaData[]> {
    return await getAreaData(id);
  }
  async function fetchFocusData(id: string): Promise<FocusData> {
    return await getFocusData(id);
  }
  
  const saveType = inject('saveType') as Ref;
  
  onMounted(async () => {
    saveType.value = 0;
    target.value = document.getElementById('app')!;
    const unwatch = watch(
      () => curChapterId.value,
      async () => {
        if (curChapterId.value) {
          const pointsData = await projectGraph(spuId, curChapterId.value);
          await ksgMap.value?.ready;
          data.value.topAreas = [];
          data.value.points = pointsData;
  
          ksgMap.value?.reloadData();
        }
      }
    );
  });
  //================================ksg-map================================
  
  const handleWheel = (e) => {
    if (!isBig.value) return;
    // console.log(e.deltaY)
    if (e.deltaY > 0) {
      switchScreen(1);
    } else if (e.deltaY < 0) {
      switchScreen(0);
    } else {
      alert('用户鼠标没有上下滑动');
    }
  };
  const handleScroll = (e) => {
    switchScreen(0);
  };
  const toggleMap = () => {
    isMap.value = !isMap.value;
  
    if (isMap.value) {
      player.value?.requestPIP();
    } else {
      player.value?.exitPIP();
    }
  };
  watch(
    () => isBig.value,
    (newV) => {
      console.log('111isbig', newV);
    }
  );
  watch(
    () => bigScreen.value,
    (newV) => {
      console.log('111bigScreen', newV);
    }
  );
  watch(
    () => isMap.value,
    (newV) => {
      console.log('111map', newV);
    }
  );
  
  // 视频播放器实例
  // TODO：不优雅，后期优化
  const getSeconds = (seconds: string) => {
    // 将 1   61 这种数字或者字符串转为   00:00:01  00:01:01
    if (!seconds) return 0;
    const array = seconds.split(':');
    let res = 0;
    array.forEach((item) => {
      res = res * 60 + parseInt(item, 10);
    });
    return res;
  };
  /**
   * 文稿和视频联动功能
   */
  
  const clickQuestion = (questionId: string) => {
    // prjManuscript.value.handleQuestionList(questionId);
    drawerControllerStore.questionId = questionId;
  };
  const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
    const res = await getQuestionListApi(uniqueCode, chapterId);
    questionList.value = res.data;
    const worker = new Worker(new URL('@/utils/videoWorker.ts', import.meta.url), {
      type: 'module'
    });
    worker.onmessage = (
      e: MessageEvent<{
        videoStringList: Array<{ list: Array<string>; start: number }>;
        originalVideoStringList: Array<{ list: Array<string>; start: number }>;
        regString: string;
        unitList: Array<{
          tagName: string;
          children: any[];
          index: number;
          qids: number[];
          highlight: boolean;
          stringIndex: number;
        }>;
        uncommonWordMap: Map<string, string>;
      }>
    ) => {
      wordStore.videoStringList = e.data.videoStringList;
      wordStore.originalVideoStringList = e.data.originalVideoStringList;
      wordStore.regString = e.data.regString;
      wordStore.unitList = e.data.unitList;
      wordStore.uncommonWordMap = e.data.uncommonWordMap;
      let index = 0;
      const videoStringList = wordStore.getVideoStringList;
      videoCaptionList.value?.forEach((videoCaption) => {
        videoCaption.forEach((item) => {
          item.caption = videoStringList[index++];
        });
      });
      // 当在worker中打印日志信息时，需要查看obj数据则注释下面代码
      worker.terminate();
    };
    // //TODO
    // questionList.value = questionList.value?.filter((item)=> item.questionId == questionId)
    // console.log('111', questionList.value)
    worker.postMessage({
      questionList: toRaw(questionList.value),
      videoCaptionList: toRaw(videoCaptionList.value)
    });
  };
  const handleDeleteQuestion = async (questionId: string) => {
    deleteQuestionApi(questionId).then(async (res) => {
      await handleQuestionList(uniqueCode.value, curChapterId.value as string);
      ElMessage.success('删除成功');
      // console.log(res.data.result)
      const newvalue = res.data.result;
      // AsyncPrjManuscript = defineAsyncComponent(() => import('./PrjManuscript.vue'));
      // nextTick(()=>{
      //   console.log(prjManuscript.value.test)
      // })
      // setTimeout(() => {
      //   console.log(prjManuscript.value.test)
      // }, 5000);
      console.log(prjManuscript.value);
      prjManuscript.value!.afterDeleteQuestion(newvalue);
      // wordContent.value='123'
      // console.log(wordContent.value)
    });
  };
  
  // TODO: 后期优化，增加节流
  let scrollBarAble = true;
  let scrollBarTimer: any;
  const handleVideoWheel = () => {
    scrollBarAble = false;
    if (scrollBarTimer) {
      clearTimeout(scrollBarTimer);
    }
    scrollBarTimer = setTimeout(() => {
      scrollBarAble = true;
    }, 2000);
  };
  const tempOid = ref(-1);
  const handleTimeupdateFn = (currentTime: number | string) => {
    // myPlayer的获取有问题，waq注释了这一句 todo需要修改
    // if (!myPlayer.value) return;
    playerTime.value = currentTime as number;
    // console.log('currentTime1111', currentTime)
    // FIXME: hoverPList没了，可能得换一个东西看
    // const hoverPList = prjManuscript.value?.hoverPList as VideoCaptionListObj[];
    // const hoverPList=videoCaptionList.value
    // console.log((e.target as HTMLVideoElement).currentTime.toFixed(2), typeof currentTime);
    // console.log(currentTime, typeof currentTime);
    // console.log('hoverPList', prjManuscript.value?.hoverPList);
    // console.count();
    let manuIndex = 0; //当前字幕段
    let idx = -1;
    let curoid = -1;
    manuIndex = videoCaptionList.value!.findIndex((hoverPList) => {
      idx = hoverPList?.findIndex((hoverParam) => {
        // console.log(hoverParam)
        if (
          getSeconds(hoverParam.startTime) <= currentTime &&
          getSeconds(hoverParam.endTime) >= currentTime
        ) {
          // console.log(hoverParam.caption)
          curoid = hoverParam.oid;
          return true;
        }
      });
      // console.log('idx', idx)
      if (idx != -1) return true;
    });
    if (idx != -1 && curoid != tempOid.value) {
      // console.log('idx',idx)
      // console.log('index',index)
      tempOid.value = curoid;
      prjManuscript.value!.hoverPList(curoid); //字幕高亮
      if (scrollBarAble) {
        prjManuscript.value!.scrollBar(manuIndex); // 滑动滚动条
      }
    }
  };
  
  provide(
    'prjSectionInfo',
    computed(() => ({
      chapterId: curChapterId.value,
      prjId: prjId.value,
      uniqueCode: uniqueCode.value
    }))
  );
  
  // 处理章节变化
  
  //当我从一个章节切到另一个章节时，我需要发送现在章节的end请求和新章节的start请求, 结束现在章节的start请求和新章节的end请求
  
  const handleChangeSectionFn = async (chapterId: number) => {
    if (activeIndex.value != chapterId) {
      router.replace({
        query: {
          ...route.query,
          chapterId: chapterId
        }
      });
      handleChapterChange(chapterId);
      activeIndex.value = chapterId;
      // console.log(uniqueCode.value, chapterId);
      const res = await getPrjSectionApi(spuId, chapterId);
      // console.log('处理章节变化', res);
      // 拿到了新的video资源
      projectDetailData.value = res.data;
      //   changeVideoFn(prjDetailData.value?.videoUrl as string);
      curChapterId.value = projectDetailData.value?.chapterId;
      videoCaptionList.value = res.data.videoCaptionList; //
      await handleQuestionList(uniqueCode.value, chapterId.toString());
    }
  };
  
  // 小屏转大屏的时候操控状态STATE_FLAG
  // 切换状态
  const handleReturnInitFn = () => {
    prjManuscript.value!.changeStateFn(STATE_FLAG.init);
  };
  
  // 异步组件
  // TODO
  // @ts-ignore
  // let AsyncPrjManuscript;
  // const showAsync = ref(false);
  const handleDocumentClick = (event: MouseEvent) => {
    if (!event.target.closest('.sectionMenu')) {
      console.log('点击了其他地方old', isSectionMenuShow.value);
      if (isSectionMenuShow.value == true) {
        isSectionMenuShow.value = !isSectionMenuShow.value;
        console.log('点击了其他地方new', isSectionMenuShow.value);
      }
    }
  };
  const ready = ref(false);
  onMounted(async () => {
    // console.log('父组件VideoContent onMounted');
    // // 拿到项目信息
    // if(!spuId)
    //   spuId=sessionStorage.getItem('spuId') as string;
    // const res = await getPrjDetailApi(spuId, initChapterId); // 两个接口返回的数据格式不一致
    // console.log(res)
    // // TODO:这里应该再接受一个type字段
    // let idx = res.data.validIndex; // 后端会把所有小节都返还，但只有一个对象是有信息的，就是这个index对应的小节
    // prjDetailData.value = res.data.list.chapterList[idx];
    // //   initVideo(); // 初始化视频
    // curChapterId.value = prjDetailData.value?.chapterId;
    // chapterList.value = res.data.list.chapterList; // 拿到章节列表
    // activeIdx.value = res.data.list.chapterList[idx].chapterId;
    // //todo 接口修改需要调整,原来从接口读出来的是prjId
    // //gc说改为prjId
    // prjId.value = res.data.list.prjId; // 拿到项目id
    // videoCaptionList.value = res.data.list.chapterList[idx].videoCaptionList; // 拿到章节信息
    // console.log('111', videoCaptionList.value);
    // questionList.value = res.data.list.chapterList[idx].questionList; // 拿到问题信息
  
    // // AsyncPrjManuscript = defineAsyncComponent(() => import('./PrjManuscript.vue'));
    // handleQuestionList(activeIdx.value)
    // showAsync.value=true;
    // nextTick(()=>{
    //   console.log(prjManuscript.value)
    //     console.log(prjManuscript.value.test)
    //   })
    //点击其他区域的时候修改isSectionMenuShow的值为flase
    if (!learningStore.written) {
      const res = await getPrjDetailApi(spuId, initChapterId);
      console.log('getprjdetail:', res);
      learningStore.setInfo(res.data);
      learningStore.chapterId = learningStore.chapterList[0].chapterId;
      router.replace({
        query: {
          ...route.query,
          chapterId: learningStore.chapterId
        }
      });
    }
    // TODO:这里应该再接受一个type字段
    let idx = learningStore.validIndex; // 后端会把所有小节都返还，但只有一个对象是有信息的，就是这个index对应的小节
    projectDetailData.value = learningStore.chapterList[idx];
    //   initVideo(); // 初始化视频
    curChapterId.value = projectDetailData.value?.chapterId;
  
    chapterList.value = learningStore.chapterList; // 拿到章节列表
    activeIndex.value = learningStore.chapterList[idx]?.chapterId;
    //todo 接口修改需要调整,原来从接口读出来的是prjId
    //gc说改为prjId
    prjId.value = learningStore.prjId; // 拿到项目id
    uniqueCode.value = learningStore.uniqueCode;
    videoCaptionList.value = learningStore.chapterList[idx]?.videoCaptionList; // 拿到章节信息
    questionList.value = learningStore.chapterList[idx]?.questionList; // 拿到问题信息
    // uniqueCode.value = res.data.uniqueCode;
    // AsyncPrjManuscript = defineAsyncComponent(() => import('./PrjManuscript.vue'));
    await handleQuestionList(uniqueCode.value, curChapterId.value as string);
    // showAsync.value = true;
    initUserBehaviour(curChapterId.value);
    ready.value = true;
    document.addEventListener('click', handleDocumentClick);
    emitter.emit('initHandler', async () => {
      const res = await getPrjSectionApi(spuId, curChapterId.value);
      // console.log('处理章节变化', res);
      // 拿到了新的video资源
      projectDetailData.value = res.data;
      //   changeVideoFn(prjDetailData.value?.videoUrl as string);
      curChapterId.value = projectDetailData.value?.chapterId;
      videoCaptionList.value = res.data.videoCaptionList; //
      await handleQuestionList(uniqueCode.value, curChapterId.value);
    });
  });
  // onBeforeUpdate(() => {
  //   console.count('父组件更新的次数');
  //   console.log('父组件onBeforeUpdate');
  // });
  onBeforeUnmount(() => {
    // console.log('父组件onBeforeUnmount');
    // console.log('视频结束时长'+endTime.value.endTime)
    document.removeEventListener('click', handleDocumentClick);
  });
  defineExpose({ curChapterId });
  </script>
  
  <style scoped lang="less">
  .sectionMenu {
    //margin: 10px;
    cursor: pointer;
    //transform: scale(2);
  }
  .sectionMenuContent {
    border: 1px solid rgb(220, 223, 230);
    border-radius: 5px;
    position: absolute;
    transition: all 0.5s ease-in-out;
    left: 90px;
    //width: 284px;
    // height: calc(100vh - 70px - 90px - 63px);
    z-index: 10;
    background-color: white;
    box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 5px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      background: var(--color-grey);
    }
    .section {
      height: 41px;
      font-size: 14px;
      padding: 0 10px;
      background-color: white;
      display: flex;
      align-items: center;
      font-family: var(--text-family);
      color: var(--color-black);
      cursor: pointer;
      &:hover {
        background-color: #f2f2f2;
      }
      .sectionTitle {
        margin-left: 5px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .main-wrapper {
    display: flex;
    //margin-top: 10px;
  }
  
  .videoPage {
    .left-video-wrapper {
      min-height: 485px;
      height: calc(100vh - 60px - 70px - 250px);
    }
    .map-wrapper {
      min-height: 485px;
      height: calc(100vh - 60px - 70px - 250px);
    }
  
    .right-content {
      min-height: 485px;
    }
    .map-content {
      min-height: 485px;
    }
  }
  
  .left-video-wrapper {
    margin-left: 44px;
    width: calc(61.8vw - 10px);
    margin-right: 10px;
    display: flex;
    scroll-snap-align: start;
  
    .left-title-list {
      width: 25px;
      //margin-left: 44px;
      margin-right: 13px;
      // background-color: coral;
      display: flex;
      flex-direction: column;
      align-items: center;
  
      .title {
        font-size: 12px;
        font-weight: 400;
        color: #797979;
      }
    }
  
    .left-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      width: calc(61.8vw - 10px);
      &.big {
        height: calc(100vh - 160px);
      }
  
      .video-title {
        padding-left: 10px;
        padding-right: 10px;
        position: relative;
        bottom: 0px;
        background-color: #f2f2f2;
        height: 63px;
        font-size: 24px;
        font-weight: 400;
        color: var(--color-black);
        font-family: var(--title-family);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .btn {
          background-color: white;
          border-radius: 14px;
          height: 28px;
          font-size: 14px;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 10px;
          border: 1px solid var(--color-theme-project);
          color: var(--color-theme-project);
          font-family: var(--text-family);
          &:hover {
            background-color: var(--color-theme-project);
            color: white;
            cursor: pointer;
          }
        }
        .icon {
          cursor: pointer;
        }
  
        .icon-wrapper {
          width: 16px;
          height: 12px;
          margin: 0 5px;
          background: url('@/assets/svgs/u2985.svg') no-repeat center/contain;
          cursor: pointer;
  
          &:hover {
            background-image: url('@/assets/svgs/u4176.svg');
          }
        }
      }
  
      .video {
        // background-color: rgb(242, 242, 242);
        flex: 1;
        position: relative;
  
        .coverPic-wrapper {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          background-color: var(--color-black);
          background-repeat: no-repeat;
          background-size: cover;
          // background-size: 100% auto;
        }
  
        .video-btn-wrapper {
          width: 50px;
          height: 50px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          cursor: pointer;
        }
  
        .expand-logo {
          position: absolute;
          right: 10px;
          // bottom: 10px;
          cursor: pointer;
  
          &:hover {
            font-weight: 500;
          }
        }
      }
  
      .video-footer-info {
        height: 40px;
        display: flex;
        align-items: center;
        background-color: #ffffff;
        box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
        border: 1px solid rgb(242, 242, 242);
        padding-left: 10px;
        width: 100%;
        position: relative;
  
        .video-footer {
          vertical-align: middle;
          transform: translateY(-3px);
        }
  
        .footer-logo-wrapper {
          width: 90%;
          display: flex;
          align-items: center;
          position: absolute;
        }
  
        .footer-title {
          font-size: 18px;
          font-weight: 300;
          color: var(--color-black);
          margin-left: 17px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
  
  .map-wrapper {
    margin-left: 44px;
    width: calc(61.8vw - 10px);
    margin-right: 10px;
    display: flex;
    scroll-snap-align: start;
  
    .left-title-list {
      width: 25px;
      //margin-left: 44px;
      margin-right: 13px;
      // background-color: coral;
      display: flex;
      flex-direction: column;
      align-items: center;
  
      // .section {
      //   height: 41px;
      //   font-size: 14px;
      //   padding: 0 10px;
      //   background-color: white;
      //   display: flex;
      //   align-items: center;
      //   font-family: var(--text-family);
      //   color: var(--color-black);
      //   cursor: pointer;
      //   &:hover {
      //     background-color: #f2f2f2;
      //   }
      //   .sectionTitle {
      //     margin-left: 5px;
      //     word-break: break-all;
      //     overflow: hidden;
      //     text-overflow: ellipsis;
      //     white-space: nowrap;
      //   }
      // }
  
      .title {
        font-size: 12px;
        font-weight: 400;
        color: #797979;
      }
  
      //.title-line {
      //  width: 25px;
      //  height: 24px;
      //  text-align: center;
      //  line-height: 24px;
      //  background-color: #f2f2f2;
      //  border-radius: 5px 0px 0px 5px;
      //  font-size: 13px;
      //  font-weight: 400;
      //  color: var(--color-black);
      //  margin-bottom: 2px;
      //  cursor: pointer;
      //
      //  &.active {
      //    background-color: var(--color-theme-project);
      //    color: rgb(254, 254, 246);
      //    border-radius: 5px 0px 0px 5px;
      //    box-shadow: rgba(0, 85, 121, 0.376) 0px 3px 3px 0px;
      //  }
      //}
    }
  
    .left-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      width: calc(61.8vw - 10px);
      display: flex;
      &.big {
        height: calc(100vh - 160px);
      }
  
      .video-title {
        padding-left: 10px;
        padding-right: 10px;
        position: relative;
        bottom: 0px;
        background-color: #f2f2f2;
        height: 63px;
        font-size: 24px;
        font-weight: 400;
        color: var(--color-black);
        font-family: var(--title-family);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .btn {
          background-color: white;
          border-radius: 14px;
          height: 28px;
          font-size: 14px;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 10px;
          border: 1px solid var(--color-theme-project);
          color: var(--color-theme-project);
          font-family: var(--text-family);
          &:hover {
            background-color: var(--color-theme-project);
            color: white;
            cursor: pointer;
          }
        }
        .icon {
          cursor: pointer;
        }
  
        .icon-wrapper {
          width: 16px;
          height: 12px;
          margin: 0 5px;
          background: url('@/assets/svgs/u2985.svg') no-repeat center/contain;
          cursor: pointer;
  
          &:hover {
            background-image: url('@/assets/svgs/u4176.svg');
          }
        }
      }
  
      .video {
        // background-color: rgb(242, 242, 242);
        flex: 1;
        position: relative;
  
        .coverPic-wrapper {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          background-color: var(--color-black);
          background-repeat: no-repeat;
          background-size: cover;
          // background-size: 100% auto;
        }
  
        .video-btn-wrapper {
          width: 50px;
          height: 50px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          cursor: pointer;
        }
  
        .expand-logo {
          position: absolute;
          right: 10px;
          // bottom: 10px;
          cursor: pointer;
  
          &:hover {
            font-weight: 500;
          }
        }
      }
  
      .video-footer-info {
        height: 40px;
        display: flex;
        align-items: center;
        background-color: #ffffff;
        box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
        border: 1px solid rgb(242, 242, 242);
        padding-left: 10px;
        width: 100%;
        position: relative;
  
        .video-footer {
          vertical-align: middle;
          transform: translateY(-3px);
        }
  
        .footer-logo-wrapper {
          width: 90%;
          display: flex;
          align-items: center;
          position: absolute;
        }
  
        .footer-title {
          font-size: 18px;
          font-weight: 300;
          color: var(--color-black);
          margin-left: 17px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
  .map-content {
    height: 704px;
  }
  .right-map {
    // background-color: var(--color-second);
    width: 100%;
    height: 704px;
    position: relative;
  
    .close-icon {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
    }
  }
  .right-content {
    // min-height: 485px;
    height: calc(100vh - 60px - 70px - 66.5px); // right-content的offsetTop为150px
    width: 35%;
    // overflow: hidden;
    scroll-snap-align: start;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  
    .questionList {
      width: 100%;
      height: 60%;
      background-color: #f2f2f2;
      border-radius: 5px;
      box-sizing: border-box;
      padding: 2%;
      overflow: auto;
      .question-item {
        font-size: 13px;
        width: 100%;
        min-height: 50px;
        background-color: white;
        border-radius: 4px;
        padding-left: 6px;
        margin-top: 8px;
        box-sizing: border-box;
        padding: 5px;
        display: flex;
        justify-content: space-between;
        &:hover {
          cursor: pointer;
        }
        .title-line {
          display: flex;
          align-items: center;
        }
      }
    }
  
    .content-wrapper {
      background-color: #f2f2f2;
      // width: 100%;
      // height: 100%;
    }
  }
  .main-wrapper.big {
    display: block;
    .left-video-wrapper {
      width: calc(100% - 77px - 77px + 38px);
      //min-height: 100vh;
    }
    .map-wrapper {
      width: calc(100% - 77px - 77px + 38px);
    }
  
    .right-content {
      box-sizing: border-box;
      //height: 100%;
      width: calc(100% - 60px);
      max-width: 1380px;
      // height: 800px;
      //min-height: 100vh;
      margin: 0 auto;
      //height: 100%;
  
      &:deep(.text-wrapper) {
        height: 800px !important; // 大屏的高度
      }
    }
  }
  .switchFloor {
    position: absolute;
    right: 10px;
    top: calc(50% + 30px);
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .btn {
      cursor: pointer;
      background-color: #dcdfe6;
      height: 30px;
      width: 30px;
      border-radius: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--color-deep);
      &:hover,
      &.light {
        background-color: var(--color-theme-project);
        color: white;
      }
      //&.light {
      //  background-color: var(--color-theme-project);
      //  color: white;
      //}
    }
  }
  </style>
  