import { http } from '@/apis';
import qs from 'qs';
import { timestampToTime, formatTime } from '@/utils/timeUtils';
import type { PrjForm, PrjType } from '@/types/project';
import type { QuestionType } from '@/types/question';

// 获取项目基本信息 (获取项目的部分信息接口)
export function getPartProApi(spuId: string) {
  return http.request<{
    list: Array<{
      prjType: PrjType;
      createTime: string;
      prjForm: PrjForm;
      id: number;
      title: string;
      userName: string;
      userCoverPic: string;
      prjTags: Array<{
        id: number;
        content: string;
      }>;
    }>;
  }>({
    method: 'get',
    url: '/tprj/getPart',
    params: {
      spuId
    }
  });
}

// 获取项目详情信息(初始化项目详情信息)
// 获取项目详情信息文稿与视频都用的是同一个接口
export function getPrjDetailApi(spuId: string, chapterId: string) {
  return http.request<{
    validIndex: number;
    prjId: number;
    uniqueCode: string;
    prjForm: PrjForm;
    prjType: PrjType;
    chapterList: any[];
  }>({
    method: 'get',
    url: '/tprj/projectInfo',
    params: {
      spuId,
      chapterId
    }
  });
}

// 获取小节信息接口
export function getPrjSectionApi(spuId: string, chapterId: string) {
  return http.request<{
    videoUrl: string;
    chapterId: number;
    chapterName: string;
    chaCoverPic: string;
    videoCaptionList: any[];
    chapterNum: number;
  }>({
    method: 'get',
    url: '/tprjVideo/getVideo',
    params: {
      spuId,
      chapterId
    }
  });
}

export interface QuestionItf {
  /**
   * 关联文本内容
   */
  associatedWords: string;
  /**
   * 小节id，内容所属节ID
   */
  sectionId: number;
  /**
   * spuId，唯一标识id
   */
  spuId: string;
  /**
   * 内容id
   */
  // contentId: string;  // 改成 assessmentId
  assessmentId: number;
  /**
   * 关键字
   */
  keyword: string;
  /**
   * 问题类型，是什么、为什么、怎么做、开放性问题
   */
  questionType: string;
  [property: string]: any;
}
//知识详情页获取项目+测评的ID列表
export function getIdListApi(klgCode: string, tagId: number) {
  return http.request({
    method: 'get',
    url: '/project-goods/getExerciseRecommendList',
    params: {
      klgCode,
      tagId
    }
  });
}
export function getExerciseDetailApi(exerciseId: string) {
  return http.request({
    method: 'get',
    url: '/project-goods/getExerciseDetail',
    params: {
      exerciseId
    }
  });
}
export function addQuestionApi(data: QuestionItf) {
  return http.request({
    method: 'post',
    url: '/question/save',
    data
  });
}

export function getPrjMoreInfoApi(spuId: string) {
  return http.request({
    method: 'get',
    url: '/tprj/detail',
    params: {
      spuId
    }
  });
}

// 下面两个接口都是文稿项目

export function getManuProjectSectionApi(spuId: string, chapterId: string) {
  return http.request<{
    wordContentType: number;
    chapterName: string;
    contentId: number;
    wordContent: string;
    chapterId: number;
  }>({
    method: 'get',
    url: '/tprjWords/getWords',
    params: {
      spuId,
      chapterId
    }
  });
}

//文稿类划词提交接口
export function saveQuestionApi(param: {
  associatedWords: string;
  keyword: string;
  questionType: QuestionType;
  questionDescription: string;
  uniqueCode: string;
  chapterId: string;
  contentId?: string;
}) {
  return http.request<{
    questionId: 4106;
    keyword: string;
    questionType: string;
    questionDescription: string;
    associatedWords: string;
    canDelete: boolean;
  }>({
    method: 'post',
    url: '/question/save',
    data: param
  });
}

//点击获取问题列表
export function getQuestionDetailApi(questionId: string) {
  return http.request<
    Array<{
      keyword: string;
      questionDescription: string;
      questionId: number;
      associatedWords: string;
      questionNecessity: number;
      answers: any[];
      contentId: number;
      userName: string;
      createTime: string;
      chapterId: number;
      canDelete: boolean;
      prjId: null;
      questionType: string;
    }>
  >({
    method: 'get',
    url: `/question/query`,
    params: {
      questionId
    }
  });
}

//获取问题相关知识
export function getKlgListApi(param: string) {
  return http.request({
    method: 'get',
    url: `/tprj/getKlgList/${param}`
  });
}

//回答问题
export function saveAnswerApi(param: {}) {
  return http.request({
    method: 'post',
    url: '/answer/saveAnswer',
    data: param
  });
}

//删除回答
export function deleteAnswerApi(answerId: string) {
  return http.request({
    method: 'post',
    url: `/answer/deleteAnswer`,
    data: {
      answerId
    }
  });
}

//页面获取问题列表
export function getQuestionListApi(uniqueCode: string, chapterId: string) {
  return http.request<
    Array<{
      questionId: number;
      keyword: string;
      questionType: string;
      questionDescription: string;
      canDelete: boolean;
    }>
  >({
    method: 'get',
    url: `/question/query/all`,
    params: {
      uniqueCode,
      chapterId
    }
  });
}

//删除问题
export function deleteQuestionApi(questionId: string) {
  return http.request({
    method: 'post',
    url: `/question/deleteQuestion`,
    data: {
      questionId
    }
  });
}

// 获取讲稿内容
export function getVideoContentApi(chapterId: string, spuId: string) {
  return http.request({
    method: 'get',
    url: `/tprjVideo/detail?chapterId=${chapterId}&prjId=${spuId}`
  });
}

//获取测评内容
export function getExamContentApi(assessmentId: string) {
  return http.request<{
    questionList: any;
    content: {
      uniqueCode: string;
      chapterId: string;
      contentId: string;
      examType: number;
      examTitle: string;
      examChoices: any[];
      examAnswer: string;
      examExplanation: string;
    };
  }>({
    method: 'get',
    url: `/tprjWords/getExamContent?examId=${assessmentId}`
  });
}

//测评类划词提交接口
export function saveExamQuestionApi(param: {}) {
  return http.request({
    method: 'post',
    url: '/tprjWords/markWords',
    data: param
  });
}

//获取对象存储临时密钥
export function getTempKeyApi() {
  return http.request<{
    SecurityToken: string;
    TmpSecretKey: string;
    ExpiredTime: number;
    StartTime: number;
    TmpSecretId: string;
  }>({
    method: 'get',
    url: `/cos-sts-client/getTemporaryKey`
  });
}

export function saveLearnProcessApi(
  useBeacon: boolean,
  spuId: string,
  processStartTime: number,
  processEndTime: number,
  processSectionId?: string | number | null,
  endTimeTag?: number | null
) {
  if (useBeacon) {
    navigator.sendBeacon(
      'https://www.endlessorigin.com/champaign/learning-process/saveLearnProcess',
      new Blob(
        [
          qs.stringify({
            spuId,
            processStartTime: timestampToTime(processStartTime),
            processEndTime: timestampToTime(processEndTime),
            processSectionId,
            endTimeTag: formatTime(endTimeTag)
          })
        ],
        {
          type: 'application/x-www-form-urlencoded'
        }
      )
    );
  } else {
    return http.request<{}>({
      method: 'post',
      url: '/learning-process/saveLearnProcess',
      data: qs.stringify({
        spuId,
        processStartTime: timestampToTime(processStartTime),
        processEndTime: timestampToTime(processEndTime),
        processSectionId,
        endTimeTag: formatTime(endTimeTag)
      })
    });
  }
}
