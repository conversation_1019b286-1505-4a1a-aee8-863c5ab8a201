<template>
  <div class="prj-learn-wrapper">
    <div
      class="prj-learn"
      :class="{
        wideScreen: useWideScreen
      }"
    >
      <div class="left-route">
        <div class="up-detail-card">
          <div class="prj-img">
            <img style="width: 298px; height: 185px" :src="info.coverPic" alt="" />
          </div>
          <div class="prj-info">
            <div class="title">
              <h1>{{ info.title }}</h1>
            </div>
            <div class="detail">
              {{ info.description }}
            </div>
          </div>
        </div>
        <div class="down-routes" v-if="showType == ShowType.map">
          <div class="route-items active">知识地图</div>
        </div>
      </div>
      <!-- 在此处控制组件的渲染 -->
      <div class="right-info">
        <!-- 测评首页 -->
        <KlgMap v-if="showType == ShowType.map"></KlgMap>
        <!-- 做题 -->
        <DraftExam v-if="showType == ShowType.draft" :state="state"></DraftExam>
        <!-- 提问 -->
        <VideoExam v-if="showType == ShowType.video"></VideoExam>
      </div>
    </div>
    <PayDialog v-model="payDialogVisible" />
  </div>
</template>

<script setup lang="ts">
/* 引入组件 */
import KlgMap from './components/KlgMap.vue';
import DraftExam from './components/DraftExam.vue';
import VideoExam from './components/VideoExam.vue';
import { getPrjIntroduceApi } from '@/apis/case';
import { useRouter } from 'vue-router';
import PayDialog from '@/components/PayDialog.vue';
import { useProjectStore } from '@/stores/project';
import { ShowType } from '@/types/exam';
/**
 * 从其他页面跳转到本页面，带着项目相关id，向后端请求获取数据，传递到klgMap进行显示
 */
const router = useRouter();
const route = useRoute();
const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
const payDialogVisible = ref(false);
provide('payDialogVisible', payDialogVisible);
// 传递给做题组件的信息

const spuId = route.query.spuId as string;

// 答题传参
const state = ref({
  Title: '这是一个默认的title',
  current: 1, // 如果做完了，跳到第一题,
  showWrongSets: false
});

onMounted(async () => {
  const res = await getPrjIntroduceApi({ spuId });
  projectStore.setPrjInfo(res.data);
  // 此处的数据没有做持久化管理
  // 后续可以根据需求添加
  // ideal:
  // state.value.current = res.data.currentCount == res.data.total ? 0 : res.data.currentCount; // 如果这么写， 初始化1也让后端做
  // FIXME：这个接口里没有currentCount和total，我认为得让后端加上，既然案例关联的测试可以有进度保存，那么直接进入测评项目也应该有
  state.value.Title = res.data.title;
  useWideScreen.value = false;
});

const showType = ref<ShowType>(ShowType.map);
provide('showType', showType);
const useWideScreen = inject('useWideScreen') as Ref<boolean>;
watch(
  () => showType.value,
  (newVal) => {
    if (newVal == ShowType.map) {
      useWideScreen.value = false;
    } else {
      useWideScreen.value = true;
    }
  },
  {
    immediate: true
  }
);
</script>

<style scoped lang="less">
.prj-learn-wrapper {
  background-color: white;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  white-space: nowrap;

  .prj-learn {
    width: var(--width-fixed--project);
    display: flex;
    flex-direction: row;

    .left-route {
      width: 300px;
      display: flex;
      flex-direction: column;

      .up-detail-card {
        color: var(--color-black);
        font-family: var(--text-family);
        width: 100%;
        height: 373px;
        border: 0.8px solid rgba(220, 223, 230, 1);
        border-radius: 5px;

        .prj-info {
          width: 100%;
          padding-left: 10px;
          margin-top: 40px;
          padding-right: 10px;
          padding-bottom: 10px;
          overflow: hidden;
          text-overflow: ellipsis;

          .title {
            font-family: var(--title-family);
            width: 100%;
            overflow: hidden;

            h1 {
              font-size: var(--fontsize-middle-project);
              font-weight: 700;
            }
          }

          .detail {
            width: 100%;
            white-space: pre-wrap;
            font-size: var(--fontsize-small-project);
            margin-top: 12px;
            height: 100px;
            // text-overflow: ellipsis;
            // /* 2.设置旧版弹性盒 */
            // display: -webkit-box;
            // /* 3. 控制行数*/
            // -webkit-line-clamp: 6;
            // /* 4. 设置子元素的排列方式  垂直排列*/
            // -webkit-box-orient: vertical;
            // /* 5.溢出隐藏 */
            // overflow: hidden;
          }
        }
      }

      .down-routes {
        width: 100%;
        display: flex;
        flex-direction: column;
        height: 170px;
        justify-content: space-between;
        margin-top: 30px;

        .route-items {
          height: 50px;
          width: 100%;
          font-size: var(--fontsize-large-project);
          border-radius: 5px;
          border-width: 0.8px;
          border-style: solid;
          line-height: 50px;
          padding-left: 10px;
          margin-bottom: 10px;
        }

        .route-items:hover {
          border-color: var(--color-theme-project);
          background-color: var(--color-second);
          color: var(--color-theme-project);
        }

        .active {
          color: white;
          background-color: var(--color-theme-project);
        }
      }
    }

    .right-info {
      margin-left: 13px;
      // border: 0.8px solid rgb(242, 242, 242);
      // border-radius: 5px;
      // box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
      padding-bottom: 45px;
      margin-bottom: 30px;
      flex-grow: 1;
    }
  }
  .wideScreen {
    width: 100%;
  }
}
</style>
