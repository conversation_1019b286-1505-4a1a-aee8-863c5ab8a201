import { http } from '@/apis';
import qs from 'qs';
import { timestampToTime, formatTime } from '@/utils/timeUtils';
//import type{ AreaData } from 'ksg-map/dist/types';

//获取领域详情
export function getAreaDetailApi(areaCode: string) {
  return http.request<{
    title: string;
    areaCode: string;
    authorName: string;
    createTime: string;
    description: string;
  }>({
    method: 'get',
    url: '/area/query',
    params: {
      areaCode
    }
  });
}

export function getIdListApi(areaCode: string, tagId: number) {
  return http.request({
    method: 'get',
    url: '/area/exercise/recommend',
    params: {
      areaCode,
      tagId
    }
  });
}

//获取领域商品详情
export function getAreaGoodApi(current: number, queryType: string[], spuId: string) {
  return http.request({
    method: 'post',
    url: '/vip-goods/query',
    data: {
      current,
      queryType,
      spuId
    }
  });
}


//获取某个领域的推荐项目
export function getAreaRecommend(areaCode: string, current: number, type: string) {
  return http.request<{
    recommend: {
      List: string[];
    };
  }>({
    url: `/area/recommend`,
    method: 'get',
    params: {
      areaCode,
      current,
    }
  });
}



//获取某个领域的推荐项目
export function getAreaArea(areaCode: string, current: number, limit: number) {
  return http.request<{
    current: number,
    limit: number,
    total: number,
    records: [
      {
        coverPic: string,
        title: string,
        description: string,
        spuId: string,
        goodsType: number,
        isFree: number,
        buyStatus: number,
      }
    ]
  }>({
    url: `/area/vip/recommend`,
    method: 'get',
    params: {
      areaCode,
      current,
      limit,
    }
  });
}

