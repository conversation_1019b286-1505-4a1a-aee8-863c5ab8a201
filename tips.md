这是我的第一次实习修改之前的代码和写一些业务逻辑所学到的一些东西，写在这里希望后面接手的人能够及时看到不要再踩到我的坑了qwq。

1. 这个项目经过很多人的修改，前面的人与后端可能定义了一些枚举常量之类，但是后面的开发者又自己定义了一些，这导致非常混乱，甚至变量名混用了的情况，我在接手后的第20天左右发现了这个问题，我与后端商量统一了然后修改了一些，主要是prjtype和prjform这两个东西，但是应该是还有其他有类似的问题，具体代码中有的地方定义类型也没有使用定义好的枚举类型，进行判断时直接通过值来判断这不利于后期修改维护，但是没有办法每个文件都进行检查了555，后面接手的同学可以先统一一下types中定义的类型，后面开发时看到了问题再简单修改一下吧。
2. 我们这个项目还挺大的，大型项目涉及父子传值会比较坑，经常就是父组件异步请求值props传递给子组件然后子组件拿到的值是undefined，vue3父子组件挂载时的顺序是这样的: 父setup->父onbeforemount->子setup->子onbeforemount->子onmounted->父onmounted。看到这个可能会想说在父组件onbeforemount中拿数据，在子组件onbeforemount/setup中去获取数据渲染，但是问题是只要是异步拿数据(不管是then还是await)，更新数据的方法都是放在了微循环中，子组件生命周期钩子同步函数，执行时一定是拿到父组件初始化时的数据，如果子组件的template模板中有对数据的应用，在父组件onbeforemount之前修改数据甚至还会导致子组件渲染两次的问题，有时控制台报错但是页面没有任何问题也很可能是第一次渲染数据是空的问题，解决办法我认为比较好是在父组件中套一个v-if，还有一个办法是在子组件添加watch保证能够拿到最新的数据，不过这个方法必须在子组件的props里写默认值，不然还是会导致控制台报错的问题，虽然对用户没有影响，但是F12对强迫症患者还是太不友好了哈哈哈。
3. 请求数据我个人认为是最好用async/await的写法，因为便于修改，比如第二点套v-if中如果用的是promise的写法，那就必须到最后一个函数里去加xxx.value=true这样的代码，但是这个函数本身还可能再被调用啥的，这样可能会产生副作用，如果用async/await写法直接在onmounted最后加就ok了
4. 之前负责champaign的同学进行传参的方式各不相同，有用storage进行传参的，有用provide/inject传参的，有用pinia传参的，也有用路由query传参的，混用的情况也比较多，因为咱们的页面并不是单页面应用，storage传参多页面是一定存在问题的，provide/inject我看代码的时候之前负责的同学对provide的类型都没有做区分，有的是套了ref有的又没有，所以我个人建议是在父子组件传参时用pinia，在打开新的标签页时用query传参，应该会更好维护一些。

