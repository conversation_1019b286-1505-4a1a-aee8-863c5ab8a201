<template>
  <div class="card-wrapper">
    <div class="card">
      <div class="road">
        <h1 class="title">欢迎开始你的学习之旅!</h1>
      </div>
      <div
        class="profile"
        ref="profileRef"
        :class="{ expand: isExpand }"
        v-show="isBigView"
        @mousemove="unExpand"
        @mouseenter="showEditBtn = true"
        @mouseleave="showEditBtn = false"
      >
        <div
          @mouseout.stop
          style="
            width: 660px;
            height: 8px;
            background: inherit;
            background-color: rgba(25, 115, 203, 1);
            border: none;
            border-radius: 5px;
            border-bottom-right-radius: 0px;
            border-bottom-left-radius: 0px;
          "
        ></div>
        <div class="header" style="padding-left: 5px; padding-top: 5px; position: relative;" @mouseout.stop>
          <div class="header-text" style="width: 70px; margin-right: 0px">
            <h3 class="q" style="font-size: 16px; width: 70px; margin-right: 0px">我的档案</h3>
          </div>
          <div :class="{ myicon: true, 'rotate-icon': !isBigView }" @click="changeHeight()"></div>
          <div class="edit-btn" v-show="showEditBtn" @click="toUserInfo">编辑</div>
        </div>
        <div class="info" style="padding-left: 5px" @mouseout.stop>
          <div class="item">
            <span class="right-align q">学习目标:</span>
            <span class="answer">{{ userinfo.goal }}</span>
          </div>
          <div class="item">
            <span class="right-align q">向往的行业:</span>
            <span class="answer">{{ userinfo.industry }}</span>
          </div>
          <div class="item">
            <span class="right-align q">目标职业:</span>
            <span class="answer">{{ userinfo.career }}</span>
          </div>
        </div>
        <div class="skills-line-wrapper" style="padding-left: 5px" @mouseout.stop>
          <div class="skills-line">
            <div class="learn right-align q">想学的技能:</div>
            <div class="skills" ref="skillsRef" :class="{ expand: isExpand }">
              <div v-for="(skill, index) in userinfo.skills" :key="'_' + skill" class="btn" :style="{ 
                opacity: index < 4 || isExpand ? 1 : 0, 
                transition: index < 4 ? 'none' : 'opacity 0.2s ease 0.3s' 
              }">
                <el-tooltip effect="customized" :hide-after="0">
                  <div title="">
                    {{ skill.title }}
                  </div>
                  <template #content>
                    {{ skill.title }}
                  </template>
                </el-tooltip>
              </div>

              <span
                v-if="userinfo.skills!.length > 4"
                class="logo"
                :class="{ rotate: isExpand }"
                @click="handleExpand"
              >
                <el-icon style="cursor: pointer">
                  <ArrowDownBold />
                </el-icon>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="road_1" v-show="!isBigView">
        <div
          @mouseout.stop
          style="
            width: 660px;
            height: 8px;
            background: inherit;
            background-color: rgba(25, 115, 203, 1);
            border: none;
            border-radius: 5px;
            border-bottom-right-radius: 0px;
            border-bottom-left-radius: 0px;
          "
        ></div>
        <div
          class="header"
          style="padding-left: 5px; padding-top: 5px; width: 200px"
          @mouseout.stop
        >
          <div class="header-text" style="width: 70px; margin-right: 0px">
            <h3 class="q" style="font-size: 16px; width: 70px; margin-right: 0px">我的档案</h3>
          </div>
          <div
            ref="btn2"
            :class="{ myicon: true, 'rotate-icon': !isBigView }"
            @click="changeHeight"
            style="height: 16px; width: 16px; margin: 0"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toUserInfo } from '@/utils/gopage';
import CmpButton from '@/components/CmpButton.vue';
// import { getUserinfoApi } from '@/apis/path/home';
import { ArrowDownBold } from '@element-plus/icons-vue';
import { getUserDetailApi } from '@/apis/learnStatistics';
interface UserinfoItf {
  career?: string;
  goal?: string;
  industry?: string;
  skills?: [{ oid: Number; title: string }];
  uniqueCode?: string;
}
const userinfo = ref<UserinfoItf>({
  career: '暂无',
  goal: '暂无',
  industry: '暂无',
  skills: [
    {
      oid: 1,
      title: '暂无'
    }
  ]
});
const isExpand = ref(false);
const isBigView = ref(false);
const show = ref(false);
const profileRef = ref();
const skillsRef = ref();
const showEditBtn = ref(true);
// const myskills=['axure']
const changeHeight = () => {
  isExpand.value = false;
  isBigView.value = !isBigView.value;
  // rotation.value += 180;
};
const handleExpand = () => {
  isExpand.value ? unExpand() : Expand();
};
const Expand = () => {
  if (userinfo.value.skills?.length! <= 4) return;
  const lineNum = Math.floor(userinfo.value.skills?.length! / 4);
  if (profileRef.value && skillsRef.value && !isExpand.value) {
    const originalHeight = profileRef.value.offsetHeight;
    const skillsHeight = skillsRef.value.offsetHeight;
    profileRef.value.style.height = `${originalHeight + skillsHeight * lineNum}px`;
    isExpand.value = true;
  }
};
const unExpand = () => {
  if (userinfo.value.skills?.length! <= 4) return;
  profileRef.value.style.height = '109px';
  isExpand.value = false;
};
onMounted(async () => {
  const res = await getUserDetailApi();
  if (res.data) {
    userinfo.value = res.data;
    console.log(userinfo.value);
    if (userinfo.value.skills?.length! > 4) show.value = true;
  }
});
</script>

<style scoped lang="less">
@keyframes flash {
  0% {
    opacity: 0%;
  }

  50% {
    opacity: 100%;
  }

  100% {
    opacity: 0%;
  }
}

.showMore {
  display: flex;
  justify-content: center;
  height: 10px;

  :deep(.el-icon svg) {
    height: 10px;
  }

  :deep(.el-icon) {
    height: 10px;
    line-height: 10px;
  }
}

.card-wrapper {
  background-color: var(--color-back-header);
  position: relative;

  .rotate-icon {
    transform: rotate(180deg) translateY(-25%);
    transition: transform 0.3s;
  }
}

.card {
  width: var(--width-fixed--project);
  padding: 15px 10px;
  margin: 0 auto;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  min-height: 80px;
  max-height: 139px;
  // transition: 1s all;
  transition: min-height 0.3s ease;

  .road {
    display: flex;
    align-items: flex-start;

    .title {
      font-size: 36px;
      font-weight: 700;
      color: var(--color-theme-project);
      font-family: var(--title-family);
      height: 50px;
      margin-left: 50px;
    }
  }

  .road_1 {
    background-color: rgb(255, 255, 255);
    display: flex;
    flex-direction: column;
    width: 660px;
    height: 50px;
    border-radius: 5px;
    // position: absolute;
    // right: 0;
    .header {
      // overflow: hidden;
      flex-direction: row;
      display: flex;
      align-items: start;
      justify-content: start;

      .header-text {
        margin-right: 0%;
        // flex: 4;
      }

      .myicon {
        // animation: flash 4s linear infinite;
        // position: absolute;
        flex: 0.13;
        // left: 50%;
        bottom: 0;
        width: 16px;
        height: 16px;
        // display: flex;
        // align-items: center;
        // justify-content: center;
        cursor: pointer;
        background-image: url('@/assets/images/home/<USER>');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 50% 75%;
        transform: transform(180deg) translateY(-15%); //位置在Y轴上的偏移
      }
    }

    .simple {
      display: flex;
      width: 660px;
      height: 8px;
      background: inherit;
      background-color: rgba(25, 115, 203, 1);
      border: none;
      border-radius: 5px;
      border-bottom-right-radius: 0px;
      border-bottom-left-radius: 0px;
    }

    .simpletarget {
      display: flex;
      width: 660px;
      height: 42px;
      align-items: center;
      padding: 5px;
      padding-left: 15px;
    }

    .title_1 {
      font-size: 14px;
      font-weight: 700;
      color: #333333;
      font-family: var(--text-family);
      margin-left: 20px;
    }
  }

  .profile {
    width: 660px;
    border-radius: 5px;
    background-color: rgb(255, 255, 255);
    display: flex;
    flex-direction: column;
    padding-right: 15px;
    // padding-bottom: 10px;
    // height: 105px;
    // position: absolute;
    // right: 0;
    height: 109px;

    transition: height 0.3s ease;

    &.expand {
      height: 140px;
      box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
    }

    .header {
      // overflow: hidden;
      flex-direction: row;
      display: flex;
      align-items: start;
      justify-content: start;
      position: relative;

      .header-text {
        margin-right: 0%;
        // flex: 4;
      }

      .myicon {
        // animation: flash 4s linear infinite;
        // position: absolute;
        flex: 0.028;
        // left: 50%;
        bottom: 0;
        width: 16px;
        height: 16px;
        // display: flex;
        // align-items: center;
        // justify-content: center;
        cursor: pointer;
        background-image: url('@/assets/images/home/<USER>');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 50% 75%;
        transform: translateY(25%); //位置在Y轴上的偏移
      }

      .more {
        font-family:
          '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0',
          sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: var(--fontsize-middle-project);
        color: var(--color-black);
        text-align: right;
      }
    }

    .info {
      display: flex;
      justify-content: flex-start;
      margin: 10px 0px;

      .item {
        min-width: 200px;
        // margin-left: 30px;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &:first-child {
          margin-left: 0px;
        }
      }

      .answer {
        font-size: var(--fontsize-middle-project);
        font-weight: 400;
        max-width: 100px;
        color: #333333;
        font-family: var(--text-family);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .skills-line-wrapper {
      width: 100%;
      height: 25px;
      position: relative;
    }

    .skills-line {
      position: absolute;
      display: flex;
      transition: height 0.3s ease;

      &.expand {
        height: 100%;
      }

              .skills {
          display: grid;
          grid-template-columns: repeat(4, 1fr); // 一行4个
          grid-column-gap: 16px; // 列之间的距离适当加宽
          grid-row-gap: 10px;
          overflow: hidden;
          height: 25px;
          transition: height 0.3s ease;
          transition: overflow 1s ease;

          &.expand {
            height: 100%;
          }
          


        div[title] {
          padding: 0 0;
          min-width: 100px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          font-size: 12px;
          font-weight: 400;
          color: #333;
          background: #f2f2f2;
          border: none;
          border-radius: 16px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          box-shadow: none;
          transition: background 0.2s;
        }
        div[title]:hover {
          background: #e6e6e6;
        }

        .btn {
          position: relative;
        }

        .logo {
          position: absolute;
          right: 0;
          top: 10%;
          height: 20px;
          width: 24px;
          transform: translate(150%);
          cursor: pointer;
          background: #f2f2f2;
          border-radius: 36%;
          display: flex;
          align-items: center;
          justify-content: center;

        }
        .rotate {
          transform: translate(150%) rotate(180deg);
          transition: transform 0.3s ease;
        }
      }
    }
  }
}

// 问题
.q {
  font-size: var(--fontsize-middle-project);
  font-weight: 600;
  color: var(--color-black);
  font-family: var(--title-family);
  margin-right: 20px;
}

.right-align {
  display: inline-block;
  width: 78px;
  text-align: right;
}
.edit-btn {
  position: absolute;
  right: 0;
  top: 8px;
  //background: #e6f0fa;
  color: #1973cb;
  //border-radius: 6px;
  //padding: 2px 14px;
  font-size: 12px;
  font-weight: 400;
  cursor: pointer;
  transition: background 0.2s;
  z-index: 2;
  &:hover {
    //background: #d0e6fa;
    text-decoration: underline;
  }
}
</style>
<style>
.profile {
  width: 660px;
  border-radius: 5px;
  background-color: rgb(255, 255, 255);
  display: flex;
  flex-direction: column;
  padding-right: 15px;
  height: 109px;

  transition: height 0.3s ease;

  z-index: 10;
  &.expand {
    display: flex;
    height: 109px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
  }
}
</style>
