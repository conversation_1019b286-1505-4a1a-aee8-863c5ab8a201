<template>
  <div class="map-container">
    <div class="map">
      <KsgMap
        ref="ksgRef"
        :config="config"
        @load-more="handleLoadMore"
        :loading="loading"
        @click-label="handleClickLabel"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getKsgMapApi } from '@/apis/klgdetail';
import { ref, onMounted, inject, computed, watch } from 'vue';
import { KsgMap } from '@endlessorigin/KsgMap'; //直接导入组件，局部使用方式
import { MODE, type Options } from '@endlessorigin/KsgMap';
const klgCode = inject('klgCode', null);

// 计算最终的ksgCode值
const finalKlgCode = computed(() => {
  return klgCode || null;
});

const ksgRef = ref<any>(); //获取组件实例
let loading = ref<'loading' | 'loaded' | 'error'>('loading'); //loading状态
// 场景配置项
const config: Options = {
  model: MODE.Single_ROOT, //单根节点模,多根知识点场景(MODE.MULTIPLE_ROOT)，
  // 配置加载更多参数
  pointsLevelPager: {
    current: 1, //当前层级（从1层开始，默认第一层）
    levelSize: 2 //每次加载层数
  }
};
/**
 *组件挂载后第一次加载数据
 */
const dataList = ref('');
const total = ref('');
const rootid = ref('');
async function init() {
  loading.value = 'loading'; //加载中状态
  //dataList - 后端响应的知识点数组
  //total - 总共多少个数据
  //root - 知识点id
  // console.log('finalKlgCode in init:', finalKlgCode.value);
  const res = await getKsgMapApi(finalKlgCode.value, 0, 10);
  dataList.value = res.data.records;
  total.value = res.data.total;
  rootid.value = finalKlgCode.value;
  console.log('datalist', dataList.value);
  console.log('total', total.value);
  console.log('rootid', rootid.value);

  ksgRef.value?.firstLoadPointsData(dataList.value, total.value, rootid.value); //当多根知识点模式，id为邻域id，方便后期数据请求查询
  loading.value = 'loaded'; //加载完成状态
}
/**
 * 场景上拉到阈值时，加载更多知识节点回调函数
 * @param rootId 根节点id
 * @param crt 当前第几层
 * @param levelSize 每次加载的层数
 */

async function handleLoadMore(rootId: string, crt: number, levelSize: number) {
  loading.value = 'loading';
  //dataList - 知识点数据
  ksgRef.value?.loadMorePointsData(dataList.value);
  loading.value = 'loaded';
}

//点击知识点title后触发的回调
function handleClickLabel(id: string) {
  alert(id);
}
// const dataList = ref<OriginalData>({
//   topAreas: [],//ID
//   points: [],//
//   areas: [],
//   focuses: []
// });
// const dataList = ref({
//   total: 1,
//   list: [
//     {
//       klgCode: 'K1907350322875195392',
//       sortId: 1,
//       title: '<p>这是VUE，他是一个大框架 里面包含HTML JS CSS</p>'
//     }
//   ]
// });
onMounted(() => {
  // 如果已经有ksgCode值，直接初始化
  if (finalKlgCode.value) {
    init();
  }
});
// onMounted(async () => {
//
//   // dataList.value = data.map(area=> area.areaName);
//   // dataList.value = data;
//   // rootid.value = data.map(area => area.areaId);
//   // total.value = data.length;
//   // console.log("data:",data)
//   // console.log('dataList:', dataList.value);
//   // console.log('total:', total.value);
//   // console.log('rootid:', rootid.value);
// });
// 单根节点场景接口
</script>

<style scoped></style>
