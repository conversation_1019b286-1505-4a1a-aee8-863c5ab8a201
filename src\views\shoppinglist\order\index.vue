<template>
  <div class="shoplist-container">
    <div class="shoplist-header">
      <span class="shoplist-title">
        {{ title }}
      </span>
      <div class="shoplist-search">
        <el-input
          v-model="searchInput"
          class="w-50 m-2"
          :placeholder="placeholder"
          clearable
          @keyup.enter="searchFn"
        >
          <template #append><el-button :icon="Search" @click="searchFn" style="width: unset !important;"/></template>
        </el-input>
      </div>
    </div>

    <el-skeleton style="width: 100%" :rows="5" :loading="!ready" :throttle="100" animated>
      <template #default>
        <div v-if="ready">
        <div v-if="tableData.length != 0" class="shoplist-content">
          <div
            class="shoplist-item"
            v-for="item in tableData"
            :key="item.id"
          >
            <div class="shoplist-item-header">
              <span class="shoplist-item-header-time">{{ item.creatTime }}</span>
              <span class="shoplist-item-header-id">订单编号:</span><span>{{ item.orderNo }}</span>
            </div>
            <div class="shoplist-item-content">
              <div class="shoplist-item-content-img">
                <!-- <img width="78px" height="49px" :src="item.coverPic"/> -->
                <img :src="item.coverPic" />
              </div>
              <div class="shoplist-item-content-info">
                <div class="shoplist-item-content-detail1">
                  <span @click="openDetailDialog(item)" class="shoplist-item-content-detail1-text" v-html="item.goodsName"></span>
                </div>
                <div class="shoplist-item-content-detail2">
                  <span class="shoplist-item-content-detailprefix">标价:</span
                  ><span>{{ item.subtotal.toFixed(2) }}元</span>
                </div>
                <div class="shoplist-item-content-detail1">
                  <span>实付:</span><span>{{ item.actualPaidAmount.toFixed(2) }}元</span>
                </div>
                <div class="shoplist-item-content-detail2">
                  <p>状态:</p>
                  <div v-if="item.orderStatus == 10">
                    <div class="status-row">
                      <span>订单取消</span>
                      <div class="button-group">
                        <div class="repay-style" @click.stop="deletePayment(item)">删除订单</div>
                      </div>
                    </div>
                  </div>
                  <p v-if="item.orderStatus == 20">订单完成</p>
                  <div v-else-if="item.orderStatus == 0">
                    <div class="status-row">
                      <span>待支付</span>
                      <div class="button-group">
                        <div class="repay-style" @click.stop="repay(item)">继续支付</div>
                        <div class="repay-style" @click.stop="cancelPayment(item)">取消订单</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 空状态管理 -->
        <div v-else class="lc-empty"><el-empty description="暂无订单记录" /></div>
        <div v-if="tableData.length != 0" class="shoplist-bottom">
          <div class="down-more">
            <span class="footerBtn" @click="getMore" v-if="shouldMore">
              <span class="myicon"></span>
              加载更多
            </span>
          </div>
          <div class="no-more" v-if="!shouldMore && current != 1">已经是最底部啦</div>
          <Backtop></Backtop>
        </div>
        </div>
      </template>
      <template #template>
        <div class="shoplist-content">
          <div v-for="i in 5" :key="i" class="shoplist-item">
            <div class="shoplist-item-header">
              <el-skeleton-item variant="text" style="width: 30%; height: 16px" />
              <el-skeleton-item variant="text" style="width: 40%; height: 16px" />
            </div>
            <div class="shoplist-item-content">
              <div class="shoplist-item-content-img">
                <el-skeleton-item variant="image" style="width: 78px; height: 49px" />
              </div>
              <div class="shoplist-item-content-info">
                <div class="shoplist-item-content-detail1">
                  <el-skeleton-item variant="text" style="width: 80%; height: 18px; margin-bottom: 8px" />
                </div>
                <div class="shoplist-item-content-detail2">
                  <el-skeleton-item variant="text" style="width: 60%; height: 16px; margin-bottom: 4px" />
                </div>
                <div class="shoplist-item-content-detail1">
                  <el-skeleton-item variant="text" style="width: 50%; height: 16px; margin-bottom: 4px" />
                </div>
                <div class="shoplist-item-content-detail2">
                  <el-skeleton-item variant="text" style="width: 40%; height: 16px" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </el-skeleton>

    <el-dialog v-model="dialogVisible" width="1014px">
      <template #header>
        <div class="dialog-header">
          <span style="font-size: 18px; font-weight: 600">订单详情</span>
        </div>
      </template>
      <div class="dialog-container">
        <div class="dialog-header">
          <div class="dialog-header-left">
            <span>订单编号:</span>
            <span>{{ clickedItem.orderNo }}</span>
            <span v-if="clickedItem.orderStatus == 0">(待支付)</span>
            <span v-if="clickedItem.orderStatus == 10">(订单取消)</span>
            <span v-if="clickedItem.orderStatus == 20">(支付成功)</span>
          </div>
          <div class="dialog-header-right">
            <span>{{ clickedItem.creatTime }}</span>
          </div>
        </div>
        <div class="dialog-middle">
          <div class="dialog-middle-title">
            <span>购买商品</span>
          </div>
          <div class="dialog-middle-info">
            <div class="dialog-middle-img"><img :src="clickedItem.coverPic" /></div>
            <div class="dialog-middle-title">
              <!-- <span class="dialog-fontstyle1">名称</span> -->
              <span class="dialog-jump-title" style="cursor: pointer" @click="goPage">
                {{ clickedItem.goodsName }}
              </span>
              <!-- TODO：这里还没写内容 -->
              <!-- <span class="dialog-middle-intro" :title="clickedItem.studyInstructions">{{
                clickedItem.studyInstructions
              }}</span> -->
            </div>
            <div class="midLine"></div>
            <div class="dialog-middle-time">
              <span class="dialog-fontstyle1">有效时间:</span>
              <span class="dialog-fontstyle2"
                >{{ clickedItem.creatTime.split(' ')[0] }} 至
                {{ clickedItem.effectiveTime.split(' ')[0] }}</span
              >
            </div>
          </div>
          <div class="dialog-middle-price">
            <span class="dialog-fontstyle2"
              >商品价格: &nbsp;{{ clickedItem.subtotal.toFixed(2) }}元</span
            >
            <span class="dialog-fontstyle2"
              >优惠: &nbsp;{{ clickedItem.discountAmount.toFixed(2) }}元</span
            >
            <span class="dialog-fontstyle1"
              >实付款金额: &nbsp;{{ clickedItem.actualPaidAmount.toFixed(2) }}元</span
            >
          </div>
        </div>

        <div class="dialog-bottom">
          <div><span class="dialog-fontstyle3">支付详情</span></div>
          <div>
            <span class="dialog-fontstyle2"
              >支付方式:
              <span v-if="clickedItem.paymentMethod == 1">支付宝</span>
              <span v-else-if="clickedItem.paymentMethod == 2">微信</span>
              <span v-else-if="clickedItem.paymentMethod == null">无</span></span
            >
          </div>
          <div>
            <span class="dialog-fontstyle2"
              >支付状态:
              <span v-if="clickedItem.orderStatus == 0">待支付</span>
              <span v-else-if="clickedItem.orderStatus == 10">订单取消</span>
              <span v-else-if="clickedItem.orderStatus == 20">已支付</span>
            </span>
          </div>
          <div>
            <span class="dialog-fontstyle2"
              >支付时间:<span>{{
                clickedItem.paymentTime ? clickedItem.paymentTime.split(' ')[0] : ' 无'
              }}</span>
            </span>
          </div>
          <div>
            <span class="dialog-fontstyle2"
              >支付流水号:<span>{{
                clickedItem.transactionNumber ? clickedItem.transactionNumber : ' 无'
              }}</span></span
            >
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button 
          class="dialog-button-gopay"
          @click="goPay(clickedItem)" 
          v-if="clickedItem.orderStatus == 0"
          >继续支付</el-button>
          <el-button class="dialog-button" @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>

  <QRcodeDialog
    v-model="payDialogVisible"
    @paySuccess="getShoppinglist(current, limit, order_type, orderNumberOrTitle)"
    :orderNo="currentOrderNo"
    :skuId="skuId"
  ></QRcodeDialog>
</template>

<script setup lang="ts">
import { watch, ref, provide, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { Search } from '@element-plus/icons-vue';
import { getShoppinglistApi } from '@/apis/shoppinglist';
import QRcodeDialog from '@/components/QRcodeDialog.vue';
import Backtop from '@/components/Backtop.vue';
import { PrjType } from '@/types/project';
import { GoodsType } from '@/types/goods';
import { useLearnStore } from '@/stores/learnintro';
import { getPrjIntroduceApi } from '@/apis/case';
import { useProjectStore } from '@/stores/project';
import { cancelPaymentApi } from '@/apis/payment';
import { deletePaymentApi } from '@/apis/payment';

const learnStore = useLearnStore();
const router = useRouter();
const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
let shoplist_titleid: any;
let title = ref('');
let searchInput = ref(''); // 搜索

const goPage = () => {
  console.log('props.spuId', clickedItem.spuId);
  try {
    learnStore.setintroData(clickedItem.spuId);
    let path: string;
    path = '/goodIntroduce';
    const { href } = router.resolve({
      path: path,
      query: {
        spuId: clickedItem.spuId
      }
    });
    window.open(href, '_blank');
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
// 打开重新支付弹窗
const payDialogVisible = ref(false);
let checkedpay = ref(0);
const currentOrderNo = ref('');
const skuId = ref('');
const repay = async (item: any) => {
  console.log(item);
  skuId.value = item.skuId;
  const res = await getPrjIntroduceApi({ spuId: item.spuId });
  projectStore.setPrjInfo(res.data);
  payDialogVisible.value = true;
  currentOrderNo.value = item.orderNo;
  checkedpay.value = item;

  //存到仓库，给购买弹窗直接使用
  const actualPaidAmount = item.actualPaidAmount;
  const creatTime = item.creatTime;
  const discountAmount = item.discountAmount;
  const orderNo = item.orderNo;
  const orderStatus = item.orderStatus;
  const subtotal = item.subtotal;
  const createdOrder = {actualPaidAmount, creatTime, discountAmount, orderNo, orderStatus, subtotal}
  projectStore.createdOrder = createdOrder
};
const closeDialog = (val: any) => {
  payDialogVisible.value = val;
};
//取消订单
const cancelPayment = async (item: any) => {
  console.log('-------');
  const res = await cancelPaymentApi(item.orderNo);
  console.log(res);
  getShoppinglist(current.value, limit, order_type.value, orderNumberOrTitle.value);

  console.log('-------');
};
//删除订单
const deletePayment = async (item: any) => {
  console.log('-------');
  const res = await deletePaymentApi(item.orderNo);
  console.log(res);
  getShoppinglist(current.value, limit, order_type.value, orderNumberOrTitle.value);

  console.log('-------');
};
// 获取订单列表，初始化是资料订单
const placeholder = ref('');
const limit = 10;
const current = ref(1);
let order_type = ref(0); // 0是资料，1是会员
let orderNumberOrTitle = ref('');
const tableData = ref<any[]>([]); // 订单列表
const tabelTotal = ref(0); // 列表总数
let currentTotal = ref(0);
let shouldMore = ref(true);
const ready = ref(false);

const getShoppinglist = async (
  current: number,
  limit: number,
  order_type: number,
  orderNumberOrTitle: string
) => {
  try {
    const res = await getShoppinglistApi(current, limit, order_type, orderNumberOrTitle);
    if (res.success) {
      tabelTotal.value = res.data.total;
      tableData.value = res.data.records;
      currentTotal.value = (current - 1) * limit + res.data.records.length;
      shouldMore.value = currentTotal.value < tabelTotal.value ? true : false;
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
  } finally {
    ready.value = true;
  }
};

// 初始化
const InitData = () => {
  current.value = 1;
  order_type.value = 0;
  orderNumberOrTitle.value = '';
  ready.value = false;
  // 一进入页面watch就会生效，可以在watch中加载数据
  // getShoppinglist(current.value, limit, order_type.value, orderNumberOrTitle.value);
};

InitData();

// 加载更多
const getMore = () => {
  current.value += 1;
  getShoppinglistApi(current.value, limit, order_type.value, orderNumberOrTitle.value).then(
    (res) => {
      tabelTotal.value = res.data.total;
      tableData.value = tableData.value.concat(res.data.records);
      currentTotal.value = (current.value - 1) * limit + res.data.records.length;
      shouldMore.value = currentTotal.value < tabelTotal.value ? true : false;
    }
  );
};

// 根据订单编号等搜索
const searchFn = async () => {
  current.value = 1;
  orderNumberOrTitle.value = searchInput.value;
  ready.value = false;
  await getShoppinglist(current.value, limit, order_type.value, orderNumberOrTitle.value);
};

// 打开订单详情
const dialogVisible = ref(false);
const clickedItem = reactive({
  oid: null,
  goodsName: '',
  studyInstructions: '',
  fieldName: '',
  coverPic: '',
  orderNo: '',
  subtotal: 0.0,
  discountAmount: 0.0,
  creatTime: '',
  paymentTime: '',
  effectiveTime: '',
  orderStatus: 0,
  paymentMethod: 0,
  transactionNumber: '',
  actualPaidAmount: 0.0,
  learnType: 0,
  orderType: 0,
  spuId: '',
  skuId: ''
});
const openDetailDialog = (item: any) => {
  console.log(item);
  Object.assign(clickedItem, {
    oid: item.oid,
    goodsName: item.goodsName,
    studyInstructions: item.studyInstructions,
    fieldName: item.fieldName,
    coverPic: item.coverPic,
    orderNo: item.orderNo,
    goodsPrice: item.goodsPrice,
    discountAmount: item.discountAmount,
    actualPaidAmount: item.actualPaidAmount,
    creatTime: item.creatTime,
    paymentTime: item.paymentTime,
    effectiveTime: item.effectiveTime,
    orderStatus: item.orderStatus,
    paymentMethod: item.paymentMethod,
    transactionNumber: item.transactionNumber,
    subtotal: item.subtotal,
    // 跳转介绍页所需接口
    learnType: item.learnType,
    orderType: item.orderType,
    spuId: item.spuId,
    skuId: item.skuId
  });
  dialogVisible.value = true;
};

// 防抖定时器
let routeChangeTimer: NodeJS.Timeout | null = null;

// 监听路由变化
watch(
  () => router.currentRoute.value,
  async () => {
    // 清除之前的定时器
    if (routeChangeTimer) {
      clearTimeout(routeChangeTimer);
    }

    // 设置新的定时器，0.5秒后执行
    routeChangeTimer = setTimeout(async () => {
      shoplist_titleid = router.currentRoute.value.params.id;
      if (shoplist_titleid == 'prjorder') {
        title.value = '资料订单';
        order_type.value = 0;
        placeholder.value = '请输入订单编号/资料名称';
      } else {
        title.value = '会员订单';
        order_type.value = 1;
        placeholder.value = '请输入订单编号/领域名称';
      }
      current.value = 1;
      orderNumberOrTitle.value = '';
      ready.value = false;
      await getShoppinglist(current.value, limit, order_type.value, orderNumberOrTitle.value);
    }, 500); // 0.5秒防抖
  },
  { immediate: true }
);

//订单详情弹窗内去支付
const goPay = (clickedItem: any) => {
  dialogVisible.value = false;
  repay(clickedItem);
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (routeChangeTimer) {
    clearTimeout(routeChangeTimer);
  }
});
</script>

<style lang="less" scoped>
@titlefontsize: 18px;
@titlefontfamily: var(--title-family);
@headerfontfamily: var(--title-family);
@headerfontsize: 14px;
@margin: 10px;
@bgclor: #f2f2f2;

.shoplist-container {
  // 重写element变量
  --el-color-primary: var(--color-theme-project);

  color: var(--color-black);
  display: flex;
  flex-direction: column;
  width: 1000px;

  .shoplist-header {
    display: flex;
    justify-content: space-between;

    .shoplist-title {
      font-family: @titlefontfamily;
      font-size: @titlefontsize;
      margin-left: 2 * @margin;
    }

    .shoplist-search {
      width: 225px;
    }
  }

  .shoplist-content {
    padding-left: @margin;
    box-sizing: border-box;
    width: 100%;
    margin-top: 2 * @margin;

    .shoplist-item {
      box-shadow: 0px 5px 5px @bgclor;
      margin-bottom: @margin;

      .shoplist-item-header {
        height: 25px;
        background-color: @bgclor;
        font-size: @headerfontsize;
        font-family: @headerfontfamily;
        line-height: 25px;

        .shoplist-item-header-time {
          margin-left: @margin;
          margin-right: 4 * @margin;
          font-weight: 600;
        }

        .shoplist-item-header-id {
          font-weight: 400;
          margin-right: @margin;
        }
      }

      .shoplist-item-content {
        display: flex;
        height: 74px;
        align-items: center;

        .shoplist-item-content-img {
          img {
            width: 78px;
            height: 49px;
            margin-left: 10px;
          }
        }

        .shoplist-item-content-info {
          display: flex;
          flex-direction: row;
          flex: 1;
          padding-left: @margin;
          padding-right: 7 * @margin;
          justify-content: space-between;

          .shoplist-item-content-detail1 {
            width: 30%;
            font-family: @headerfontfamily;
            font-weight: 600;
            font-size: @headerfontsize;

            .shoplist-item-content-detail1-text:hover {
              cursor: pointer;
            }
          }

          .shoplist-item-content-detail2 {
            width: 30%;
            font-family: @headerfontfamily;
            font-weight: 400;
            font-size: @headerfontsize;
            display: flex;
            flex-direction: row;

            .status-row {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;
            }

            .button-group {
              margin-left: 10px;
              display: flex;
              gap: 10px;
            }

            .repay-style {
              font-size: 12px;
              font-weight: 400;
              color: #4d0819;
              text-decoration: underline;
              display: inline-block;
              cursor: pointer;
            }

            .repay-style:hover {
              font-weight: 600;
            }

            .shoplist-item-content-detailprefix {
              margin-right: @margin;
            }
          }
        }
      }
    }
  }
}

.lc-empty {
  height: 370px;
}
// 对话框样式
.dialog-fontstyle1 {
  font-size: 14px;
  font-weight: 600;
  font-family: var(--title-family);
}

.dialog-fontstyle2 {
  font-size: 14px;
  font-weight: 400;
  font-family: var(--text-family);
  color: var(--color-black);
}

.dialog-fontstyle3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-black);
}

.titleClass {
  font-size: 18px;
  font-weight: 600;
}

.dialog-container {
  font-family: var(--title-family);
  // width: 100%;
  .dialog-header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    height: 39px;
    align-items: center;
    padding-left: 17px;
    background-color: #ffd37a;
    margin-bottom: 10px;

    .dialog-header-left {
      .dialog-fontstyle1();
      margin-right: 80px;
    }

    .dialog-header-right {
      .dialog-fontstyle2();
    }
  }

  .dialog-middle {
    margin-top: 10px;
    border: 1px solid @bgclor;
    border-bottom: none;
    border-radius: 5px;
    padding: 10px 10px 20px 10px;
    .midLine {
      display: flex;
      margin: 0 30px;
      width: 2px;
      height: 55px;
      background-color: #f2f2f2;
    }

    .dialog-middle-title {
      .dialog-fontstyle3();
      flex-direction: column;
    }

    .dialog-middle-info {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      margin-top: 10px;

      .dialog-middle-img {
        width: 80px;
        height: 54px;
        margin-right: 10px;
      }

      .dialog-middle-title {
        display: flex;
        flex-direction: column;
      }
      .dialog-middle-intro {
        overflow: hidden;
        text-overflow: ellipsis;
        // white-space: nowrap;
        height: 40px;
        width: 470px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-family: var(--text-family);
        font-weight: 400;
        font-size: 14px;
      }

      .dialog-middle-time {
        display: flex;
        flex-direction: column;
      }

      .dialog-jump-title {
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
      }

      .dialog-jump-title:hover {
        color: var(--color-theme-project);
      }
    }

    .dialog-middle-price {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      padding-right: 10px;
    }
  }

  .dialog-bottom {
    height: 158px;
    background-color: #fff9eb;
    display: flex;
    flex-direction: column;

    div {
      margin-left: 20px;
      margin-top: 5px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;

  .dialog-button-gopay {
    background-color: rgba(25, 115, 203, 1); 
    color: rgba(255, 255, 255, 0.996078431372549);
    margin-right: 40px;
  }
}

.el-button {
  width: 120px;
  height: 43px;
}

.el-button:hover {
  background-color: var(--color-second);
  color: var(--color-theme-project);
  font-weight: 600;
  border: 1px solid var(--color-theme-project);
}

.shoplist-bottom {
  display: flex;
  justify-content: center;
  flex-direction: row;
  margin-top: 53px;
  margin-bottom: 53px;
}
.no-more {
  display: flex;
  text-align: center;
  justify-content: center;
  color: var(--color-theme-project);
  font-family: var(--text-family);
  font-weight: 400;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  margin: 10px 0;
}

.down-more {
  display: flex;
  text-align: center;
  justify-content: center;
  color: var(--color-theme-project);
  font-family: var(--text-family);
  font-weight: 400;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  margin: 10px 0;
  .footerBtn {
    width: 90px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    .myicon {
      width: 14px;
      height: 12px;
      margin-right: 5px;
      background-image: url('@/assets/images/project/u3964.svg');
    }
    &:hover {
      font-weight: bold;
    }
  }
}

.down-more:hover {
  font-weight: bolder;
}
</style>
