import { http } from '@/apis';

// 获取用户个人信息
export function getUserInfoApi() {
  return http.request<{
    list: {
      learnedKlg: number;
      myviplist: any[];
    };
  }>({
    method: 'get',
    url: '/userSpace/getUserKlgAndVipGoodsList'
  });
}

// 获取领域信息
export function getFieldListApi(current?: number, limit?: number) {
  return http.request<{
    total: number;
    areaList: Array<{
      skuList: Array<{
        cardType: number;
        discount: number;
        dealPrice: number;
        skuId: number | null;
        price: number;
      }>;
      fieldDescription: string;
      vipGoodId: number;
      fieldId: string;
      field_name: string;
    }>;
  }>({
    method: 'post',
    url: '/userSpace/getFieldList',
    data: {
      current,
      limit
    }
  });
}

// 保存背景图片
export function saveBgimgApi(params: any) {
  return http.request<{
    backgroundImgurl: string;
  }>({
    method: 'post',
    url: '/userSpace/saveBgimage',
    data: params
  });
}

// 获取背景图片
export function getBgimgApi() {
  return http.request<{
    backgroundImgurl: string;
  }>({
    method: 'get',
    url: '/userSpace/getBgimage'
  });
}

//获取用户权限信息
export function getPermissionApi() {
  return http.get<{
    goal: string;
    industry: string;
    career: string;
    skills: Array<{
      title: string;
    }>;
    uniqueCode: string;
    username: string;
    coverPic: string;
    selfIntroduction: string;
    phone: string;
    userPermissionList: Array<{
      name: string;
      isAble: boolean;
      children: any[];
    }>;
  }>('/user-project/getUserAuth');
}

export function getRecommendApi() {
  return http.get<
    [
      {
        title: string;

        coverPic: string;

        klgCount: number;

        priceList: Array<{
          skuId: string;

          cardType: number;

          subtotal: number;

          discount: number;

          actualPaidAmount: number;

          studyTime: number;
        }>;
      }
    ]
  >('/userSpace/vip/recommend');
}


//获取领域商品详情
export function getBoughtVip(current: number, limit: number) {
  return http.request({
    url: '/userSpace/vip/page',
    params: {
      current,
      limit
    }
  });
}
