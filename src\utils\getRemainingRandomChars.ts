export function getRemainingRandomChars(input: string, length: number): string[] {
    // 定义Unicode字符的范围（只取常见的 BMP 字符，U+0000 到 U+FFFF）
    const MAX_CODE_POINT = 0xffff;
  
    // 将输入字符串的字符存储到一个 Set 中（去重）
    const usedChars = new Set(input);
  
    // 创建一个数组，保存所有未使用的字符
    const remainingChars = [];
  
    // 遍历 BMP 范围内的所有字符，过滤掉已使用的字符
    for (let i = 1; i <= MAX_CODE_POINT; i++) {
      const char = String.fromCharCode(i);
      if (!usedChars.has(char)) {
        remainingChars.push(char);
      }
      if (remainingChars.length >= length) {
        break;
      }
    }
  
    return remainingChars;
  }