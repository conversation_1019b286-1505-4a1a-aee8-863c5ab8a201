import { useDraftWordStoreV2 } from '@/stores/draftWordV2';
import { findAllOccurrences } from './regUtils';
import { type RenderInfo } from '@/types/word';
import { trim } from 'lodash-es';
// import { useLatexStore } from '@/stores/latex';

export function handleVideoSearch(
  str: string,
  regString: string,
  renderInfoIndexes: Array<{
    listIndex: number;
    index: number;
  }>,
  renderInfoListList: RenderInfo[][]
): void {
  const positions = findAllOccurrences(regString, str);
  for (const position of positions) {
    find(position, str.length, renderInfoIndexes, renderInfoListList);
  }
}

function find(
  postion: number,
  offset: number,
  renderInfoIndexes: Array<{
    listIndex: number;
    index: number;
  }>,
  renderInfoListList: RenderInfo[][]
) {
  for (let i = postion; i < postion + offset; i++) {
    const { listIndex, index } = renderInfoIndexes[i];
    const renderInfo = renderInfoListList[listIndex][index];
    renderInfo.searchCount++;
  }
}
