<template>
  <div class="mask-layer">
    <div class="dialog-container">
      <span class="iconfont icon-icon_close" @click="close"></span>
      <div class="dialog-container-info">
        <div class="dialog-container-myinfo">
          <div class="dialog-container-myinfo-content">
            <div class="dialog-container-img">
              <!-- <img :src="props.userInfo.coverPic" alt="" class="img-style" v-if="props.userInfo.coverPic">
              <img src="../../../../public/headPic.jpg" alt="" class="img-style" v-else/> -->
              <img :src="props.userInfo.coverPic" alt="" class="img-style" />
            </div>
            <div class="dialog-container-name">
              <div class="dialog-container-name-top">
                <span class="dialog-container-name-topleft">{{ props.userInfo.username }}</span>
                <span class="dialog-container-name-topright"
                  >(登录手机号:{{ props.userInfo.phone }})</span
                >
              </div>
              <span class="userspace-fontstyle3" v-if="isVIP">{{
                props.userInfo.selfIntroduction
              }}</span>
              <span class="userspace-fontstyle3" v-if="!isVIP"
                >您当前还不是会员哦，快成为领域会员，进入自己的知识地图吧。</span
              >
            </div>
          </div>
        </div>
        <div class="dialog-container-fieldinfo">
          <div class="dialog-container-fieldinfo-content">
            <div class="dialog-container-fieldinfo-top">
              请根据自己的学习需求，选择合适的领域成为会员。成为会员后，您可以学习该领域内的知识讲解项目，点亮自己的知识网络
            </div>
            <div class="field-tags">
              <div class="field-tags-top" style="justify-content: left; align-items: center">
                <!-- <span
                  class="iconfont icon-arrowcircleleft2"
                  :class="canLeft ? 'bluestyle' : 'graystyle'"
                  @click="toleft"
                ></span> -->
                <el-icon :class="canLeft ? 'bluestyle' : 'graystyle'" @click="toleft">
                  <el-icon-arrow-left />
                </el-icon>
                <div
                  class="field-tags-item"
                  v-for="(item, index) in areaList"
                  :key="item.fieldId"
                  :class="checkedField.fieldId == item.fieldId ? 'clickSty' : 'defaultSty'"
                  @click="selectField(item, index)"
                >
                  {{ item.field_name }}
                </div>
                <!-- <span
                  class="iconfont icon-arrowcircleright2"
                  :class="canRight ? 'bluestyle' : 'graystyle'"
                  @click="toright"
                ></span> -->
                <el-icon :class="canRight ? 'bluestyle' : 'graystyle'" @click="toright">
                  <el-icon-arrow-right />
                </el-icon>
              </div>
              <div class="field-tags-bottom">
                <p class="description">{{ checkedField.fieldDescription }}</p>
                <div class="payforcard">
                  <div
                    class="payforcard-base"
                    v-for="(item, index) in checkedField.skuList"
                    :key="index"
                    @click="pickmonthpay(item)"
                    :class="pickcardType == item.cardType ? 'pickstyle' : 'defaultstyle'"
                  >
                    <div>{{ cardTypeMap.get(item.cardType) }}</div>
                    <div class="userspace-fontstyle4">{{ item.price }}元</div>
                    <div class="userspace-fontstyle2">连续包月续费{{ item.discount }}折</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <QRcode :skuId="skuId" :goodsType="1" @paySuccess="close"></QRcode>
      <SuccessAnimation v-model="paySuccess"></SuccessAnimation>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, defineProps, onBeforeMount, nextTick } from 'vue';
import SuccessAnimation from '@/components/SuccessAnimation.vue';
import QRcode from '@/components/QRcode.vue';
import { cardTypeMap } from '@/types/constant'; // 转换卡的类型
import { getFieldListApi } from '@/apis/userspace';
import { ElMessage } from 'element-plus';
// 加载二维码
const props = defineProps(['dialogAddVisible', 'userInfo', 'userKlgInfo']);
// 判断是否有会员
const isVIP = props.userKlgInfo.myviplist && props.userKlgInfo.myviplist.length > 0 ? true : false;
// 获取领域列表
interface skuData {
  cardType: number;
  discount: number;
  price: number;
  dealPrice: number;
  skuId: number;
}
interface fieldData {
  skuList: skuData[];
  fieldDescription: string;
  fieldId: string;
  field_name: string;
  vipGoodId: number;
}

const paySuccess = ref(false);
const areaList = ref<fieldData[]>([
  {
    skuList: [
      {
        cardType: 1,
        discount: 0,
        price: 0,
        dealPrice: 0,
        skuId: -1
      }
    ],
    fieldDescription: '',
    fieldId: '',
    field_name: '',
    vipGoodId: 0
  }
]); // 领域数据列表
const pickcardType = ref(1); // 控制月卡或年卡的样式
const areaListTotal = ref(0); // 总数
const current = ref(1); // 当前页数
const limit = 4; //呈现个数
// 选中领域
const checkedField = ref<fieldData>({
  skuList: [
    {
      cardType: 1,
      discount: 0,
      price: 0,
      dealPrice: 0,
      skuId: -1
    }
  ],
  fieldDescription: '',
  fieldId: '',
  field_name: '',
  vipGoodId: 0
});
const checkedpay = ref(0); // 价格
const currentIndex = ref(0); // 当前索引，范围在0-3
const canRight = ref(true); // 是否可以向右
const canLeft = ref(false);
const skuId = ref(0);
const getAreaList = (current: number, limit: number) => {
  getFieldListApi(current, limit).then((res) => {
    areaList.value = res.data.areaList;
    currentIndex.value = 0;
    areaListTotal.value = res.data.total;
    checkedField.value = areaList.value[0];
    pickcardType.value = areaList.value[0].skuList[0].cardType;
    checkedpay.value = areaList.value[0].skuList[0].dealPrice;
    skuId.value = areaList.value[0].skuList[0].skuId;
  });
};
// 初次获取
onBeforeMount(() => {
  getAreaList(current.value, limit);
});

// 领域数据切换以及点击
// 选择月卡、季卡和年卡
function pickmonthpay(item: any) {
  checkedpay.value = item.dealPrice;
  pickcardType.value = item.cardType;
  skuId.value = item.skuId;
}
async function selectField(item: any, index: number) {
  checkedField.value = item;
  pickcardType.value = checkedField.value.skuList[0].cardType;
  currentIndex.value = index;
  checkedpay.value = checkedField.value.skuList[0].dealPrice;
  skuId.value = checkedField.value.skuList[0].skuId;
  await nextTick();
  const count = (current.value - 1) * limit + currentIndex.value + 1;
  if (count == areaListTotal.value) {
    canRight.value = false;
    canLeft.value = true;
  }
  if (currentIndex.value == 0 && current.value == 1) {
    canLeft.value = false;
    canRight.value = true;
  }
}
// 右移
function toright() {
  canLeft.value = true;
  const count = (current.value - 1) * limit + currentIndex.value + 1;
  if (count == areaListTotal.value - 1) {
    canRight.value = false;
  }
  if (count < areaListTotal.value && currentIndex.value < 3) {
    currentIndex.value += 1;
    pickcardType.value = checkedField.value.skuList[0].cardType;
    checkedField.value = areaList.value[currentIndex.value];
    checkedpay.value = checkedField.value.skuList[0].dealPrice;
    skuId.value = checkedField.value.skuList[0].skuId;
  } else if (count < areaListTotal.value && currentIndex.value == 3) {
    current.value += 1;
    getAreaList(current.value, limit);
  } else if (count == areaListTotal.value) {
    ElMessage({
      type: 'info',
      showClose: true,
      message: '已经是最后一个啦'
    });
  }
}
// 左移
function toleft() {
  canRight.value = true;
  if (currentIndex.value == 1 && current.value == 1) {
    canLeft.value = false;
  }
  if (currentIndex.value > 0) {
    currentIndex.value -= 1;
    pickcardType.value = checkedField.value.skuList[0].cardType;
    checkedField.value = areaList.value[currentIndex.value];
    checkedpay.value = checkedField.value.skuList[0].dealPrice;
    skuId.value = checkedField.value.skuList[0].skuId;
  } else if (currentIndex.value == 0 && current.value > 1) {
    current.value -= 1;
    getAreaList(current.value, limit);
  } else if (currentIndex.value == 0 && current.value == 1) {
    ElMessage({
      type: 'warning',
      showClose: true,
      message: '已经是第一个啦'
    });
  }
}
// 关闭dialog
// 接收父组件传递的dialogAddVisible
const emit = defineEmits(['closeDialog']);
function close() {
  emit('closeDialog', false);
  paySuccess.value = true;
}
</script>

<style scoped lang="less">
.graystyle {
  color: #797979;
}
.bluestyle {
  color: var(--color-theme-project);
}
.userspace-fontstyle1 {
  font-size: 18px;
  font-weight: 700;
  color: #4d0819;
  font-family: var(--title-family);
}

.userspace-fontstyle2 {
  font-size: 14px;
  font-weight: 400;
  font-family: var(--text-family);
  color: var(--color-black);
}

.userspace-fontstyle3 {
  font-size: 12px;
  font-weight: 400;
  color: var(--color-deep);
}

.userspace-fontstyle4 {
  font-size: 28px;
  font-weight: 400;
}

.img-style {
  width: 60px;
  height: 60px;
}

.mask-layer {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);
  width: 100%;
  /*宽度设置为100%，这样才能使隐藏背景层覆盖原页面*/
  height: 100%;
  filter: alpha(opacity=60);
  /*设置透明度为60%*/
  /*opacity: 0.6;*/
  /*非IE浏览器下设置透明度为60%*/
  z-index: 9999;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.dialog-container {
  margin-top: -200px;
  border-radius: 5px;
  z-index: 99999;
  width: 908px;
  height: 543px;
  display: flex;
  flex-direction: row;
  position: relative;
}
.icon-icon_close {
  position: absolute;
  right: 10px;
  top: 10px;
}

.dialog-container-info {
  width: 556px;
  display: flex;
  flex-direction: column;

  .dialog-container-myinfo {
    height: 110px;
    background: linear-gradient(to right, #ffd37a, #fff9eb);

    .dialog-container-myinfo-content {
      position: absolute;
      left: 23px;
      top: 31px;
      display: flex;
      flex-direction: row;

      .dialog-container-name {
        margin-left: 10px;
        margin-top: 10px;

        .dialog-container-name-top {
          margin-bottom: 10px;
          .dialog-container-name-topleft {
            .userspace-fontstyle1();
            margin-right: 20px;
          }

          .dialog-container-name-topright {
            .userspace-fontstyle2();
            color: #4d0819;
          }
        }
      }
    }
  }

  .dialog-container-fieldinfo {
    width: 556px;
    flex: 1;
    background-color: #fff;

    .dialog-container-fieldinfo-content {
      margin-left: 23px;
      margin-top: 18px;

      .dialog-container-fieldinfo-top {
        .userspace-fontstyle2();
        // letter-spacing: 1.5px;
      }

      .field-tags {
        margin-top: 27px;
        width: 519px;
        height: 314px;
        .field-tags-top {
          height: 28px;
          display: flex;
          justify-content: center;
          flex-direction: row;
          position: relative;
          .icon-arrowcircleleft2 {
            position: absolute;
            left: -18px;
            top: 5px;
          }
          .icon-arrowcircleleft2:hover {
            cursor: pointer;
          }
          .icon-arrowcircleright2 {
            position: absolute;
            right: -18px;
            top: 5px;
          }
          .icon-arrowcircleright2 {
            cursor: pointer;
          }
          .field-tags-item:hover {
            cursor: pointer;
          }
          .field-tags-item {
            width: 126px;
            height: 29px;
            margin-right: 2px;
            font-size: 13px;
            font-weight: 400;
            text-align: center;
            line-height: 28px;
            border: 1px solid #f2f2f2;
          }
          .defaultSty {
            border-radius: 5px 5px 0 0;
            background-color: #fff;
          }
          .clickSty {
            border-radius: 5px 5px 0 0;
            background-color: #f2f2f2;
            font-family: var(--title-family);
          }
          .field-tags-item:hover {
            background-color: #f2f2f2;
          }
          .field-tags-item:last-of-type {
            margin-right: 0;
          }
        }
        .field-tags-bottom {
          background-color: #f2f2f2;
          .description {
            .userspace-fontstyle2();
            padding-top: 12px;
            padding-left: 10px;
          }
          .payforcard {
            display: flex;
            align-items: center;
            justify-content: space-around;
            height: 259px;
            flex-direction: row;
            font-family: var(--title-family);

            .payforcard-base {
              width: 163px;
              height: 165px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: space-around;
              border-radius: 5px;
              // margin-right: 30px;
              cursor: pointer;
              box-shadow: var(--shadow);
            }

            .pickstyle {
              background-color: var(--color-pay);
              color: #4d0819;
            }
            .defaultstyle {
              background-color: #fff;
              color: var(--color-black);
            }
          }
        }
      }
    }
  }
}
</style>
