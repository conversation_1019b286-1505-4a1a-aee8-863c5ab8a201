<template>
  <div class="main-wrapper" :class="{ big: isBig, map: !isMap }">
    <div class="left-video-wrapper">
      <!-- 左侧的文稿 -->
      <div class="left-main">
        <div class="header-wrapper">
          <div class="left-header" ref="header">
            <div class="title">{{ prjInfo.title + '题目' }}</div>
            <div class="base-info">
              <div class="creater">
                <img :src="prjInfo.userCoverPic" class="avatar" />
                <div class="name">{{ prjInfo.userName }}</div>
              </div>
              <div class="time">{{ prjInfo.createTime }}</div>
              <el-popover
                v-model:visible="descriptionVisible"
                placement="bottom-start"
                trigger="click"
                width="200"
                class="custom-popover"
              >
                <!-- 气泡卡片内容 -->
                <div class="popover-content">
                  <span v-html="prjTargetObj.description"></span>
                  <span v-html="prjTargetObj.purpose"></span>
                </div>

                <!-- 触发内容 -->
                <template #reference>
                  <div class="function-tag">项目介绍</div>
                </template>
              </el-popover>
              <!-- <div class="function-tag" @click="descriptionVisible = !descriptionVisible">项目介绍</div> -->
              <!-- <div class="function-tag">案例知识地图</div> -->
            </div>
          </div>
          <div class="right">
            <PrjStudyInfo
              ref="studyInfo"
              :knowledgeSum="klg.klgNumbers"
              :studyPercent="learnedPct"
              :acquaintePercent="graspKlgPct"
              :targetKlgs="prjType == PrjType.case ? null : targetKlgs"
              class="prj-study-info"
            ></PrjStudyInfo>
            <div v-if="tags.length <= 6" class="tags">
              <el-tooltip
                v-for="tag in tags"
                :key="tag.id"
                :content="tag.content"
                effect="customized"
              >
                <PrjTag :label="tag.content" class="prj-tag"></PrjTag>
              </el-tooltip>
            </div>
            <div v-else class="tags">
              <el-tooltip
                v-for="tag in tags.slice(0, 6)"
                :key="tag.id"
                :content="tag.content"
                effect="customized"
              >
                <PrjTag :label="tag.content" class="prj-tag"></PrjTag>
              </el-tooltip>
              <div ref="more">
                <el-icon class="more" @click="handleExpandTag"><ArrowDown /></el-icon>
              </div>
            </div>
          </div>
          <Transition name="fade">
            <div v-if="descriptionVisible || tagsVisible" ref="float" class="float">
              <div v-if="tagsVisible" class="float-right">
                <div class="tags">
                  <el-tooltip
                    v-for="tag in tags.slice(6)"
                    :key="tag.id"
                    :content="tag.content"
                    effect="customized"
                  >
                    <div class="prj-tag">
                      <div class="tag-content">
                        {{ tag.content }}
                      </div>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </Transition>
        </div>

        <div>
          <template v-if="wordContent">
            <PrjManuscript
              :wordContent="wordContent"
              :questionList="questionList ?? []"
              @refresh="handleQuestionList"
              ref="manuScript"
              @search="handleSearch"
              class="lineWordContent"
            ></PrjManuscript>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getManuProjectSectionApi,
  getQuestionListApi,
  deleteQuestionApi,
  saveQuestionApi
} from '@/apis/learning';
import type { Chapter, QuestionData } from '@/types/learning';
import { STATE_FLAG } from '@/types/learning';
import PrjManuscript from '@/views/areadetail/PrjManuscript.vue';
// import PrjManuscript from '@/views/learning/components/PrjManuscript.vue';
import { defineExpose } from 'vue';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { useLearningStore } from '@/stores/learning';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { useWordStore } from '@/stores/word';
import { PrjType } from '@/types/project';
import { emitter } from '@/utils/emitter';
import { useDraftWordStoreV2 } from '@/stores/draftWordV2';
import { handleDraftQuestion } from '@/utils/handleQuestion';
import { Event } from '@/types/event';

const drawerControllerStore = useDrawerControllerStore();
const { mode } = storeToRefs(drawerControllerStore);
const saveType = inject('saveType') as Ref;

const draftWordStore = useDraftWordStoreV2();

watch(
  () => mode.value,
  (newVal) => {
    if (newVal == Mode.read) {
      const draftString = draftWordStore.getReadModeDraftString();
      if (draftString) {
        wordContent.value = draftString;
      }
    } else {
      const draftString = draftWordStore.getAskModeDraftString();
      if (draftString) {
        wordContent.value = draftString;
      }
    }
  }
);

// watch(
//   () => mode.value,
//   (newVal) => {
//     if (newVal == Mode.ask) {
//       wordContent.value = wordStore.getOriginalDraftString
//         ? wordStore.getOriginalDraftString
//         : wordContent.value;
//     } else {
//       wordContent.value = wordStore.getDraftString ? wordStore.getDraftString : wordContent.value;
//     }
//   }
// );

const handleSearch = () => {
  if (mode.value == Mode.read) {
    const draftString = draftWordStore.getReadModeDraftString();
    if (draftString) {
      wordContent.value = draftString;
    }
  } else {
    const draftString = draftWordStore.getAskModeDraftString();
    if (draftString) {
      wordContent.value = draftString;
    }
  }
};
const router = useRouter();
const learningStore = useLearningStore();
const wordStore = useWordStore();

// const changedContent=ref()

const isSectionMenuShow = ref(false);
const manuScript = ref<InstanceType<typeof PrjManuscript>>();

// 项目的spuId
const route = useRoute();
const spuId = route.query.spuId as string;

const isMap = ref<boolean>(true); // 是否展示地图
const isBig = ref<boolean>(false); // 大屏小屏

const isShowButton = ref<boolean>(true); //是否显示展开按钮

const wordContent = ref();

// 项目的prjId
const prjId = ref();
const uniqueCode = ref();
// 项目的章节id
const curChapterId = ref();
// 项目详情
const projectDetailData = shallowRef<Chapter>();
// 章节相关的数据
const chapterList = shallowRef<Chapter[]>();
const activeIndex = ref();
// 拿到章节信息
const questionList = shallowRef<QuestionData[]>();
//内容id
const contentId = ref<string | number>();

// 处理章节变化
//当我切换到当前章节时，我需要发送老的end请求和当前章节的start请求, 结束老章节的start请求和当前章节的end请求

const { initUserBehaviour, handleChapterChange } = userBehaviour(
  PrjForm.draft,
  activeIndex,
  curChapterId
);

//================================ksg-map================================
import { getAreaData, getFocusData, getChapterGraph } from '@/apis/ksgmap';
import KsgMap from 'ksg-map';
import type {
  GlobalConfig,
  OriginalData,
  AreaData,
  PointData,
  FocusData
} from 'ksg-map/dist/types';
import PrjTag from '@/views/learning/components/PrjTag.vue';
import anime from 'animejs/lib/anime.es.js';
import type { PrjinfoItf, PrjTag as PrjTagType } from '@/types/learning';
import { Mode, QuestionAction, type RenderInfo } from '@/types/word';
import type { QuestionType } from '@/types/question';

const klg = inject('klg') as Ref;
const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
const target = ref<HTMLElement | null>(null);
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: true,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: false
});
const data = ref<OriginalData>({
  topAreas: [],
  areas: [],
  focuses: [],
  points: []
});
async function fetchAreaData(id: string): Promise<AreaData[]> {
  return await getAreaData(id);
}
async function fetchFocusData(id: string): Promise<FocusData> {
  return await getFocusData(id);
}
const prjType = inject('prjType') as Ref; // 1讲解 2案例 3测评
const learnedPct = ref();
const graspKlgPct = ref();
const targetKlgs = ref();
// for (let i = 0; i < 10; ++i) {
//   targetKlgs.value.push({ id: i, klgTitle: '知识' + i + '12312312' });
// }
onMounted(async () => {
  saveType.value = 0;

  learnedPct.value =
    klg.value.klgNumbers == 0 ? 0 : Math.floor((klg.value.learned / klg.value.klgNumbers) * 100); // 已经学习的比例
  graspKlgPct.value =
    klg.value.klgNumbers == 0 ? 0 : Math.floor((klg.value.graspKlg / klg.value.klgNumbers) * 100); // 已经掌握的比例
});
const props = defineProps(['prjInfo', 'targetKlgs', 'prjTargetObj']);

const tagsVisible = ref(false);
const descriptionVisible = ref(false);

const prjTargetObj = ref();
const tags = ref<PrjTagType[]>([]);

const header = ref();
const float = ref();

function handleClose(event: MouseEvent) {
  if (header.value.contains(event.target as HTMLElement)) {
    return;
  }
  descriptionVisible.value = false;
  tagsVisible.value = false;
}
function handleExpandTag() {
  tagsVisible.value = !tagsVisible.value;
}
onMounted(() => {
  document.addEventListener('click', handleClose);
  watch(
    () => descriptionVisible || tagsVisible,
    () => {
      if (descriptionVisible || tagsVisible) {
        anime({
          targets: header.value,
          backgroundColor: {
            value: '#F2F2F2',
            duration: 300,
            easing: 'linear'
          }
        });
      } else {
        anime({
          targets: header.value,
          backgroundColor: {
            value: '#FFFFFF',
            duration: 300,
            easing: 'linear'
          }
        });
      }
    }
  );
});
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClose);
});
watch(
  () => props,
  (newvalue) => {
    if (newvalue.prjTargetObj) prjTargetObj.value = newvalue.prjTargetObj;

    if (newvalue.targetKlgs) targetKlgs.value = newvalue.targetKlgs;

    if (newvalue.prjInfo.prjTags) tags.value = newvalue.prjInfo.prjTags as Array<PrjTagType>;

    nextTick(() => {
      console.log('new', props);
    });
  },
  { deep: true, immediate: true }
);

onMounted(async () => {
  target.value = document.getElementById('app')!;
  const unwatch = watch(
    () => curChapterId.value,
    async () => {
      if (curChapterId.value) {
        const pointsData = await getChapterGraph(spuId, curChapterId.value);
        await ksgMap.value?.ready;
        data.value.topAreas = [];
        data.value.points = pointsData;

        ksgMap.value?.reloadData();
      }
    }
  );
});
//================================ksg-map================================

const handleChangeSectionFn = async (chapterId: number) => {
  if (activeIndex.value != chapterId) {
    router.replace({
      query: {
        ...route.query,
        chapterId: chapterId
      }
    });
    handleChapterChange(chapterId);
    activeIndex.value = chapterId;
    const res = await getManuProjectSectionApi(spuId, chapterId);
    // console.log('处理章节变化', res);
    projectDetailData.value = res.data; // 默认第一个
    curChapterId.value = res.data.chapterId;
    activeIndex.value = res.data.chapterId;
    // questionList.value = prjDetailData.value?.questionList; // 拿到问题信息
    contentId.value = res.data.contentId;
    learningStore.contentId = contentId.value;
    // console.log('questionList', questionList.value);
    wordContent.value = res.data.wordContent; //拿到富文本内容
    await handleQuestionList(uniqueCode.value, curChapterId.value); //改了获取问题的接口
  }
  isSectionMenuShow.value = false;
};

const clickQuestion = (questionId: any) => {
  // manuScript.value.handleQuestionList(questionId);
  drawerControllerStore.questionId = questionId;
};
const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
  const res = await getQuestionListApi(uniqueCode, chapterId);
  questionList.value = res.data;

  const worker = new Worker(new URL('@/worker/draftWorker.ts', import.meta.url), {
    type: 'module'
  });
  worker.onmessage = async function (
    e: MessageEvent<{
      regString: string;
      renderInfoIndexes: number[];
      renderInfoList: RenderInfo[];
    }>
  ) {
    draftWordStore.regString = e.data.regString;
    draftWordStore.renderInfoIndexes = e.data.renderInfoIndexes;
    draftWordStore.renderInfoList = e.data.renderInfoList;
    toRaw(questionList.value)?.forEach((question) => {
      handleDraftQuestion(
        question,
        toRaw(draftWordStore.regString),
        toRaw(draftWordStore.renderInfoIndexes),
        toRaw(draftWordStore.renderInfoList)
      );
    });

    wordContent.value = draftWordStore.getReadModeDraftString();
    // console.log(wordContent.value);
    // await nextTick();
    // hljs.highlightAll();

    // wordContent.value = e.data.draftStringList.join('');
    // worker.terminate();
  };
  worker.postMessage({
    htmlString: toRaw(wordContent.value),
    regString: toRaw(draftWordStore.regString),
    renderInfoIndexes: toRaw(draftWordStore.renderInfoIndexes),
    renderInfoList: toRaw(draftWordStore.renderInfoList)
  });
};
const handleDeleteQuestion = (questionId: string) => {
  emitter.emit(Event.REMOVE_QUESTION, questionId);
};
const prjManuscript = ref(); // 小屏转大屏的时候操控状态STATE_FLAG
// 切换状态
const handleReturnInitFn = () => {
  prjManuscript.value.changeStateFn(STATE_FLAG.init);
};
// assessmentId
const assessmentId = ref();

//FIXME：7.20
//目前方案：通过记录beforeunload的时间戳和unload的时间戳之差， 大于10是刷新，小于10是关闭，这个值本身可能依赖于页面复杂度和电脑性能
//不是特别稳定，有时候关闭会被判断为刷新，基本准确率能达到90%以上
//也无法实现关掉一个网页然后又快速打开的防抖操作
//断网了就没有历史记录了，没办法，因为发送endStudy请求要网qwq

//7.22
//现在不对刷新和关闭做区分了, 只对change可见性的操作做防抖

onMounted(async () => {
  let idx = learningStore.validIndex;
  projectDetailData.value = learningStore.chapterList[idx]; // 默认第一个
  assessmentId.value = projectDetailData.value?.assessmentId; // assessmentId
  chapterList.value = learningStore.chapterList; // 拿到章节列表
  curChapterId.value = projectDetailData.value?.chapterId;
  activeIndex.value = projectDetailData.value?.chapterId;
  // questionList.value = prjDetailData.value?.questionList; // 拿到问题信息
  // console.log('questionList', questionList.value);
  // console.log('prjDetailData.value?.wordContent', prjDetailData.value?.wordContent)
  wordContent.value = learningStore.chapterList[idx].wordContent; //拿到富文本内容
  wordContent.value = wordContent.value;
  uniqueCode.value = learningStore.uniqueCode;
  prjId.value = learningStore.prjId; // 拿到项目id
  contentId.value = projectDetailData.value?.contentId;
  learningStore.contentId = contentId.value;

  //initUserBehaviour(curChapterId.value);

  await handleQuestionList(uniqueCode.value, curChapterId.value);
});
watch(
  () => route.query.spuId,
  async (newVal) => {
    curChapterId.value = route.query.chapterId;
    let idx = learningStore.validIndex;
    projectDetailData.value = learningStore.chapterList[idx]; // 默认第一个
    assessmentId.value = projectDetailData.value?.assessmentId; // assessmentId
    chapterList.value = learningStore.chapterList; // 拿到章节列表
    curChapterId.value = projectDetailData.value?.chapterId;
    activeIndex.value = projectDetailData.value?.chapterId;
    // questionList.value = prjDetailData.value?.questionList; // 拿到问题信息
    // console.log('questionList', questionList.value);
    // console.log('prjDetailData.value?.wordContent', prjDetailData.value?.wordContent)
    wordContent.value = learningStore.chapterList[idx].wordContent; //拿到富文本内容
    wordContent.value = wordContent.value;
    uniqueCode.value = learningStore.uniqueCode;
    prjId.value = learningStore.prjId; // 拿到项目id
    contentId.value = projectDetailData.value?.contentId;
    learningStore.contentId = contentId.value;
    console.log('9999999', curChapterId.value);
    //initUserBehaviour(curChapterId.value);
    draftWordStore.regString = '';
    await handleQuestionList(uniqueCode.value, curChapterId.value);
  }
);
const addQuestionFn = async (params: {
  associatedWords: string;
  keyword: string;
  questionType: QuestionType;
  questionDescription: string;
  uniqueCode: string;
  chapterId: string;
  contentId?: string;
}) => {
  const res = await saveQuestionApi(params);
  if (res.success) {
    ElMessage.success('保存问题成功');
    handleDraftQuestion(
      res.data,
      toRaw(draftWordStore.regString),
      toRaw(draftWordStore.renderInfoIndexes),
      toRaw(draftWordStore.renderInfoList),
      QuestionAction.add
    );
    wordContent.value = draftWordStore.getReadModeDraftString();
    questionList.value?.push(res.data);
  } else {
    ElMessage.error('保存问题失败');
  }
};

const removeQuestionFn = async (questionId: string) => {
  const res = await deleteQuestionApi(questionId);
  if (res.success) {
    ElMessage.success('删除成功');
    const rawQuestionList = toRaw(questionList.value);
    const questionIndex = rawQuestionList?.findIndex((item) => item.questionId == questionId);
    handleDraftQuestion(
      rawQuestionList![questionIndex as number],
      toRaw(draftWordStore.regString),
      toRaw(draftWordStore.renderInfoIndexes),
      toRaw(draftWordStore.renderInfoList),
      QuestionAction.remove
    );
    rawQuestionList?.splice(questionIndex as number, 1);
    triggerRef(questionList);
    wordContent.value = draftWordStore.getReadModeDraftString();
  } else {
    ElMessage.error('删除失败');
  }
};

onMounted(() => {
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);
});

onBeforeUnmount(() => {
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);
});

// 提供isBig数据
provide(
  'isBig',
  computed(() => toValue(isBig))
);

// 章节信息
provide(
  'prjSectionInfo',
  computed<{ chapterId: string; prjId: string; contentId: string }>(() => ({
    chapterId: curChapterId.value ?? '',
    uniqueCode: uniqueCode.value ?? '',
    contentId: String(contentId.value ?? ''),
    prjId: prjId.value ?? ''
  }))
);
provide(
  'assessmentId',
  computed(() => assessmentId.value)
);

defineExpose({ curChapterId: curChapterId });
</script>

<style scoped lang="less">
:deep(.sectionMenu) {
  margin: 10px;
  cursor: pointer;
  transform: scale(2);
}
.sectionMenuContent {
  position: absolute;
  transition: width 0.5s ease-in-out;
  left: 90px;
  //width: 284px;
  //height: 673px;
  border-radius: 5px;
  // FIXME：视频的高度不对所以丑，这个高度是原型上的，年后回来再改视频高度就对了
  z-index: 10;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;

  border: 1px solid rgb(220, 223, 230);
  border-radius: 5px;
  box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    background: var(--color-grey);
  }
  .section {
    height: 41px;
    font-size: 14px;
    padding: 0 10px;
    background-color: white;
    display: flex;
    align-items: center;
    font-family: var(--text-family);
    color: var(--color-black);
    cursor: pointer;
    &:hover {
      background-color: #f2f2f2;
    }
    .sectionTitle {
      margin-left: 5px;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  // .title-item {
  //   width: 90%;
  //   cursor: pointer;
  //   height: 40px;
  //   display: flex;
  //   align-items: center;
  //   padding-left: 20px;
  //   font-size: 14px;
  //   &:hover {
  //     cursor: pointer;
  //     font-weight: 700;
  //     background-color: #f2f2f2;
  //   }
  //   margin-top: 4px;
  // }
}

.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
.main-wrapper {
  display: flex;
  justify-content: space-between;
  //waq 10px->30px
  margin-top: 15px;
  margin-bottom: 15px;
  width: 1223px;
}

.left-video-wrapper {
  //flex-basis: 61%;
  margin-left: 44px;
  // min-height: 485px;
  height: calc(100vh - 60px - 70px - 50px);
  max-width: 1350px;
  margin-right: 10px;
  display: flex;
  scroll-snap-align: start;
  transition: width 1s;

  .left-title-list {
    width: 25px;
    margin-right: 20px;
    // background-color: coral;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title-item {
      width: 90%;
      cursor: pointer;
      height: 40px;
      display: flex;
      align-items: center;
      padding-left: 20px;
      font-size: 14px;
      &:hover {
        cursor: pointer;
        font-weight: 700;
        background-color: #f2f2f2;
      }
      margin-top: 4px;
    }

    .title {
      font-size: 12px;
      font-weight: 400;
      color: #797979;
    }

    .title-num {
      width: 25px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      background-color: #f2f2f2;
      border-radius: 5px 0px 0px 5px;
      font-size: 13px;
      font-weight: 400;
      color: #333333;
      margin-bottom: 2px;
      cursor: pointer;

      &.active {
        background-color: var(--color-theme-project);
        color: rgb(254, 254, 246);
        border-radius: 5px 0px 0px 5px;
        box-shadow: rgba(0, 85, 121, 0.376) 0px 3px 3px 0px;
      }
    }
  }

  .left-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 515px;
    .video-title {
      height: 25px;
      font-size: 18px;
      font-weight: 400;
      color: var(--color-black);
      font-family: var(--text-family);
      margin-bottom: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .mapBtn {
        height: 20px;
        border: 1px solid var(--color-theme-project);
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 10px;
        .text {
          font-size: 12px;
          color: var(--color-theme-project);
          margin-right: 5px; // 121, 121, 121
          cursor: pointer;
          white-space: nowrap;
        }
        &:hover {
          cursor: pointer;
          background-color: var(--color-second);
        }
      }
      .icon {
        cursor: pointer;
      }

      .icon-wrapper {
        width: 16px;
        height: 12px;
        margin-right: 5px;
        background: url('@/assets/svgs/u2985.svg') no-repeat center/contain;
        cursor: pointer;

        &:hover {
          background-image: url('@/assets/svgs/u4176.svg');
        }
      }
    }

    .video {
      // background-color: rgb(242, 242, 242);
      flex: 1;
      position: relative;

      .coverPic-wrapper {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: var(--color-black);
        background-repeat: no-repeat;
        background-size: cover;
        // background-size: 100% auto;
      }

      .video-btn-wrapper {
        width: 50px;
        height: 50px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
      }

      .expand-logo {
        position: absolute;
        right: 10px;
        bottom: 10px;
        cursor: pointer;

        &:hover {
          font-weight: 500;
        }
      }
    }

    .video-footer-info {
      height: 40px;
      display: flex;
      align-items: center;
      background-color: #ffffff;
      box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
      border: 1px solid rgb(242, 242, 242);
      padding-left: 10px;
      width: 100%;
      position: relative;

      .video-footer {
        vertical-align: middle;
        transform: translateY(-3px);
      }

      .footer-logo-wrapper {
        width: 90%;
        display: flex;
        align-items: center;
        position: absolute;
      }

      .footer-title {
        font-size: 18px;
        font-weight: 300;
        color: #333333;
        margin-left: 17px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
  .header-wrapper {
    display: flex;
  }
  .left-header {
    background-color: #f2f2f2;
    margin-left: 10px;
    width: 668px;
    /* width: calc(61.8vw - 10px); */
    &.big {
      margin: 0 auto;
    }
    .title {
      font-size: 18px;
      font-weight: 700;
      margin: 10px 0px 10px 0;
    }
    .base-info {
      font-size: 12px;
      display: flex;
      align-items: center;
      color: var(--color-deep);
      .creater {
        display: flex;
        align-items: center;
        .avatar {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
        .name {
        }
      }
      .time {
        margin: 0 10px;
      }
      .function-tag {
        margin: 0 10px;
        padding: 0 10px;
        border-radius: 10px;
        border: 1px solid var(--color-deep);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;

        &:hover {
          background-color: var(--color-inactive-project);
          cursor: pointer;
        }
      }
    }
  }
  .right {
    .prj-study-info {
      // margin: 5px 0px 0px 0;
    }
    .tags {
      align-items: center;
    }
  }
  .tags {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    margin-left: 15px;
    .prj-tag {
      width: 80px;
      height: 20px;
      margin: 5px 5px;
      border-radius: 10px;
      color: var(--color-black);
      background-color: var(--color-inactive-project);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-sizing: border-box;
      transition: border 0.3s ease;

      .tag-content {
        max-width: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:hover {
        border: 1px solid var(--color-deep);
      }
    }
    .more {
      width: 18px;
      height: 18px;
      margin: 5px 5px;
      border-radius: 10px;
      color: var(--color-black);
      background-color: var(--color-inactive-project);
      &:hover {
        border: 1px solid var(--color-deep);
      }
    }
  }
}

.right-content {
  // min-height: 485px;
  height: calc(100vh - 60px - 70px - 50px); // right-content的offsetTop为150px
  flex-basis: 36%;
  // overflow: hidden;
  scroll-snap-align: start;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .right-map {
    // background-color: var(--color-second);
    //flex-shrink: 1;
    width: 100%;
    height: 58%;
    position: relative;

    .close-icon {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
    }
  }
  .questionList {
    width: 100%;
    height: 40%;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 2%;
    overflow: auto;
    .question-item {
      font-size: 13px;
      width: 100%;
      min-height: 50px;
      background-color: white;
      border-radius: 4px;
      padding-left: 6px;
      margin-top: 8px;
      box-sizing: border-box;
      padding: 5px;
      display: flex;
      justify-content: space-between;
      &:hover {
        cursor: pointer;
      }
      .title-line {
        display: flex;
        align-items: center;
      }
    }
  }

  .content-wrapper {
    background-color: #f2f2f2;
    width: 100%;
    height: 100%;
  }
}

.main-wrapper.big {
  display: block;

  .left-video-wrapper {
    width: 100%;
    min-height: 100vh;
  }

  .right-content {
    box-sizing: border-box;
    width: calc(100% - 25px);
    margin-top: 10px;
    // height: 800px;
    min-height: 100vh;
    margin-left: 25px;
    height: 100%;

    &:deep(.text-wrapper) {
      height: 800px !important; // 大屏的高度
    }
  }
}
.main-wrapper.map {
  justify-content: center;
  .left-video-wrapper {
    width: 1350px;
  }
}
.custom-popover {
  .el-popper {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1); /* 修改阴影 */
    border: 1px solid #dcdfe6; /* 增加边框 */
    padding: 10px; /* 增加内边距 */
  }

  .el-popper__arrow {
    border-color: transparent transparent #f4f4f9 transparent; /* 修改箭头颜色 */
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
