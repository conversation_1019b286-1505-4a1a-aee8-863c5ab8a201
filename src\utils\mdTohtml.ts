/**
 * 将markdown文本中的数学公式转换为script标签格式，然后使用markdown-it进行渲染
 *
 * 处理流程：
 * 1. 先匹配块级公式 $$公式$$ 转换为 <script type="math/tex; mode=display">公式</script>
 * 2. 再匹配行内公式 $公式$ 转换为 <script type="math/tex">公式</script>
 * 3. 使用markdown-it进行最终渲染
 */
/**
 * 简化版本的markdown渲染函数，仅处理数学公式转换
 * 不依赖markdown-it，适用于只需要公式转换的场景
 *
 * @param text - 包含数学公式的文本
 * @returns 转换后的文本
 */
import MarkdownIt from "markdown-it";

function convertMathFormulas(text: string): string {
  // 优化：处理块级公式 $$公式$$
  // \s* 匹配零个或多个空白字符
  // 将 \s* 放在捕获组 (.*?) 的外部，这样匹配到的空格就不会被包含在公式内
  let result = text.replace(/\$\$\s*(.*?)\s*\$\$/gs, (_, formula) => {
    return `<script type="math/tex; mode=display">${formula}</script>`;
  });

  // 优化：处理行内公式 $公式$
  // 同样应用 \s* 技巧
  // 注意：原正则表达式中的 (?:[^$\n]|\\\$)+? 部分是为了正确处理公式中包含 \$ 的情况，我们需要保留它。
  result = result.replace(
    /(?<!\$)\$(?!\$)\s*((?:[^$\n]|\\\$)+?)\s*\$(?!\$)/g,
    (_, formula) => {
      return `<script type="math/tex">${formula}</script>`;
    }
  );

  return result;
}

/**
 * 渲染md为html
 */
export function markdownToHtml(markdownText: string): string {
  // 先转换数学公式
  const processedText = convertMathFormulas(markdownText);

  // 如果提供了MarkdownIt构造函数，则使用它进行渲染
  const md = new MarkdownIt({
    html: true, // 允许HTML标签
    breaks: true, // 将换行符转换为<br>
    linkify: true, // 自动识别链接
  });

  return md.render(processedText);
}
// 转义html


