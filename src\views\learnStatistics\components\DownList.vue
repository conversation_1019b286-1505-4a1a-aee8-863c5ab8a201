<template>
  <!-- 
    将页面下方的历史记录部分抽离组件
    负责实现历史记录模块的所有功能
   -->
  <div class="learn-down">
    <div class="up-title">
      <div class="left-title">
        <img
          style="width: 18px; height: 18px; margin-right: 10px"
          src="@/assets/images/learn/u3865.svg"
          alt="history"
        />
        浏览历史
      </div>
      <div class="right-btns">
        <div
          :class="currentIndex == index ? 'btn-active' : 'btns'"
          class="btns"
          v-for="(label, index) in btnsData"
          :key="'btn' + index"
          @click="selectData(index)"
        >
          {{ label }}
        </div>
        <div class="btns" style="width: 100px" @click="clearHistoryVisible = true">
          清除浏览历史
        </div>
        <el-input
          v-model="historyKey"
          style="width: 225px; height: 30px"
          placeholder="请搜索浏览历史"
          size="small"
          @keyup.enter="keyForHistory"
          :prefix-icon="Search"
          clearable
        />
      </div>
    </div>
    <!-- 骨架屏 -->
    <div class="mid-content" v-if="loading">
      <div 
        v-for="index in skeletonCount" 
        :key="'skeleton_' + index"
        class="skeleton-browse-history"
      >
        <div class="skeleton-left-date">
          <div class="skeleton-date"></div>
          <div class="skeleton-triggle"></div>
        </div>
        <div class="skeleton-time">
          <div class="skeleton-time-icon"></div>
          <div class="skeleton-time-text"></div>
        </div>
        <div class="skeleton-main-info">
          <div class="skeleton-left-img"></div>
          <div class="skeleton-right-detail">
            <div class="skeleton-title">
              <div class="skeleton-title-text"></div>
              <div class="skeleton-delete-icon"></div>
            </div>
            <div class="skeleton-info">
              <div class="skeleton-left-info">
                <div class="skeleton-see-label"></div>
                <div class="skeleton-see-content"></div>
                <div class="skeleton-time-tag"></div>
              </div>
              <div class="skeleton-right-info">
                <div class="skeleton-logo-info">
                  <div class="skeleton-logo"></div>
                  <div class="skeleton-username"></div>
                </div>
                <div class="skeleton-ver-line"></div>
                <div class="skeleton-prj-type"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 实际内容 -->
    <div class="mid-content" v-else-if="historyTotal != 0">
      <BrowseHistory
        ref="browseHistory"
        v-for="(hisItem, index) in historyInfo"
        :key="index + '_'"
        :history="hisItem"
        :showDate="showDates[index]"
        @afterDelete="getHistory"
      ></BrowseHistory>
    </div>
    <!-- 后续优化 数据加载完成后提示信息变更  有问题 -->
    <div v-else-if="historyTotal == 0" class="empty-container">
      <el-empty description="暂无浏览记录" />
    </div>

    <div v-if="historyCurPage != historyTotalPage && !loading && historyTotal != 0" class="down-more">
      <span class="footerBtn" @click="getMore">
        <span class="myicon"></span>
        加载更多
      </span>
    </div>

    <div
      v-else
      v-if="historyTotalPage != 1 && !loading && historyTotal != 0"
      style="width: 100%; display: flex; justify-content: center; color: var(--color-theme-project)"
    >
      暂无更多数据
    </div>
  </div>

  <el-dialog class="dg" v-model="clearHistoryVisible" :show-close="false" width="526px" top="40vh">
    <template #header>
      <div class="dg-header">
        <h1>清除浏览历史</h1>
        <img
          @click="clearHistoryVisible = !clearHistoryVisible"
          style="width: 16px; height: 16px; cursor: pointer; float: right"
          src="@/assets/images/prjlearn/close.svg"
          alt=""
        />
      </div>
    </template>
    <div class="content">
      <img src="@/assets/images/prjlearn/process_warn.svg" alt="" />
      确认清除后，数据将无法恢复。是要清除所有的浏览历史吗？
    </div>
    <template #footer>
      <div class="foot-btns">
        <CmpButton class="btn" type="info" @click="clearHistoryVisible = !clearHistoryVisible"
          >关闭窗口</CmpButton
        >
        <CmpButton class="btn" type="primary" @click="deleteAll">确认清除</CmpButton>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getHistoryListApi } from '@/apis/learnStatistics';
import BrowseHistory from './BrowseHistory.vue';
import { Search } from '@element-plus/icons-vue';
//底部浏览历史筛选按钮
const btnsData = ['全部', '近一月', '近一周', '今天'];
const showDates = ref<boolean[]>([]);
let currentIndex = ref(0);

const isScroll = ref(false);

// 请求标识符，用于防止竞态条件
let currentRequestId = 0;

// 防抖定时器
let selectDataTimer: NodeJS.Timeout | null = null;

const selectData = (index: number) => {
  if (index == currentIndex.value) {
    return;
  }

  // 清除之前的定时器
  if (selectDataTimer) {
    clearTimeout(selectDataTimer);
  }

  // 设置新的定时器，0.5秒后执行
  selectDataTimer = setTimeout(() => {
    if (index == currentIndex.value) {
      return;
    }
    currentIndex.value = index;
    // 重置分页变量
    historyCurPage.value = 1;
    //搜索内容数组清空
    historyInfo.value.splice(0, historyInfo.value.length);
    isScroll.value = true;
    loading.value = true;
    getHistory();
  }, 500); // 0.5秒防抖
};
const historyCurPage = ref(1);
const historyTotal = ref(3);
const historyInfo = ref([]);
const historyLimit = ref(10);
const historyTotalPage = ref(1);
const loading = ref(true);

// 计算骨架屏数量
const skeletonCount = computed(() => {
  //return Math.min(historyTotal.value, historyLimit.value); // 最多显示10个骨架屏
  return historyLimit.value;
});
// 获取
const getMore = () => {
  // 使用增加limit的方法 实现加载更多的展示
  historyCurPage.value += 1;
  // historyLimit.value += 10;
  // if (historyLimit.value + 10 > historyTotal.value) {
  //   historyLimit.value = historyTotal.value;
  // } else {
  //   historyLimit.value += 10;
  // }
  isScroll.value = false;
  getHistory();
};

// 删除所有
const browseHistory = ref(null);
const deleteAll = () => {
  clearHistoryVisible.value = false;
  if (browseHistory.value != null) {
    //@ts-ignore
    browseHistory.value[0].delHistory('all');
  } else {
    ElMessage.warning('历史记录为空~');
  }
};
// 历史记录搜索关键字
const historyKey = ref('');

// 清除浏览历史弹窗设置
const clearHistoryVisible = ref(false);

//含关键字的历史记录搜索
const keyForHistory = () => {
  historyInfo.value.splice(0, historyInfo.value.length);
  isScroll.value = true;
  getHistory();
};
// 获取历史记录
const currentDate = ref<string>();

const getHistory = () => {
  loading.value = true;
  
  // 生成当前请求的唯一标识符
  const requestId = ++currentRequestId;
  
  const params = {
    current: historyCurPage.value,
    limit: historyLimit.value,
    title: historyKey.value,
    time: currentIndex.value
  };
  
  getHistoryListApi(params)
    .then((res) => {
      // 检查这个响应是否是最新的请求
      if (requestId !== currentRequestId) {
        console.log('忽略过期的请求响应');
        return;
      }
      
      // 此处对类型进行判断
      historyTotal.value = res.data.total;
      historyTotalPage.value = res.data.totalPage;

      for (let i = 0; i < res.data.list.length; i++) {
        historyInfo.value.push(res.data.list[i]);
        //console.log('------', res.data.list[i]);
        if (currentDate.value == undefined) {
          showDates.value.push(true);
          currentDate.value = res.data.list[i].date;
        } else {
          if (currentDate.value == res.data.list[i].date) {
            showDates.value.push(false);
          } else {
            showDates.value.push(true);
            currentDate.value = res.data.list[i].date;
          }
        }
      }
      //console.log('------');
      console.log(res);
      if(isScroll.value) {
        nextTick(()=>{
        window.scrollTo({ top: 410, behavior: 'smooth' });
      })
      }
      loading.value = false;
    })
    .catch((error) => {
      // 检查这个错误响应是否是最新的请求
      if (requestId !== currentRequestId) {
        console.log('忽略过期的请求错误');
        return;
      }
      console.log(error);
      loading.value = false;
    });
};
onMounted(() => {
  //   getList();
  isScroll.value = false
  getHistory();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (selectDataTimer) {
    clearTimeout(selectDataTimer);
  }
});


</script>

<style scoped lang="less">
.learn-down {
  padding-top: 34px;
  width: var(--width-fixed--project);
  padding-bottom: 30px;
  background-color: white;
  min-height: 500px;

  // 骨架屏样式 - 优化版本，更简洁美观
  .skeleton-browse-history {
    width: 100%;
    height: 120px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 0;
    
    // 左侧指示器
    .skeleton-left-date {
      width: 60px;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      .skeleton-date {
        height: 12px;
        width: 40px;
        background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.8s ease-in-out infinite;
        border-radius: 3px;
        margin-bottom: 8px;
      }
      
      .skeleton-triggle {
        width: 3px;
        height: 8px;
        background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.8s ease-in-out infinite;
        border-radius: 1px;
      }
    }
    
    // 时间区域
    .skeleton-time {
      width: 100px;
      height: 100%;
      display: flex;
      align-items: center;
      margin-left: 20px;
      
      .skeleton-time-icon {
        width: 14px;
        height: 14px;
        background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.8s ease-in-out infinite;
        border-radius: 3px;
      }
      
      .skeleton-time-text {
        height: 12px;
        width: 60px;
        background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.8s ease-in-out infinite;
        border-radius: 3px;
        margin-left: 8px;
      }
    }
    
    // 主要信息区域
    .skeleton-main-info {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: row;
      margin-left: 20px;
      
      // 图片占位符
      .skeleton-left-img {
        width: 160px;
        height: 100px;
        background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.8s ease-in-out infinite;
        border-radius: 8px;
        margin-right: 20px;
      }
      
      // 右侧详情
      .skeleton-right-detail {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        
        // 标题区域
        .skeleton-title {
          width: 100%;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 15px;
          
          .skeleton-title-text {
            height: 16px;
            width: 70%;
            background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.8s ease-in-out infinite;
            border-radius: 3px;
          }
          
          .skeleton-delete-icon {
            width: 14px;
            height: 14px;
            background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.8s ease-in-out infinite;
            border-radius: 3px;
          }
        }
        
        // 信息区域
        .skeleton-info {
          width: 100%;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          
          // 左侧信息
          .skeleton-left-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            flex: 1;
            
            .skeleton-see-label {
              height: 12px;
              width: 30px;
              background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
              background-size: 200% 100%;
              animation: skeleton-loading 1.8s ease-in-out infinite;
              border-radius: 3px;
              margin-right: 8px;
            }
            
            .skeleton-see-content {
              height: 12px;
              width: 120px;
              background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
              background-size: 200% 100%;
              animation: skeleton-loading 1.8s ease-in-out infinite;
              border-radius: 3px;
              margin-right: 8px;
            }
            
            .skeleton-time-tag {
              height: 12px;
              width: 50px;
              background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
              background-size: 200% 100%;
              animation: skeleton-loading 1.8s ease-in-out infinite;
              border-radius: 3px;
            }
          }
          
          // 右侧信息
          .skeleton-right-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 12px;
            
            .skeleton-logo-info {
              display: flex;
              align-items: center;
              
              .skeleton-logo {
                width: 16px;
                height: 16px;
                background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
                background-size: 200% 100%;
                animation: skeleton-loading 1.8s ease-in-out infinite;
                border-radius: 50%;
                margin-right: 6px;
              }
              
              .skeleton-username {
                height: 12px;
                width: 50px;
                background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
                background-size: 200% 100%;
                animation: skeleton-loading 1.8s ease-in-out infinite;
                border-radius: 3px;
              }
            }
            
            .skeleton-ver-line {
              height: 12px;
              width: 1px;
              background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
              background-size: 200% 100%;
              animation: skeleton-loading 1.8s ease-in-out infinite;
            }
            
            .skeleton-prj-type {
              height: 12px;
              width: 60px;
              background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
              background-size: 200% 100%;
              animation: skeleton-loading 1.8s ease-in-out infinite;
              border-radius: 3px;
            }
          }
        }
      }
    }
  }
  
  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    50% {
      background-position: 0% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  .up-title {
    width: 100%;
    display: flex;
    height: 30px;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;

    .left-title {
      width: 10%;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .right-btns {
      width: 48%;
      height: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .btns {
        height: 100%;
        width: 70px;
        border-radius: 4px;
        border-style: solid;
        border-color: var(--color-theme-project);
        border-width: 0.8px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--color-theme-project);
        font-size: var(--fontsize-middle-project);
        cursor: pointer;
      }

      .btns:hover {
        background-color: var(--color-second);
        font-weight: bolder;
      }

      .btn-active {
        background-color: var(--color-theme-project);
        color: white;
      }

      .btn-active:hover {
        background-color: var(--color-theme-project);
        color: white;
        font-weight: bolder;
      }
    }
  }

  .mid-content {
    margin-top: 30px;
    min-height: 500px;
  }

  .empty-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 600px; /* 增加空状态的高度 */
    padding: 40px 0;
  }

  .down-more {
    width: 100%;
    display: flex;
    justify-content: center;
    text-align: center;
    color: var(--color-theme-project);
    font-family: var(--text-family);
    font-weight: 400;
    font-size: 14px;
    height: 20px;
    line-height: 20px;
    margin: 10px 0;
    cursor: pointer;
    .footerBtn {
      width: 90px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .myicon {
        width: 14px;
        height: 12px;
        margin-right: 5px;
        background-image: url('@/assets/images/project/u3964.svg');
      }
      &:hover {
        font-weight: bold;
      }
    }
  }

  .down-more:hover {
    font-weight: bolder;
  }

  .dg {
    .dg-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      border-bottom: 2px solid #f2f2f2;
      padding-bottom: 15px;

      h1 {
        font-size: 18px;
        font-weight: 700;
        color: var(--color-theme-project);
      }
    }
  }

  .content {
    width: 100%;
    white-space: pre-wrap;
  }
}

.foot-btns {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;

  .btn {
    width: 160px;
    height: 43px;

    &:nth-child(2) {
      margin-left: 20px;
    }
  }
}
</style>
