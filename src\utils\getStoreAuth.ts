import { getTemp<PERSON>eyApi } from '@/apis/learning';
import COS from 'cos-js-sdk-v5';

const config = {
  Bucket: 'wujinbenyuan2023-1314810137', // 存储桶，必须字段
  Region: 'ap-beijing'
};

const cos = new COS({
  getAuthorization: async function (options, callback) {
    const res = await getTempKeyApi();
    const data = res.data;
    callback(data);
  }
});

export function getVideoSrc(Key: string): Promise<{
  source: string;
  Authorization: string;
  url: string;
}> {
  return new Promise((resolve, reject) => {
    cos.getObjectUrl(
      {
        ...config,
        Domain: 'https://store.endlessorigin.com',
        Key
      },
      function (err, data) {
        if (err) {
          reject(err);
        } else {
          const url = data.Url;
          const [source, query] = url.split('?');
          resolve({
            url,
            source,
            Authorization: query
          });
        }
      }
    );
  });
}
