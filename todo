1. 显示学习进度时第一次渲染没有问题，但是通过路由切换，浏览器前进后退等操作会导致异常 yes
2. 免费商品购买应该是属性网页状态，而不是直接跳转到详情页面 yes
3. 文稿的详情页有时会发送chapterid为undefined的问题，但是很难复现 yes
4. 记录学习进度时startTime可能产生问题
5. 视频类和文稿类的章节选择不一致
6. 讲解类项目没购买时也可以跳转
7. 划词有两个逻辑，需要删除mathjax的东西
8. 测评需要单独做自适应
9. 划词行为全部修改为抽屉的形式
10. 点击划词内容全部修改为抽屉的形式
11. 使用window.beforeunload处理用户行为采集，并不是所有浏览器都兼容，需要通过page-lifcycle来兼容
12. champagin需要删除所有回答高亮问题的部分
13. 领域介绍页需要单独做
14. 领域没有详情页
15. 视频学习和文稿学习切换章节的方式不一致，需要修改
16. 当用户没有勾选同意时，二维码需要提示用户“请先勾选我已阅读并同意了《无尽本源支付协议》”
17. 禁止讲稿的复制
18. 视频加上手机号防伪码
19. 讲稿加上手机号防伪码