<template>
  <div class="content-section">
    <div class="gray-area">
      <div class="header-section">
        <h3
          v-html="selectedProject.goodsName"
          style="font-size: 20px; margin-bottom: 10px"
          @click="toPrj(selectedProject.spuId)"
        ></h3>
        <div class="base-info">
          <div class="creater">
            <img :src="selectedProject.editorPic" class="avatar" />
            <div class="name">{{ selectedProject.editorName }}</div>
          </div>
          <div class="time">{{ selectedProject.publishTime }}</div>
        </div>

        <p style="padding-right: 4px">说明：{{ selectedProject.studyInstructions }}</p>
      </div>
      <div class="thumbnail-section">
        <div class="thumbnail-large white-box">
          <img
            v-if="prjs[currentIndex]"
            :src="prjs[currentIndex].coverPic"
            alt="Selected Project"
          />
        </div>
        <div
          class="thumbnail-small white-box"
          v-for="(project, index) in displayedThumbnails"
          :key="project.spuId"
          @click="selectProjectByThumbnail(index)"
        >
          <img :src="project.coverPic" alt="Project Thumbnail" />
        </div>
      </div>
    </div>
    <div class="white-area">
      <!-- <img v-if="selectedProject.userCoverPic" :src="selectedProject.userCoverPic" alt="Selected Project" /> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PrjinfoItf } from '@/types/learning';
import { getCaseListApi } from '@/apis/klgdetail';

//const prjInfo = ref<PrjinfoItf>({});
const klgCode = inject('klgCode') as string;
const prjs = ref([
  {
    spuId: '',
    goodsName: '',
    coverPic: '',
    prjForm: '',
    editorName: '',
    editorPic: '',
    studyInstructions: '',
    publishTime: '',
    hasPermission: 1
  }
]);
const currentIndex = ref(0);
const selectedProject = ref(prjs.value[currentIndex.value]);
const total = ref(0);

const selectProject = (index: number) => {
  currentIndex.value = index % total.value;
  selectedProject.value = prjs.value[currentIndex.value];
};

const selectProjectByThumbnail = (thumbnailIndex: number) => {
  const start = (currentIndex.value + 1) % total.value;
  const actualIndex = (start + thumbnailIndex) % total.value;
  selectProject(actualIndex);
};
const displayedThumbnails = computed(() => {
  const start = (currentIndex.value + 1) % total.value;
  const end = (currentIndex.value + 4) % total.value;

  if (end < start) {
    return [...prjs.value.slice(start, total.value), ...prjs.value.slice(0, end)];
  } else {
    return prjs.value.slice(start, end);
  }
});

const router = useRouter();
const toPrj = (spuId: string) => {
  router.push({
    path: '/goodIntroduce',
    query: {
      spuId: spuId
    }
  });
};

onMounted(async () => {
  const res = await getCaseListApi(klgCode);
  prjs.value = res.data.list;

  total.value = prjs.value.length;
  selectedProject.value = prjs.value[currentIndex.value];
  console.log('prjs:', prjs);
});
</script>

<style scoped>
.content-section {
  padding: 10px;
  .gray-area {
    margin-top: 95px;
    margin-left: 45px;
    width: 1111px;
    height: 405px;
    background-color: #f2f2f2;
    position: relative;
    display: flex;
    /* justify-content: center; */
    /* align-items: center; */
    flex-direction: column;
    overflow: hidden;
    padding-top: 50px;

    .header-section {
      position: absolute;
      left: calc(50% + 35px);
      /* text-align: center; */
      margin-bottom: 10px;
      color: #333;

      .base-info {
        font-size: 12px;
        display: flex;
        align-items: center;
        color: var(--color-deep);
        margin-bottom: 10px;
        .creater {
          display: flex;
          align-items: center;
          .avatar {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 5px;
          }
          .name {
          }
        }
        .time {
          margin: 0 10px;
        }
      }
    }

    .thumbnail-section {
      display: flex;
      gap: 12px;
      width: 100%;
      position: absolute;
      left: calc(50% + 35px);
      top: calc(60% + 18px);

      max-width: calc(100% - 10px); /* 确保不会超出gray区域 */
      overflow: hidden;
      .thumbnail-large {
        width: 160px;
        height: 120px;
      }

      .thumbnail-small {
        margin-top: 15px;
        width: 140px;
        height: 105px;
      }

      .white-box {
        background-color: #ffffff;
        border-radius: 4px;
        border: 1px solid rgba(220, 223, 230, 1);
        overflow: hidden;
      }
    }
  }
  .white-area {
    border-radius: 4px;
    border: 1px solid rgba(220, 223, 230, 1);
    width: 600px;
    height: 450px;
    background-color: #ffffff;
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(100px, -280px);
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
