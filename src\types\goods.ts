export enum BuyStatus {
  nobuy,  // 值为 0
  bought  // 值为 1
}
// 是否是会员商品
// 目前的会员商品只有领域
// 非会员商品包括（案例，讲解，测评。。。。
export enum GoodsType {
  common = 0,
  vip = 1
}

export enum CardType {
  year = 1,
  season = 2,
  month = 3
}

export interface Sku {
  cardType: CardType;
  goodsName: string;
  discount: number;
  paymentAmount: string;
  price: string;
  skuId: string;
  studyTime: number;
}

export const cardTypeMap = new Map([
  [CardType.month, '月卡'],
  [CardType.season, '季卡'],
  [CardType.year, '年卡']
]);

//是否具访问权限（购买过商品才能访问课程）
export enum PermissionCode{
  nobuy="A030142"
}