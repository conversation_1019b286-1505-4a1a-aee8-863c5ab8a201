import type { PrjForm } from '@/types/project';
export enum STATE_FLAG {
  init = 'init',
  asking = 'asking',
  answering = 'answer'
}
/**
 * 项目基本信息
 */
export interface PrjinfoItf {
  prjForm?: number;
  createTime?: string;
  prjTags?: PrjTag[];
  title?: string;
  spuId?: string;
  userCoverPic?: string;
  userName?: string;
  uniqueCode?: string;
  [property: string]: any;
}

export interface PrjTag {
  id?: number;
  content?: string;
  [property: string]: any;
}

export interface prjDetailDataItf {
  /**
   * 项目节集合
   */
  chapterList: Chapter[];
  /**
   * 1为视频形式；2为文稿形式
   */
  prjForm: PrjForm;
  /**
   * 项目id
   */
  prjId: number;
  /**
   * 项目商品唯一标识
   */
  [property: string]: any;
}

/**
 * 项目节集合
 */
export interface Chapter {
  /**
   * 视频封面图片地址
   */
  chaCoverPic: string;
  /**
   * 项目节标题
   */
  chapterName: string;
  /**
   * 项目章节id
   */
  chapterId: number;
  /**
   * 项目节序号
   */
  chapterNum: number;
  /**
   * 问题列表
   */
  questionList: QuestionData[];
  /**
   * 小节id
   */
  sectionId: number;
  /**
   * 视频项目返回的整个字幕列表
   */
  videoCaptionList: VideoCaptionListObj[];
  /**
   * 视频存储地址
   */
  videoUrl: string;
  [property: string]: any;
}

export interface QuestionData {
  /**
   * 答案列表
   */
  answerList?: AnswerList[];
  /**
   * 关联文本内容
   */
  associatedWords?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建者ID
   */
  creatorId?: string;
  /**
   * 创建者显示名
   */
  creatorName?: string;
  /**
   * 关键字内容
   */
  keyword: string;
  /**
   * 问题ID
   */
  questionId: number;
  /**
   * 问题必要性（1.必须问题；2.参考问题）
   */
  questionNecessity: string;
  /**
   * 问题类型（是什么/为什么）
   */
  questionType: string;
  [property: string]: any;
  /**
   * 问题描述（只用于开放性问题）
   */
  questionDescription: string;
}

export interface AnswerList {
  /**
   * 答案解释说明
   */
  answerExplanation: string;
  /**
   * 答案id
   */
  answerId: number;
  /**
   * 答案知识ID集合
   */
  answerKlgs: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 创建者ID
   */
  creatorId: string;
  /**
   * 创建者显示名
   */
  creatorName: string;
  [property: string]: any;
}

export interface VideoCaptionListObj {
  /**
   * 当前字幕的内容
   */
  caption: string;
  /**
   * 当前字幕的结束时间
   */
  endTime?: string;
  /**
   * 当前这段字幕的oid
   */
  oid: number;
  /**
   * 当前字幕的开始时间
   */
  startTime: string;
  /**
   * 是否是段首，0代表是段首
   */
  beginning?: number;
  [property: string]: any;
}
