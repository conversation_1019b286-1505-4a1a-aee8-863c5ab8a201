import type { QuestionData, VideoCaptionListObj } from '@/types/learning';

/**
 * 问题与视频文稿时间轴匹配结果接口
 */
export interface QuestionTimelineMatch {
  startTime: string;
  keyword: string;
  questionType: string;
  questionId: number;
}

/**
 * 扁平化后的caption接口
 */
interface FlatCaption {
  caption: string;
  startTime: string;
  paragraphIndex: number;
}

/**
 * 匹配候选项接口
 */
interface MatchCandidate {
  startIndex: number;
  endIndex: number;
  startTime: string;
  score: number;
  matchedText: string;
  captionCount: number;
}

// 时间转换辅助函数
export function timeToSeconds(timeStr: string): number {
  const parts = timeStr.split(':');
  if (parts.length === 3) {
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
  } else if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseInt(parts[1]);
  }
  return parseInt(timeStr);
}

/**
 * 匹配问题列表与视频文稿时间轴（优化版）
 *
 * 特点：
 * 1. 兼容一维和三维数组结构
 * 2. 为每个时间点创建独立的数组元素（不去重）
 * 3. 跨段落匹配时返回第一个caption的startTime
 * 4. 优先选择最短、最紧凑的匹配
 * 5. 动态调整匹配范围，支持长文本匹配
 * 6. 只返回必要的四个字段：startTime, keyword, questionType, questionId
 *
 * @param questionList 问题列表
 * @param videoCaptionList 视频文稿列表（支持一维或三维数组）
 * @returns 按时间排序的匹配结果数组
 */
export function matchQuestionsWithTimeline(
  questionList: QuestionData[],
  videoCaptionList: VideoCaptionListObj[][] | VideoCaptionListObj[]
): QuestionTimelineMatch[] {
  const matches: QuestionTimelineMatch[] = [];

  // 智能扁平化处理，兼容多种数据结构
  const flattenCaptions = (
    captionList: VideoCaptionListObj[][] | VideoCaptionListObj[]
  ): FlatCaption[] => {
    const allCaptions: FlatCaption[] = [];

    // 检测数据结构类型
    if (!Array.isArray(captionList) || captionList.length === 0) {
      return allCaptions;
    }

    // 如果第一个元素是数组，说明是三维结构
    if (Array.isArray(captionList[0])) {
      const threeDimensionalList = captionList as VideoCaptionListObj[][];
      threeDimensionalList.forEach((paragraphList, paragraphIndex) => {
        if (Array.isArray(paragraphList)) {
          paragraphList.forEach((captionItem: VideoCaptionListObj) => {
            if (captionItem && captionItem.caption) {
              allCaptions.push({
                caption: captionItem.caption,
                startTime: captionItem.startTime,
                paragraphIndex
              });
            }
          });
        }
      });
    }
    // 如果第一个元素有caption属性，说明是一维结构
    else if (captionList[0] && 'caption' in captionList[0]) {
      const oneDimensionalList = captionList as VideoCaptionListObj[];
      oneDimensionalList.forEach((captionItem: VideoCaptionListObj) => {
        if (captionItem && captionItem.caption) {
          allCaptions.push({
            caption: captionItem.caption,
            startTime: captionItem.startTime,
            paragraphIndex: 0 // 一维结构统一设为段落0
          });
        }
      });
    }

    return allCaptions;
  };

  questionList.forEach((question) => {
    if (!question.associatedWords) return;

    const associatedWords = question.associatedWords.trim();

    // 使用优化的扁平化处理
    const allCaptions = flattenCaptions(videoCaptionList);

    if (allCaptions.length === 0) {
      console.warn('No valid captions found for matching');
      return;
    }

    // 找到所有可能的匹配
    const allPossibleMatches: MatchCandidate[] = [];

    // 动态计算最大检查范围，基于associatedWords长度
    const estimatedCaptionCount = Math.ceil(associatedWords.length / 10); // 假设每个caption平均10个字符
    const maxCaptionsToCheck = Math.min(Math.max(8, estimatedCaptionCount * 2), allCaptions.length);

    for (let i = 0; i < allCaptions.length; i++) {
      const actualMaxCheck = Math.min(maxCaptionsToCheck, allCaptions.length - i);

      for (let length = 1; length <= actualMaxCheck; length++) {
        let combinedText = '';

        // 构建连续的文本组合
        for (let k = 0; k < length; k++) {
          combinedText += allCaptions[i + k].caption;
        }

        // 检查是否包含associatedWords
        if (combinedText.includes(associatedWords)) {
          const paragraphSpan =
            allCaptions[i + length - 1].paragraphIndex - allCaptions[i].paragraphIndex;

          // 计算匹配质量分数（越小越好）
          // 长度权重×100 + 跨段落权重×10 + 时间权重×1
          const score = length * 100 + paragraphSpan * 10 + timeToSeconds(allCaptions[i].startTime);

          allPossibleMatches.push({
            startIndex: i,
            endIndex: i + length - 1,
            startTime: allCaptions[i].startTime,
            score: score,
            matchedText: combinedText,
            captionCount: length
          });

          break; // 找到最短匹配后停止扩展
        }
      }
    }

    // 按质量分数排序并选择最优匹配
    allPossibleMatches.sort((a, b) => a.score - b.score);

    const usedCaptions = new Set<number>();

    for (const match of allPossibleMatches) {
      // 检查是否与已选择的匹配重叠
      let hasOverlap = false;
      for (let i = match.startIndex; i <= match.endIndex; i++) {
        if (usedCaptions.has(i)) {
          hasOverlap = true;
          break;
        }
      }

      if (!hasOverlap) {
        // 标记所有参与的caption为已使用
        for (let i = match.startIndex; i <= match.endIndex; i++) {
          usedCaptions.add(i);
        }

        // 创建最终匹配记录 - 只包含必要的四个字段
        matches.push({
          startTime: match.startTime,
          keyword: question.keyword || '',
          questionType: question.questionType || '',
          questionId: question.questionId
        });
      }
    }
  });

  // 按时间排序
  matches.sort((a, b) => {
    const timeA = timeToSeconds(a.startTime);
    const timeB = timeToSeconds(b.startTime);
    return timeA - timeB;
  });

  return matches;
}
/**
 * 格式化时间显示（去掉前导的00:）
 * @param timeStr 时间字符串 (HH:MM:SS)
 * @returns 格式化后的时间字符串
 */
export const formatDisplayTime = (timeStr: string): string => {
  if (!timeStr) return '00:00';

  // 如果以 "00:" 开头，则去掉前两位
  if (timeStr.startsWith('00:')) {
    return timeStr.substring(3);
  }

  return timeStr;
};

/**
 * 根据startTime获取格式化的显示时间
 * @param startTime 开始时间字符串（如 "00:01:30"）
 * @returns 格式化后的时间字符串（去掉前导00:）
 */
export const getDisplayTime = (startTime: string): string => {
  if (!startTime) return '00:00';

  // 如果前两个时间数字是0则去掉（如 "00:01:30" -> "01:30"）
  const parts = startTime.split(':');
  if (parts.length >= 3 && parts[0] === '00') {
    // 去掉小时部分的00
    return parts.slice(1).join(':');
  } else if (parts.length >= 2 && parts[0] === '0') {
    // 去掉分钟部分的前导0
    return startTime.substring(1);
  }

  return startTime;
};

/**
 * 弹幕数据接口
 */
export interface DanmuItem {
  id: number;
  txt: string;
  time: number; // 触发时间（秒）
  style?: {
    color?: string;
    fontSize?: string;
    fontWeight?: string;
    background?: string;
    padding?: string;
    borderRadius?: string;
    border?: string;
  };
  mode?: 'scroll' | 'top' | 'bottom';
  duration?: number;
  questionId?: number;
}

/**
 * 弹幕管理器类
 */
export class QuestionDanmuManager {
  private displayedDanmus = new Set<number>(); // 已显示的弹幕ID集合
  private danmuList: DanmuItem[] = []; // 预处理的弹幕列表
  private tolerance = 0.5; // 时间匹配容错范围（秒）

  /**
   * 将时间字符串转换为秒数
   */
  private timeToSeconds(timeStr: string): number {
    if (!timeStr) return 0;
    const parts = timeStr.split(':');
    let seconds = 0;

    if (parts.length === 3) {
      // HH:MM:SS
      seconds = parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    } else if (parts.length === 2) {
      // MM:SS
      seconds = parseInt(parts[0]) * 60 + parseInt(parts[1]);
    } else {
      // SS
      seconds = parseInt(parts[0]);
    }

    return seconds;
  }

  /**
   * 格式化问题弹幕内容
   */
  private formatDanmuContent(keyword: string, questionType: string): string {
    // 如果是开放性问题，不显示questionType
    if (questionType === '开放性问题') {
      return `[${keyword}]`;
    }
    return `[${keyword}]${questionType}`;
  }

  /**
   * 初始化弹幕数据
   */
  initializeDanmus(questionTimelineMatches: QuestionTimelineMatch[]): void {
    this.danmuList = questionTimelineMatches.map((match, index) => ({
      id: match.questionId || Date.now() + index,
      txt: this.formatDanmuContent(match.keyword, match.questionType),
      time: this.timeToSeconds(match.startTime),
      style: {
        color: '#ffffff',
        fontSize: '16px',
        fontWeight: 'bold',
        background: 'linear-gradient(45deg, #409eff, #67c23a)',
        padding: '4px 8px',
        borderRadius: '12px',
        border: '1px solid rgba(255, 255, 255, 0.3)'
      },
      mode: 'scroll' as const,
      duration: 6000, // 6秒显示时间
      questionId: match.questionId
    }));

    // 按时间排序
    this.danmuList.sort((a, b) => a.time - b.time);

    console.log('🎯 初始化问题弹幕:', this.danmuList);
  }

  /**
   * 检查并获取需要显示的弹幕
   */
  checkAndGetDanmus(currentTime: number): DanmuItem[] {
    const danmusToShow: DanmuItem[] = [];

    for (const danmu of this.danmuList) {
      // 检查时间是否匹配（在容错范围内）
      if (Math.abs(currentTime - danmu.time) <= this.tolerance) {
        // 检查是否已经显示过
        if (!this.displayedDanmus.has(danmu.id)) {
          danmusToShow.push(danmu);
          this.displayedDanmus.add(danmu.id);
          console.log(`🎯 触发问题弹幕: ${danmu.txt} at ${currentTime}s`);
        }
      }
    }

    return danmusToShow;
  }

  /**
   * 处理快进/快退场景，批量检查遗漏的弹幕
   */
  handleSeek(currentTime: number): DanmuItem[] {
    const danmusToShow: DanmuItem[] = [];

    for (const danmu of this.danmuList) {
      // 如果当前时间已经超过弹幕时间，且弹幕未显示过
      if (currentTime >= danmu.time && !this.displayedDanmus.has(danmu.id)) {
        // 只显示最近5秒内的弹幕，避免一次性显示太多
        if (currentTime - danmu.time <= 5) {
          danmusToShow.push(danmu);
          this.displayedDanmus.add(danmu.id);
          console.log(`🎯 快进补偿弹幕: ${danmu.txt}`);
        } else {
          // 标记为已显示，但不实际显示
          this.displayedDanmus.add(danmu.id);
        }
      }
    }

    return danmusToShow;
  }

  /**
   * 重置弹幕状态（用于重新播放）
   */
  reset(): void {
    this.displayedDanmus.clear();
    console.log('🎯 重置问题弹幕状态');
  }

  /**
   * 获取指定时间点的弹幕信息（用于调试）
   */
  getDanmuAtTime(time: number): DanmuItem | null {
    return this.danmuList.find((danmu) => Math.abs(danmu.time - time) <= this.tolerance) || null;
  }

  /**
   * 获取所有弹幕时间点（用于调试）
   */
  getAllDanmuTimes(): number[] {
    return this.danmuList.map((danmu) => danmu.time);
  }
}


export const getSeconds = (seconds: string) => {
  // 将 1   61 这种数字或者字符串转为   00:00:01  00:01:01
  if (!seconds) return 0;
  const array = seconds.split(':');
  let res = 0;
  array.forEach((item) => {
    res = res * 60 + parseInt(item, 10);
  });
  return res;
};