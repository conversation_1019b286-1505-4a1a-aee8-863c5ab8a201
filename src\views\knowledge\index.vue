<template>
  <div class="picture">
    <!-- <img style="width:1400px" src="@/assets/images/knowledge/knowledgeindex.gif" /> -->
    <KsgMap ref="ksgMap" class="ksg-map"
      v-model:config="config"
      :data="data"
      :fetchAreaData="fetchAreaData"
		  :fetchFocusData="fetchFocusData"
    />
  </div>
</template>

<script setup lang="ts">
import { getAreaData, getKnowledgeOriginData, getFocusData as getFocusData, getAreaGraph } from '@/apis/ksgmap';
import KsgMap from 'ksg-map'
import type { GlobalConfig, OriginalData, AreaData, FocusData } from 'ksg-map/dist/types';

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null)
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: true,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: true,
  autoLoadLevel: true,
})
const data= ref<OriginalData>({
  topAreas: [],
  points: [],
  areas: [],
  focuses: [],
})

async function fetchAreaData(id: string): Promise<AreaData[]> {
  return await getAreaData(id);
}
async function fetchFocusData(id: string): Promise<FocusData> {
  return await getFocusData(id);
}

onMounted(async () => {
  const areas = (await getAreaGraph())
  await ksgMap.value?.ready;
  data.value.topAreas = areas.map(area => area.areaId);
  data.value.areas = areas;
  ksgMap.value?.reloadData();
})

</script>

<style scoped lang="less">
.picture {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}
.ksg-map {
  width: 160vh;
  height: 90vh;
}
</style>
