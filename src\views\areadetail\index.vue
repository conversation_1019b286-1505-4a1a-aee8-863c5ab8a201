<script setup lang="ts">
import ClassicEditor from '@/components/editors/Veditor.vue';
// import CkEditorClassic from '@/components/CkEditor/CkEditorClassic.vue';
import CmpButton from '@/components/CmpButton.vue';
import { getPreklglistApi } from '@/apis/klgdetail';
import { getAreaDetailApi } from '@/apis/area';
import AreaContent from './areaContent.vue'; // 知识内容组件
import MapContent from './MapContent.vue';
import CardPage from './CardPage.vue'; //暂时不需要-隐藏-没写完
import EvaluationContent from './EvaluationContent.vue'; // 讲解测评组件
import {
  Document,
  EditPen,
  Opportunity,
  Expand,
  Fold,
  ArrowRightBold,
  Orange
} from '@element-plus/icons-vue';

const route = useRoute();
const areaCode = route.query.areaCode as string;
provide('areaCode', areaCode);

const currentContent = ref('area'); // 默认显示知识内容
const isFolded = ref(false);

//领域详情数据类型
const areaDetail = ref<areadetailData>({
  title: '',
  areaCode: '',
  authorName: '',
  createTime: '',
  description: ''
});
onMounted(async () => {
  try {
    const response = await getAreaDetailApi(areaCode);
    areaDetail.value.title = response.data.title;
    areaDetail.value.areaCode = response.data.areaCode;
    areaDetail.value.createTime = response.data.createTime;
    areaDetail.value.authorName = response.data.authorName;
    areaDetail.value.description = response.data.description;
  } catch (error) {
    console.error('获取数据失败:', error);
  }
});

const currentActive = ref('area');
const setActive = (content: string) => {
  currentContent.value = content;
  currentActive.value = content;
};
const currentContentComponent = computed(() => {
  if (currentContent.value == 'area') {
    return {
      component: AreaContent,
      props: { areaDetail: areaDetail.value, areaCode: areaCode }
    };
  } else if (currentContent.value == 'evaluation') {
    return {
      component: EvaluationContent,
      props: {}
    };
  } else if (currentContent.value == 'map') {
    return {
      component: MapContent,
      props: { areaDetail: areaDetail.value, areaCode: areaCode }
    };
  }
  //else if (currentContent.value == 'card') {
  //   return {
  //     component: CardPage,
  //     props: { klgDetail: klgDetail.value, areaCode: areaCode }
  //   };
  // }
  return null;
});

const toggleNavbar = () => {
  isFolded.value = !isFolded.value;
};

interface areadetailData {
  title: string;
  areaCode: string;
  authorName: string;
  createTime: string;
  description: string;
}

//===========================ksg-map==================================
import { getLevelData } from '@/apis/ksgmap';
import KsgMap from 'ksg-map';
import type { GlobalConfig, OriginalData, AreaData, PointData } from 'ksg-map/dist/types';

const ksgMap = ref<InstanceType<typeof KsgMap> | null>(null);
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: false,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: true
});
const data = ref<OriginalData>({
  topAreas: [],
  points: [],
  areas: [],
  focuses: []
});

async function fetchLevelData(ids: string[], type: 'area' | 'focus', level: number, limit: number) {
  return (await getLevelData([areaCode], level, limit)).data;
}
//===========================ksg-map==================================

// 获取知识点详情
const oid = ref<string>('1');

//const getklgdetail = (oid: string) => {
//getklgdetailApi(oid).then((res) => {
// console.log('获取详情',res.data.list)
// klgdetail.value = res.data.list;
//});
//};
// getklgdetail(oid.value);

// 前驱知识相关
interface preklgData {
  oid: number;
  title: string;
  sort: string;
}
let currentPage = ref(1);
const limit = 10;
const preklglist = ref<preklgData[]>([]);
const total = ref(0);
let checkedpre = ref(0);

// 初始化
const InitData = () => {
  currentPage.value = 1;
  getPreklglistApi(oid.value, currentPage.value, limit);
};

//InitData();

const handleCurrentChange = (val: number) => {
  getPreklglistApi(oid.value, val, limit);
};

function pickPre(item: any) {
  checkedpre.value = item.oid;
}
</script>
<template>
  <div class="areadetail-container">
    <div class="areadetail-innercontainer">
      <div class="navbar" :class="{ folded: isFolded }">
        <div class="nav-change" @click="toggleNavbar">
          <el-icon v-if="isFolded"><Expand /></el-icon>
          <el-icon v-else><Fold /></el-icon>
        </div>
        <div class="nav-items" :class="{ folded: isFolded }">
          <div
            @click="setActive('area')"
            :class="['nav-item', { active: currentActive == 'area' }]"
          >
            <el-icon><Document /></el-icon>
            <span v-if="!isFolded">领域描述</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
          <div @click="setActive('map')" :class="['nav-item', { active: currentActive == 'map' }]">
            <el-icon><Orange /></el-icon>
            <span v-if="!isFolded">知识源图</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
          <!-- todo:这个组件后期可能需要 -->
          <!-- <div
            @click="setActive('card')"
            :class="['nav-item', { active: currentActive == 'card' }]"
          >
            <el-icon><Orange /></el-icon>
            <span v-if="!isFolded">成为会员</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div> -->
          <div
            @click="setActive('evaluation')"
            :class="['nav-item', { active: currentActive == 'evaluation' }]"
          >
            <el-icon><EditPen /></el-icon>
            <span v-if="!isFolded">讲解测评</span>
            <el-icon v-if="!isFolded" style="margin-left: 28px"><ArrowRightBold /></el-icon>
          </div>
        </div>
      </div>
      <div class="content-container">
        <div class="areadetail-header">
          <div class="areadetail-name" v-html="areaDetail.title"></div>
          <div class="areadetail-more">
            <span>贡献者:{{ areaDetail.authorName }}</span>
            <span>创建时间:{{ areaDetail.createTime }}</span>
          </div>
        </div>

        <div class="areadetail-container-content">
          <!-- 动态内容区域 -->
          <component
            :is="currentContentComponent.component"
            v-bind="currentContentComponent.props"
          ></component>
        </div>
      </div>

      <!-- <el-dialog v-model="dialogVisible" width="526px" :z-index="10">
        <template #header>
          <div class="dialog-header">我要反馈</div>
        </template>
        <div class="dialog-title" v-html="klgDetail.klgTitle"></div>
        <div class="dialog-feedback-title">反馈内容</div>

        <div class="dialog-feedback-content">
          <CkEditorClassic></CkEditorClassic>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <CmpButton type="info" @click="dialogVisible = false" class="dialog-button"
              >关闭窗口</CmpButton
            >
            <CmpButton type="primary" @click="saveFeedBack" class="dialog-button"
              >提交反馈</CmpButton
            >
          </span>
        </template>
      </el-dialog> -->
    </div>
  </div>
</template>


<style scoped lang="less">
// .dialog-footer {
//   display: flex;
//   justify-content: center;
// }

// .dialog-button {
//   width: 160px;
//   height: 43.2px;
//   font-size: 14px;
//   margin-right: 21px;
// }

.areadetail-container {
  margin-top: 20px;
  display: flex;
  //align-items: center;
  justify-content: center;

  .areadetail-innercontainer {
    width: 1400px;
    display: flex;

    .navbar {
      display: flex;
      flex-direction: column;
      //background-color: #f4f4f4;
      padding: 10px;
      flex: 0 0 160px;
      //justify-content: center;
      margin-top: 64px;

      .nav-change {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .el-icon {
          font-size: 25px;
          color: #333333;
        }
      }

      .nav-item {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        height: 60px;
        transition:
          background-color 0.3s,
          border-radius 0.3s;
        color: #333333;
        margin-bottom: 12px;

        .el-icon {
          margin-left: 5px;
          margin-right: 5px;

          &:hover {
            //transform: scale(1.2); // 鼠标悬停时放大
          }
        }
      }

      .nav-item:hover {
        background-color: #f2f2f2;
        border-radius: 5px;
      }

      .nav-item.active {
        background-color: #f2f2f2;
        border-radius: 5px;
      }
    }
    .navbar.folded {
      flex: 0 0 70px;
    }
    .nav-item span {
      display: inline; // 默认显示
    }

    .navbar.folded .nav-item span {
      display: none; // 折叠时隐藏文字
    }

    .content-container {
      display: flex;
      flex-direction: column;
      margin-left: 10px;

      .areadetail-header {
        border-bottom: 2px solid #f2f2f2;
        padding-bottom: 20px;
        //margin-bottom: 20px;
        .areadetail-name {
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 9px;
        }

        .areadetail-more {
          font-size: 14px;
          font-weight: 400;
          color: #797979;

          span {
            margin-right: 83px;
          }
        }
      }

      .areadetail-container-content {
        display: flex;
        //margin-top: 10px;
      }
    }
  }
}

.pagination-block {
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: center;
  margin-top: 5px;
}
</style>
