<template>
  <div class="warpper">
    <el-drawer v-model="drawerVisible" size="50%" direction="ltr" class="drawer">
      <template #header>
        <div class="header">查看问题</div>
      </template>
      <el-scrollbar class="drawer-body">
        <div class="question">
          <div class="title">
            <div class="creator-name">{{ curQuestion.userName }}</div>
            的提问
          </div>
          <div class="description">
            <template v-if="curQuestion.questionType != '开放性问题'">
              [<span class="key-words" v-html="processAllLatexEquations(curQuestion.keyword)"></span
              >】<span class="question-type">{{ curQuestion.questionType }}</span>
            </template>
            <template v-else>
              "<span class="key-words" v-html="processAllLatexEquations(curQuestion.keyword)"></span
              >"<span v-html="processAllLatexEquations(curQuestion.questionDescription)"></span>
            </template>
          </div>
          <div class="bottom">
            <div class="date">
              {{ curQuestion.createTime }}
            </div>
            <div class="operate">
              <div
                v-if="curQuestion.canDelete"
                class="click-delete"
                @click="handleDeleteQuestion()"
              >
                删除
              </div>
            </div>
          </div>
        </div>
        <div class="all-answer">
          <div v-for="answer in curQuestion.answers" :key="answer.answerId" class="answer-list">
            <el-card class="answer-card">
              <div class="title">
                <span class="name">
                  {{ answer.userName }}
                </span>
                的回答
              </div>
              <div class="klg-list">
                <div
                  class="klg-item"
                  v-for="item in answer.knowledgeList"
                  :key="item.code"
                  @click="goToDetail(item.code, item.type)"
                >
                  <el-tooltip placement="top" effect="customized">
                    <template #content>
                      <span v-html="item.title"></span>
                    </template>
                    <el-tag class="klg-tag">
                      <div class="klg-tag-text" v-html="item.title"></div>
                    </el-tag>
                  </el-tooltip>
                </div>
              </div>
              <div
                class="description"
                v-html="processAllLatexEquations(answer.answerExplanation)"
              ></div>
              <div class="bottom">
                <div class="date">
                  {{ answer.createTime }}
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </el-scrollbar>
    </el-drawer>
    <div
      ref="floatingElement"
      :style="
        floatingVisible ? floatingStyles : { position: 'fixed', top: '-9999px', left: '-9999px' }
      "
      class="questionPop"
    >
      <Transition name="scale" :duration="{ enter: 200, leave: 50 }">
        <div
          v-if="floatingVisible && questionList && questionList.length > 0"
          class="floating-content"
        >
          <div
            v-for="(question, index) in questionList"
            :key="question.questionId"
            class="floating-content-item"
            @click="showDrawer(question)"
          >
            <div style="display: flex; align-items: center">
              <span class="keyword-container">
                【
                <el-tooltip
                  placement="top"
                  :content="processAllLatexEquations(question.keyword)"
                  :raw-content="true"
                  :show-after="200"
                  effect="customized"
                >
                  <span
                    class="questionList ellipsis"
                    style="word-break: break-all"
                    v-html="question.keyword"
                  ></span>
                </el-tooltip>

                】
              </span>
              <span v-if="question.questionType != '开放性问题'" class="question-type">{{
                question.questionType
              }}</span
              ><span v-else v-html="question.questionDescription"></span>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { QuestionData } from '@/types/learning';
import { saveAnswerApi, deleteQuestionApi, getQuestionDetailApi } from '@/apis/learning';
import { onMounted, onUnmounted, ref, nextTick } from 'vue';
import { useFloating, shift, flip, offset, autoUpdate, size } from '@floating-ui/vue';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { emitter } from '@/utils/emitter';
import { QuestionType } from '@/types/question';
import { KlgType } from '@/types';
import { Event } from '@/types/event';
import { processAllLatexEquations, convertMathTagsToMDLatex } from '@/utils/latexUtils';

const drawerControllerStore = useDrawerControllerStore();
const { questionId } = storeToRefs(drawerControllerStore);
const model = defineModel<HTMLElement | null>();
const router = useRouter();
const route = useRoute();

const questionList = ref<any[]>();
const curQuestion = ref();
const drawerVisible = ref(false);
interface Klg {
  id: string;
  name: string;
}

const referenceElement = ref<HTMLElement | null>(null);
const floatingElement = ref<HTMLElement | null>(null);
const floatingVisible = ref(false);
const { floatingStyles } = useFloating(referenceElement, floatingElement, {
  open: floatingVisible,
  placement: 'top',
  middleware: [
    offset(5), // 添加一些偏移，使浮动内容不会紧贴参考元素
    flip({
      fallbackPlacements: ['bottom-start'], // 如果顶部空间不足，翻转到底部并保持左对齐
      padding: 10 // 与视口边缘保持10px的距离
    }),
    shift({
      padding: 10, // 确保内容不会太靠近视口边缘
      crossAxis: true // 允许在交叉轴上移动
    }),
    size({
      apply({ availableWidth, elements }) {
        // 限制浮动元素的最大宽度为可用宽度
        Object.assign(elements.floating.style, {
          maxWidth: `${Math.min(400, availableWidth)}px`
        });
      },
      padding: 10 // 与视口边缘保持10px的距离
    })
  ],
  whileElementsMounted: autoUpdate // 自动更新位置
});

const handleDeleteQuestion = async () => {
  emitter.emit(Event.REMOVE_QUESTION, curQuestion.value.questionId);
  drawerVisible.value = false;
};

watch(
  () => model.value,
  async (newVal) => {
    if (newVal) {
      const ids = newVal!.getAttribute('data-qid');
      if (ids == '' || ids == undefined) {
        return;
      }

      const qids = ids.split(',');
      if (qids.length == 1) {
        // 只有一个问题ID时，直接获取问题详情并显示抽屉
        const res = await getQuestionDetailApi(qids[0]);
        questionList.value = res.data;
        curQuestion.value = questionList.value![0];
        drawerVisible.value = true;
      } else {
        // 有多个问题ID时，分别获取每个问题的详情
        try {
          // 创建一个问题列表数组
          questionList.value = [];

          // 对每个问题ID单独发起请求
          const promises = qids.map((qid) => getQuestionDetailApi(qid));
          const results = await Promise.all(promises);

          // 合并所有问题数据
          results.forEach((res) => {
            if (res.data && res.data.length > 0) {
              questionList.value!.push(res.data[0]);
            }
          });

          // 显示浮动内容让用户选择
          referenceElement.value = newVal;

          // 先关闭浮动内容，确保重新定位
          floatingVisible.value = false;

          // 使用setTimeout确保DOM更新后再显示浮动内容
          setTimeout(() => {
            // 打开浮动内容
            floatingVisible.value = true;

            // 在浮动内容渲染后，确保它在视口内
            nextTick(() => {
              if (floatingElement.value) {
                const floatingEl = floatingElement.value as HTMLElement;

                // 如果浮动元素不在视口内，调整其位置
                if (!isInViewport(floatingEl)) {
                  floatingEl.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'nearest'
                  });
                }
              }
            });
          }, 100); // 短暂延迟确保滚动已完成
        } catch (error) {
          console.error('获取问题详情失败:', error);
        }
      }

      model.value = null;
    }
  }
);

watch(
  () => questionId.value,
  async (newVal) => {
    if (newVal) {
      const res = await getQuestionDetailApi(newVal);
      questionList.value = res.data;
      curQuestion.value = questionList.value![0];
      drawerVisible.value = true;
      questionId.value = '';
    }
  }
);

// 检查元素是否在视口内
const isInViewport = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};

// 关闭浮动内容
const closeFloating = () => {
  // 立即隐藏浮动内容，避免过渡动画导致位置变化
  if (floatingElement.value) {
    // 先将元素设置为不可见，然后再改变状态
    floatingElement.value.style.visibility = 'hidden';
    // 使用 setTimeout 确保视觉上立即隐藏，然后再改变状态
    setTimeout(() => {
      floatingVisible.value = false;
      // 重置可见性，以便下次显示
      if (floatingElement.value) {
        floatingElement.value.style.visibility = '';
      }
    }, 0);
  } else {
    floatingVisible.value = false;
  }
};

// 点击事件处理函数 - 使用捕获阶段
const handleDocumentClick = (event: MouseEvent) => {
  // 如果悬浮内容未打开，不需要处理
  if (!floatingVisible.value) return;

  // 检查点击是否在浮动元素内
  const isClickInFloating = floatingElement.value?.contains(event.target as Node);

  // 如果点击不在浮动元素内，则关闭浮动内容
  if (!isClickInFloating) {
    closeFloating();
  }
};

const showDrawer = async (question: any) => {
  // 设置当前问题并显示抽屉
  curQuestion.value = question;
  drawerVisible.value = true;
};

const goToDetail = (code: string, type: KlgType) => {
  if (type == KlgType.knowledge) {
    const { href } = router.resolve({
      path: '/klgdetail',
      query: {
        klgCode: code
      }
    });
    window.open(href, '_blank');
  } else if (type == KlgType.area) {
    // const { href } = router.resolve({
    //   path: '/areadetail',
    //   query: {
    //     areaCode: code
    //   }
    // });
    const { href } = router.resolve({
      path: '/area',
      query: {
        areaCode: code
      }
    });
    window.open(href, '_blank');
  }
};

// 为抽屉添加点击事件监听
const addDrawerClickListener = () => {
  const drawerElements = document.querySelectorAll('.el-drawer');
  drawerElements.forEach((drawer) => {
    drawer.addEventListener('click', (event) => {
      // 检查点击是否在浮动元素内
      const isClickInFloating = floatingElement.value?.contains(event.target as Node);

      // 如果悬浮内容打开，且点击不在浮动元素内，则关闭悬浮内容
      if (floatingVisible.value && !isClickInFloating) {
        closeFloating();
      }
    });
  });
};

onMounted(() => {
  // 添加全局点击事件监听 - 使用捕获阶段
  document.addEventListener('click', handleDocumentClick, true);

  // 使用MutationObserver监听DOM变化，当抽屉被添加到DOM时添加点击事件监听
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        // 检查是否有新的抽屉元素被添加
        setTimeout(addDrawerClickListener, 100);
      }
    });
  });

  // 开始观察document.body的子节点变化
  observer.observe(document.body, { childList: true, subtree: true });

  // 初始化时检查是否有抽屉元素
  addDrawerClickListener();
});

onUnmounted(() => {
  // 移除事件监听器，注意第三个参数需要与添加时一致
  document.removeEventListener('click', handleDocumentClick, true);

  // 移除所有抽屉上的点击事件监听器
  const drawerElements = document.querySelectorAll('.el-drawer');
  drawerElements.forEach((drawer) => {
    // 使用克隆节点替换原节点，移除所有事件监听器
    const newDrawer = drawer.cloneNode(true);
    if (drawer.parentNode) {
      drawer.parentNode.replaceChild(newDrawer, drawer);
    }
  });
});
</script>

<style scoped lang="less">
.warpper {
  &:deep(.el-drawer__header) {
    padding: 10px 10px 10px 10px;
    margin: 0;
    border-bottom: 1px solid #dddddd;
    color: #333333;
    font-family:
      '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
    font-size: 16px;
    font-weight: 700;
  }

  &:deep(.el-drawer__body) {
    padding: 0;
  }

  .drawer-body {
    height: 100%;
    color: #333333;
    display: flex;
    flex-direction: column;
    font-family:
      '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
    font-size: 12px;
    font-weight: 400;
    padding-left: 10px;
    padding-right: 10px;

    .question {
      display: flex;
      flex-direction: column;
      align-items: left;

      .title {
        margin-top: 10px;
        display: flex;
        justify-content: left;
        font-size: 12px;

        .creator-name {
          font-weight: 700;
          margin-right: 10px;
        }
      }

      .description {
        margin-top: 10px;
        font-size: 14px;
        white-space: nowrap; /* 防止内容换行 */
        display: flex; /* 使用flex布局 */
        flex-wrap: nowrap; /* 禁止换行 */
        align-items: center; /* 垂直居中 */

        .key-words {
          font-weight: 700;
          display: inline-block; /* 内联块级元素 */
          :deep(p) {
            display: inline;
            margin: 0;
            padding: 0;
          }
        }

        .question-type {
          display: inline-block; /* 内联块级元素 */
          margin-left: 0; /* 移除左边距 */
        }

        span {
          display: inline-block; /* 确保所有span都是内联块级显示 */
        }
      }

      .bottom {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        .date {
          font-size: 12px;
          color: '#999999';
        }

        .operate {
          display: flex;
          flex-direction: row;

          .click-delete {
            color: var(--color-deep);
            &:hover {
              font-weight: 700;
              cursor: pointer;
            }
            margin: 0 10px;
          }

          .click-answer {
            color: var(--color-theme-project);
            &:hover {
              font-weight: 700;
              cursor: pointer;
            }
          }
        }
      }
    }
    .my-answer {
      .divider {
        margin: 22px 0 20px 0;
      }

      .title {
        margin-left: 20px;

        .name {
          margin-right: 10px;
          font-weight: 700;
        }
      }

      .add-klg {
        padding-left: 20px;
        margin-top: 10px;
      }

      .klg-list {
        padding-left: 20px;
        margin-top: 10px;
      }

      .description {
        padding-left: 20px;
        margin-top: 10px;
        margin-bottom: 10px;
      }

      .bottom {
        display: flex;
        flex-direction: row-reverse;
      }
    }
    .all-answer {
      .answer-list {
        &:deep(.el-card__body) {
          padding-left: 10px;
        }

        .answer-card {
          margin: 10px 0px 10px 20px;
          background-color: rgba(214, 233, 246, 0.996078431372549);

          .title {
            .name {
              font-weight: 700;
            }
          }

          .klg-list {
            margin-top: 10px;
          }

          .description {
            font-size: 16px;
            margin-top: 10px;
            margin-bottom: 10px;
          }

          .bottom {
            .date {
              color: '#999999';
            }
          }
        }
      }
    }

    .klg-item {
      display: inline;
      &:hover {
        cursor: pointer;
      }

      .klg-tag {
        border-color: #1661ab;
        border-radius: 0;
        margin: 0 8px 3px 0px;
        background-color: white;
        border-radius: 3px;
        .klg-tag-text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 20ch;
          color: #1661ab;
          font-size: 12px;
          font-family: '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
          font-weight: 400;
        }
      }
    }
    .button {
      width: 90px;
      height: 27px;
      font-size: 12px;
      font-family: '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
      font-weight: 400;
      font-variant: small-caps;
      color: white;
      background-color: var(--color-theme-project);
    }
  }
}
.questionPop {
  z-index: 9999;
  position: fixed;
  /* 确保元素在隐藏时不会移动位置 */
  &[style*='visibility: hidden'] {
    opacity: 0;
    transition: opacity 0.01s;
  }
}

.floating-content {
  padding: 8px 12px;
  background: var(--color-theme-project);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family:
    '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 14px;
  font-weight: 400;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  max-width: 100%;
  overflow: hidden;
  min-width: 150px;
  animation: fadeIn 0.2s ease-in-out;

  .floating-content-item {
    padding: 0;
    transition: all 0.2s ease;
    max-width: 400px;
    max-height: 50px;
    &:hover {
      font-weight: 700;
      cursor: pointer;
      transform: translateX(2px);
    }
    .keyword-container {
      display: inline-flex;
      align-items: center;
      white-space: nowrap;
      max-width: 320px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-enter-active {
  transition: all 0.2s ease-in-out;
}

.scale-leave-active {
  /* 离开时使用更短的过渡时间，避免位置变化 */
  transition: all 0.05s ease-in-out;
  /* 确保元素在离开时不会改变位置 */
  position: absolute;
}

.scale-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.scale-leave-to {
  opacity: 0;
  /* 离开时不使用缩放效果，避免位置变化 */
  transform: scale(1);
}
</style>
