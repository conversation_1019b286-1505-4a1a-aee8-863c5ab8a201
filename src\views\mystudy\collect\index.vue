<template>
  <div class="collect-container">
    <div>
      <div class="collect-header">
        <span class="question-title" @click="$router.push('/userspace')">
          <img style="height: 10px; width: 10px" src="@/assets/images/myStudy/back.svg" />
          返回个人空间
        </span>
      </div>
      <div style="display: flex; justify-content: space-between; margin-bottom: 15px">
        <div style="align-items: center; display: flex; font-size: 18px; font-weight: bold">
          收藏夹
        </div>
        <div class="collect">
          <el-input
            v-model="input"
            class="w-50 m-2"
            placeholder="请输入问题"
            :prefix-icon="Search"
            @keyup.enter="searchFn"
            clearable
          />
        </div>
      </div>
    </div>
    <div class="collectData">
      <template v-if="array.length > 0"
        ><template v-for="(item, index) in array.slice((currentPage - 1) * 10, currentPage * 10)">
          <el-divider></el-divider>
          <div @click="openDetail(item)" class="collect-item">
            <span style="margin-right: 10px">
              {{ index + (currentPage - 1) * 10 + 1 }}
            </span>
            <span
              class="my-stem"
              v-html="transToIcon(array[index + (currentPage - 1) * 10].stem)"
              style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 1070px"
            ></span>
          </div> </template
      ></template>
<!--      <template v-else><span class="collect-item">暂无数据</span></template>-->
      <div v-else class="lc-empty"><el-empty description="暂无收藏题目" /></div>
    </div>
    <div class="bottom" v-if="array.length > 0">
      <span>共{{ total }}条</span>
      <el-pagination
        layout="prev, pager, next"
        :total="total"
        background
        @current-change="getMore()"
        v-model:current-page="currentPage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import { getCollectDataApi, getCollectListApi } from '@/apis/collect';
import type { ExamData } from '@/types/exam';
import { useIdListStore } from '@/stores/idListStore';
import { transToIcon } from '@/utils/function';

const idListStore = useIdListStore();
const array = ref<exerciseInfo[]>([]);
const currentPage = ref(1);
const router = useRouter();
const route = useRoute();
const total = ref(0);

const input = ref('');
const oldInput = ref('');

const getMore = async () => {
  if (array.value[(currentPage.value - 1) * 10]) return;
  const res = await getCollectListApi({
    current: currentPage.value,
    keyword: oldInput.value
  });
  array.value = res.data.exerciseStemPage.records;
};

interface exerciseInfo {
  exerciseId: string;
  stem: string; //题干
}

onMounted(() => {
  getCollectListApi({
    current: currentPage.value
  }).then((res) => {
    total.value = res.data.exerciseStemPage.total;
    array.value = res.data.exerciseStemPage.records;
    idListStore.setIdList(res.data.exerciseIdList);
  });

  //array.value.splice(0, res.data.records.length, ...res.data.records);
});

watch(
  () => route.fullPath,
  (newPath) => {
    getCollectListApi({
      current: currentPage.value
    }).then((res) => {
      total.value = res.data.exerciseStemPage.total;
      array.value = res.data.exerciseStemPage.records;
      idListStore.setIdList(res.data.exerciseIdList);
    });
  }
);

const searchFn = async () => {
  if (input.value == oldInput.value) return;
  oldInput.value = input.value;
  currentPage.value = 1;
  const res = await getCollectListApi({
    current: currentPage.value,
    keyword: input.value
  });
  total.value = res.data.exerciseStemPage.total;
  array.value = res.data.exerciseStemPage.records;
};

const openDetail = async (item: any) => {
  router.push({
    path: '/mystudy/detail',
    query: { exerciseId: item.exerciseId }
  });
};
</script>

<style lang="less" scoped>
@titlefontsize: 18px;
@titlefontfamily: '黑体';
@headerfontfamily: '华文细黑';
@headerfontsize: 14px;
@margin: 10px;
@bgclor: #eee;

.my-stem {
  :deep(p) {
    display: inline;
  }
}
.fontstyle1 {
  font-size: 14px;
  margin-right: 90px;
}

.fontstyle2 {
  font-size: 12px;
}

.fontstyle3 {
  font-size: 16px;
  margin-right: 45px;
  font-weight: 700;
}

.fontstyle4 {
  font-size: 14px;
  font-weight: 700;
}

.collect-container {
  // 重写element样式
  --el-color-primary: var(--color-theme-project);

  display: flex;
  flex-direction: column;
  width: 1090px;
  padding-left: 2 * @margin;
  padding-top: 13px;

  .collect-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .question-title {
      font-family: @titlefontfamily;
      font-size: @headerfontsize;
      color: var(--color-theme-project);
      &:hover {
        font-weight: 700;
        cursor: pointer;
      }
    }
  }
  .collect {
    width: 225px;
    margin-right: 0px;
  }
  .collectData {
    .collect-item {
      color: var(--color-black);
      cursor: pointer;
      font-size: 14px;
      height: 46px; // 设置每行的高度为46px
      line-height: 46px;
      //width: 1070px;
    }
    .el-divider--horizontal {
      margin: 1px 0;
    }
  }

  .question-middle {
    font-size: 12px;
  }

  .question-content {
    margin-top: 18px;
    margin-bottom: 44px;
    width: 100%;

    .question-content-item {
      width: 100%;
      height: 69px;
      box-shadow: 0px 5px 5px #eee;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .question-content-item-left {
        padding-left: 25px;
        width: 75%;

        .question-content-item-top {
          margin-bottom: 5px;
          display: flex;
          flex-direction: row;

          .question-content-item-top-left {
            .fontstyle1();
            width: 40%;
          }

          .question-content-item-top-right {
            .fontstyle2();
            flex: 1;
          }
        }

        .question-content-item-bottom {
          display: flex;
          flex-direction: row;

          .question-content-item-bottom-item {
            .fontstyle3();
            width: 30%;
          }

          .question-content-item-bottom-item2 {
            width: 20%;
          }
        }
      }

      .question-content-item-right {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-right: 20px;
        justify-content: flex-start;
        flex: 1;

        .question-content-item-img {
          margin-right: 5px;

          img {
            width: 74px;
            height: 49px;
          }
        }
      }
    }
  }
}

.question-bottom {
  display: flex;
  flex-direction: row;
  margin-top: 53px;
  margin-bottom: 53px;
}

.lc-empty {
  height: 670px;
}
.no-more {
  width: 100%;
  display: flex;
  text-align: center;
  justify-content: center;
  color: var(--color-theme-project);
  font-family: var(--text-family);
  font-weight: 400;
  font-size: 14px;
  height: 20px;
  line-height: 20px;
  margin: 10px 0;
}
.down-more {
  width: 100%;
  display: flex;
  justify-content: center;
  color: var(--color-theme-project);
  cursor: pointer;
}

.down-more:hover {
  font-weight: bolder;
}

.bottom {
  display: flex;
  justify-content: space-between;
  margin-top: 1px;
  margin-bottom: 20px;
  background-color: rgba(220, 223, 230, 0.996078431372549);
  box-sizing: border-box;
  height: 46px;
  padding: 0 20px;
  align-items: center;
  font-size: 14px;
}
:deep(.el-pagination.is-background) .el-pager li.is-active {
  background-color: #ccc !important; // 设置当前页按钮的背景颜色为白色
  border-color: #ffffff !important; // 设置边框颜色为白色
}
</style>
