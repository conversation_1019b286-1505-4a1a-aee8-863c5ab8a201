import { defineStore } from 'pinia';
import { startsWith, endsWith } from 'lodash-es';
import { escape } from 'lodash-es';

export const useWordStore = defineStore('word', {
  state: () => ({
    regString: '',
    videoStringList: [] as Array<{ list: Array<string>; start: number }>,
    originalVideoStringList: [] as Array<{ list: Array<string>; start: number }>,
    exerciseStringList: [] as Array<{ list: Array<string>; start: number }>,
    originalExerciseStringList: [] as Array<{ list: Array<string>; start: number }>,
    draftStringList: [] as string[],
    originalDraftStringList: [] as string[],
    unitList: [] as Array<{
      tagName: string;
      children: any[];
      index: number;
      qids: number[];
      highlight: boolean;
      stringIndex: number;
    }>,
    uncommonWordMap: new Map<string, string>()
  }),
  getters: {
    getOriginalDraftString(): string {
      return this.originalDraftStringList.join('');
    },
    getDraftString(): string {
      let index = 0;
      const stringList = [];
      let tempQid = null;
      let tempContent = null;
      let tempClassName = null;
      let unMatchTempText = '';
      const regex = /span[^>]*data-qid="([^"]+)"\s+class="([^"]+)"[^>]*>(.*?)<\/span>/;
      for (let i = 0; i < this.draftStringList.length; i++) {
        const match = this.draftStringList[i].match(regex);
        if (match) {
          const qid = match[1];
          const className = match[2];
          const content = match[3];
          if (unMatchTempText.length > 0) {
            stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
            unMatchTempText = '';
          }
          if (tempQid == null) {
            tempQid = qid;
            tempContent = content;
            tempClassName = className;
          } else if (tempQid == qid && tempClassName == className) {
            tempContent += content;
          } else {
            stringList.push(
              `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
            );
            tempQid = qid;
            tempContent = content;
            tempClassName = className;
          }
        } else {
          if (tempQid != null) {
            stringList.push(
              `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
            );
          }
          if (startsWith(this.draftStringList[i], '<') && endsWith(this.draftStringList[i], '>')) {
            if (unMatchTempText.length > 0) {
              stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
              unMatchTempText = '';
            }
            stringList.push(this.draftStringList[i]);
          } else {
            unMatchTempText += this.draftStringList[i];
          }
          tempQid = null;
          tempContent = null;
          tempClassName = null;
        }
      }
      if (tempQid != null) {
        stringList.push(
          `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
        );
      }
      if (unMatchTempText.length > 0) {
        stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
      }
      return stringList.join('');
    },
    getOriginalVideoStringList(): string[] {
      return this.originalVideoStringList.map((item) => {
        return item.list.join('');
      });
    },
    getVideoStringList(): string[] {
      let index = 0;
      return this.videoStringList.map((item) => {
        const stringList = [];
        let tempQid = null;
        let tempContent = null;
        let tempClassName = null;
        let unMatchTempText = '';
        const regex = /span[^>]*data-qid="([^"]+)"\s+class="([^"]+)"[^>]*>(.*?)<\/span>/;
        for (let i = 0; i < item.list.length; i++) {
          const match = item.list[i].match(regex);
          if (match) {
            const qid = match[1];
            const className = match[2];
            const content = match[3];
            if (unMatchTempText.length > 0) {
              stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
              unMatchTempText = '';
            }
            if (tempQid == null) {
              tempQid = qid;
              tempContent = content;
              tempClassName = className;
            } else if (tempQid == qid && tempClassName == className) {
              tempContent += content;
              tempClassName = className;
            } else {
              stringList.push(
                `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
              );
              tempQid = qid;
              tempContent = content;
              tempClassName = className;
            }
          } else {
            if (tempQid != null) {
              stringList.push(
                `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
              );
            }
            if (startsWith(item.list[i], '<') && endsWith(item.list[i], '>')) {
              if (unMatchTempText.length > 0) {
                stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
                unMatchTempText = '';
              }
              stringList.push(item.list[i]);
            } else {
              unMatchTempText += item.list[i];
            }
            tempQid = null;
            tempContent = null;
            tempClassName = null;
          }
        }
        if (tempQid != null) {
          stringList.push(
            `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
          );
        }
        if (unMatchTempText.length > 0) {
          stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
        }
        return stringList.join('');
      });
    },
    getOriginalExerciseStringList(): string[] {
      return this.originalExerciseStringList.map((item) => {
        return item.list.join('');
      });
    },
    getExerciseStringList(): string[] {
      let index = 0;
      return this.exerciseStringList.map((item) => {
        const stringList = [];
        let tempQid = null;
        let tempContent = null;
        let tempClassName = null;
        let unMatchTempText = '';
        const regex = /span[^>]*data-qid="([^"]+)"\s+class="([^"]+)"[^>]*>(.*?)<\/span>/;
        for (let i = 0; i < item.list.length; i++) {
          const match = item.list[i].match(regex);
          if (match) {
            const qid = match[1];
            const className = match[2];
            const content = match[3];
            if (unMatchTempText.length > 0) {
              stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
              unMatchTempText = '';
            }
            if (tempQid == null) {
              tempQid = qid;
              tempContent = content;
              tempClassName = className;
            } else if (tempQid == qid && tempClassName == className) {
              tempContent += content;
              tempClassName = className;
            } else {
              stringList.push(
                `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
              );
              tempQid = qid;
              tempContent = content;
              tempClassName = className;
            }
          } else {
            if (tempQid != null) {
              stringList.push(
                `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
              );
            }
            if (startsWith(item.list[i], '<') || endsWith(item.list[i], '>')) {
              if (unMatchTempText.length > 0) {
                stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
                unMatchTempText = '';
              }
              stringList.push(item.list[i]);
            } else {
              unMatchTempText += item.list[i];
            }
            tempQid = null;
            tempContent = null;
            tempClassName = null;
          }
        }
        if (tempQid != null) {
          stringList.push(
            `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
          );
        }
        if (unMatchTempText.length > 0) {
          stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
        }
        return stringList.join('');
      });
    }
  },

  actions: {
    buildLineWord(node: Node): string {
      let lineWordList: string[] = [];
      const dfs = (node: Node) => {
        if (node.nodeType == Node.TEXT_NODE) {
          const textContent = (node.textContent as string).trim();
          if (textContent?.length == 0) {
            return;
          }
          for (const char of textContent) {
            if (char != '\n') {
              lineWordList.push(escape(char));
            }
          }
        } else if (node.nodeType == Node.ELEMENT_NODE) {
          if ((node as HTMLElement).tagName == 'IMG') {
            lineWordList.push((node as HTMLElement).outerHTML);
          } else {
            if ((node as HTMLElement).closest("[class^='inline-equation']")) {
              const element = (node as HTMLElement).closest("[class^='inline-equation']");
              const latexCode = element?.getAttribute('latexCode') as string;
              lineWordList.push(`<script type="math/tex">${latexCode}</script>`);
            } else if ((node as HTMLElement).closest("[class^='equation']")) {
              const element = (node as HTMLElement).closest("[class^='equation']");
              const latexCode = element?.getAttribute('latexCode') as string;
              lineWordList.push(`<script type="math/tex; mode=display">${latexCode}</script>`);
            } else {
              for (const child of (node as HTMLElement).childNodes) {
                dfs(child);
              }
            }
          }
        }
      };
      for (const childNode of node.childNodes) {
        dfs(childNode);
      }
      return lineWordList.join('');
    }
  }
});
