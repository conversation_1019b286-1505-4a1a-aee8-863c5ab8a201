import { Plugin } from 'xgplayer';

const { POSITIONS } = Plugin;

export default function createMyPlugin(handler: Function) {
  return class widerPlugin extends Plugin {
    // 插件的名称，将作为插件实例的唯一key值
    static get pluginName() {
      return 'widerPlugin';
    }

    static get defaultConfig() {
      return {
        // 挂载在controls的右侧，如果不指定则默认挂载在播放器根节点上
        position: POSITIONS.CONTROLS_RIGHT
      };
    }

    constructor(args: any) {
      super(args);
    }

    beforePlayerInit() {
      // TODO 播放器调用start初始化播放源之前的逻辑
    }

    afterPlayerInit() {
      // TODO 播放器调用start初始化播放源之后的逻辑
    }

    afterCreate() {
      this.onClick = handler;
      // 对当前插件根节点绑定click事件
      this.bind('click', this.onClick);
      //TODO 插件实例化之后的一些逻辑
    }

    destroy() {
      this.unbind('click', this.onClick);
      // 播放器销毁的时候一些逻辑
    }

    render() {
      return `<xg-icon class="xgplayer-cssfullscreen" data-index="1">
    <div class="xgplayer-icon">
    <svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40" class="xg-get-cssfull">
  <path fill="#fff" transform="scale(1.3, 1.3)" class="path_full" d="M9,10v1a.9.9,0,0,1-1,1,.9.9,0,0,1-1-1V9A.9.9,0,0,1,8,8h2a.9.9,0,0,1,1,1,.9.9,0,0,1-1,1Zm6,4V13a1,1,0,0,1,2,0v2a.9.9,0,0,1-1,1H14a1,1,0,0,1,0-2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>
</svg><svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40" class="xg-exit-cssfull">
  <path fill="#fff" transform="scale(1.3, 1.3)" d="M9,10V9a.9.9,0,0,1,1-1,.9.9,0,0,1,1,1v2a.9.9,0,0,1-1,1H8a.9.9,0,0,1-1-1,.9.9,0,0,1,1-1Zm6,4v1a1,1,0,0,1-2,0V13a.9.9,0,0,1,1-1h2a1,1,0,0,1,0,2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>
</svg></div>
     <div class="xg-tips  " lang-key="CSSFULLSCREEN_TIPS">
    Cssfullscreen
    </div>
    </xg-icon>`;
    }
  };
}
