import { http } from '@/apis';
import { PrjForm, PrjType } from '@/types/project';
import { GoodsType } from '@/types/goods';

//获取项目介绍详情接口参数
interface prjIntroduceParams {
  /**
   * 项目唯一标识
   */
  spuId: string;
  skuId?: string;
}
interface quesDetailParams {
  spuId: string;
  chapterId: number;
  keyword?: string;
  self: boolean;
}

interface priceList {
  skuId: string;
  cardType: number;
  subtotal: number;
  discount: number;
  actualPaidAmount: number;
  studyTime: number;
}
// 获取Vip商品
// 获取Vip商品
export function getVipIntroduceApi(data: prjIntroduceParams) {
  return http.post('/vip-goods/query', data);
}
//获取项目介绍页 新接口！！！
export function getPrjIntroduceApi(data: prjIntroduceParams) {
  return http.post<{
    studyInstructions: string;
    klgCount: number;
    prjType?: PrjType;
    latestChapterId?: number;
    priceList?: priceList[]; //除了会员商品以外数组长度为1 获取sku的信息
    chapterList: any[];
    coverPic: string;
    description: string;
    title: string;
    editorName: string;
    goodsType: GoodsType; //0 项目商品 1 会员商品
    prjForm?: PrjForm; //1 视频 2 文稿

    skuList: any[]; //---？----
    buyStatus: 1; //---？----
    spuId: string; //---？----
    userCoverPic: string; //---？----
    graspKlg: number; //---？----
    learnedTime: number; //----？----
    skuId: string; //---？----
    learned: number; //---？----
    userName: string; //---？----

    validDays?: number; //账号剩余有效天数
    learnedDays?: string; //已学习天数
    learnedKlgCount?: number; //已学习知识点数
    masteredKlgCount?: number; //已掌握知识点数
    targetKnowledgeCount?: number; //目标知识点数/目标领域数
    questionCount?: number; //问题总数
    learnedQuestionCount?: number; //已学习问题数
    editorPic: string;
    startTime: string; //有效期开始时间
    expirationTime: string; //有效期过期时间
    purpose?: string; //除会员商品外
    hasPermission: number; //是否购买
  }>('/project-goods/introduce', data);
}
interface questionItem {
  questionId: number;
  keyword: string;
  questionType: string;
  questionWeight: string;
  questionNecessity: string;
  learned: boolean;
}
interface questionParams {
  spuId: string;
  chapterId: number;
  keyword: string;
  self: boolean;
  hideLearned: boolean;
}
//获取问题列表接口 新
export function getQuestionListApi(params: questionParams) {
  return http.get<
    Array<{
      questionId: number;
      keyword: string;
      questionType: string;
      questionWeight: string;
      questionNecessity: string;
      learned: boolean;
    }>
  >('/project-goods/introduce/question/query', params);
}

export function getOrderInfoApi(skuId: string) {
  return http.get<{}>('/project-goods/introduce/order/query', { skuId });
}

//获取每一章节的问题列表接口
export function getQuesDetailApi(params: quesDetailParams) {
  return http.get<{
    list: any[];
  }>('/project-goods/introduce/question/query', params);
}

// 获取项目学习进程接口参数
interface studyProcessParams extends prjIntroduceParams {}
// 获取项目学习进程接口
export function getStudyProcess(params: studyProcessParams) {
  return http.get<{
    list: any[];
  }>('/project-goods/getStudyProcess', params);
}

// 获取用户学习计划接口
export function getScheduleApi(spuId: string) {
  return http.get<{
    startTime: string;
    endTime: 'string';
    learnDaysNum: number;
    learnWeekPlan: null;
  }>('/learning-schedule/getSchedule', { spuId });
}

// 编辑学习计划接口参数
interface setScheduleParams {
  spuId: string;
  learnWeekPlan: string;
}
// 编辑学习计划接口
export function setScheduleApi(params: setScheduleParams) {
  return http.get('/learning-schedule/SetSchedule', params);
}

/**waq */
//获取收藏夹title接口参数
interface getCollectDataParams {
  limit: number;
  current: number;
  spuId?: string;
}

export function getExamRelatedApi(params: getCollectDataParams) {
  return http.get<{
    current: number;
    limit: number;
    total: number;
    records: any[];
  }>('knowledgeExam/getAssessmentRelated', params);
}
