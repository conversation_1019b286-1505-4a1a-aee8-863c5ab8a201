<template>
  <div class="mode">
    <el-switch v-model="mode" @change="handleChangeMode" />
    <span>{{ mode ? '提问模式' : '阅读模式' }}</span>
  </div>
  <div class="content">
    <div class="error-top-content" id="htmlContent">
      <div class="content-question">
        {{ data.order }}.
        <div v-html="examData.examTitle"></div>
      </div>
      <div v-if="data.examType == ExamType.choice">
        <el-form-item label="">
          <el-radio-group v-model="data.myAnswer" class="selection-style">
            <el-radio
              :label="indexIntoAlpha(index)"
              v-for="(selection, index) in examData.examChoices"
              :key="index"
              disabled
            >
              <span>{{ indexIntoAlpha(index) }}</span>
              <!-- {{ selection }} -->
              <span v-html="selection" class="option"></span>
            </el-radio>
          </el-radio-group> </el-form-item
        >,
      </div>
      <div v-if="data.examType == ExamType.judgment">
        <el-form-item label="">
          <el-radio-group v-model="data.myAnswer" class="selection-style">
            <el-radio label="1" disabled><span class="iconfont icon-duigou1"></span></el-radio>
            <el-radio label="2" disabled><span class="iconfont icon-cuowu"></span></el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div v-if="data.examType == ExamType.fillBlank" class="replycontent-style">
        <p class="replycontent-title">回答:</p>
        <!-- <ckeditor :editor="editor" v-model="item.myAnswer"></ckeditor> -->
        <div>{{ data.myAnswer }}</div>
      </div>
      <div v-if="data.examType == ExamType.shortAnswer" class="replycontent-style">
        <p class="replycontent-title">回答:</p>
        <!-- <ckeditor :editor="editor" v-model="item.myAnswer"></ckeditor> -->
        <div>{{ data.myAnswer }}</div>
      </div>
      <div class="explanation-wrap">
        <div class="detail">
          <div class="choice">
            <div>
              答案：
              <div v-html="examData.examAnswer"></div>
            </div>
          </div>
          <div class="description">
            说明：
            <div v-html="examData.examExplanation"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="error-top-list">
      <p>问题列表</p>
      <div
        v-for="question in allQuestion"
        :key="question.questionId"
        class="questionliststyle"
        @click="handleQuestionList(question.questionId)"
      >
        <!-- <span>{{ questionItem.keyword }} {{ questionItem.questionType }}</span> -->
        <div class="title-line">
          "
          <span v-html="question.keyword"></span>
          "
          <span v-html="question.questionType" v-if="question.questionType != '开放性问题'"></span>
          <span v-html="question.questionDescription" v-else></span>
        </div>
        <div
          @click.stop="handleDeleteQuestion(question.questionId)"
          v-if="question.canDelete"
          style="cursor: pointer"
        >
          ×
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { documentData, questionData } from '@/types/data';
import indexIntoAlpha from '@/utils/indexIntoAlpha';
import type { FormRules } from 'element-plus';
import { getExamContentApi, saveExamQuestionApi, deleteQuestionApi } from '@/apis/learning';
import { handleLineWord, updataDom, markWord, unmarkWord } from '@/utils/lineWord';
import { ExamType } from '@/types/exam';
import { useLearningStore } from '@/stores/learning';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { emitter } from '@/utils/emitter';

const drawerControllerStore = useDrawerControllerStore();
const learningStore = useLearningStore();
const examData = ref({
  id: 0,
  examType: '',
  examTitle: '',
  examChoices: '',
  examAnswer: '',
  examExplanation: ''
}); //从后端拿到的数据
const showqList = ref(false);
const allQuestion = ref([]);
const qList = ref([]); //span关联的问题
const { mode } = storeToRefs(drawerControllerStore); // false为read模式 或者true为 question 模式
// 从父组件获取题目具体信息
const props = defineProps(['questionObj']);
// let data: documentData = props.tiwenObj;
let data = ref<documentData>({
  projectId: '',
  examId: 0, //测试题目id
  sectionId: 0, //小节id
  order: 0, //题目序号
  sectionTitle: '', //小节名称
  examType: 0, //1.填空 2选择 3判断 4问答
  examTitle: '', //题目
  examChoices: [''], //选项
  examAnswer: '', //答案
  myAnswer: '',
  examExplanation: '', //解释说明
  questionList: [
    {
      questionId: 0, //问题自增id
      associatedWords: '', //关联文本内容
      keyword: '', //关键字内容
      questionType: '', //问题类型，是什么、为什么、怎么做
      questionNecessity: 0, //问题必要性 1必须问题，2参考问题
      creatorId: '', //创建者id
      creatorName: '', //创建者名字
      createTime: '', //创建时间
      explanation: '',
      answerList: [
        {
          answerId: 0, //回答自增id
          answerKlgs: [
            {
              klgId: 0,
              klgTitle: ''
            }
          ], //知识点名称列表
          answerExplanation: '', //解释
          createTime: '', //回答时间
          creatorName: '', //回答者名称
          modifiedTime: '' //修改时间
        }
      ]
    }
  ]
});

watch(
  () => props.questionObj,
  async (newVal) => {
    console.log(newVal);
    data.value = newVal;
    const res = await getExamContentApi(data.value.examId);
    learningStore.uniqueCode = res.data.content.uniqueCode;
    learningStore.chapterId = res.data.content.chapterId;
    learningStore.contentId = res.data.content.contentId;
    examData.value = res.data.content;
    allQuestion.value = res.data.questionList;
    nextTick(() => {
      mode.value = false;
      markWord();
    });
  },
  { deep: true, immediate: true }
);

const closeAnswerDialog = () => {
  showqList.value = false;
};
const handleDeleteQuestion = async (questionId) => {
  // console.log(1111111111111)
  const res = await deleteQuestionApi(questionId);
  console.log(res);
  // handleQuestionList(curChapterId.value);
  emitter.emit('refresh');
};

const handleSubmitQuestion = (newQuestion) => {
  // console.log(data.value)
  // console.log(examData.value)
  const params = {
    prjId: data.value.prjId,
    chapterId: data.value.sectionId,
    contentId: data.value.examId,
    keyword: newQuestion.qContent,
    questionType: newQuestion.qType,
    associatedWords: newQuestion.relatedText,
    questionDescription: newQuestion.questionDescription
  };
  saveExamQuestionApi(params).then((res) => {
    if (res.success) {
      // refreshTextContent(res.data.prText)
      // emits('refresh', prjSectionInfo.value.chapterId)
      ElMessage.success('保存问题成功');
      console.log(res);
      allQuestion.value = res.data.questionList;
      examData.value = res.data.result;
    } else {
      ElMessage.error(res.message);
    }
  });
};

const handleChangeMode = () => {
  if (!mode.value) {
    markWord(); // 阅读模式
  } else {
    unmarkWord(); // 提问模式
  }
};

const handleQuestionList = (id) => {
  // getQuestionList(id).then((res) => {
  //   // questionDialogRef.value.showDialog(questionList, 2);
  //   qList.value = res;
  //   showqList.value = true;
  // });
  drawerControllerStore.questionId = id;
};

onMounted(() => {
  nextTick(() => {
    markWord();
  });
  emitter.emit('initHandler', async () => {
    const res = await getExamContentApi(data.value.examId);
    learningStore.uniqueCode = res.data.content.uniqueCode;
    learningStore.chapterId = res.data.content.chapterId;
    learningStore.contentId = res.data.content.contentId;
    examData.value = res.data.content;
    allQuestion.value = res.data.questionList;
  });
});
// 不使用以下代码
// 查看问题详情
// const dialogDetailVisible = ref(false);
// // 转换问题类型
// const questionTypeMap = new Map([
//   [1, '是什么?'],
//   [2, '为什么？'],
//   [3, '怎么做？'],
//   [4, '自定义类型']
// ]);
// // 当前点开的问题
// let currentQuestion = ref<questionData>({
//   questionId: 0, //问题自增id
//   associatedWords: '', //关联文本内容
//   keyword: '', //关键字内容
//   questionType: '', //问题类型，是什么、为什么、怎么做,自定义
//   questionNecessity: 0, //问题必要性 1必须问题，2参考问题
//   creatorId: '', //创建者id
//   creatorName: '', //创建者名字
//   createTime: '', //创建时间
//   explanation: '',
//   answerList: [
//     {
//       answerId: 0, //回答自增id
//       answerKlgs: [
//         {
//           klgId: 0,
//           klgTitle: ''
//         }
//       ], //知识点名称列表
//       answerExplanation: '', //解释
//       createTime: '', //回答时间
//       creatorName: '', //回答者名称
//       modifiedTime: '' //修改时间
//     }
//   ]
// });

// const questionDetail = (item: questionData) => {
//   dialogDetailVisible.value = true;
//   console.log('当前问题详情', currentQuestion.value.associatedWords);
//   currentQuestion.value = item;
// };

// // 划词弹窗
// const buildupQuestionVisible = ref(false); //提问弹窗
// const { handleMouseSelect, form } = useDelineateWords();
// interface RuleForm {
//   referenceContent: string;
//   keyword: string;
//   type: number;
// }
// const ruleFormRef = ref<any>();
// const rules = ref<FormRules<RuleForm>>({
//   keyword: [
//     {
//       required: true,
//       message: '请输入关键字'
//     }
//   ],
//   type: [
//     {
//       required: true,
//       message: '请选择问题类型'
//     }
//   ]
// });

// // 高亮
// const lightword = ref('');
// const showlightWord = ref('');
// watch(lightword, (val) => {
//   showlightWord.value = val
//     .replace(/<p>/g, '')
//     .replace(/<\/p>/g, '')
//     .replace(/[.[*?+;-^$|(){}/]|\]|\\/g, '\\$&');
//   const pattern = new RegExp(`(${showlightWord.value})`, 'gi');
//   data.value.explanation = data.value.explanation.replace(
//     pattern,
//     '<span class="highlight">$1</span>'
//   );
//   let showlightWord2;
//   if (lightword.value.indexOf('math/tex') != -1) {
//     showlightWord2 = val
//       .replace('math/tex', 'math/tex; mode=display')
//       .replace(/<p>/g, '')
//       .replace(/<\/p>/g, '')
//       .replace(/[.[*?+;-^$|(){}/]|\]|\\/g, '\\$&');
//     const pattern2 = new RegExp(`(${showlightWord2})`, 'gi');
//     data.value.explanation = data.value.explanation.replace(
//       pattern2,
//       '<span class="highlight">$1</span>'
//     );
//   }
// });

// // 划词
// const DelineateWords = async () => {
//   await handleMouseSelect();
//   buildupQuestionVisible.value = true;
// };
// // 提交问题
// const submitQuestion = () => {
//   ruleFormRef.value.validate((valid: any) => {
//     if (valid) {
//       const params = {
//         sectionId: data.value.sectionId,
//         assessmentId: data.value.assessmentId,
//         // FIXME: 还没对好
//         spuId: data.value.spuId,
//         associatedWords: form.value.referenceContent,
//         questionType: form.value.type,
//         keyword: form.value.keyword
//       };
//       addQuestionApi(params)
//         .then((res) => {
//           // @ts-ignore
//           if (res.code == 20000) {
//             buildupQuestionVisible.value = false;
//             lightword.value = form.value.keyword;
//           } else {
//             ElMessage({
//               // @ts-ignore
//               message: res.message,
//               type: 'warning'
//             });
//           }
//         })
//         .catch((error) => {
//           ElMessage({
//             message: error,
//             type: 'warning'
//           });
//         });
//     } else {
//       ElMessage({
//         message: '验证失败',
//         type: 'warning'
//       });
//     }
//   });
// };
</script>

<style lang="less" scoped>
// :deep(.highlight) {
//   color: var(--color-theme-project);
//   cursor: pointer;
// }
// :deep(.highlight:hover) {
//   font-weight: 700;
// }
.mode {
  margin-left: 10px;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  display: flex;
  // align-items: center;

  .el-switch {
    margin-right: 10px;
  }
}
.content {
  display: flex;

  .error-top-content {
    padding-right: 20px;
    width: 527px;
    margin-bottom: 20px;
    // height: auto;
    // margin-bottom: 100px;

    .content-question {
      font-size: 14px;
      font-weight: 700;
      color: #333333;
      margin-left: 74px;
      display: flex;
      flex-wrap: wrap;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .selection-style {
      display: flex;
      flex-direction: column;
      margin-left: 30px;
      align-items: flex-start;

      .option {
        color: #333333;
        cursor: text;
        user-select: text;
      }

      .el-radio {
        margin-top: 18px;

        span {
          margin-left: 32px;
          margin-right: 5px;
        }
      }
    }

    .explanation-wrap {
      width: 447px;
      min-height: 134px;
      border-radius: 5px;
      margin-left: 30px;
      padding: 11px 22px;
      font-size: 14px;
      white-space: pre-wrap;

      .detail {
        .choice {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        }

        .description {
          margin-top: 20px;
          // overflow: auto
        }
      }
    }
  }

  .error-top-list {
    width: 422px;
    background-color: rgba(242, 242, 242, 0.376);
    border-radius: 5px;
    padding: 15px 22px;

    // 这里使用固定高度后续可能出现问题
    // overflow-y: auto;
    min-height: 355px;

    p {
      font-size: 14px;
      font-weight: 700;
    }

    .questionliststyle {
      display: flex;
      flex-direction: row;
      // align-items:space-around;
      justify-content: space-between;
      margin-top: 17px;
      font-size: 14px;
      background-color: white;
    }

    .title-line {
      display: flex;
    }

    .title-line:hover {
      cursor: pointer;
      color: var(--color-theme-project);
      font-weight: 700;
    }

    // .questionliststyle:hover {
    //   cursor: pointer;
    //   color: var(--color-theme-project);
    //   font-weight: 700;
    // }
  }
}

.my-header {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-theme-project);
}

.dialog-content {
  white-space: pre-wrap;

  .dialog-content-top {
    width: 540px;
    min-height: 40px;
    background-color: #f2f2f2;
    border-radius: 2px;
    display: flex;
    align-items: center;
    padding-left: 15px;
    margin-bottom: 5px;
  }

  .dialog-content-middle {
    font-size: 12px;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .dialog-content-bottom {
    width: 540px;
    min-height: 222px;
    background-color: #f2f2f2;
    border-radius: 2px;
    padding: 12px 10px;

    .line {
      width: 517px;
      height: 2px;
      background-color: #ffff;
      margin: 11px auto;
    }

    .section-name {
      font-size: 12px;
      font-weight: 700;
      margin-right: 18px;
    }

    .section-item {
      margin-bottom: 8px;
      white-space: pre-wrap;
    }

    .section-time {
      font-size: 12px;
    }

    .section2 {
      padding-left: 25px;
      margin-bottom: 10px;
    }

    .section-klg {
      font-size: 14px;
      font-weight: 400;
      color: #005579;

      span {
        margin-right: 37px;
      }

      span:hover {
        font-weight: 700;
        pointer: cursor;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;

  .btn {
    width: 160px;
    height: 43px;
    margin-right: 21px;
  }
}
</style>
