<template>
  <div v-if="exerciseInfo.exerciseId">
    <div class="header-wrapper">
      <div class="left-header">
        <div class="title-row" style="display: flex; align-items: center; justify-content: space-between;">
          <div class="title">测试</div>
          <el-button class="ask-btn" type="info" size="small" plain @click="handleAskQuestion" style="border: unset; margin-left: 10px;" v-if="answerShow">
            <el-icon style="margin-right: 4px; border: unset;"><QuestionFilled /></el-icon>
            有看不懂的地方点我提问题
          </el-button>
        </div>
      </div>
    </div>
    <div class="qt-detail">
      <div class="main-content">
        <div class="title-wrapper">
          <div class="title" style="display: flex; align-items: center;">
            <span>{{ questionTypeText }}</span>
            <el-icon style="font-size: 22px; margin-left: 8px; cursor: pointer;" @click="addCollect">
              <StarFilled :style="{ color: isCollected ? '#FFD700' : '#C0C4CC' }" />
            </el-icon>
          </div>
        </div>
        <div><el-divider></el-divider></div>
        <div class="answer-check" v-if="answerShow">
          <div class="answer">
            <div v-if="isRight == 0" class="msg correct-color">
              <img
                style="width: 14px; height: 14px"
                src="@/assets/images/prjlearn/correct.png"
                alt=""
              />
              恭喜你，答对了！
            </div>
            <div v-if="isRight == 1" class="msg error-color">
              <img
                style="width: 14px; height: 14px"
                src="@/assets/images/prjlearn/error.png"
                alt=""
              />
              很遗憾，您的答案不正确。
            </div>
          </div>
        </div>
        <div class="content">
          <div class="content-question" v-html="currentTest.stem"></div>
          <div
            v-if="currentTest.type == ExerciseType.blank && !answerShow"
            class="replycontent-style"
          >
            <p class="replycontent-title">回答:</p>
            <ClassicEditor v-model="singleAnswer"></ClassicEditor>
          </div>
          <div v-if="currentTest.type == ExerciseType.single">
            <el-form-item label="">
              <el-radio-group v-model="singleAnswer" class="selection-style" :disabled="answerShow">
                <el-radio
                  :label="indexIntoAlpha(index)"
                  v-for="(selection, index) in currentTest.content"
                  :key="index"
                >
                  <span class="inline-label">
                    <span>{{ indexIntoAlpha(index) }}</span
                    ><span v-html="selection.text"></span> </span
                  ></el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div v-if="currentTest.type == ExerciseType.multi">
            <el-form-item label="">
              <el-checkbox-group v-model="multiAnswer" class="selection-style" :disabled="answerShow">
                <el-checkbox
                  :label="indexIntoAlpha(index)"
                  v-for="(selection, index) in currentTest.content"
                  :key="index"
                >
                  <span class="inline-label">
                    <span style="margin-right: 5px">{{ indexIntoAlpha(index) }}</span
                    ><span v-html="selection.text"></span>
                  </span>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div v-if="currentTest.type == ExerciseType.judge">
            <el-form-item>
              <el-radio-group v-model="singleAnswer" class="selection-style" :disabled="answerShow">
                <el-radio label="1"><span class="iconfont icon-duigou1"></span></el-radio>
                <el-radio label="0"><span class="iconfont icon-cuowu"></span></el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
      </div>
      <!-- 上一题--查看答案--下一题按钮 -->
      <div class="btns" style="margin-top: 10px">
        <CmpButton class="btn" type="primary" @click="checkAnswer" v-if="submitShow"
        >提交</CmpButton
        >

        <CmpButton class="btn" type="info" v-if="!isLastItem && submitShow" @click="toNext"
        >跳过</CmpButton
        >
      </div>
      <!-- 查看答案 -->
      <div v-if="answerShow" class="answer-section">
        <div class="answer-label">答案</div>
        <div class="answer-content">
          <template v-if="currentTest.type == ExerciseType.judge">
            {{
              currentTest.answer.replace(/<\/?span[^>]*>/g, '') == '1' ? '正确' : '错误'
            }}
          </template>
          <template v-else-if="currentTest.type == ExerciseType.multi || currentTest.type == ExerciseType.single">
            <span v-html="JSON.parse(currentTest.answer.replace(/<\/?span[^>]*>/g, '')).join(', ')"></span>
          </template>
          <template v-else>
            <span v-html="currentTest.answer"></span>
          </template>
        </div>
        <div class="explanation-label">解析说明</div>
        <div class="explanation-content" v-html="currentTest.explanation"></div>
      </div>
    </div>
    <AskQuestionDialog
      v-if="askDialogVisible"
      :exercise-info="currentTest"
      :klg-code="props.klgCode"
      :transmit-spuId="props.transmitSpuId"
      :transmit-chapterId="props.transmitChapterId"
      :transmit-uniqueCode="props.transmitUniqueCode"
      :type="3"
      @close="askDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import ClassicEditor from '@/components/editors/Veditor.vue';
import CmpButton from '@/components/CmpButton.vue';
import indexIntoAlpha from '@/utils/indexIntoAlpha';
import type { exerciseItem } from '@/types/exercise';
import { ExerciseType } from '@/types/exercise';
import { ElMessage } from 'element-plus';
import { StarFilled, QuestionFilled } from '@element-plus/icons-vue';
import { addFavoritesApi, cancelFavoritesApi } from '@/apis/collect';
import { saveExerciseRecordApi } from '@/apis/exercise';
import AskQuestionDialog from '../AskQuestionDialog.vue';

const props = defineProps(['exerciseInfo', 'isLastItem', 'klgCode', 'transmitSpuId', 'transmitChapterId', 'transmitUniqueCode']);
const emits = defineEmits(['toNext', 'collectChange']);

const currentTest = ref<exerciseItem>({
  exerciseId: '',
  type: 0, //1.单选 2多选 3填空 4判断
  stem: '', //题目
  content: [],
  answer: '', //答案
  explanation: '' //解释说明
});

const questionTypeText = computed(() => {
  switch (props.exerciseInfo.type) {
    case 1:
      return '单选题';
    case 2:
      return '多选题';
    case 3:
      return '填空题';
    case 4:
      return '判断题';
    default:
      return '全部题型';
  }
});

async function toNext() {
  emits('toNext');
}

// 移除重复的 onMounted，因为上面已经添加了

watch(
  () => props.exerciseInfo,
  (newVal) => {
    if (newVal) {
      currentTest.value = { ...newVal };
      // 更新收藏状态
      isCollected.value = newVal.collected || false;
      answerShow.value = false;
      submitShow.value = true;
      singleAnswer.value = '';
      multiAnswer.value = [];
    }
  }
);

const singleAnswer = ref(''); //填空题+单选题+判断题
const multiAnswer = ref<string[]>([]); //多选题

const isRight = ref(0); // 0表示显示正确，1表示显示错误，2表示都不显示
const answerShow = ref(false); //显示答案
const submitShow = ref(true); //显示提交按钮
const isCollected = ref(false);

// 初始化收藏状态
onMounted(() => {
  currentTest.value = props.exerciseInfo;
  // 从接口数据中获取收藏状态
  isCollected.value = props.exerciseInfo.collected || false;
});

const askDialogVisible = ref(false);

// 查看/提交答案
const checkAnswer = async () => {
  if (
    currentTest.value.type == ExerciseType.single ||
    currentTest.value.type == ExerciseType.judge
  ) {
    // 单选题或判断题：确保答案不能为空
    if (!singleAnswer.value) {
      ElMessage({
        message: '请回答此题',
        type: 'warning'
      });
      return;
    }
  } else if (currentTest.value.type == ExerciseType.multi) {
    // 多选题：确保至少选择一个选项
    if (multiAnswer.value.length == 0) {
      ElMessage({
        message: '请至少选择一个选项',
        type: 'warning'
      });
      return;
    }
  } else if (currentTest.value.type == ExerciseType.blank) {
    // 填空题：确保用户已输入答案
    if (!singleAnswer.value.trim()) {
      ElMessage({
        message: '请填写答案',
        type: 'warning'
      });
      return;
    }
  }

  let answerList: string[] = [];
  let lastAnswer = '';
  if (currentTest.value.type == ExerciseType.single) {
    answerList[0] = singleAnswer.value;
    lastAnswer = JSON.stringify(answerList);
  } else if (currentTest.value.type == ExerciseType.multi) {
    answerList = [...multiAnswer.value];
    lastAnswer = JSON.stringify(answerList);
  } else {
    lastAnswer = singleAnswer.value;
  }

  // 调用接口判断答案正确性
  try {
    //这里先这样写，后续要改
    const res = await saveExerciseRecordApi({
      exerciseId: currentTest.value.exerciseId,
      answer: lastAnswer,
      klgCode: props.klgCode
    });
    currentTest.value.answer = (res.data as any).correctAnswer;
    currentTest.value.explanation = (res.data as any).explanation;
    let result = (res.data as any).score;
    if (result) {
      isRight.value = 0;
    } else {
      isRight.value = 1;
    }
    if (currentTest.value.answer || currentTest.value.explanation) {
      answerShow.value = true;
      submitShow.value = false;
    }
  } catch (e) {
    ElMessage.error('提交答案失败');
  }
};

const addCollect = async () => {
  try {
    if (!isCollected.value) {
      const res = await addFavoritesApi(currentTest.value.exerciseId);
      if (res.success) {
        ElMessage({
          type: 'info',
          message: '已加入收藏夹'
        });
        isCollected.value = true;
        // 更新当前习题的收藏状态
        currentTest.value.collected = true;
        // 通知父组件收藏状态变化
        emits('collectChange', {
          exerciseId: currentTest.value.exerciseId,
          collected: true
        });
      } else {
        ElMessage({
          type: 'info',
          message: res.message || '系统问题，请稍后再试'
        });
      }
    } else {
      const res = await cancelFavoritesApi(currentTest.value.exerciseId);
      if (res.success) {
        ElMessage({
          type: 'info',
          message: '已取消收藏'
        });
        isCollected.value = false;
        // 更新当前习题的收藏状态
        currentTest.value.collected = false;
        // 通知父组件收藏状态变化
        emits('collectChange', {
          exerciseId: currentTest.value.exerciseId,
          collected: false
        });
      } else {
        ElMessage({
          type: 'info',
          message: res.message || '系统问题，请稍后再试'
        });
      }
    }
  } catch (e) {
    ElMessage.error('操作失败');
  }
};

function handleAskQuestion() {
  if (!answerShow.value) {
    ElMessage.warning('请先完成本题再提问');
    return;
  }
  askDialogVisible.value = true;
}
</script>

<style lang="less" scoped>
.header-wrapper,
.left-header {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
}
.left-header {
  .title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .title {
    font-size: 18px;
    font-weight: 600;
    margin: 10px 0px 10px 0;
  }
}
.qt-detail {
  //width: 100%;
  padding: 8px;
  background-color: #ffffff;
  //height: 600px;
  min-height: 450px;
  margin: 8px;


  border-width: 1px;
  border-style: solid;
  border-color: rgba(220, 223, 230, 1);
  border-radius: 8px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  .main-content {
    margin-top: 15px;
    width: 100%;
    background-color: #ffffff;

    .title-wrapper {
      display: flex;
      .title {
        width: 100%;
        height: 3px;
        line-height: 3px;
        font-size: 16px;
        font-weight: 400;
        padding-left: 10px;
        font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
      }
      .add-collect {
        font-size: 14px;
        width: 100px;
        color: var(--color-theme-project);
        display: flex;
        justify-content: center;
        text-align: center;
        align-items: center;
        font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
      }
    }
    .content {
      padding-left: 31px;
      padding-right: 31px;
      margin-top: 15px;
      .content-question {
        display: flex;
        flex-wrap: wrap;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 16px;
        font-weight: 400;
        font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
      }
      .selection-style {
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        align-items: flex-start;
        .inline-label {
          display: inline-flex;
          align-items: center;
          font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
          font-weight: 400;
          font-size: 15px;
        }
        .el-radio {
          width: 100%;
          margin-bottom: 5px;
          white-space: nowrap;
          span {
            margin-right: 5px;
          }
        }
        .el-checkbox {
          width: 100%;
          margin-bottom: 5px;
          white-space: nowrap;
        }
      }
      .replycontent-style {
        margin-top: 31px;
        margin-left: 32px;
        font-size: 14px;
        font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
        .replycontent-title {
          margin-bottom: 10px;
        font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
        }
      }
    }
  }
  .btns {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    .btn {
      width: 80px;
      height: 32px;
      margin-left: 40px;
      border-radius: 4px;
    }
  }
  .answer-check {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 10px;
    align-items: center;
    .answer {
      width: 92%;
      font-size: 14px;
      font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
      .msg {
        height: 37px;
        display: flex;
        align-items: center;
        width: 100%;
        img {
          margin-left: 7px;
          margin-right: 7px;
        }
      }
      .correct-color {
        color: #67c23a;
        background-color: #f0f9eb;
      }
      .error-color {
        background-color: #fdf6ec;
        color: #e6a23c;
      }
      .detail {
        width: 100%;
        border: 1px solid #f2f2f2;
        border-radius: 4px;
        margin-top: 4px;
        padding-left: 25px;
        padding-right: 16px;
        font-size: 14px;
        font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
        .choice {
          display: flex;
          flex-direction: row;
          width: 100%;
          justify-content: space-between;
        }
        .description {
          margin-top: 20px;
          white-space: pre-wrap;
        }
      }
    }
  }
  .answer-wrap {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;
    .answer {
      width: 92%;
      .msg {
        height: 37px;
        display: flex;
        align-items: center;
        width: 100%;
        img {
          margin-left: 7px;
          margin-right: 7px;
        }
      }
      .correct-color {
        color: #67c23a;
        background-color: #f0f9eb;
      }
      .error-color {
        background-color: #fdf6ec;
        color: #e6a23c;
      }
      .detail {
        width: 100%;
        border: 1px solid #f2f2f2;
        border-radius: 4px;
        margin-top: 4px;
        padding-left: 25px;
        padding-right: 16px;
        font-size: 14px;
        font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
        .choice {
          display: flex;
          flex-direction: row;
          width: 100%;
          justify-content: space-between;
        }
        .description {
          margin-top: 20px;
          white-space: pre-wrap;
        }
      }
    }
  }
  .answer-section {
    margin-top: 32px;
    padding-left: 32px;
    .answer-label, .explanation-label {
      //font-weight: 500;
      margin: 24px 0 8px 0;
      font-size: 16px;
      font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
    }
    .answer-content, .explanation-content {
      font-size: 15px;
      color: #222;
      margin-bottom: 8px;
      line-height: 2;
      word-break: break-all;
      font-family: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0 55 Regular", "阿里巴巴普惠体 3.0", sans-serif;
    }
    .answer-label {
      margin-top: 0;
    }
  }
}
</style>
