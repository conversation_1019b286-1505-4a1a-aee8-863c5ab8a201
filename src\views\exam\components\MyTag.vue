<script setup lang="ts">
import { onMounted, ref } from 'vue';

const props = defineProps({
  type: {
    type: String,
    required: false,
    default: 'target'
  },
  tagId: {
    type: String,
    required: true
  },
  deletable: {
    type: Boolean,
    required: false,
    default: true
  }
});
const emit = defineEmits(['delete']);
const handleDelete = () => {
  emit('delete', props.tagId);
};
onMounted(() => {});
</script>

<template>
  <span class="target">
    <span class="text">
      <slot></slot>
    </span>
    <span v-if="deletable" @click="handleDelete" class="btn">×</span>
  </span>
</template>

<style scoped>
.target {
  height: 35px;
  width: 120px;
  border: 1px solid var(--color-theme-project);
  background-color: white;
  color: var(--color-theme-project);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
  .btn {
    color: var(rgba(242, 242, 242, 1));
  }
}
.tag {
  height: 25px;
  width: 120px;
  border-radius: 10px;
  background-color: #9eb7e5;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.btn {
  margin-left: 5px;
  cursor: pointer;
}
</style>
