视频详情页自适应问题 ---- 已解决

视频详情页 标签展示自适应问题

划词点击后 弹窗内容绘制 与 功能实现 ------ 首要内容

页面跳转逻辑修改 数据处理方式调整  ------ 原本的状态管理存在的问题是 多页面开启时 内容更新 错乱
-- 修改方法：使用路由传参 在页面获取信息 并做处理
-- 实际修改方法 采用localStorage和sessionStorage共同完成id的传值
页面跳转传参方式修改 ----3.6日

测试页面 视频类型 -- 功能还没调

进行中:
页面跳转必要变量的传递  
从首页 跳转到介绍页 传递商品类型变量 控制显示

----------小修改--------
DownList 学习页面搜索历史记录部分 搜索框细节修改 增加清空操作 同时搜索结果展示存在bug 需要后续修改

资料页面 加载更多展示控制 当没有更多数据要加载时 显示为 暂无更多数据

文稿详情页 样式细节调整
----------end ----------

----------划词部分-------
划词时 注意判断时机  目前是鼠标抬起事件 也就是onmouseup事件触发划词参数
注意划词内容为空的情况 避免多次弹窗
