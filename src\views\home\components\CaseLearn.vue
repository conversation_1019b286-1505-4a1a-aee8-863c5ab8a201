<template>
  <!-- 案例学习 -->
  <LayoutCard v-bind="data"></LayoutCard>
</template>

<script setup lang="ts">
import LayoutCard from './LayoutCard.vue';
import { getPrjinfoApi } from '@/apis/home';
import { PrjType } from '@/types/project';
const data = ref({
  title: '案例学习',
  word: '选择一个感兴趣的案例去学习并实现它',
  tip: '享受个性化、定制化的学习案例',
  prjFormList: [],
  big: true,
  prjType: PrjType.case,
  getListApi: getPrjinfoApi
});
</script>

<style scoped lang="less"></style>
