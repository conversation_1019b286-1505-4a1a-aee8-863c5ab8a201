<template>
  <!-- 注意：这个页面用在个人空间添加会员卡和会员商品推荐页 -->
  <div class="bigbox">
    <div class="warpper-vip">
      <el-dialog v-model="model" width="1000px" :show-close="false" class="dialog-container">
        <span class="iconfont icon-icon_close" @click="model = false"></span>
        <div class="header">
          <div class="title">购买方案</div>
          <el-divider class="divider"></el-divider>
        </div>
        <div class="body">
          <div class="left">
            <el-scrollbar class="sku-list">
              <div v-for="sku in skuList" :key="sku.title" class="sku-group">
                <!-- 只有个人空间添加会员卡时显示领域名 -->
                <div class="group-title" v-if="addVip">{{ sku.title }}</div>
                <div class="introduce" v-if="addVip">
                  请您选择购买方式，成为领域会员，看到领域内全部知识讲解。
                </div>
                <div
                  v-for="(item, index) in sku.priceList"
                  :key="item.skuId + '-' + index"
                  class="sku-item"
                  :class="{ active: detail.skuId == item.skuId }"
                  @click="showDetail(item, sku)"
                >
                  <div class="arrow">
                    <el-icon
                      size="large"
                      :color="detail.skuId == item.skuId ? '#ffd684' : '#666666'"
                    >
                      <ArrowRightBold />
                    </el-icon>
                  </div>
                  <div>
                    <div class="pay-card-tip">
                      成为领域会员，您可以看到更多知识讲解。
                    </div>
                    <div class="price">
                      {{ sku.title }} 领域 <b>{{ getCardTypeLabel(item.cardType) }}</b> 会员，只需
                      {{ item.actualPaidAmount }} 元，请点击下单支付。
                    </div>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
          <div class="right">
            <div class="prj-info">
              <img class="image" :src="detail.coverPic" />
              <div class="title" v-html="detail.title"></div>
            </div>
            <div class="content">
              <div class="box">
                <div class="normal">包含知识</div>
                <div class="highlight">{{ detail.klgCount }}个</div>
                <div class="tip">赠送讲解和测评项目</div>
              </div>
              <div class="box">
                <div class="normal">学习周期</div>
                <div class="highlight">{{ detail.studyTime }}天</div>
              </div>
            </div>
            <div class="price">
              <div class="subtotal">价格: {{ detail.subtotal }} 元</div>
              <div class="pay">实付: {{ detail.actualPaidAmount }} 元</div>
              <div class="pay-btn-box">
                <CmpButton class="pay-btn" type="primary" @click="handleClick">
                  下单支付
                </CmpButton>
              </div>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
    <QRcodeDialog
      v-model="payDialogVisible"
      v-if="ready"
      :skuId="currentSkuId"
      @paySuccess="handleBuy"
    />
  </div>
</template>

<script setup lang="ts">
import { useProjectStore } from '@/stores/project';
import { useLearningStore } from '@/stores/learning';
import CmpButton from '@/components/CmpButton.vue';
import QRcodeDialog from './QRcodeDialog.vue';
import { getProjectRecommendApi, getVipSkuListApi } from '@/apis/payment';
import { getRecommendApi } from '@/apis/userspace';
import type { Sku } from '@/types';
import { cardTypeMap } from '@/types/constant';
import { ArrowDownBold, ArrowUpBold, ArrowRightBold } from '@element-plus/icons-vue';

const projectStore = useProjectStore();
const learningStore = useLearningStore();
const { info } = storeToRefs(projectStore);

const isFree = ref(false);

const model = defineModel();
const payDialogVisible = ref(false);
const props = defineProps(['spuId', 'selectedSkuId', 'isUserAdd']);
//记录选择的会员商品
const choose = ref(props.selectedSkuId);

interface priceItem {
  skuId: string;
  cardType: number;
  subtotal: number;
  discount: number;
  actualPaidAmount: number;
  studyTime: number;
}
interface skuItem {
  title: string;
  coverPic: string;
  klgCount: number;
  priceList: priceItem[];
}
const skuList = ref<skuItem[]>([]);

interface detailItem {
  skuId: string;
  title: string;
  coverPic: string;
  klgCount: number;
  studyTime: number;
  subtotal: number;
  actualPaidAmount: number;
}
const currentSkuId = ref('');
const detail = ref<detailItem>({
  skuId: '',
  title: '',
  coverPic: '',
  klgCount: 0,
  studyTime: 0,
  subtotal: 0,
  actualPaidAmount: 0
});
const showDetail = (item: priceItem, sku: skuItem) => {
  detail.value.skuId = item.skuId;
  currentSkuId.value = detail.value.skuId;
  detail.value.title = sku.title;
  detail.value.coverPic = sku.coverPic;
  detail.value.klgCount = sku.klgCount;
  detail.value.studyTime = item.studyTime;
  detail.value.subtotal = item.subtotal;
  detail.value.actualPaidAmount = item.actualPaidAmount;
};
const getCardTypeLabel = (cardType: number) => {
  switch (cardType) {
    case 1:
      return '年度';
    case 2:
      return '季度';
    case 3:
      return '月度';
    default:
      return '未知';
  }
};

const route = useRoute();
const spuId = route.query.spuId as string;

const ready = ref(false);
//是否选择了推荐商品
const isRecommend = ref(false);
const emit = defineEmits(['paySuccess']);

const handleBuy = async () => {
  emit('paySuccess');
};

const handleClick = () => {
  model.value = false;
  payDialogVisible.value = true;
};
const addVip = ref(false);
watch(
  () => props.selectedSkuId,
  async (newVal) => {
    if (newVal) {
      ready.value = false;
      if (props.isUserAdd) {
        addVip.value = true;
        const res = await getRecommendApi();
        skuList.value = res.data;
        if (skuList.value.length > 0) {
          showDetail(skuList.value[0].priceList[0], skuList.value[0]);
        }
      } else {
        addVip.value = false;
        //以下逻辑是单个会员商品
        const res = await getVipSkuListApi(props.spuId);
        skuList.value[0] = res.data;
        const priceItem = skuList.value[0].priceList.find((item) => item.skuId == newVal);
        showDetail(priceItem, skuList.value[0]);
      }
      ready.value = true;
    }
  }
);
onMounted(async () => {
  //只在最开始调用了
  console.log('selected', props.selectedSkuId);
  if (props.isUserAdd) {
    addVip.value = true;
    const res = await getRecommendApi();
    skuList.value = res.data;
    if (skuList.value.length > 0) {
      showDetail(skuList.value[0].priceList[0], skuList.value[0]);
    }
  } else {
    addVip.value = false;
    //以下逻辑是单个会员商品
    const res = await getVipSkuListApi(props.spuId);
    skuList.value[0] = res.data;
    const priceItem = skuList.value[0].priceList.find((item) => item.skuId == props.selectedSkuId);
    showDetail(priceItem, skuList.value[0]);
  }

  ready.value = true;
});
</script>

<style lang="less" scoped>
.warpper-vip {
  &:deep(.el-dialog) {
    border-radius: 5px;
    height: 543px;
  }
  &:deep(.el-dialog__header) {
    padding: 0;
  }
  &:deep(.el-dialog__body) {
    padding: 0;
    color: #333333;
    font-size: 12px;
    font-family:
      '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0';
    font-weight: 400;
    display: flex;
    flex-direction: column;
  }
  .dialog-container {
    width: 1000px;
    height: 543px;
    border-radius: 10px;
    display: flex;

    .icon-icon_close {
      position: absolute;
      right: 10px;
      top: 10px;
    }

    .header {
      // width: 1000px;
      height: 60px;
      display: flex;
      margin: 0;
      //padding: 0px 20px 0px 20px;
      flex-direction: column;

      .title {
        margin: 0 0 10px 0;
        font-size: 18px;
        font-weight: bold;
      }

      .divider {
        margin: 0 0px;
      }
    }

    .body {
      width: 1000px;
      height: 463px;
      display: flex;
      flex-direction: row;

      .left {
        width: 500px;
        height: 463px;
        display: flex;
        flex-direction: column;
        white-space: pre-wrap;
        overflow-wrap: break-word;

        .introduce {
          font-size: 14px;
          font-weight: 400;
          padding: 10px 0px 10px 0px;
        }

        .sku-list {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          .group-title {
            font-size: 16px;
            font-weight: bold;
          }

          .sku-item {
            position: relative;
            width: 480px;
            height: 80px;
            display: flex;
            flex-direction: column;
            //background-color: #fafafa;
            background-color: rgba(242, 242, 242, 1);
            border-radius: 5px;
            margin: 10px 0px 10px 0;

            .tip {
              margin: 10px 10px 0px 20px;
            }

            .price {
              margin: 10px 20px 10px 10px;
              font-size: 16px;
            }

            .arrow {
              position: absolute;
              right: 10px;
              top: 30px;
            }
          }

          .sku-item.active {
            background-color: #fff6e5;
            border: 1px solid #ffd684;
          }
        }
      }

      .right {
        width: 500px;
        height: 463px;
        display: flex;
        flex-direction: column;

        .prj-info {
          padding: 10px 10px 10px 30px;
          display: flex;
          flex-direction: row;

          .image {
            width: 140px;
            height: 102px;
            border-radius: 5px;
          }

          .title {
            margin-left: 20px;
            display: flex;
            flex-wrap: wrap;
            align-content: center;
            color: #333333;
            font-size: 16px;
            font-weight: 700;
          }
        }

        .content {
          display: flex;
          flex-direction: row;
          justify-content: center;

          .box {
            width: 160px;
            height: 160px;
            background-color: #ffd37a;
            border-radius: 5px;
            margin: 10px 30px;
            display: flex;
            flex-direction: column;
            box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.1);

            .normal {
              margin: 30px auto 5px auto;
              color: #4d0819;
              font-size: 18px;
              font-weight: 700;
            }

            .highlight {
              margin: 5px auto 5px auto;
              color: #4d0819;
              background-color: unset;
              font-size: 28px;
              font-weight: 700;
            }

            .tip {
              margin: 5px auto;
              color: #4d0819;
              font-size: 14px;
              font-weight: 400;
            }
          }
        }

        .price {
          margin: 10px;
          .subtotal {
            font-size: 16px;
          }
          .pay {
            font-size: 18px;
            font-weight: 700;
          }
          .pay-btn-box {
            display: flex;
            flex-direction: row-reverse;
            .pay-btn {
              margin: 10px 30px;
              width: 100px;
              height: 35px;
              border-radius: 5px;
              font-size: var(--fontsize-middle-project);
            }
          }
        }
      }
    }
  }
}
.pay-card-tip {
  font-size: 13px;
  color: #333;
  margin-bottom: 4px;
  padding: 5px 20px 0 8px;
}
</style>
