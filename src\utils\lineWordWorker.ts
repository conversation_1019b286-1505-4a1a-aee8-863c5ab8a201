import URLParse from 'url-parse';
import { parseFragment, serializeOuter, type DefaultTreeAdapterTypes } from 'parse5';
import { cloneDeep } from 'lodash-es';
import escapeStringRegexp from '@/utils/escapeStringRegexp';
import { useWordStore } from '@/stores/lineWordStore';
//import { WorkerType } from "@/utils/constant"
import he from 'he';
export enum WorkerType {
  draft = 1,
  video = 2,
  exercise = 3,
  proof = 4
}
type Parse5DocumentFragment = DefaultTreeAdapterTypes.DocumentFragment;
type Element = DefaultTreeAdapterTypes.Element;
type TextNode = DefaultTreeAdapterTypes.TextNode;
type ChildNode = DefaultTreeAdapterTypes.ChildNode;

const singleCloseTag = [
  'IMG',
  'BR',
  'HR',
  'INPUT',
  'AREA',
  'HR',
  'LINK',
  'META',
  'BASE',
  'BASEFONT',
  'PARAM',
  'COL',
  'FRAME',
  'EMBED'
];

const uncommonWordMap = new Map<string, string>(); // 映射map

let workerType;
let allStringList: Array<{ list: Array<string>; start: number }> = []; // 新串列表
let originalAllStringList: Array<{ list: Array<string>; start: number }> = []; // 源串列表
let regString = ''; // 正则串

const unitList: Array<{
  tagName: string;
  children: any[];
  index: number;
  qids: number[];
  highlight: boolean;
  stringIndex: number;
}> = []; // 单元列表

onmessage = function (
  e: MessageEvent<{
    questionList: any[];
    content: any;
    workerType: number | string; // draft: 1, video: 2, exercise: 3, proof: 4
  }>
) {
  const questionList = e.data.questionList;
  const htmlStringList: string[] = handleHtmlStringList(e.data.content, e.data.workerType);
  buildRegString(htmlStringList);
  questionList?.forEach((question: any) => {
    handleQuestion(question);
  });
  //   console.log("打印regString", JSON.stringify(regString, null, 2))
  //   console.log("打印StringList",JSON.stringify(allStringList, null, 2));
  //   console.log("打印originalStringList", JSON.stringify(originalAllStringList, null, 2));
  //   console.log("打印unitList", JSON.stringify(unitList, null, 2));
  postMessage({
    allStringList,
    originalAllStringList,
    regString,
    unitList,
    uncommonWordMap
  });
};

// 处理生成htmlStringList 并 保存workerType
function handleHtmlStringList(data: any, type: number | string): string[] {
  switch (type) {
    case WorkerType.draft:
    case 'draft':
      workerType = WorkerType.draft;
      return handleDraft(data);
      break;
    case WorkerType.video:
    case 'video':
      workerType = WorkerType.video;
      return handleVideo(data);
      break;
    case WorkerType.exercise:
    case 'exercise':
      workerType = WorkerType.exercise;
      return handleExercise(data);
      break;
    case WorkerType.proof:
    case 'proof':
      workerType = WorkerType.proof;
      return handleProof(data);
      break;
    default:
      console.error('worker类型错误!');
      return [];
  }
}
// 处理文稿
function handleDraft(wordContent: any): string[] {
  const tempDraftList = [];
  tempDraftList.push(wordContent);
  return tempDraftList;
}
// 处理视频
function handleVideo(videoCaptionList: any[]): string[] {
  const tempVideoList = [];
  videoCaptionList.forEach(
    (
      item: Array<{
        oid: number;
        startTime: string;
        endTime: string;
        caption: string;
        beginning: number;
      }>
    ) => {
      item.forEach((captionItem) => {
        tempVideoList.push(`<span oid="${captionItem.oid}">${captionItem.caption}</span>`);
      });
    }
  );
  return tempVideoList;
}
// 处理习题
function handleExercise(exercise: any): string[] {
  const tempExerciseList = [];
  tempExerciseList.push(`<span etype="stem" type="${exercise.type}">${exercise.stem}</span>`);
  if (exercise.content) {
    exercise.content.forEach((item) => {
      tempExerciseList.push(
        `<span etype="content" contentid="${item.optionId}">${item.text}</span>`
      );
    });
  }
  tempExerciseList.push(`<span etype="answer">${exercise.answer}</span>`);
  tempExerciseList.push(`<span etype="explanation">${exercise.explanation}</span>`);
  return tempExerciseList;
}
// 处理论证块
function handleProof(proofList: any[]): string[] {
  const tempProofList = [];
  proofList.forEach((block: any) => {
    block.klgProofCondList.forEach((cond: any) => {
      tempProofList.push(
        `<span blockid="${block.klgProofBlockId}" condid="${cond.klgProofCondId}" sortid="${cond.sort}">${cond.cnt}</span>`
      );
    });
    tempProofList.push(`<span blockid="${block.klgProofBlockId}">${block.conclusion}</span>`);
  });
  return tempProofList;
}
// 处理问题
function handleQuestion(question: any): void {
  const str = buildRegSubString(question.associatedWords as string, uncommonWordMap);
  if (str.length == 0) {
    return;
  }
  const positions = findAllOccurrences(regString, str.replace(/\n/g, ''));
  for (const position of positions) {
    find(position, str.length, question.questionId);
  }
}
// 构建正则串
function buildRegString(htmlStringList: string[]) {
  let contentIndex = 0;
  let uncommonWordIndex = 0;
  let stringIndex = 0;
  let start = 0;
  const uncommonWords = getRemainingRandomChars(htmlStringList.join(''), 100);
  htmlStringList.forEach((htmlString: string) => {
    const dom = {
      tagName: '',
      children: [],
      index: -1,
      qids: [],
      highlight: false,
      stringIndex: -1
    };
    const regCharList: string[] = [];
    const tempDiv = parseFragment(htmlString) as Parse5DocumentFragment;
    let stringList: string[] = [];
    let codeFlag = false;
    const dfs = (node: ChildNode, parent: any) => {
      if (node.nodeName == 'code') {
        node.nodeName = 'span';
        node.tagName = 'span';
        codeFlag = true;
      }
      if (node.nodeName == '#text') {
        let textContent = he.escape((node as TextNode).value as string);
        if (textContent?.length == 0) {
          return;
        }
        if (codeFlag) {
          const tempList = textContent.split('\n');
          for (const str of tempList) {
            const newDom: {
              tagName: string;
              children: any[];
              index: number;
              qids: number[];
              highlight: boolean;
              stringIndex: number;
            } = {
              tagName: 'p',
              children: [],
              index: -1,
              qids: [],
              highlight: false,
              stringIndex: -1
            };
            const newDom2: {
              tagName: string;
              children: any[];
              index: number;
              qids: number[];
              highlight: boolean;
              stringIndex: number;
            } = {
              tagName: 'TEXT',
              children: [],
              index: -1,
              qids: [],
              highlight: false,
              stringIndex: -1
            };
            stringList.push('<p>');
            contentIndex++;
            stringIndex++;
            for (const char of str) {
              if (char == '<') {
                console.log('');
              }
              const unit = {
                tagName: 'unit',
                children: [char],
                index: contentIndex,
                qids: [],
                highlight: false,
                stringIndex: stringIndex++
              };
              newDom2.children.push(unit);
              unitList.push(unit);
              contentIndex += char.length;
              stringList.push(char);
              regCharList.push(char);
            }
            stringList.push('</p>');
            contentIndex++;
            stringIndex++;
            newDom.children.push(newDom2);
            parent.children.push(newDom);
          }
          codeFlag = false;
        } else {
          const newDom: {
            tagName: string;
            children: any[];
            index: number;
            qids: number[];
            highlight: boolean;
            stringIndex: number;
          } = {
            tagName: 'TEXT',
            children: [],
            index: -1,
            qids: [],
            highlight: false,
            stringIndex: -1
          };
          for (const char of textContent) {
            if (char != '\n') {
              const unit = {
                tagName: 'unit',
                children: [char],
                index: contentIndex,
                qids: [],
                highlight: false,
                stringIndex: stringIndex++
              };
              newDom.children.push(unit);
              unitList.push(unit);
              contentIndex += char.length;
              stringList.push(char);
              regCharList.push(char);
            }
          }
          parent.children.push(newDom);
        }
      } else if ((node as Element).tagName) {
        if (node.nodeName == 'img') {
          const src = node.attrs.find((attr) => attr.name == 'src')?.value;
          const outerHTML = serializeOuter(node);
          const obj = URLParse(src as string);
          const key = obj.pathname.split('/').pop() as string;
          if (!uncommonWordMap.has(key)) {
            uncommonWordMap.set(key, uncommonWords[uncommonWordIndex]);
            stringList.push(outerHTML);
            regCharList.push(uncommonWords[uncommonWordIndex]);
            const unit = {
              tagName: 'unit',
              children: [outerHTML],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            parent.children.push(unit);
            unitList.push(unit);
            contentIndex += uncommonWords[uncommonWordIndex].length;
            uncommonWordIndex++;
          } else {
            const unit = {
              tagName: 'unit',
              children: [outerHTML],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            parent.children.push(unit);
            unitList.push(unit);
            const str = uncommonWordMap.get(key)!;
            contentIndex += str.length;
            stringList.push(outerHTML);
            regCharList.push(str);
          }
        } else if ((node as Element).nodeName == 'script') {
          const key = ((node as Element).childNodes[0] as TextNode).value;
          const outerHTML = serializeOuter(node);
          if (!uncommonWordMap.has(key)) {
            uncommonWordMap.set(key, uncommonWords[uncommonWordIndex]);
            stringList.push(outerHTML);
            regCharList.push(uncommonWords[uncommonWordIndex]);
            const unit = {
              tagName: 'unit',
              children: [outerHTML],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            parent.children.push(unit);
            unitList.push(unit);
            contentIndex += uncommonWords[uncommonWordIndex].length;
            uncommonWordIndex++;
          } else {
            const unit = {
              tagName: 'unit',
              children: [outerHTML],
              index: contentIndex,
              qids: [],
              highlight: false,
              stringIndex: stringIndex++
            };
            parent.children.push(unit);
            unitList.push(unit);
            const str = uncommonWordMap.get(key)!;
            contentIndex += str.length;
            stringList.push(outerHTML);
            regCharList.push(str);
          }
        } else {
          const newDom: {
            tagName: string;
            children: any[];
            index: number;
            qids: number[];
            highlight: boolean;
            stringIndex: number;
          } = {
            tagName: (node as Element).nodeName,
            children: [],
            index: -1,
            qids: [],
            highlight: false,
            stringIndex: -1
          };
          parent.children.push(newDom);
          const etype = (node as Element).attrs.find((attr) => attr.name == 'etype')?.value;
          const blockid = (node as Element).attrs.find((attr) => attr.name == 'blockid')?.value;
          const condid = (node as Element).attrs.find((attr) => attr.name == 'condid')?.value;
          const sortid = (node as Element).attrs.find((attr) => attr.name == 'sortid')?.value;
          if (etype) {
            const contentid = (node as Element).attrs.find(
              (attr) => attr.name == 'contentid'
            )?.value;
            const type = (node as Element).attrs.find((attr) => attr.name == 'type')?.value;
            let pushFlag = false;
            if (contentid && !type) {
              stringList.push(
                `<${(node as Element).nodeName} etype="${etype}" contentid="${contentid}">`
              );
              pushFlag = true;
            }
            if (type && !contentid) {
              stringList.push(`<${(node as Element).nodeName} etype="${etype}" type="${type}">`);
              pushFlag = true;
            }
            if (!pushFlag) {
              stringList.push(`<${(node as Element).nodeName} etype="${etype}">`);
            }
          } else if (blockid) {
            if (condid) {
              stringList.push(
                `<${(node as Element).nodeName} blockid="${blockid}" condid="${condid}" sortid="${sortid}">`
              );
            } else {
              stringList.push(`<${(node as Element).nodeName} blockid="${blockid}">`);
            }
          } else {
            stringList.push(`<${(node as Element).nodeName}>`);
          }
          stringIndex++;
          for (const child of (node as Element).childNodes) {
            dfs(child, newDom);
          }
          if (!singleCloseTag.includes((node as Element).tagName.toUpperCase())) {
            stringList.push(`</${(node as Element).tagName}>`);
            stringIndex++;
          }
        }
      }
    };

    for (const child of tempDiv.childNodes) {
      dfs(child, dom);
    }
    regString += regCharList.join('');
    allStringList.push({
      list: stringList,
      start: start
    });
    start = stringIndex;
  });
  originalAllStringList = cloneDeep(allStringList);
}
// 建正则子串
export function buildRegSubString(htmlString: string, uncommonWordMap: any): string {
  const tempDiv = parseFragment(htmlString) as Parse5DocumentFragment;
  let regSubString = '';

  const dfs = (node: ChildNode) => {
    if (node.nodeName == '#text') {
      const textContent = (node as TextNode).value as string;
      if (textContent?.length == 0) {
        return;
      }
      for (const char of textContent) {
        if (char != '\n') {
          regSubString += char;
        }
      }
    } else if ((node as Element).tagName) {
      if ((node as Element).nodeName == 'img') {
        const src = (node as Element).attrs.find((attr) => attr.name == 'src')?.value;
        const obj = URLParse(src as string);
        const key = decodeURIComponent(obj.pathname.split('/').pop() as string);
        regSubString += uncommonWordMap.get(key);
      } else if ((node as Element).nodeName == 'script') {
        const key = ((node as Element).childNodes[0] as TextNode).value;
        regSubString += uncommonWordMap.get(key);
      } else {
        for (const child of (node as Element).childNodes) {
          dfs(child);
        }
      }
    }
  };

  for (const childNode of tempDiv.childNodes) {
    dfs(childNode);
  }
  return regSubString;
}
// 找位置
function find(postion: number, offset: number, questionId: number) {
  for (let i = postion; i < postion + offset; i++) {
    const unit = unitList[i];
    if (unit) {
      unit.qids.push(questionId);
      handleUnit(unit);
    }
  }
}
function findAllOccurrences(str: string, subStr: string) {
  const matchStr = str.toLowerCase();
  const matchSubStr = subStr.toLowerCase();
  const regex = new RegExp(escapeStringRegexp(matchSubStr), 'g');
  const matches = [...matchStr.matchAll(regex)];
  const positions = matches.map((match) => match.index);
  return positions;
}
// 处理unit
function handleUnit(node: {
  tagName: string;
  children: any[];
  index: number;
  qids: number[];
  highlight: boolean;
  stringIndex: number;
}) {
  if (node.qids.length == 0) {
    if (!node.highlight) {
      allStringList.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] = node.children[0];
        }
      });
    } else {
      allStringList.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span class="highlight2">${node.children[0]}</span>)`;
        }
      });
    }
  } else {
    const qids = node.qids.join(',');
    if (!node.highlight) {
      allStringList.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span data-qid="${qids}" class="highlight">${node.children[0]}</span>`;
        }
      });
    } else {
      allStringList.forEach((item) => {
        if (item.list[node.stringIndex - item.start]) {
          item.list[node.stringIndex - item.start] =
            `<span data-qid="${qids}" class="highlight highlight2">${node.children[0]}</span>`;
        }
      });
    }
  }
}
// 获取未使用字符
export function getRemainingRandomChars(input: string, length: number): string[] {
  // 定义Unicode字符的范围（只取常见的 BMP 字符，U+0000 到 U+FFFF）
  const MAX_CODE_POINT = 0xffff;

  // 将输入字符串的字符存储到一个 Set 中（去重）
  const usedChars = new Set(input);

  // 创建一个数组，保存所有未使用的字符
  const remainingChars = [];

  // 遍历 BMP 范围内的所有字符，过滤掉已使用的字符
  for (let i = 1; i <= MAX_CODE_POINT; i++) {
    const char = String.fromCharCode(i);
    if (!usedChars.has(char)) {
      remainingChars.push(char);
    }
    if (remainingChars.length >= length) {
      break;
    }
  }

  return remainingChars;
}
