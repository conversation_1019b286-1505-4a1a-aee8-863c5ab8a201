<template>
  <div class="qt-detail">
    <!-- 头部 -->
    <div class="header">
      <div class="header-left">
        <h1>{{ Title }}</h1>
      </div>
      <div class="process">测试完成度：{{ currentCount }} / {{ total }}</div>
    </div>
    <!-- 完成测评 -->
    <div class="finish-content">
      <p class="finish-accuracy">恭喜！您的正确率是{{ accuracy }}!</p>
      <div class="btns2">
        <CmpButton class="btn" type="info" @click="checkError">查看错题</CmpButton>
        <CmpButton class="btn" type="primary" @click="finishandBack">完成测评</CmpButton>
      </div>
    </div>
    <!-- 查看错题 -->
    <div class="error-wrap" v-if="errorListVisible">
      <div class="error-innerwrap" v-for="item in errorList" :key="item.examId">
        <div class="error-top">
          <div class="error-top-content">
            <div class="content-question">({{ item.order }}).{{ item.examTitle }}</div>
            <div v-if="item.examType == ExamType.fillBlank" class="replycontent-style">
              <p class="replycontent-title">回答:</p>
              <div v-html="item.myAnswer" class="replycontent"></div>
            </div>
            <div v-if="item.examType == ExamType.choice">
              <el-form-item label="">
                <el-radio-group v-model="item.myAnswer" class="selection-style">
                  <el-radio
                    :label="indexIntoAlpha(index)"
                    v-for="(selection, index) in item.examChoices"
                    :key="index"
                    disabled
                    ><span>{{ indexIntoAlpha(index) }}</span
                    >{{ selection }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </div>
            <div v-if="item.examType == ExamType.judgment">
              <el-form-item label="">
                <el-radio-group v-model="item.myAnswer" class="selection-style">
                  <el-radio :label="true" disabled
                    ><span class="iconfont icon-duigou1"></span
                  ></el-radio>
                  <el-radio :label="false" disabled
                    ><span class="iconfont icon-cuowu"></span
                  ></el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div v-if="item.examType == ExamType.shortAnswer" class="replycontent-style">
              <p class="replycontent-title">回答:</p>
              <div v-html="item.myAnswer" class="replycontent"></div>
            </div>
            <div class="explanation-wrap">
              <div class="detail">
                <div class="choice">
                  <div v-if="item.examType == ExamType.judgment">
                    答案：{{ item.examAnswer == true ? '正确' : '错误' }}
                  </div>
                  <div v-else>答案：{{ item.examAnswer }}</div>
                  <div
                    style="color: var(--color-theme-project); font-weight: 700; cursor: pointer"
                    @click="buildupQuestion(item)"
                  >
                    提问
                  </div>
                </div>
                <div class="description">
                  说明：
                  <div>{{ item.examExplanation }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="error-top-list">
            <!-- 开放性问题应该使用问题描述，点击问题以后需要弹出回答 -->
            <p>问题列表</p>
            <template v-for="questionItem in item.questionList" :key="questionItem.questionId">
              <div class="hover-style questionliststyle">
                <span v-html="questionItem.keyword"></span>
                <span>{{ questionItem.questionType }}?</span>
              </div>
            </template>
          </div>
        </div>
        <div class="error-bottom">
          <!-- 只有有父id的才有 -->
          <CmpButton class="btn" type="primary" @click="addFavorites(item)">加入收藏夹</CmpButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/* 引入组件 */
import CmpButton from '@/components/CmpButton.vue';

/* 引入数据相关函数 */
import indexIntoAlpha from '@/utils/indexIntoAlpha';
import type { documentData } from '@/types/data';

/* 引入接口函数 */
import { addFavoritesApi } from '@/apis/exam';
import { ExamType } from '@/types/exam';

/* 引入vue相关函数 */
import { useRoute } from 'vue-router';
import { ShowType } from '@/types/exam';
const route = useRoute();
const spuId = route.query.spuId as string;

// 点击完成
const props = defineProps(['wrongSets']);
const total = ref(0);
const Title = ref('');
const accuracy = ref(0);
const currentCount = ref(0);
const showType = inject('showType') as Ref<ShowType>;
let errorList = ref<Array<documentData>>([
  {
    spuId: '',
    examId: 0, //测试题目id
    sectionId: 0, //小节id
    order: 0, //题目序号
    sectionTitle: '', //小节名称
    examType: ExamType.fillBlank, //1.填空 2选择 3判referenceContent断 4问答
    examTitle: '', //题目
    examChoices: [], //选项A
    examAnswer: '', //答案
    myAnswer: '',
    examExplanation: '', //解释说明
    questionList: [
      {
        questionId: 0, //问题自增id
        associatedWords: '', //关联文本内容
        keyword: '', //关键字内容
        questionType: '', //问题类型，是什么、为什么、怎么做
        questionNecessity: 0, //问题必要性 1必须问题，2参考问题
        creatorId: '', //创建者id
        creatorName: '', //创建者名字
        createTime: '', //创建时间
        explanation: '',
        answerList: [
          {
            answerId: 0, //回答自增id
            answerKlgs: [], //知识点名称列表
            answerExplanation: '', //解释
            createTime: '', //回答时间
            creatorName: '', //回答者名称
            modifiedTime: '' //修改时间
          }
        ]
      }
    ]
  }
]);
onMounted(() => {
  accuracy.value = props.wrongSets.accuracy;
  Title.value = props.wrongSets.Title;
  currentCount.value = props.wrongSets.currentCount;
  errorList.value = props.wrongSets.list;
  total.value = props.wrongSets.list.length;
});

watch(
  () => props.wrongSets,
  (newVal) => {
    accuracy.value = newVal.accuracy;
    Title.value = newVal.Title;
    currentCount.value = newVal.currentCount;
    errorList.value = newVal.list;
    total.value = newVal.list.length;
  }
);

// 查看错题
const errorListVisible = ref(false);
const checkError = () => {
  errorListVisible.value = true;
};

// 提问:两种情况，1是在答题的时候提问，2是在错题集提问
const emit = defineEmits(['buildupQuestion']);
const buildupQuestion = (item: documentData) => {
  emit('buildupQuestion', item);
};

// 完成测评并返回
const finishandBack = () => {
  showType.value = ShowType.map;
};

// 加入收藏夹
const addFavorites = (item: documentData) => {
  const params = {
    examId: item.examId,
    spuId
  };
  addFavoritesApi(params).then((res) => {
    if (res.success) {
      ElMessage({
        message: res.message,
        type: 'success'
      });
    } else {
      ElMessage({
        message: res.message,
        type: 'warning'
      });
    }
    console.log('加入收藏夹成功');
  });
};
</script>

<style lang="less" scoped>
// 滑动效果
.qt-detail {
  width: 1100px;
  padding: 8px 23px 20px 10px;

  .header {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f2f2f2;

    .header-left {
      display: flex;
      flex-direction: row;
      height: 100%;
      align-items: center;

      .back-btn {
        height: 56px;
        width: 80px;
      }

      h1 {
        margin-left: 40px;
        font-size: 20px;
        font-weight: 700;
      }
    }

    .process {
      font-size: 14px;
      color: var(--color-theme-project);
    }
  }
  .finish-content {
    margin-top: 15px;
    width: 970px;
    height: 80px;
    background-color: #f0f9eb;
    margin-left: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .finish-accuracy {
      margin-left: 40px;
    }

    .btns2 {
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin-left: 369px;

      .btn {
        width: 160px;
        height: 44px;
        margin-left: 20px;
      }
    }
  }

  .btns {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;

    .btn {
      width: 160px;
      height: 44px;
      margin-left: 20px;
    }
  }

  .error-wrap {
    margin-left: 40px;
    .error-innerwrap {
      width: 970px;
      padding-top: 15px;
      min-height: 472px;
      margin-top: 19px;
      background-color: #ffffff;
      border: 0.8px solid rgb(242, 242, 242);
      border-radius: 5px;
      box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
      .error-top {
        display: flex;
        flex-direction: row;

        .error-top-content {
          padding-right: 50px;
          width: 527px;

          .content-question {
            font-size: 14px;
            font-weight: 700;
            color: #333333;
            margin-left: 83px;
            display: flex;
            flex-wrap: wrap;
            white-space: pre-wrap;
            word-wrap: break-word;
          }

          .replycontent-title {
            margin-left: 30px;
          }

          .replycontent {
            width: 447px;
            margin: 10px 10px 10px 30px;
          }

          .selection-style {
            display: flex;
            flex-direction: column;
            margin-left: 30px;
            align-items: flex-start;

            .el-radio {
              margin-top: 18px;

              span {
                margin-left: 32px;
                margin-right: 5px;
              }
            }
          }

          .explanation-wrap {
            width: 447px;
            min-height: 134px;
            background-color: #ffffff;
            border: 0.8px solid rgb(242, 242, 242);
            border-radius: 5px;
            margin-left: 30px;
            padding: 11px 22px;
            font-size: 14px;

            .detail {
              white-space: pre-wrap;

              .choice {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
              }

              .description {
                margin-top: 20px;
              }
            }
          }
        }

        .error-top-list {
          width: 422px;
          height: 355px;
          background-color: rgba(242, 242, 242, 0.376);
          border-radius: 5px;
          padding: 15px 22px;
          overflow-y: auto;

          p {
            font-size: 14px;
            font-weight: 700;
          }

          .hover-style:hover {
            font-weight: 700;
            color: #005579;
            cursor: pointer;
          }

          .questionliststyle {
            margin-top: 17px;
            font-size: 14px;
          }
        }
      }

      .error-bottom {
        margin-top: 18px;
        margin-left: 30px;
        margin-bottom: 40px;

        .btn {
          width: 160px;
          height: 44px;
        }
      }
    }
  }
}
</style>
