<template>
  <div class="learning-container textfont">
    <!-- 主要内容区域 -->

    <!-- 返回按钮 -->
    <div class="left">
      <el-skeleton :loading="!ready" :rows="3" animated>
        <template #default>
          <div class="back-button" @click="handleBack">
            <img class="backBtn" @click="handleBack()" src="@/assets/images/prjlearn/u4508.svg" />
            <span class="areaTitle titlefont">{{ goodsInfo.title }}</span>
          </div>
          <div class="author-info">
            <img :src="goodsInfo.editorPic" class="avatar" />
            <span class="author-name">{{ goodsInfo.editorName }}</span>
          </div>
          <div class="teach titlefont">
            <img :src="TV" class="avatar" style="margin-right: 10px" />
            知识讲解
          </div>
          <!-- 左侧课程列表 -->
          <div class="course-list">
            <div
              v-if="records.length != 0"
              v-for="record in records"
              :key="record.spuId"
              class="course-card"
              :class="{ active: selectedCourse?.spuId === record.spuId }"
            >
              <div class="course-image">
                <img :src="record.coverPic || myimage" :alt="record.title" />
              </div>
              <div class="course-info">
                <h3 class="course-title titlefont" @click="handleCourseClick(record)">
                  {{ record.title || '未命名课程' }}
                </h3>
                <div class="author-info">
                  <img :src="record.userCoverPic || myimage" class="avatar" />
                  <span class="author-name">{{ record.userName || '未知作者' }}</span>
                </div>
              </div>
            </div>
            <div v-else style="width: 540px; width: 540px"><el-empty description="暂无课程" /></div>
          </div>
        </template>
        <template #template>
          <!-- 骨架屏模板 -->
          <div class="skeleton-back-button">
            <el-skeleton-item
              variant="image"
              style="width: 20px; height: 20px; margin-right: 12px"
            />
            <el-skeleton-item variant="text" style="width: 200px; height: 24px" />
          </div>
          <div class="skeleton-author-info">
            <el-skeleton-item
              variant="image"
              style="width: 16px; height: 16px; margin-right: 5px"
            />
            <el-skeleton-item variant="text" style="width: 80px; height: 12px" />
          </div>
          <div class="skeleton-teach">
            <el-skeleton-item
              variant="image"
              style="width: 25px; height: 20px; margin-right: 10px"
            />
            <el-skeleton-item variant="text" style="width: 80px; height: 16px" />
          </div>
          <div class="skeleton-course-list">
            <div v-for="i in 3" :key="i" class="skeleton-course-card">
              <el-skeleton-item
                variant="image"
                style="width: 170px; height: 124px; border-radius: 4px"
              />
              <div class="skeleton-course-info">
                <el-skeleton-item
                  variant="text"
                  style="width: 200px; height: 20px; margin-bottom: 10px"
                />
                <div class="skeleton-author-info">
                  <el-skeleton-item
                    variant="image"
                    style="width: 16px; height: 16px; margin-right: 5px"
                  />
                  <el-skeleton-item variant="text" style="width: 60px; height: 12px" />
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
    <!-- 右侧详情区域 -->
    <div class="course-detail">
      <!-- KsgMap覆盖层 -->
      <div class="ksg-map-overlay">
        <KsgMap
          ref="ksgRef"
          :config="config"
          @load-more="handleLoadMore"
          :loading="loading"
          @click-label="handleClickLabel"
          class="ksgmap"
        />
      </div>

      <!-- 右上角统计信息 -->
      <div class="stats-corner">
        <div class="stat-line">
          <span class="stat-label">已掌握:</span>
          <span class="stat-value">{{ computedStats.masteredKlg }}</span>
        </div>
        <div class="stat-line">
          <span class="stat-label">全掌握:</span>
          <span class="stat-value">{{ computedStats.fullyMasteredKlg }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import myimage from '@/assets/images/learn/prjwrap.png';
import TV from '@/assets/images/area/tv.svg';
import { getAreaGoods } from '@/apis/arealearn';
import { getPrjIntroduceApi } from '@/apis/case';

// KsgMap相关导入
import { projectGraph } from '@/apis/ksgmap';
import { KsgMap, MODE } from '@endlessorigin/KsgMap';

const route = useRoute();
const router = useRouter();

// 商品详情信息
const goodsInfo = ref({
  title: '未命名课程',
  editorName: '未知作者',
  editorPic: myimage,
  masteredKlgCount: 0,
  fullyMasteredKlgCount: 0,
  klgCount: 0
});

// 课程数据 - 直接使用API返回的records
const records = ref([]);

// 添加ready变量控制加载状态
const ready = ref(false);

// 从URL获取spuId
const spuId = ref(route.query.spuId || '');

// 当前选中的课程

// 计算统计数据
const computedStats = computed(() => {
  return {
    masteredKlg: `${goodsInfo.value.masteredKlgCount}/${goodsInfo.value.klgCount}`,
    fullyMasteredKlg: `${goodsInfo.value.fullyMasteredKlgCount}/${goodsInfo.value.klgCount}`
  };
});

// 获取商品详情数据
const fetchGoodInfo = async () => {
  if (!spuId.value) {
    console.warn('spuId参数缺失');
    return;
  }

  try {
    const res = await getPrjIntroduceApi({ spuId: spuId.value });
    console.log('获取商品详情:', res);

    if (res && res.data) {
      goodsInfo.value = res.data;
    }
  } catch (error) {
    console.error('获取商品详情失败:', error);
  }
};

// 获取课程数据
const fetchCourses = async () => {
  const response = await getAreaGoods(spuId.value, 0, 10);
  if (response && response.data && response.data.records) {
    records.value = response.data.records;
  }

  ready.value = true;
};

// KsgMap相关变量
const ksgRef = ref();
const loading = ref('loading');

// KsgMap配置项
const config = {
  model: MODE.MULTIPLE_ROOT,
  pointsLevelPager: {
    current: 1,
    levelSize: 2
  }
};

// 存储知识图谱数据
const dataList = ref('');
const ksgTotal = ref('');
const rootid = ref('');
const total = ref(0);
// 固定的API参数
const fixedParams = {
  spuId: 'P1935945912330551296',
  chapterId: 812,
  current: 0,
  limit: 10
};

// 初始化KsgMap
const initKsgMap = async () => {
  loading.value = 'loading';

  try {
    // 调用projectGraph API
    const pointsData = await projectGraph(
      fixedParams.spuId,
      fixedParams.chapterId.toString(),
      fixedParams.current,
      fixedParams.limit
    );

    console.log('KsgMap数据:', pointsData);

    // 设置数据
    dataList.value = pointsData.records;
    total.value = pointsData.total;

    // 初始化KsgMap组件
    if (ksgRef.value && dataList.value) {
      ksgRef.value.firstLoadPointsData(dataList.value, total.value, rootid.value);
      loading.value = 'loaded';
    }
  } catch (error) {
    console.error('KsgMap初始化失败:', error);
    loading.value = 'error';
  }
};

// 处理加载更多
const handleLoadMore = (rootId, crt, levelSize) => {
  console.log('加载更多:', rootId, crt, levelSize);
};

// 处理点击标签
const handleClickLabel = (id) => {
  console.log('点击标签:', id);
};

const handleBack = () => {
  router.push({
    path: '/goodIntroduce',
    query: { spuId: spuId.value }
  });
};

// 处理课程标题点击事件
const handleCourseClick = (record) => {
  // 保存领域spuId到sessionStorage，供项目页面使用
  sessionStorage.setItem('areaSpuId', spuId.value);

  router.push({
    path: '/arealearning/prj',
    query: {
      spuId: record.spuId,
      areaSpuId: spuId.value // 传递领域spuId
    }
  });
};

onMounted(async () => {
  // 初始化时设置ready为false
  ready.value = false;

  // 获取商品详情数据
  await fetchGoodInfo();
  // 获取课程数据
  await fetchCourses();
  // 初始化知识图谱
  initKsgMap();
});
</script>

<style scoped>
.author-info {
  display: flex;
  align-items: center;

  .avatar {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 5px;
  }

  .author-name {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    color: #666666;
  }
}
.learning-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 15px;
  background-color: #ffffff;
  display: flex;
  gap: 20px;
}
.left {
  .author-info,
  .teach {
    margin-top: 5px;
    margin-left: 25px;
  }
  .teach {
    margin-top: 10px;
    font-weight: 600;
    font-style: normal;
    font-size: 16px;
    color: #1973cb;
    line-height: 20px;
    display: flex;
    align-items: center;
    img {
      width: 25px;
      height: 20px;
      margin-right: 10px;
    }
  }
}
.back-button {
  display: inline-flex;
  align-items: center;

  .backBtn {
    margin-right: 12px;
    cursor: pointer;
  }
  .areaTitle {
    font-weight: 600;
    font-style: normal;
    font-size: 20px;
    color: #333333;
  }
}

.course-list {
  width: 540px;
  border-radius: 8px;
  padding: 20px;
  padding-top: 10px;
}

.course-card {
  /* width: 540px; */
  height: 124px;
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
}

.course-card:last-child {
  margin-bottom: 0;
}

.course-image {
  flex-shrink: 0;
  width: 170px;
  height: 124px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.course-image img {
  width: 100%;
  height: 100%;
  overflow: clip;
}

.course-info {
  flex: 1;
  min-width: 0;
  padding-left: 5px;
  .author-info {
    margin-top: 30px;
    margin-left: 0;
  }
  .avatar {
    margin-right: 5px;
  }
}

.course-title {
  color: #333333;
  margin-top: 30px;
  font-weight: 600;
  font-style: normal;
  font-size: 20px;
  &:hover {
    cursor: pointer;
    color: rgb(25, 115, 203);
  }
}

.course-detail {
  width: 800px;
  height: 600px;
  border-radius: 50px;
  margin-top: 50px;
  position: relative;
}
/* KsgMap覆盖层样式 */
.ksg-map-overlay :deep(.ksgmap) {
  position: absolute;
  top: 0;
  left: 0;
  width: 800px !important;
  height: 600px !important;
}

.close-icon:hover {
  background: rgba(255, 255, 255, 1);
  color: #333333;
}

.stats-corner {
  position: absolute;
  top: 10px;
  right: 30px;
  text-align: right;
}

.stat-line {
  margin-bottom: 8px;
  font-size: 14px;
}

.stat-line:last-child {
  margin-bottom: 0;
}

.stat-label,
.stat-value {
  font-size: 13px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.996);
  line-height: normal;
  font-feature-settings: 'kern';
}
.stat-label {
  margin-right: 5px;
}
.detail-content {
  margin-top: 60px;
}

.detail-title {
  font-family: sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.course-description {
  font-family: sans-serif;
  font-size: 16px;
  color: #ecf0f1;
  line-height: 1.6;
  margin: 0 0 30px 0;
}

.ksg-map-btn:hover {
  background: #1565c0;
  transform: translateY(-1px);
}

.detail-content {
  margin-top: 60px;
}

.detail-title {
  font-family: sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.course-description {
  font-family: sans-serif;
  font-size: 16px;
  color: #ecf0f1;
  line-height: 1.6;
  margin: 0;
}

/* 骨架屏样式 */
.skeleton-back-button {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.skeleton-author-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.skeleton-teach {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.skeleton-course-list {
  width: 540px;
}

.skeleton-course-card {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  height: 124px;
}

.skeleton-course-info {
  flex: 1;
  padding-left: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
</style>
