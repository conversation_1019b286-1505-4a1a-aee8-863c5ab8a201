.titlefont {
  font-size: 18px;
}
.title {
  display: inline-block;
}
.warpper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.99607843);
  font-size: 12px;
  font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0';
  font-weight: 400;
  padding: 10px;
  overflow-y: auto;
  /* 恢复滚动，但使用粘性定位来固定项目信息 */
  overflow-x: hidden;
}
.progress-card {
  flex-shrink: 0;
  /* 防止被压缩 */
  overflow: hidden;
}
.progress-card .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.2s ease;
}
.progress-card .card-header:hover {
  background-color: #f8f9fa;
}
.progress-card .card-header .header-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}
.progress-card .card-header .header-left .back-icon {
  margin-right: 12px;
  color: #666;
  cursor: pointer;
  transition: color 0.2s ease;
}
.progress-card .card-header .header-left .back-icon:hover {
  color: #409eff;
}
.progress-card .card-header .header-left .project-info {
  flex: 1;
  min-width: 0;
}
.progress-card .card-header .header-left .project-info .meta-info {
  margin-left: 25px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 11px;
  position: relative;
}
.progress-card .card-header .header-left .project-info .meta-info .author-info {
  display: flex;
  align-items: center;
  gap: 4px;
}
.progress-card .card-header .header-left .project-info .meta-info .author-info .avatar {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}
.progress-card .card-header .header-left .project-info .meta-info .author-info .author-name {
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
}
.progress-card .card-header .header-left .project-info .meta-info .create-time {
  color: #666666;
  font-size: 12px;
}
.progress-card .card-header .expand-arrow {
  transition: transform 0.2s ease;
  position: absolute;
  right: 5px;
  font-size: 15px;
  cursor: pointer;
}
.progress-card .card-header .expand-arrow.expanded {
  transform: rotate(180deg);
}
.progress-card .progress-content {
  margin-top: 20px;
  padding: 0 25px 0;
}
.progress-card .progress-content .progress-circles {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}
.progress-card .progress-content .progress-circles .progress-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.progress-card .progress-content .progress-circles .progress-item .progress-label {
  margin-top: 8px;
  text-align: center;
}
.progress-card .progress-content .progress-circles .progress-item .progress-label .label-text {
  margin-bottom: 2px;
}
.progress-card .progress-content .tags-section {
  margin-bottom: 16px;
}
.progress-card .progress-content .tags-section .tags-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}
.progress-card .progress-content .tags-section .tags-grid .tag-item {
  padding: 6px 12px;
  background-color: #f0f2f5;
  border-radius: 16px;
  font-size: 11px;
  color: #333333;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s ease;
}
.progress-card .progress-content .tags-section .tags-grid .tag-item:hover {
  background-color: #e6f7ff;
  color: #1890ff;
}
.progress-card .progress-content .tags-section .tags-grid .tag-item.more-tag {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.progress-card .progress-content .tags-section .tags-grid .tag-item.more-tag:hover {
  background-color: #409eff;
  color: white;
}
.progress-card .progress-content .intro-section {
  text-align: center;
}
.backBtn {
  cursor: pointer;
  height: 14px;
  width: 15px;
  margin-right: 10px;
}
.chapter-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 22px;
}
.chapter-section .chapterCatalog {
  font-size: 14px !important;
}
.chapter-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.chapter-section .section-header .chapter-count {
  font-size: 12px;
  color: #666;
}
.chapter-section .chapter-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}
.chapter-section .chapter-item {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  height: 40px;
  box-sizing: border-box;
}
.chapter-section .chapter-item:hover {
  background-color: #f2f2f2;
  transform: scale(1.02);
  /* 放大效果 */
}
.chapter-section .chapter-item.active {
  background-color: #f2f2f2;
}
.chapter-section .chapter-item.active .chapter-name {
  font-weight: 600;
}
.chapter-section .chapter-item .chapter-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #cbc7c7;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  margin-right: 12px;
  flex-shrink: 0;
}
.chapter-section .chapter-item .chapter-content {
  flex: 1;
  min-width: 0;
}
.chapter-section .chapter-item .chapter-content .chapter-name {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.chapter-section .chapter-item .chapter-content .chapter-meta .duration {
  font-size: 11px;
  color: #999;
}
.chapter-section .chapter-item .chapter-status {
  flex-shrink: 0;
  margin-left: 8px;
  align-self: center;
}
.chapter-section .chapter-item .chapter-status .lock-icon {
  color: #999;
  font-size: 16px;
}
.chapter-section .chapter-item .chapter-status .play-icon {
  font-size: 16px;
}
.chapter-section .no-chapters {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 40px 20px;
}
.divider {
  flex-shrink: 0;
  /* 防止被压缩 */
  height: 2px;
  width: 300px;
  background-color: #f0f0f0;
  margin-left: 25px;
  margin-top: 5px;
  margin-bottom: 20px;
}
.divider {
  margin-bottom: 2px;
}
