<template>
  <div class="content-container">
    <div class="areadetail-container-content">
      <div class="areadetail-and-reference">
        <div class="areadetail">
          <!-- <div
            class="klgdetail-video-list"
            v-if="props.klgDetail.projectList && props.klgDetail.projectList.length > 0"
          >
            <div
              class="klgdetail-video-item"
              v-for="item in props.klgDetail.projectList"
              :key="item.uniqueCode"
            >
              <template v-if="item.userCoverPic && item.uniqueCode">
                <img :src="item.userCoverPic" style="width: 128px; height: 67px" />
              </template>
            </div>
          </div> -->
          <div class="areadetail-content">
            <!-- <div
              class="klgdetail-synonym"
              v-if="props.areaDetail.sysTitles && props.areaDetail.sysTitles.length > 0"
            >
              同义词:{{ props.klgDetail.sysTitles }}
            </div> -->
            <p class="areadetail-content-p" v-html="props.areaDetail.description"></p>
          </div>
        </div>
      </div>
      <!-- <MapContent :areaDetail="props.areaDetail" :klgCode="props.areaCode"></MapContent> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import ClassicEditor from '@/components/editors/Veditor.vue';
import { defineProps } from 'vue';
import KsgMap from 'ksg-map';
import type { GlobalConfig, OriginalData, AreaData, PointData } from 'ksg-map/dist/types';
import { getLevelData } from '@/apis/ksgmap';
import { getklgdetailApi, saveFeedBackApi, getPreklglistApi } from '@/apis/klgdetail';
import { ElSwitch } from 'element-plus';
import MapContent from './MapContent.vue';

const props = defineProps<{
  areaDetail: {
    title: string;
    areaCode: string;
    authorName: string;
    createTime: string;
    description: string;
  };
  areaCode: string;
}>();

onMounted(async () => {
  console.log('666', props.areaDetail);
  //InitData();
  // TODO: prjId 之后改为 uniqueCode
  //   const points = (await getLevelData([props.klgCode], 0, 5)).data;
  //   await ksgMap.value!.ready;
  //   data.value = {
  //     topAreas: [],
  //     points,
  //     areas: [],
  //     focuses: []
  //   };
  //   ksgMap.value?.reloadData();
});
</script>

<style scoped>
.content-container {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  .areadetail-container-content {
    margin-top: 20px;
    display: flex;
    flex-direction: row;

    .areadetail-and-reference {
      margin-right: 10px;
      width: 781px;

      .areadetail {
        .areadetail-name {
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 9px;
        }

        .areadetail-more {
          font-size: 14px;
          font-weight: 400;
          color: #797979;

          :is(span) {
            margin-right: 83px;
          }
        }

        .areadetail-video {
          margin-top: 5px;
          display: flex;
          flex-direction: row;

          .areadetail-video-item {
            width: 128px;
            height: 67px;
            margin-right: 10px;
          }
        }

        .areadetail-content {
          /* margin-top: 10px; */
          /* padding: 10px; */
          min-height: 322px;
          /* border: 1px solid #eee; */

          .areadetail-synonym {
            font-size: 12px;
            font-weight: 400;
            margin-bottom: 13px;
            color: #333333;
          }

          .areadetail-content-p {
            white-space: pre-wrap;
            text-indent: 2em;
            font-size: 14px;
          }
        }
      }

      .reference {
        margin-top: 20px;

        .reference-title {
          font-size: 16px;
          font-weight: 700;
          color: #333333;
          margin-bottom: 19px;
        }

        .reference-list {
          .reference-list-item {
            width: 781px;
            height: 33px;
            /* background-color: rgba(242, 242, 242, 0.996); */
            font-size: 14px;
            font-weight: 400;
            line-height: 33px;
            margin-bottom: 5px;
            padding-left: 10px;

            :is(span) {
              margin-right: 10px;
            }
          }
        }
      }
    }

    .preklg-container {
      flex: 1;

      .preklg {
        .preklg-title-left {
          font-size: 16px;
          font-weight: 700;
        }

        .preklg-title-right {
          font-size: 12px;
          color: #aaaaaa;
        }

        .preklg-list {
          width: 589px;

          .clickedClass {
            background-color: #1973cb;
            color: #fff;
          }

          .defaultClass {
            background-color: #ffff;
          }

          .preklg-list-item {
            height: 32px;
            border: 1px solid #eee;
            margin-top: 5px;
            line-height: 32px;
            text-indent: 5px;
            display: flex;
            justify-content: space-between;
            padding-right: 15px;

            .item1 {
              font-size: 12px;
              font-weight: 700;
            }

            .item2 {
              font-size: 12px;
              font-weight: 400;
            }
          }

          .pagination-block {
            display: flex;
            width: 100%;
            flex-direction: row;
            justify-content: center;
            margin-top: 5px;
          }
        }
      }
    }
  }

  .areadetail-container-feedback {
    width: 100%;
    height: 46px;
    background-color: rgba(0, 85, 121, 0.13);
    background-color: #d6e9f6;
    line-height: 46px;
    text-indent: 10px;
    color: #1973cb;
    margin-top: 10px;
    margin-bottom: 20px;

    .feedback-style1 {
      font-size: 18px;
      font-weight: bold;
    }

    .feedback-style2 {
      font-size: 12px;
    }

    .hoverStyle:hover {
      cursor: pointer;
    }
  }

  .el-dialog {
    .dialog-header {
      font-size: 18px;
      font-weight: 700;
      color: #1973cb;
    }

    .dialog-feedback-content {
      width: 480px;
      height: 337px;
      margin-top: 8px;
    }

    .dialog-title {
      font-size: 16px;
      font-weight: 700;
      color: #333333;
      margin-bottom: 18px;
    }
    .dialog-footer {
      display: flex;
      justify-content: center;
    }

    .dialog-button {
      width: 160px;
      height: 43.2px;
      font-size: 14px;
      margin-right: 21px;
    }
  }
}
</style>
