<template>
  <div class="warpper-free">
    <el-dialog v-model="model" class="dg" :show-close="false" width="526px" top="40vh">
      <template #header>
        <div class="dg-header">
          <h1>资料购买</h1>
          <img
            @click="model = !model"
            style="width: 16px; height: 16px; cursor: pointer"
            src="@/assets/images/prjlearn/close.svg"
            alt=""
          />
        </div>
      </template>
      <div class="content" v-if="props.isVip">
        购买 <span v-html="info.title" class="drop-line"></span> 学习资料，学习周期为
        {{ props.selectVipGood.studyTime }} 天，支付价格
        {{ props.selectVipGood.actualPaidAmount }}
        元，购买后，学习资料会在我的“正在学”页面找到。
      </div>
      <div class="content" v-else-if="!props.isVip && !props.isRecommend">
        购买 <span v-html="info.title" class="drop-line"></span> 学习资料，学习周期为
        {{ info.priceList[0].studyTime }} 天，支付价格
        {{ info.priceList[0].actualPaidAmount }}
        元，购买后，学习资料会在我的“正在学”页面找到。
      </div>
      <div class="content" v-else-if="!props.isVip && props.isRecommend">
        购买
        <span v-html="props.selectedRecommend.title" class="drop-line"></span> 学习资料，学习周期为
        {{ props.selectedRecommend.studyTime }} 天，支付价格
        {{ props.selectedRecommend.actualPaidAmount }}
        元，购买后，学习资料会在我的“正在学”页面找到。
      </div>
      <template #footer>
        <div class="foot-btns">
          <CmpButton class="btn" type="info" @click="model = !model">我知道了</CmpButton>
          <CmpButton class="btn" type="primary" @click="handleBuy"> 确认购买 </CmpButton>
        </div>
      </template>
    </el-dialog>
    <SuccessAnimation v-model="paySuccess"></SuccessAnimation>
  </div>
</template>

<script setup lang="ts">
import SuccessAnimation from './SuccessAnimation.vue';
import { useProjectStore } from '@/stores/project';
import { BuyStatus } from '@/types/goods';
import { getPaymentcodeApi } from '@/apis/payment';
import { getPrjIntroduceApi } from '@/apis/case';

const props = defineProps(['isVip', 'selectVipGood', 'isRecommend', 'selectedRecommend']);

const route = useRoute();
const spuId = route.query.spuId as string;

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);

const model = defineModel();
const paySuccess = ref(false);
const handleBuy = async () => {
  let skuId = '';
  if (props.isVip) {
    skuId = props.selectVipGood.skuId;
  } else if (!props.isVip && !props.isRecommend) {
    skuId = info.value.priceList[0].skuId;
  } else if (!props.isVip && props.isRecommend) {
    skuId = props.selectedRecommend.skuId;
  }
  const res = await getPaymentcodeApi({
    skuId: skuId,
    channel: 0
  });
  if (res.success) {
    model.value = false;
    paySuccess.value = true;
    const res = await getPrjIntroduceApi({
      spuId: route.query.spuId as string
    });
    console.log(res.data);
    projectStore.setPrjInfo(res.data);
  }
};
</script>

<style scoped lang="less">
.dg {
  height: 242px;
  .dg-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #f2f2f2;
    padding-bottom: 15px;

    h1 {
      font-size: 18px;
      font-weight: 700;
      color: var(--color-theme-project);
    }
  }

  .content {
    width: 100%;
    white-space: pre-wrap;
    .drop-line {
      :deep(p) {
        display: inline;
      }
    }
  }

  .foot-btns {
    width: 100%;
    display: flex;
    // flex-direction: row;
    justify-content: center;

    .btn {
      width: 160px;
      height: 43px;

      &:nth-child(2) {
        margin-left: 20px;
      }
    }
  }
}
.warpper-free {
  &:deep(.el-dialog) {
    border-radius: 5px;
  }
}
</style>
