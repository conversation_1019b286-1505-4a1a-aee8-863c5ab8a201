<template>
  <div class="shop-container">
    <div class="shop-menu">
      <router-link
        to="/shoppinglist/order/prjorder"
        class="shop-menu-item"
        :class="menuIndex == 1 ? 'pickStyle' : 'defaultStyle'"
        @click="pickprg"
        >资料订单</router-link
      >
      <router-link
        to="/shoppinglist/order/viporder"
        class="shop-menu-item"
        :class="menuIndex == 2 ? 'pickStyle' : 'defaultStyle'"
        @click="pickvip"
        >会员订单</router-link
      >
    </div>
    <div class="shop-list">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter();
const ready = ref(false);
let menuIndex = ref(1);
function pickprg() {
  menuIndex.value = 1;
}
function pickvip() {
  menuIndex.value = 2;
}

watch(
  menuIndex,
  () => {
    menuIndex.value == 1
      ? router.push('/shoppinglist/order/prjorder')
      : router.push('/shoppinglist/order/viporder');
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
@width: 1400px;
@menuwidth: 300px;
@menuheight: 50px;
@fontfamily: var(--title-family);
@fontsize: 16px;
@bgdeepcolor: var(--color-theme-project);
@bglightcolor: #fff;
@bghovercolor: var(--color-second);
@margin: 10px;

.router-link-active {
  text-decoration: none;
  color: #fff;
}

a {
  text-decoration: none;
  color: #fff;
}

.hoverstyle {
  background-color: @bghovercolor;
  color: @bgdeepcolor;
  border: 1px solid @bgdeepcolor;
}

.shop-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-top: 2 * @margin;

  .shop-menu {
    .shop-menu-item {
      height: @menuheight;
      width: @menuwidth;
      font-family: @fontfamily;
      border: 1px solid var(--color-theme-project);
      font-size: @fontsize;
      margin-bottom: @margin;
      display: flex;
      align-items: center;
      padding-left: @margin;
      margin-left: -80px;
      cursor: pointer;
    }

    .pickStyle {
      background-color: @bgdeepcolor;
      color: #fff;
    }

    .defaultStyle {
      background-color: @bglightcolor;
      border-color: rgba(220, 223, 230, 1);
      color: var(--color-black);
    }

    .shop-menu-item:hover {
      .hoverstyle();
    }
  }
}
</style>
