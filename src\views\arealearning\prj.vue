<template>
  <div class="LearnContainer">
    <div class="catalog">
      <PrjInfo
        v-if="ready"
        :buy-status="buyStatus"
        :area-info="areaInfo"
        :back-path="backPath"
        :back-query="backQuery"
        :course-list="courseList"
        :current-course-id="spuId"
        @chapter-change="handleChapterChange"
        @course-change="handleCourseChange"
        @title-change="handleTitleChange"
      />
    </div>
    <div v-if="ready" class="main-content">
      <PrjManuWrapper
        v-if="prjForm == PrjForm.draft"
        ref="pay"
        :key="`manu-${spuId}-${prjForm}`"
        :payDialogVisible="payDialogVisible"
      />
      <PrjVideoWrapper
        v-else
        ref="pay"
        :key="`video-${spuId}-${prjForm}`"
        :payDialogVisible="payDialogVisible"
      />
    </div>

    <!-- 目前只有会员商品使用这个组件  -->
    <PayDialog
      v-model="payDialogVisible"
      v-if="ready && isVip"
      :spuId="info.spuId"
      :selectedSkuId="selectedSkuId"
      :isUserAdd="false"
      @paySuccess="handleBuy"
    />

    <!-- 从介绍页直接打开购买页的一定不是会员商品 会员商品需要先选择 -->
    <QRcodeDialog
      v-model="payDialogVisible"
      :skuId="skuId"
      v-if="ready && !isVip"
      @paySuccess="handleBuy"
    />
  </div>
</template>

<script setup lang="ts">
import { PrjForm, PrjType } from '@/types/project';
import PrjInfo from './components/PrjInfo.vue';
import type { PrjinfoItf } from '@/types/learning';
import { BuyStatus, GoodsType, PermissionCode } from '@/types/goods';
import { getPartProApi, getPrjDetailApi } from '@/apis/learning';
import PrjManuWrapper from './components/PrjManuWrapper.vue';
import PrjVideoWrapper from './components/PrjVideoWrapper.vue';
import { useLearningStore } from '@/stores/learning';
import { useDraftWordStoreV2 } from '@/stores/draftWordV2';
import { useVideoWordStoreV2 } from '@/stores/videoWordV2';
import OutPermission from '@/components/OutPermission.vue';
import { useProjectStore } from '@/stores/project';
import { getPrjIntroduceApi, getVipIntroduceApi } from '@/apis/case';
import { getAreaGoods } from '@/apis/arealearn';

const learningStore = useLearningStore();
const draftWordStore = useDraftWordStoreV2();
const videoWordStore = useVideoWordStoreV2();

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);

const isVip = ref(false); //是否是会员商品
const skuId = ref('');
const selectedSkuId = ref('');
const buyStatus = ref(false); // 是否已购买
const pay = ref(null);
const payDialogVisible = ref(false);

const ready = ref(false);
const route = useRoute();
const router = useRouter();
// 使用响应式的 spuId，确保路由变化时能及时更新
const spuId = computed(() => route.query.spuId as string);
const chapterId = computed(() => route.query.chapterId as string);
const NoPermisson = ref(false);
const errorInfo = ref('');

// 声明响应式变量 - 需要在函数定义之前声明
const prjInfo = ref<PrjinfoItf>({});
const prjForm = ref<PrjForm>();
const prjType = ref<PrjType>();

// 领域信息
const areaInfo = ref<{
  title?: string;
  editorName?: string;
  editorPic?: string;
  masteredKlgCount?: number;
  fullyMasteredKlgCount?: number;
  klgCount?: number;
}>({});

// 返回路径信息
const backPath = ref('/arealearning');
const backQuery = ref<Record<string, any>>({});

// 课程列表数据
const courseList = ref<
  Array<{
    spuId: string;
    title: string;
    coverPic?: string;
    userName?: string;
    userCoverPic?: string;
  }>
>([]);

// 当前选中项标题状态
const currentItemTitle = ref('');

// 课程切换加载状态
const courseChanging = ref(false);

// 数据状态管理 - 参考learning页面模式
const dataLoaded = ref(false);
const questionListCache = ref<Map<string, any>>(new Map());

const handleBuy = async () => {
  // 获取最新的项目信息
  const res = await getPrjIntroduceApi({
    spuId: route.query.spuId as string
  });

  // 更新项目store
  projectStore.setPrjInfo(res.data);
  projectStore.setSpuId(spuId.value);

  // 更新本地状态 - 这里是关键，只更新权限相关状态
  if (res.data.buyStatus == BuyStatus.bought) {
    buyStatus.value = true;
  } else {
    buyStatus.value = false;
  }

  // 更新provide的prjInfo，让PrjInfo组件能获取到最新信息
  prjInfo.value = { ...prjInfo.value, ...res.data };

  // 显示购买成功提示
  ElMessage.success('购买成功！您现在可以访问所有章节内容了');
};

// 处理章节切换
const handleChapterChange = async (chapterId: number, preview: boolean) => {
  console.log('父组件接收到章节切换事件:', chapterId, preview);

  // 如果是预览章节或者已购买，允许切换
  if (preview || buyStatus.value) {
    // 只更新路由参数，让子组件通过路由变化监听器处理数据更新
    router.replace({
      query: {
        ...route.query,
        chapterId: chapterId
      }
    });
  } else {
    // 没有权限，显示购买对话框
    payDialogVisible.value = true;
  }
};

// 处理课程切换 - 简化版本，参考learning模块
const handleCourseChange = (courseId: string) => {
  console.log('父组件接收到课程切换事件:', courseId);

  // 如果是同一个课程，不做任何操作
  if (courseId === spuId.value) {
    return;
  }

  // 只更新路由参数，让子组件通过路由变化监听器处理数据更新
  router.replace({
    query: {
      ...route.query,
      spuId: courseId
    }
  });
};

// 处理标题变化
const handleTitleChange = (title: string) => {
  console.log('父组件接收到标题变化事件:', title);
  currentItemTitle.value = title;
};

// 统一的问题列表获取函数，避免重复调用
const getQuestionListForChapter = async (uniqueCode: string, chapterId: string) => {
  const cacheKey = `${uniqueCode}-${chapterId}`;

  // 检查缓存
  if (questionListCache.value.has(cacheKey)) {
    console.log('使用缓存的问题列表数据:', cacheKey);
    return questionListCache.value.get(cacheKey);
  }

  try {
    console.log('获取问题列表数据:', cacheKey);
    const { getQuestionListApi } = await import('@/apis/learning');
    const res = await getQuestionListApi(uniqueCode, chapterId);

    // 缓存结果
    questionListCache.value.set(cacheKey, res.data);
    return res.data;
  } catch (error) {
    console.error('获取问题列表失败:', error);
    return [];
  }
};

// 监听项目类型变化，确保组件切换时正确初始化
watch(
  () => prjForm.value,
  (newPrjForm, oldPrjForm) => {
    if (newPrjForm !== oldPrjForm && oldPrjForm !== undefined) {
      console.log('项目类型发生变化:', oldPrjForm, '->', newPrjForm);
      // 项目类型变化时，通过key的变化强制重新创建组件
      // 这已经通过模板中的key实现了
    }
  }
);

provide('prjInfo', prjInfo);
provide('prjForm', prjForm);
provide('prjType', prjType);
provide('buyStatus', buyStatus);
provide('currentItemTitle', currentItemTitle);
provide('questionListCache', questionListCache);
provide('dataLoaded', dataLoaded);
provide('getQuestionListForChapter', getQuestionListForChapter);
const useWideScreen = inject('useWideScreen') as Ref<boolean>;
// 课程切换已经在 handleCourseChange 中实现，不需要单独的 reloadProjectData 函数

// 获取领域信息和课程列表的函数
const loadAreaInfoAndCourseList = async () => {
  const areaSpuId = (route.query.areaSpuId as string) || sessionStorage.getItem('areaSpuId');
  if (areaSpuId) {
    try {
      const areaRes = await getPrjIntroduceApi({ spuId: areaSpuId });
      if (areaRes.data) {
        areaInfo.value = {
          title: areaRes.data.title,
          editorName: areaRes.data.editorName,
          editorPic: areaRes.data.editorPic,
          masteredKlgCount: areaRes.data.masteredKlgCount || 0,
          fullyMasteredKlgCount: (areaRes.data as any).fullyMasteredKlgCount || 0,
          klgCount: areaRes.data.klgCount || 0
        };
        // 设置返回路径
        backQuery.value = { spuId: areaSpuId };

        // 获取课程列表数据
        try {
          const courseRes = await getAreaGoods(areaSpuId, 0, 10);
          if (courseRes && courseRes.data && (courseRes.data as any).records) {
            courseList.value = (courseRes.data as any).records.map((record: any) => ({
              spuId: record.spuId,
              title: record.title || '未命名课程',
              coverPic: record.coverPic,
              userName: record.userName || '未知作者',
              userCoverPic: record.userCoverPic
            }));
            console.log('课程列表已更新:', courseList.value);
          }
        } catch (error) {
          console.error('获取课程列表失败:', error);
        }
      }
    } catch (error) {
      console.error('获取领域信息失败:', error);
    }
  }
};

onMounted(async () => {
  // 获取领域信息和课程列表
  await loadAreaInfoAndCourseList();

  // 并行获取所有必要的数据，避免串行等待
  const [res, res2, res3] = await Promise.all([
    getPartProApi(spuId.value),
    getPrjDetailApi(spuId.value, chapterId.value),
    (typeof spuId.value === 'string' && spuId.value.startsWith('V')
      ? getVipIntroduceApi
      : getPrjIntroduceApi)({ spuId: spuId.value })
  ]);

  // 处理项目基本信息
  prjInfo.value = res.data.list[0];
  prjForm.value = prjInfo.value.prjForm;
  prjType.value = prjInfo.value.prjType;

  // 重置store
  if (prjForm.value == PrjForm.draft) {
    draftWordStore.$reset();
  } else {
    videoWordStore.$reset();
  }

  // 检查权限
  if (String(res2.code) === PermissionCode.nobuy) {
    NoPermisson.value = true;
    errorInfo.value = res2.message || '获取项目详情失败';
    ready.value = true;
    return; // 如果没有权限，直接返回，不执行后续代码
  }

  // 处理项目介绍信息
  if (!res3.data) {
    ElMessage.error('项目不存在');
    router.push({ path: '/home' });
    return;
  }

  // 更新 projectStore 和 prjInfo
  projectStore.setPrjInfo(res3.data);
  projectStore.setSpuId(spuId.value);
  prjInfo.value = { ...prjInfo.value, ...res3.data };

  // 只有在有权限的情况下才执行
  if (res2.data) {
    learningStore.setInfo(res2.data);

    // 设置初始的当前选中项标题 - 应该是项目标题而不是章节标题
    const currentCourse = courseList.value.find((course) => course.spuId === spuId.value);
    if (currentCourse) {
      currentItemTitle.value = currentCourse.title;
      console.log('初始化时设置项目标题:', currentCourse.title);
    } else if (res3.data && (res3.data as any).title) {
      // 如果没有找到课程信息，使用项目介绍中的标题
      currentItemTitle.value = (res3.data as any).title;
      console.log('初始化时设置项目介绍标题:', (res3.data as any).title);
    }

    // 安全地更新路由参数
    const validChapter = res2.data.chapterList[res2.data.validIndex];
    if (validChapter && validChapter.chapterId) {
      router.replace({
        query: {
          ...route.query,
          chapterId: validChapter.chapterId
        }
      });
    }
  }

  // 设置其他状态
  skuId.value = info.value.priceList[0].skuId;
  if (info.value.goodsType == GoodsType.vip) {
    isVip.value = true;
  } else {
    isVip.value = false;
  }

  ready.value = true;
  if (info.value.buyStatus == BuyStatus.bought) {
    buyStatus.value = true;
  } else {
    buyStatus.value = false;
  }

  if (!buyStatus.value && isVip.value) {
    selectedSkuId.value = info.value.priceList[0].skuId;
  }
});

// 监听路由参数变化，重新加载课程列表
watch(
  () => route.query.areaSpuId,
  async (newAreaSpuId) => {
    if (newAreaSpuId) {
      console.log('检测到领域spuId变化，重新加载课程列表:', newAreaSpuId);
      await loadAreaInfoAndCourseList();
    }
  }
);

// 监听spuId变化，重新获取项目信息并更新prjForm
watch(
  () => route.query.spuId,
  async (newSpuId, oldSpuId) => {
    if (newSpuId && newSpuId !== oldSpuId && ready.value) {
      ready.value = false;
      console.log('父组件检测到spuId变化，重新获取项目信息:', newSpuId);

      try {
        // 重新获取项目基本信息
        const res = await getPartProApi(newSpuId as string);
        prjInfo.value = res.data.list[0];
        const newPrjForm = prjInfo.value.prjForm;
        const newPrjType = prjInfo.value.prjType;

        console.log('项目类型变化:', prjForm.value, '->', newPrjForm);

        // 更新项目类型
        prjForm.value = newPrjForm;
        prjType.value = newPrjType;

        // 重置对应的store
        if (newPrjForm == PrjForm.draft) {
          draftWordStore.$reset();
        } else {
          videoWordStore.$reset();
        }

        // 重新获取项目介绍信息
        const res3 = await (
          typeof newSpuId === 'string' && newSpuId.startsWith('V')
            ? getVipIntroduceApi
            : getPrjIntroduceApi
        )({ spuId: newSpuId as string });

        if (res3.data) {
          // 更新 projectStore 和 prjInfo
          projectStore.setPrjInfo(res3.data);
          projectStore.setSpuId(newSpuId as string);
          prjInfo.value = { ...prjInfo.value, ...res3.data };

          // 更新当前选中项标题
          const currentCourse = courseList.value.find((course) => course.spuId === newSpuId);
          if (currentCourse) {
            currentItemTitle.value = currentCourse.title;
            console.log('更新项目标题:', currentCourse.title);
          } else if (res3.data && (res3.data as any).title) {
            currentItemTitle.value = (res3.data as any).title;
            console.log('更新项目介绍标题:', (res3.data as any).title);
          }

          // 更新购买状态
          if ((res3.data as any).buyStatus == BuyStatus.bought) {
            buyStatus.value = true;
          } else {
            buyStatus.value = false;
          }
        }

        console.log('✅ 父组件项目信息更新完成');
      } catch (error) {
        console.error('❌ 父组件项目信息更新失败:', error);
      }

      ready.value = true;
    }
  }
);

watch(
  () => (pay.value as any)?.payDialogVisible,
  () => {
    payDialogVisible.value = (pay.value as any)?.payDialogVisible;
  }
);

onBeforeUnmount(() => {
  useWideScreen.value = false;
});
</script>

<style scoped lang="less">
.LearnContainer {
  display: flex;
  width: 100%;
  height: calc(100vh - 70px); /* 减去Header的70px高度 */
  overflow: hidden; /* 禁止页面级滚动 */
}

// 统一滚动条样式混合

// 左侧栏样式 - catalog
.catalog {
  width: 345px;
  background-color: #ffffff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}

// 中间主内容区样式 - main-content
.main-content {
  flex: 1;
  overflow: hidden; /* 移除滚动条，让内部组件处理滚动 */
  padding: 0;
  display: flex;
  flex-direction: column;
}
</style>
