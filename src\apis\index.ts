import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig } from 'axios';
import { toLogin } from '@/utils/gopage';
import { convertMathFormulas } from '@/utils/latexUtils';
import he from 'he';
import DOMPurify from 'dompurify';
import { decodeHTML } from 'entities';

type LoadingInstanceType = ReturnType<typeof ElLoading.service>;
let loadingInstance: LoadingInstanceType | null = null;
let requestNum = 0;
let error_flag = false;
declare module 'axios' {
  export interface AxiosRequestConfig {
    loading?: boolean;
  }
}

export const createAxiosByinterceptors = (config?: AxiosRequestConfig): AxiosInstance => {
  const instance = axios.create({
    timeout: 10000, //超时配置
    baseURL: '',
    // withCredentials: true,  //跨域携带cookie
    ...config // 自定义配置覆盖基本配置
  });
  const addLoading = () => {
    // 增加loading 如果pending请求数量等于1，弹出loading, 防止重复弹出
    requestNum++;
    if (requestNum == 1) {
      // loadingInstance = ElLoading.service({
      //   text: '正在努力加载中....',
      //   background: 'rgba(0, 0, 0, 0)'
      // });
    }
  };

  const cancelLoading = () => {
    // 取消loading 如果pending请求数量等于0，关闭loading
    requestNum--;
    if (requestNum == 0) loadingInstance?.close();
  };

  // 添加请求拦截器
  instance.interceptors.request.use(
    function (config: any) {
      // 在发送请求之前做些什么
      const { loading = true } = config;
      if (loading) addLoading();
      return config;
    },
    function (error) {
      // 对请求错误做些什么
      return Promise.reject(error);
    }
  );

  // 添加响应拦截器
  instance.interceptors.response.use(
    function (response) {
      // @ts-ignore
      const { loading = true } = response.config;
      // 对响应数据做点什么
      if (loading) cancelLoading();

      dfs(response.data);
      return response.data;
    },
    function (error) {
      // 对响应错误做点什么
      const { loading = true } = error.config;
      if (loading) cancelLoading();
      if (!error_flag) {
        error_flag = true;
        switchErrorStatus(error);
      }
      return Promise.reject(error);
    }
  );
  return instance;
};

const dfs = (node: any) => {
  for (const key in node) {
    if (typeof node[key] === 'string') {
      node[key] = convertMathFormulas(node[key]);
    } else if (typeof node[key] === 'object') {
      dfs(node[key]);
    }
  }
};

const switchErrorStatus = (error: any) => {
  const url = error.response.headers['x-redirect-url'];
  const status = error.status ?? error.response.status;
  switch (status) {
    case 302:
    case 403:
      console.log('error', error);
      if (error.config.url === '/user/info/query' && error.response.code === 'A020100') {
        console.log('允许访问未登录页面');
        break;
      } else {
        console.log('跳转啊！');
        window.open(url, '_self');
      }
      break;
    default:
      ElMessage.error(error.response.data.message || '服务端异常');
  }
  // 避免以下弹出很多提示框
  setTimeout(() => (error_flag = false), 1000);
};

const escapeScriptTags = (htmlString: string): string => {
  // Regex to match <script> tags without any attributes
  const scriptTagRegex = /<script>(.*?)<\/script>/gis;

  return htmlString.replace(scriptTagRegex, (match, content) => {
    // Encode the entire matched script tag
    console.log('转移：', he.encode(match));
    return he.encode(match);
  });
};

const axioInstance: AxiosInstance = createAxiosByinterceptors({
  baseURL: import.meta.env.VITE_APP_CHAMP_API_BASE
}); // 请求CRUD

export interface ResponseData<T> {
  data: T;
  success: boolean;
  message: string;
  code: number;
}

export const http = {
  request: async function <T>(config: any): Promise<ResponseData<T>> {
    return axioInstance.request(config);
  },
  get: async function <T>(url: string, params?: any, loading?: boolean): Promise<ResponseData<T>> {
    return axioInstance.get(url, {
      params,
      loading
    });
  },
  post: async function <T>(url: string, data?: any, loading?: boolean): Promise<ResponseData<T>> {
    return axioInstance.post(url, data, {
      loading
    });
  },
  put: async function <T>(url: string, data?: any): Promise<ResponseData<T>> {
    return axioInstance.put(url, data);
  },
  delete: async function <T>(url: string, data?: any): Promise<ResponseData<T>> {
    return axioInstance.delete(url, data);
  },
  patch: async function <T>(url: string, data?: any): Promise<ResponseData<T>> {
    return axioInstance.patch(url, data);
  }
};

let http_info: AxiosInstance;
let http_auth: AxiosInstance;
(function () {
  http_info = createAxiosByinterceptors({
    baseURL: import.meta.env.VITE_APP_INFO_API_BASE
  });
  http_auth = createAxiosByinterceptors({
    baseURL: import.meta.env.VITE_APP_AUTH_API_BASE
  });
})();

export { http_auth, http_info };
