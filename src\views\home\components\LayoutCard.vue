<template>
  <div class="card">
    <h3 class="title">{{ title }}</h3>
    <h1 class="word">{{ word }}</h1>
    <h2 class="tip">{{ tip }}</h2>
    <div class="card-body">
      <!-- 分类展示估计以后用不到了 -->
      <div class="card-body-title" v-if="prjFormList && prjFormList.length > 0">
        <!-- 视频，文稿，领域 -->
        <span
          v-for="(type, idx) in prjFormList"
          :key="'_' + idx"
          class="title-text"
          @click="changeTitleFn(type)"
          :class="{ active: type == activeType }"
          >{{ mapFn(type) }}
        </span>
      </div>
      <div class="card-body-list">
        <el-skeleton :loading="!ready" animated>
          <template #default>
            <template v-if="ready">
              <BigGoodsCard
                style="grid-column-start: 1; grid-column-end: 3"
                v-if="big"
                v-bind="list[0]"
              ></BigGoodsCard>
              <GoodsCard v-for="(video, idx) in list.slice(start, end)" :key="idx + '_'" v-bind="video">
              </GoodsCard>
            </template>
          </template>
          <template #template>
            <div class="skeleton-list">
              <div v-if="big" class="big-skeleton">
                <el-skeleton-item variant="image" style="width: 630px; height: 215px" />
                <div style="padding: 16px;">
                  <el-skeleton-item variant="p" style="width: 60%" />
                  <el-skeleton-item variant="p" style="width: 100%" />
                  <el-skeleton-item variant="p" style="width: 100%" />
                  <el-skeleton-item variant="p" style="width: 40%" />
                  <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 12px;">
                    <el-skeleton-item variant="text" style="width: 80px" />
                    <el-skeleton-item variant="text" style="width: 60px" />
                  </div>
                </div>
              </div>
              <div v-for="i in (big ? 6 : 8)" :key="'_' + i" class="small-skeleton">
                <el-skeleton-item variant="image" style="width: 300px; height: 215px" />
                <div style="padding: 12px;">
                  <el-skeleton-item variant="p" style="width: 50%" />
                  <el-skeleton-item variant="p" style="width: 100%" />
                  <el-skeleton-item variant="p" style="width: 100%" />
                  <el-skeleton-item variant="p" style="width: 50%" />
                  <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 8px;">
                    <el-skeleton-item variant="text" style="width: 60px" />
                    <el-skeleton-item variant="text" style="width: 40px" />
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-skeleton>
      </div>
    </div>
    <div class="card-footer">
      <span class="footerBtn" @click="$router.push('/classification')">
        <span class="myicon"></span>
        更多内容
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import BigGoodsCard from './BigGoodsCard.vue';
import GoodsCard from '@/components/GoodsCard.vue';
import { PrjType, PrjForm } from '@/types/project';
import { GoodsType } from '@/types/goods';
interface Props {
  title: string;
  word: string;
  tip: string;
  prjFormList: Array<PrjForm>;
  big: boolean;
  getListApi: Function;
  prjType: PrjType;
}

interface childProps {
  /**
   * 项目封面
   */
  coverPic: string;
  /**
   * 项目表述
   */
  description: string;

  goodsType: GoodsType;
  /**
   * 问题次数
   */
  prjQuestionNumbers?: number;
  /**
   * 学习次数
   */
  prjStudyFrequence?: number;
  /**
   * 学习时长
   */
  studyTime: number;
  //视频时长
  duration: string;
  countWord: string;
  /**
   * 项目标题
   */
  title: string;

  //跳转介绍页需要用到的参数
  // goodsId: number;
  prjType: PrjType;
  /**
   * 项目商品唯一标识
   */
  spuId: string;
  //视频形式还是文稿形式
  prjForm: PrjForm;
  userCoverPic?: string;
  userName?: string;
  //开始学习需要判断的参数
  /**
   * 1表示免费商品，0表示付费商品
   */
  isFree: number;
  buyStatus: number;
  latestChapterId: number;
  [property: string]: any;
}

const props = defineProps<Props>();
const list = ref<Array<childProps>>([]);
const getListApi = props.getListApi;
const mapFn = (engText: PrjForm) => {
  return engText == PrjForm.video ? '视频' : engText == PrjForm.draft ? '文稿' : '领域';
};
const activeType = ref<PrjForm>(PrjForm.video);
const changeTitleFn = (val: PrjForm) => {
  activeType.value = val;
  getList(); // 刷新列表
};
// big图片起始地址为0，1-7
// 不是大图的化 0 - 8
// 先写死
const start = computed(() => (props.big ? 1 : 0));
const end = computed(() => (props.big ? 7 : 8));
// 获取视频列表
const ready = ref(false);
const getList = async () => {
  let res;
  //区别案例+知识讲解与测评 后续需要更改测评接口
  if (props.prjFormList.length != 0) {
    res = await getListApi(props.prjType, activeType.value);
    list.value = res.data.list;
  } else {
    res = await getListApi(props.prjType);
    list.value = res.data;
  }

  ready.value = true;
};

onMounted(() => {
  getList();
  console.log('activeType', activeType, 'PrjType', props.prjType);
});
</script>

<style scoped lang="less">
@width: 1400px;
@boxPadding: 10px;
@titleFamily: var(--title-family);
@textFamily: var(--text-family);

.card {
  width: @width;
  padding: @boxPadding;
  margin: 24px auto;
  border-radius: 2px;
  box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
  background-color: #ffffff;
  padding-left: 25px;
  padding-top: 18px;

  .title {
    font-size: 16px;
    font-weight: 600;
    font-family: var(--title-family);
    color: var(--color-black);
  }

  .word {
    font-size: 28px;
    font-weight: 600;
    color: var(--color-black);
    font-family: @textFamily;
    line-height: 1.8;
  }

  .tip {
    font-size: 14px;
    font-weight: 400;
    color: var(--color-deep);
    margin-bottom: 10px;
    font-family: @textFamily;
  }

  .card-body {
    padding-left: 24px;
    padding-right: 40px;

    .card-body-title {
      height: 43px;
      line-height: 43px;
      background-color: #f2f2f2;
      border-radius: 2px;
      margin: 27px 0 10px;
      padding-left: 20px;

      .title-text {
        margin-right: 66px;
        font-size: 16px;
        font-weight: 700;
        font-family: var(--title-family);
        color: var(--color-black);
        cursor: pointer;

        &.active {
          color: var(--color-theme-project);
        }

        &:hover {
          color: var(--color-theme-project);
        }
      }
    }

    .card-body-list {
      display: grid;
      grid-template-columns: repeat(4, 300px); // 重复4次300px
      grid-column-gap: 30px; // 列之间的距离 34px
      grid-row-gap: 20px; // 行之间的距离 10px
    }

    .skeleton-list {
      display: grid;
      grid-template-columns: repeat(4, 300px);
      grid-column-gap: 30px;
      grid-row-gap: 20px;
    }

    .big-skeleton {
      grid-column-start: 1;
      grid-column-end: 3;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .small-skeleton {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .card-footer {
    height: 60px;
    line-height: 60px;
    display: flex;
    text-align: center;
    justify-content: center;
    font-family: var(--text-family);
    font-size: 14px;
    font-weight: 400;
    color: var(--color-theme-project);

    .footerBtn {
      width: 90px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .myicon {
        width: 14px;
        height: 12px;
        margin-right: 5px;
        background-image: url('@/assets/images/home/<USER>');
      }
      &:hover {
        font-weight: bold;
      }
    }
  }
}
</style>
