/**
 * 问题导航管理工具类
 * 用于管理问题列表的缓存、索引和导航逻辑
 */

export interface QuestionItem {
  questionId: number;
  keyword: string;
  questionType: string;
  questionWeight?: string;
  questionNecessity?: string;
  learned?: boolean;
  canDelete?: boolean;
}

export interface ChapterItem {
  chapterId: number;
  chapterName: string;
  preview?: boolean;
}

export interface FlatQuestion extends QuestionItem {
  chapterId: string;
  chapterIndex: number;
  questionIndex: number;
}

export interface QuestionPosition {
  chapterIndex: number;
  questionIndex: number;
  totalChapters: number;
  totalQuestionsInChapter: number;
}

export interface NavigationResult {
  type: 'question' | 'project-detail';
  questionId?: string;
  chapterId?: string;
}

export class QuestionNavigator {
  private chapterQuestionMap: Map<string, QuestionItem[]> = new Map();
  private flatQuestionList: FlatQuestion[] = [];
  private chapterList: ChapterItem[] = [];
  private currentPosition: QuestionPosition | null = null;

  /**
   * 构建导航数据
   * @param chapterList 章节列表
   * @param questionLists 各章节的问题列表数组
   */
  buildNavigationData(chapterList: ChapterItem[], questionLists: QuestionItem[][]) {
    this.chapterList = chapterList;
    this.chapterQuestionMap.clear();
    this.flatQuestionList = [];

    // 构建章节问题映射表
    chapterList.forEach((chapter, chapterIndex) => {
      const chapterId = String(chapter.chapterId);
      const questions = questionLists[chapterIndex] || [];
      
      this.chapterQuestionMap.set(chapterId, questions);

      // 构建扁平化问题列表
      questions.forEach((question, questionIndex) => {
        this.flatQuestionList.push({
          ...question,
          chapterId,
          chapterIndex,
          questionIndex
        });
      });
    });
  }

  /**
   * 查找当前问题位置
   * @param questionId 问题ID
   * @param chapterId 章节ID
   */
  findCurrentPosition(questionId: string, chapterId: string): QuestionPosition | null {
    const chapterIndex = this.chapterList.findIndex(
      chapter => String(chapter.chapterId) === chapterId
    );
    
    if (chapterIndex === -1) return null;

    const questions = this.chapterQuestionMap.get(chapterId) || [];
    const questionIndex = questions.findIndex(
      question => String(question.questionId) === questionId
    );

    if (questionIndex === -1) return null;

    this.currentPosition = {
      chapterIndex,
      questionIndex,
      totalChapters: this.chapterList.length,
      totalQuestionsInChapter: questions.length
    };

    return this.currentPosition;
  }

  /**
   * 从缓存中移除问题
   * @param questionId 问题ID
   * @param chapterId 章节ID
   */
  removeQuestion(questionId: string, chapterId: string): boolean {
    const questions = this.chapterQuestionMap.get(chapterId);
    if (!questions) return false;

    const questionIndex = questions.findIndex(
      question => String(question.questionId) === questionId
    );

    if (questionIndex === -1) return false;

    // 从章节问题列表中移除
    questions.splice(questionIndex, 1);

    // 从扁平化列表中移除
    const flatIndex = this.flatQuestionList.findIndex(
      question => String(question.questionId) === questionId && question.chapterId === chapterId
    );
    if (flatIndex !== -1) {
      this.flatQuestionList.splice(flatIndex, 1);
    }

    // 重新构建索引
    this.rebuildIndices();

    return true;
  }

  /**
   * 获取下一个问题
   * @param currentQuestionId 当前问题ID
   * @param currentChapterId 当前章节ID
   */
  getNextQuestion(currentQuestionId: string, currentChapterId: string): NavigationResult {
    const position = this.findCurrentPosition(currentQuestionId, currentChapterId);
    if (!position) {
      return { type: 'project-detail' };
    }

    // 1. 检查当前章节是否还有下一个问题
    const currentChapterQuestions = this.chapterQuestionMap.get(currentChapterId) || [];
    if (position.questionIndex < currentChapterQuestions.length - 1) {
      const nextQuestion = currentChapterQuestions[position.questionIndex + 1];
      return {
        type: 'question',
        questionId: String(nextQuestion.questionId),
        chapterId: currentChapterId
      };
    }

    // 2. 查找下一个有问题的章节
    for (let i = position.chapterIndex + 1; i < this.chapterList.length; i++) {
      const nextChapter = this.chapterList[i];
      const nextChapterQuestions = this.chapterQuestionMap.get(String(nextChapter.chapterId)) || [];

      if (nextChapterQuestions.length > 0) {
        return {
          type: 'question',
          questionId: String(nextChapterQuestions[0].questionId),
          chapterId: String(nextChapter.chapterId)
        };
      }
    }

    // 3. 没有找到任何问题，返回项目详情页
    return { type: 'project-detail' };
  }

  /**
   * 获取删除问题后的下一个问题（在删除前调用）
   * @param questionToDeleteId 要删除的问题ID
   * @param chapterId 章节ID
   */
  getNextQuestionAfterDelete(questionToDeleteId: string, chapterId: string): NavigationResult {
    const position = this.findCurrentPosition(questionToDeleteId, chapterId);
    if (!position) {
      console.warn('找不到要删除的问题位置:', questionToDeleteId, chapterId);
      return { type: 'project-detail' };
    }

    const currentChapterQuestions = this.chapterQuestionMap.get(chapterId) || [];

    // 1. 检查当前章节删除后是否还有问题
    if (currentChapterQuestions.length > 1) {
      // 如果不是最后一个问题，返回下一个问题
      if (position.questionIndex < currentChapterQuestions.length - 1) {
        const nextQuestion = currentChapterQuestions[position.questionIndex + 1];
        return {
          type: 'question',
          questionId: String(nextQuestion.questionId),
          chapterId: chapterId
        };
      }
      // 如果是最后一个问题，返回前一个问题
      else if (position.questionIndex > 0) {
        const prevQuestion = currentChapterQuestions[position.questionIndex - 1];
        return {
          type: 'question',
          questionId: String(prevQuestion.questionId),
          chapterId: chapterId
        };
      }
    }

    // 2. 当前章节删除后没有问题，查找其他章节
    // 先查找后续章节
    for (let i = position.chapterIndex + 1; i < this.chapterList.length; i++) {
      const nextChapter = this.chapterList[i];
      const nextChapterQuestions = this.chapterQuestionMap.get(String(nextChapter.chapterId)) || [];

      if (nextChapterQuestions.length > 0) {
        return {
          type: 'question',
          questionId: String(nextChapterQuestions[0].questionId),
          chapterId: String(nextChapter.chapterId)
        };
      }
    }

    // 3. 查找前面的章节
    for (let i = position.chapterIndex - 1; i >= 0; i--) {
      const prevChapter = this.chapterList[i];
      const prevChapterQuestions = this.chapterQuestionMap.get(String(prevChapter.chapterId)) || [];

      if (prevChapterQuestions.length > 0) {
        // 返回该章节的最后一个问题
        const lastQuestion = prevChapterQuestions[prevChapterQuestions.length - 1];
        return {
          type: 'question',
          questionId: String(lastQuestion.questionId),
          chapterId: String(prevChapter.chapterId)
        };
      }
    }

    // 4. 没有找到任何其他问题，返回项目详情页
    return { type: 'project-detail' };
  }

  /**
   * 检查是否还有剩余问题
   */
  hasRemainingQuestions(): boolean {
    return this.flatQuestionList.length > 0;
  }

  /**
   * 重新构建索引
   */
  private rebuildIndices() {
    this.flatQuestionList = [];
    
    this.chapterList.forEach((chapter, chapterIndex) => {
      const chapterId = String(chapter.chapterId);
      const questions = this.chapterQuestionMap.get(chapterId) || [];
      
      questions.forEach((question, questionIndex) => {
        this.flatQuestionList.push({
          ...question,
          chapterId,
          chapterIndex,
          questionIndex
        });
      });
    });
  }

  /**
   * 获取当前缓存的问题总数
   */
  getTotalQuestionCount(): number {
    return this.flatQuestionList.length;
  }

  /**
   * 获取指定章节的问题数量
   */
  getChapterQuestionCount(chapterId: string): number {
    const questions = this.chapterQuestionMap.get(chapterId) || [];
    return questions.length;
  }


}
