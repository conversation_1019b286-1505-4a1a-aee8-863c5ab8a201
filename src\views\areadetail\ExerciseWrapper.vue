<template>
  <div>
    <div class="qt-detail" @click="handleWord" @mouseover="handleHover" @mouseout="cancelHover">
      <div class="main-content">
        <div class="title-wrapper">
          <div class="title">
            {{ questionTypeText }}
          </div>
          <div class="add-collect" @click="addCollect">
            <el-icon><StarFilled /></el-icon>加入收藏夹
          </div>
        </div>
        <div><el-divider></el-divider></div>
        <div class="answer-check" v-if="answerShow">
          <div class="answer">
            <div v-if="isRight == 0" class="msg correct-color">
              <img
                style="width: 14px; height: 14px"
                src="@/assets/images/prjlearn/correct.png"
                alt=""
              />
              恭喜你，答对了！
            </div>
            <div v-if="isRight == 1" class="msg error-color">
              <img
                style="width: 14px; height: 14px"
                src="@/assets/images/prjlearn/error.png"
                alt=""
              />
              很遗憾，您的答案不正确。
            </div>
          </div>
          <div class="mode">
            <el-switch v-model="mode" />
            <span>{{ mode ? '提问模式' : '阅读模式' }}</span>
          </div>
        </div>
        <div :class="['content', 'lineWordContent']">
          <div class="content-question" v-html="currentTest.stem"></div>
          <div
            v-if="currentTest.type == ExerciseType.blank && !answerShow"
            class="replycontent-style"
          >
            <p class="replycontent-title">回答:</p>
            <ClassicEditor v-model="singleAnswer"></ClassicEditor>
          </div>
          <div v-if="currentTest!.type == ExerciseType.single">
            <el-form-item label="">
              <el-radio-group v-model="singleAnswer" class="selection-style">
                <el-radio
                  :label="indexIntoAlpha(index)"
                  v-for="(selection, index) in currentTest.content"
                  :key="index"
                >
                  <span class="inline-label">
                    <span>{{ indexIntoAlpha(index) }}</span
                    ><span v-html="selection.text"></span> </span
                ></el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div v-if="currentTest!.type == ExerciseType.multi">
            <el-form-item label="">
              <el-checkbox-group v-model="multiAnswer" class="selection-style">
                <el-checkbox
                  :label="indexIntoAlpha(index)"
                  v-for="(selection, index) in currentTest.content"
                  :key="index"
                >
                  <span class="inline-label">
                    <span style="margin-right: 5px">{{ indexIntoAlpha(index) }}</span
                    ><span v-html="selection.text"></span>
                  </span>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div v-if="currentTest.type == ExerciseType.judge">
            <el-form-item label="">
              <el-radio-group v-model="singleAnswer" class="selection-style">
                <el-radio label="1"><span class="iconfont icon-duigou1"></span></el-radio>
                <el-radio label="0"><span class="iconfont icon-cuowu"></span></el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
      </div>
      <!-- 上一题--查看答案--下一题按钮 -->
      <div class="btns" style="margin-top: 10px">
        <CmpButton class="btn" type="primary" @click="checkAnswer" v-if="submitShow"
          >提交</CmpButton
        >

        <CmpButton class="btn" type="info" v-if="!isLastItem && submitShow" @click="toNext"
          >跳过</CmpButton
        >
      </div>
      <!-- 查看答案 -->
      <div class="answer-wrap" v-if="answerShow">
        <div class="answer">
          <div class="detail">
            <div :class="['choice', 'lineWordContent']">
              <div v-if="currentTest.type == ExerciseType.judge" class="unlineWordContent">
                答案：{{
                  currentTest.answer.replace(/<\/?span[^>]*>/g, '') == '1' ? '正确' : '错误'
                }}
              </div>

              <div
                v-else-if="
                  currentTest.type == ExerciseType.multi || currentTest.type == ExerciseType.single
                "
              >
                答案：<span
                  v-html="JSON.parse(currentTest.answer.replace(/<\/?span[^>]*>/g, '')).join(', ')"
                ></span>
              </div>
              <div v-else>答案：<span v-html="currentTest.answer"></span></div>
            </div>
            <div :class="['description', 'lineWordContent']">
              说明：
              <div class="lineWordContent" v-html="currentTest.explanation"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import QuestionSubmit from '@/views/exam/components/QuestionSubmit.vue';
import ClassicEditor from '@/components/editors/Veditor.vue';
import InlineEditor from '@/components/editors/VeditorInline.vue';
import CmpButton from '@/components/CmpButton.vue';
// import WrongSets from './WrongSets.vue';
import { ShowType } from '@/types/exam';
import indexIntoAlpha from '@/utils/indexIntoAlpha';
import type { documentData } from '@/types/data';
import { ExamType } from '@/types/exam';
import { ExerciseType, QuestionListData } from '@/types/exercise';
import type { exerciseItem } from '@/types/exercise';
import { getDocumentListApi, saveAnswerApi } from '@/apis/exam';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { finishTestApi } from '@/apis/exam';
import { useProjectStore } from '@/stores/project';
import { useExerciseStore } from '@/stores/exercise';
import { emitter } from '@/utils/emitter';
import { saveExerciseRecordApi, getQuestionListApi } from '@/apis/exercise';
import { StarFilled } from '@element-plus/icons-vue';
import { addFavoritesApi } from '@/apis/collect';

const projectStore = useProjectStore();
const exerStore = useExerciseStore();

const { initUserBehaviour } = userBehaviour(PrjForm.draft);

const showType = inject('showType') as Ref<ShowType>;
const showWrongSets = ref(false);
const showDraftQuestionSubmit = ref(false);
const showWrongSetsQuestionSubmit = ref(false);
const router = useRouter();
const route = useRoute();
const spuId = route.query.spuId as string;
const current = ref(1); //当前在第几题,初始化的值从其他页面传递，为当前进度，给后端发信（前端来操作数据
// const currentCount = ref(0); //完成度，展示后端数据
const total = ref(1); //题目总数

const props = defineProps(['exerciseInfo', 'isLastItem']);
const klgCode = inject('klgCode') as string;
const exerciseId = ref('');

// 当前题目
const currentTest = ref<exerciseItem>({
  exerciseId: '',
  type: 0, //1.单选 2多选 3填空 4判断
  stem: '', //题目
  content: [],
  answer: '', //答案
  explanation: '' //解释说明
});

const questionTypeText = computed(() => {
  switch (props.exerciseInfo.type) {
    case 1:
      return '单选题';
    case 2:
      return '多选题';
    case 3:
      return '填空题';
    case 4:
      return '判断题';
    default:
      return '全部题型';
  }
});

const emits = defineEmits(['toNext']);
async function toNext() {
  emits('toNext');
}

//对测评内容做处理
const processData = (data: any) => {
  if (data.answer && data.type == ExerciseType.judge) {
    //
  }
  return data;
};

onMounted(async () => {
  currentTest.value = props.exerciseInfo;
  exerciseId.value = props.exerciseInfo.exerciseId;
  exerStore.setExerciseId(exerciseId.value);

  emitter.emit('initHandler', () => {
    exerciseWordStore.regString = '';
    handleQuestionList2();
  });
});

watch(
  () => props.exerciseInfo,
  async (newVal) => {
    if (newVal) {
      currentTest.value = { ...processData(newVal) };
      answerShow.value = false;
      submitShow.value = true;
      singleAnswer.value = '';
      multiAnswer.value = [];

      exerciseId.value = props.exerciseInfo.exerciseId;
      exerStore.setExerciseId(exerciseId.value);
      exerciseWordStore.regString = '';
      handleQuestionList2();

      emitter.emit('initHandler', () => {
        exerciseWordStore.regString = '';
        handleQuestionList2();
      });
    }
  }
);
// 不在后端保存答案，使用后端接口判断后在前端显示用户答案是否正确
const singleAnswer = ref(''); //填空题+单选题+判断题
const multiAnswer = ref<string[]>([]); //多选题

const isRight = ref(0); // 0表示显示正确，1表示显示错误，2表示都不显示
const answerShow = ref(false); //显示答案
const submitShow = ref(true); //显示提交按钮
// 查看/提交答案
const checkAnswer = async () => {
  if (
    currentTest.value.type == ExerciseType.single ||
    currentTest.value.type == ExerciseType.judge
  ) {
    // 单选题或判断题：确保答案不能为空
    if (!singleAnswer.value) {
      ElMessage({
        message: '请回答此题',
        type: 'warning'
      });
      return;
    }
  } else if (currentTest.value.type == ExerciseType.multi) {
    // 多选题：确保至少选择一个选项
    if (multiAnswer.value.length == 0) {
      ElMessage({
        message: '请至少选择一个选项',
        type: 'warning'
      });
      return;
    }
  } else if (currentTest.value.type == ExerciseType.blank) {
    // 填空题：确保用户已输入答案
    if (!singleAnswer.value.trim()) {
      ElMessage({
        message: '请填写答案',
        type: 'warning'
      });
      return;
    }
  }

  let answerList: string[] = [];
  let lastAnswer = '';
  if (currentTest.value.type == ExerciseType.single) {
    answerList[0] = singleAnswer.value;
    lastAnswer = JSON.stringify(answerList);
  } else if (currentTest.value.type == ExerciseType.multi) {
    answerList = [...multiAnswer.value];
    lastAnswer = JSON.stringify(answerList);
  } else {
    lastAnswer = singleAnswer.value;
  }

  //判断回答是否正确
  const res = await saveExerciseRecordApi({
    exerciseId: exerciseId.value,
    answer: lastAnswer
  });
  //保存答题结果、答案、解释
  currentTest.value.answer = res.data.correctAnswer;
  currentTest.value.explanation = res.data.explanation;
  //currentTest.value = processData(currentTest.value);

  exerciseWordStore.regString = '';
  handleQuestionList2();

  let result = res.data.score;

  if (result) {
    isRight.value = 0;
  } else {
    isRight.value = 1;
  }

  answerShow.value = true;
  submitShow.value = false;
};
const addCollect = async () => {
  const res = await addFavoritesApi(exerciseId.value);
  ElMessage({
    type: 'info',
    message: res.message
  });
};
//划词
import type { Chapter, VideoCaptionListObj, QuestionData } from '@/types/learning';
import { useWordStore } from '@/stores/word';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { intersection } from 'lodash-es';
import { useFloating } from '@floating-ui/vue';
import { useExerciseWordStoreV2 } from '@/stores/exerciseWordV2';
import { Mode, QuestionAction, type RenderInfo } from '@/types/word';
import { handleVideoQuestion } from '@/utils/handleQuestion';
import { Event } from '@/types/event';

const exerciseWordStore = useExerciseWordStoreV2();
const drawerControllerStore = useDrawerControllerStore();
const { mode } = storeToRefs(drawerControllerStore);
const matchingColor = computed(() => {
  return mode.value ? '' : '#FFDD6C';
});
const matchingHeight = computed(() => {
  return mode.value ? '' : '700';
});

const wordStore = useWordStore();
const questionList = shallowRef<QuestionListData[]>([]);

const saveType = inject('saveType') as Ref;

const handleQuestionList = async () => {
  //（获取问题列表）
  const res = await getQuestionListApi(exerciseId.value);
  questionList.value = res.data.list;
  console.log('questionList:', questionList.value);

  const worker = new Worker(new URL('@/utils/exerciseWorker.ts', import.meta.url), {
    type: 'module'
  });
  worker.onmessage = function (
    e: MessageEvent<{
      exerciseStringList: Array<{ list: Array<string>; start: number }>;
      originalExerciseStringList: Array<{ list: Array<string>; start: number }>;
      regString: string;
      unitList: Array<{
        tagName: string;
        children: any[];
        index: number;
        qids: number[];
        highlight: boolean;
        stringIndex: number;
      }>;
      uncommonWordMap: Map<string, string>;
    }>
  ) {
    wordStore.exerciseStringList = e.data.exerciseStringList;
    wordStore.originalExerciseStringList = e.data.originalExerciseStringList;
    wordStore.regString = e.data.regString;
    wordStore.unitList = e.data.unitList;
    wordStore.uncommonWordMap = e.data.uncommonWordMap;

    let index = 0;
    let tempList = [];
    for (let i = 0; i < wordStore.exerciseStringList.length; i++) {
      // 获取当前项的 HTML 元素字符串
      const item = wordStore.getExerciseStringList[index++];
      tempList.push(item);
    }
    //console.log('tempList:', tempList);
    exerStore.setExercise(transferList2Exercise(tempList));

    currentTest.value = exerStore.exercise;
    currentTest.value.content = exerStore.exercise.option;

    worker.terminate();
  };
  worker.postMessage({
    questionList: toRaw(questionList.value),
    exercise: toRaw(exerStore.exercise)
  });
};
// 把worker的list转化为exercise
const transferList2Exercise = (list: any[]): any => {
  const tempExercise = ref<{
    type: number;
    stem: string;
    option: any[];
    answer: string;
    explanation: string;
    exerciseId: string;
  }>({
    type: 0,
    stem: '',
    option: [],
    answer: '',
    explanation: '',
    exerciseId: ''
  });
  const tempList = ref<any[]>([]);
  list.forEach((item: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span'); // 获取最外层的 span 元素
    if (span && span.hasAttribute('etype')) {
      const etype = span.getAttribute('etype');
      if (span.hasAttribute('type')) {
        const type = span.getAttribute('type');
        if (type) {
          tempExercise.value.type = parseInt(type);
        }
      }
      if (etype === 'option') {
        const op = {
          optionId: span.getAttribute('optionid'),
          text: item
        };
        tempList.value.push(op);
      } else {
        tempExercise.value[etype] = item;
      }
    }
  });
  tempExercise.value.option = tempList.value;
  return tempExercise.value;
};
// 把worker的list转化为exercise
const transferList2Exercise2 = (list: any[]): any => {
  const tempExercise = ref<{
    type: number;
    stem: string;
    content: any[];
    answer: string;
    explanation: string;
  }>({
    type: 0,
    stem: '',
    content: [],
    answer: '',
    explanation: ''
  });
  const tempList = ref<any[]>([]);
  list.forEach((item: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span'); // 获取最外层的 span 元素
    if (span && span.hasAttribute('etype')) {
      const etype = span.getAttribute('etype');
      if (span.hasAttribute('type')) {
        const type = span.getAttribute('type');
        if (type) {
          tempExercise.value.type = parseInt(type);
        }
      }
      if (etype === 'content') {
        const op = {
          optionId: span.getAttribute('contentid'),
          text: item
        };
        tempList.value.push(op);
      } else {
        tempExercise.value[etype] = item;
      }
    }
  });
  tempExercise.value.content = tempList.value;
  return tempExercise.value;
};

watch(
  () => mode,
  (newValue, oldValue) => {
    if (newValue.value) {
      saveType.value = 1;
      //console.log('watch-mode ask');
      if (newValue != oldValue)
        currentTest.value = transferList2Exercise2(exerciseWordStore.getAskModeExerciseStrings());
    } else {
      //console.log('watch-mode read');
      if (newValue != oldValue)
        currentTest.value = transferList2Exercise2(exerciseWordStore.getReadModeExerciseStrings());
    }
  },
  { deep: true, immediate: true }
);
const handleQuestionList2 = async () => {
  // 处理习题
  const handleExercise = (exercise: any): string[] => {
    const tempExerciseList = [];
    tempExerciseList.push(
      `<span etype="stem" type="${exercise.type}" style="width: 100%;">${exercise.stem}</span>`
    );
    if (exercise.content) {
      exercise.content.forEach((item) => {
        tempExerciseList.push(
          `<span etype="content" contentid="${item.optionId}">${item.text}</span>`
        );
      });
    }
    tempExerciseList.push(`<span etype="answer">${exercise.answer}</span>`);
    tempExerciseList.push(`<span etype="explanation">${exercise.explanation}</span>`);
    return tempExerciseList;
  };

  const res = await getQuestionListApi(exerciseId.value);
  questionList.value = res.data.list;
  const worker = new Worker(new URL('@/worker/exerciseWorker.ts', import.meta.url), {
    type: 'module'
  });
  console.log('worker-curren', currentTest.value);
  const htmlStrings = handleExercise(currentTest.value);
  console.log('html:', htmlStrings);
  worker.onmessage = function (
    e: MessageEvent<{
      regString: string;
      renderInfoIndexes: Array<{
        listIndex: number;
        index: number;
      }>;
      renderInfoListList: RenderInfo[][];
    }>
  ) {
    exerciseWordStore.regString = e.data.regString;
    console.log('exerciseWordStore', exerciseWordStore.regString);
    exerciseWordStore.renderInfoIndexes = e.data.renderInfoIndexes;
    exerciseWordStore.renderInfoListList = e.data.renderInfoListList;
    toRaw(questionList.value)?.forEach((question) => {
      handleVideoQuestion(
        question,
        toRaw(exerciseWordStore.regString),
        toRaw(exerciseWordStore.renderInfoIndexes),
        toRaw(exerciseWordStore.renderInfoListList)
      );
    });
    currentTest.value = transferList2Exercise2(exerciseWordStore.getReadModeExerciseStrings());
  };
  worker.postMessage({
    htmlStrings,
    regString: toRaw(exerciseWordStore.regString),
    renderInfoIndexes: toRaw(exerciseWordStore.renderInfoIndexes),
    renderInfoListList: toRaw(exerciseWordStore.renderInfoListList)
  });
};
const handleWord = inject('handleWord') as (e: Event) => void;
const handleHover = (e: Event) => {
  let element = e.target as HTMLElement;
  if ((e.target as HTMLElement).closest("[class^='inline-equation'], [class^='equation']")) {
    element = (e.target as HTMLElement).closest(
      "[class^='inline-equation'], [class^='equation']"
    ) as HTMLElement;
  }
  element = element.closest('[data-qid]') as HTMLElement;
  if (element && element.classList.contains('highlight')) {
    element.classList.add('highlightHover');
    const index = parseInt(element.getAttribute('data-index') as string);
    const rootQids = (element.getAttribute('data-qid') as string).split(',');
    for (let i = index - 1; ; i--) {
      const ele = document.querySelector(`span[data-index="${i}"]`) as HTMLElement;
      if (!ele) {
        break;
      }
      const qidString = ele.getAttribute('data-qid');
      if (qidString) {
        const qids = qidString.split(',');
        if (intersection(rootQids, qids).length > 0) {
          ele.classList.add('highlightHover');
        } else {
          break;
        }
      } else {
        break;
      }
    }
    for (let i = index + 1; ; ++i) {
      const ele = document.querySelector(`span[data-index="${i}"]`) as HTMLElement;
      if (!ele) {
        break;
      }
      const qidString = ele.getAttribute('data-qid');
      if (qidString) {
        const qids = qidString.split(',');
        if (intersection(rootQids, qids).length > 0) {
          ele.classList.add('highlightHover');
        } else {
          break;
        }
      } else {
        break;
      }
    }
  }
};
const cancelHover = (e: Event) => {
  const elements = document.querySelectorAll('.highlightHover');
  elements.forEach((element) => {
    element.classList.remove('highlightHover');
  });
};
</script>

<style lang="less" scoped>
// 滑动效果
.qt-detail {
  width: 100%;
  padding: 8px 23px 20px 135px;
  background-color: #ffffff;
  height: 600px;

  .header {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f2f2f2;

    .header-left {
      display: flex;
      flex-direction: row;
      height: 100%;
      align-items: center;

      .back-btn {
        height: 56px;
        width: 80px;
      }

      h1 {
        margin-left: 16px;
        font-size: 20px;
        font-weight: 700;
      }
    }

    .process {
      font-size: 14px;
      color: var(--color-theme-project);
    }
  }

  .main-content {
    margin-top: 15px;
    width: 100%;
    background-color: #ffffff;
    .title-wrapper {
      display: flex;
      .title {
        width: 100%;
        height: 33px;
        line-height: 33px;
        font-size: 16px;
        font-weight: 700;
        //background-color: #fffff;
        padding-left: 10px;
        //border-bottom: 2px solid #f2f2f2;
      }
      .add-collect {
        font-size: 14px;
        width: 100px;
        color: var(--color-theme-project);
        display: flex;
        justify-content: center;
        text-align: center;
        align-items: center;
      }
    }
    &:deep(.search-keys) {
      color: red;
      cursor: pointer;
      font-weight: 700;
    }
    // &:deep(.highlight) {
    //   // color: var(--color-theme-project);
    //   background-color: v-bind(matchingColor);
    //   cursor: pointer;
    //   font-weight: v-bind(matchingHeight);
    // }
    &:deep(.activeElement) {
      background-color: #ff9632;
    }

    .content {
      padding-left: 62px;
      //min-height: 300px;
      margin-top: 15px;

      .content-question {
        //margin-left: 52px;
        display: flex;
        flex-wrap: wrap;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 14px;
        font-weight: 700;
      }

      .selection-style {
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        align-items: flex-start;
        max-height: 220px; /* 控制最大高度，设置合适的值 */
        overflow-y: auto; /* 添加竖向滚动条 */
        overflow-x: hidden;
        .inline-label {
          display: inline-flex;
          align-items: center;
        }

        .el-radio {
          width: 100%;
          margin-bottom: 5px;
          white-space: nowrap; /* 保证文字不换行 */
          span {
            // margin-left: 32px;
            margin-right: 5px;
          }
        }
        .el-checkbox {
          width: 100%;
          margin-bottom: 5px;
          white-space: nowrap; /* 保证文字不换行 */
        }
      }

      .replycontent-style {
        margin-top: 31px;
        margin-left: 32px;
        font-size: 14px;

        .replycontent-title {
          margin-bottom: 10px;
        }
      }
    }
  }

  .finish-content {
    margin-top: 15px;
    //width: 970px;
    height: 80px;
    background-color: #f0f9eb;
    margin-left: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .finish-accuracy {
      margin-left: 40px;
    }

    .btns2 {
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin-left: 369px;

      .btn {
        width: 160px;
        height: 44px;
        margin-left: 20px;
      }
    }
  }

  .btns {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;

    .btn {
      width: 80px;
      height: 32px;
      margin-left: 40px;
      border-radius: 4px;
    }
  }
  .answer-check {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 10px;
    .mode {
      margin-left: 10px;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      display: flex;
      align-items: center;

      .el-switch {
        margin-right: 10px;
      }
    }
    .answer {
      width: 92%;
      font-size: 14px;

      .msg {
        height: 37px;
        display: flex;
        align-items: center;

        width: 100%;

        img {
          margin-left: 7px;
          margin-right: 7px;
        }
      }

      .correct-color {
        color: #67c23a;
        background-color: #f0f9eb;
      }

      .error-color {
        background-color: #fdf6ec;
        color: #e6a23c;
      }

      .detail {
        width: 100%;
        border: 1px solid #f2f2f2;
        border-radius: 4px;
        margin-top: 4px;
        padding-left: 25px;
        padding-right: 16px;
        font-size: 14px;

        .choice {
          display: flex;
          flex-direction: row;
          width: 100%;
          justify-content: space-between;
        }

        .description {
          margin-top: 20px;
          white-space: pre-wrap;
        }
      }
    }
  }
  .answer-wrap {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;
    max-height: 235px;
    overflow-y: auto;

    .answer {
      width: 92%;

      .msg {
        height: 37px;
        display: flex;
        align-items: center;

        width: 100%;

        img {
          margin-left: 7px;
          margin-right: 7px;
        }
      }

      .correct-color {
        color: #67c23a;
        background-color: #f0f9eb;
      }

      .error-color {
        background-color: #fdf6ec;
        color: #e6a23c;
      }

      .detail {
        width: 100%;
        border: 1px solid #f2f2f2;
        border-radius: 4px;
        margin-top: 4px;
        padding-left: 25px;
        padding-right: 16px;
        font-size: 14px;

        .choice {
          display: flex;
          flex-direction: row;
          width: 100%;
          justify-content: space-between;
        }

        .description {
          margin-top: 20px;
          white-space: pre-wrap;
        }
      }
    }
  }

  .error-wrap {
    margin-top: 19px;
    margin-left: 40px;
    width: 970px;
    height: 472px;
    background-color: #ffffff;
    border: 0.8px solid rgb(242, 242, 242);
    border-radius: 5px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;

    .error-innerwrap {
      padding-top: 15px;

      .error-top {
        display: flex;
        flex-direction: row;

        .error-top-content {
          padding-right: 50px;
          width: 527px;

          .content-question {
            font-size: 14px;
            font-weight: 700;
            color: #333333;
            margin-left: 74px;
          }

          .replycontent-title {
            margin-left: 30px;
          }

          .replycontent {
            width: 447px;
            margin: 10px 10px 10px 30px;
          }

          .selection-style {
            display: flex;
            flex-direction: column;
            margin-left: 30px;
            align-items: flex-start;

            .el-radio {
              margin-top: 18px;

              span {
                margin-left: 32px;
                margin-right: 5px;
              }
            }
          }

          .explanation-wrap {
            width: 447px;
            min-height: 134px;
            background-color: #ffffff;
            border: 0.8px solid rgb(242, 242, 242);
            border-radius: 5px;
            margin-left: 30px;
            padding: 11px 22px;
            font-size: 14px;

            .detail {
              white-space: pre-wrap;

              .choice {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
              }

              .description {
                margin-top: 20px;
              }
            }
          }
        }

        .error-top-list {
          width: 422px;
          height: 355px;
          background-color: rgba(242, 242, 242, 0.376);
          border-radius: 5px;
          padding: 15px 22px;
          overflow-y: auto;

          p {
            font-size: 14px;
            font-weight: 700;
          }

          .hover-style:hover {
            font-weight: 700;
            color: #005579;
            cursor: pointer;
          }

          .questionliststyle {
            margin-top: 17px;
            font-size: 14px;
          }
        }
      }

      .error-bottom {
        margin-top: 18px;
        margin-left: 30px;
        margin-bottom: 40px;

        .btn {
          width: 160px;
          height: 44px;
        }
      }
    }
  }
  // :deep(.highlight) {
  //   color: var(--color-theme-project);
  //   cursor: pointer;
  // }
  :deep(.highlightHover) {
    font-weight: 700;
    * {
      font-weight: 700;
    }
  }
}
</style>
