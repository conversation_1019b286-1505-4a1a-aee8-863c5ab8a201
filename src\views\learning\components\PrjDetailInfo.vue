<template>
  <!-- 项目信息,项目信息是不会变得，只有章节会变 -->
  <div class="detail-info">
    <h1 class="detail-info-title">{{ prjInfo.title }}</h1>
    <div id="tagback" class="detail-info-list" :class="{ tagsExpand: expand }">
      <div class="footer-info">
        <img :src="prjInfo.userCoverPic" class="logo" />
        <span class="wjby">{{ prjInfo.userName }}</span>
      </div>
      <div class="time">{{ prjInfo.createTime }}</div>
      <div id="tags-box" class="tags-wrapper" :class="{ tagsExpand: expand }">
        <div class="tags">
          <PrjTag
            style="height: 25px; line-height: 25px; border-radius: 10px"
            :label="tag.content"
            :prefix="'#'"
            v-for="tag in tags"
            :key="tag.id"
          ></PrjTag>
        </div>
        <span class="expandBtn" @click="handleExpand()">
          <img
            class="expand"
            :style="expand ? 'transform: rotate(-180deg)' : ''"
            src="@/assets/images/prjlearn/u4475.svg"
          />
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// mock标签数据
const tags = ref([
  {
    id: 1,
    content: '前端开发'
  }
]);

import PrjTag from './PrjTag.vue';
import { getPartProApi } from '@/apis/learning';
import type { PrjinfoItf } from '@/types/learning';
const emit = defineEmits(['back', 'expandTag']);
// const tags = ref(['EDA', 'verilog', 'DFT', 'Python', 'Linux操作', 'vivado', 'FPGA']);
const expand = ref(false); // tag扩展
const prjInfo = ref<PrjinfoItf>({});
const route = useRoute();
const spuId = route.query.spuId as string;
onMounted(async () => {
  const res = await getPartProApi(spuId);
  prjInfo.value = res.data.list[0];
  tags.value = prjInfo.value.prjTags;
});
const props = defineProps(['curHeight']);
watch(
  () => props.curHeight,
  (newValue, oldVale) => {
    if (newValue == 70) {
      expand.value = false;
    }
  }
);
const handleExpand = () => {
  expand.value = !expand.value;
  // document.getElementById('tagback').style.height = document.getElementById('infoback').style.height;
  setTimeout(() => {
    let expandHeight = getComputedStyle(document.getElementById('tagback')!, 'tagsExpand')[
      'height'
    ];
    console.log(expandHeight);
    emit('expandTag', expandHeight, 'detailInfo');
  }, 0);
};

const computeOverflow = () => {
  const tagBox = document.getElementById('tags-box');
  // 父元素宽度
  const fasterWidth = tagBox!.offsetWidth;
  // 孩子总宽度
  let childrenWidth = 0;
  console.log(tagBox?.children);
  // 循环遍历子元素 计算内部内容的宽度
  for (let i = 0; i < tagBox?.children.length!; i++) {
    const element = tagBox?.children[i] as HTMLDivElement;
    childrenWidth += element.offsetWidth!;
  }

  if (childrenWidth > fasterWidth) {
    console.log('存在水平溢出');
  } else {
    console.log('未溢出');
  }
};
onMounted(() => {
  computeOverflow();
});
</script>

<style scoped lang="less">
.detail-info {
  margin-right: 10px;
  padding-top: 10px;
  transition: height 0.5s ease-in-out;
  //background-color: red;
  width: 56%;
  height: 80px;
  font-family: var(--text-family);
  // background-color: #fff;
  //padding-right: 10px;
  //position: relative;

  .detail-info-title {
    line-height: 18px;
    font-size: 18px;
    font-weight: 700;
    color: var(--color-black);
    margin-bottom: 10px;
    font-family: var(--title-family);
  }

  .detail-info-list {
    width: 100%;
    display: flex;
    height: 25px;
    // align-items: center;
    white-space: nowrap;
    // background-color: white;
    overflow: hidden;
    transition: height 10s ease-in-out;
    &.tagsExpand {
      height: auto;
      //background-color: red;
    }

    .footer-info {
      display: flex;
      align-items: center;
      height: 20px;
      line-height: 20px;

      .logo {
        max-width: 20px;
        height: auto;
        object-fit: fill;
      }

      .wjby {
        display: inline-block;
        font-size: var(--fontsize-small-project);
        font-weight: 400;
        font-family: var(--text-family);
        color: var(--color-deep);
        margin-left: 5px;
      }
    }

    .time {
      font-size: 12px;
      font-weight: 400;
      color: var(--color-deep);
      margin: 0 27px;
      line-height: 20px;
    }

    .tags-wrapper {
      // width: 70%;
      position: relative;
      padding-left: 10px;
      //padding-right: 20px;
      display: flex;
      justify-content: space-between;
      &.tagsExpand {
        //background-color: rgb(250, 250, 250);
        border-radius: 5px;
        box-shadow: 5px 5px 10px 0 rgba(0, 0, 0, 0.1);
      }
      .tags {
        //padding: 0 10px 0 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        max-width: 95%;
        .tag {
          margin-right: 20px;
          margin-bottom: 10px;
        }
        &:deep(.tag) {
          background-color: #f2f2f2;
          border: none;
          color: var(--color-black);
          //margin-right: 5px;
        }
      }
      .expandBtn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 20px;
        width: 20px;
        background-color: #f2f2f2;
        border-radius: 10px;
        cursor: pointer;
        .expand {
          width: 10px;
          height: 6px;
          transition: transform 0.5s ease-in-out;
        }
      }
    }
  }
}
</style>
