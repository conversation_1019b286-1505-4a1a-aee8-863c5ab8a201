import PicIcon from '@/assets/imgs/pic_icon.jpg';
import TableIcon from '@/assets/imgs/table_icon.jpg';
import codeSrc from '@/assets/imgs/code.png';
export const transToIcon = (str: string) => {
  let updatedStr = str;
  const pic = `<img width="16" height="16" src="${PicIcon}" />`; // 新的图片源地址
  const figureTagRegex = /<figure[^>]*>/gi;
  updatedStr = updatedStr.replace(figureTagRegex, pic);
  const imgTagRegex = /<img[^>]*>/gi; // 正则表达式匹配所有 img 标签
  updatedStr = updatedStr.replace(imgTagRegex, pic);

  const table = `<img width="16" height="16" src="${TableIcon}" />`;
  const tableTagRegex = /<figure class="table">([\s\S]*?)<\/figure>/gi;
  updatedStr = updatedStr.replace(tableTagRegex, table);

  const code = `<img width="16" height="16" src="${codeSrc}" />`;
  const codeBlockRegex = /<pre>\s*<code[^>]*>([\s\S]*?)<\/code>\s*<\/pre>/gi;
  updatedStr = updatedStr.replace(codeBlockRegex, code);

  return updatedStr;
};
