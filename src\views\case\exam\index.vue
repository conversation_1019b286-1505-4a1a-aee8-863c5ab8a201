<template>
  <div class="test-method">
    <div class="main-content">
      <TitleandInfo :istest="isTest"></TitleandInfo>
      <div class="tabletitle">
        <div class="tabletitle1">测验内容</div>
        <div class="tabletitle2">状态</div>
        <div class="tabletitle3">完成度</div>
        <div class="tabletitle4">操作</div>
      </div>
      <div
        class="content"
        v-infinite-scroll="loadExam"
        :infinite-scroll-disabled="!hasMoreExam || examLoadBusy"
      >
        <el-table :data="examData" :show-header="false">
          <el-table-column prop="title" label="测验内容">
            <template #default="scope">
              <div class="test-title">
                <div style="margin-right: 10px">
                  <img
                    v-if="scope.row.prjForm == PrjForm.draft"
                    src="@/assets/images/prjlearn/u6418.svg"
                    alt=""
                  />
                  <img
                    style="width: 16px; height: 16px"
                    v-else
                    src="@/assets/images/prjlearn/u4065.svg"
                    alt=""
                  />
                </div>
                <div>{{ scope.row.prjTitle }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="testStatus" label="状态" width="120">
            <template #default="scope">
              <div v-if="scope.row.testStatus == 1">已完成</div>
              <div v-else-if="scope.row.testStatus == 2">进行中</div>
              <div v-else>未开始</div>
            </template>
          </el-table-column>
          <el-table-column label="完成度" width="120">
            <template #default="scope">
              <div>{{ scope.row.completionRate }}/{{ scope.row.total }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <div @click="goexam(scope.row.spuId)">
                <span class="action" v-if="scope.row.completionRate == 0">准备测验</span>
                <span class="action" v-else-if="scope.row.completionRate < scope.row.total"
                  >继续测验</span
                >
                <span class="action" v-else>重新测验</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="right-info">
      <div class="title">
        <img src="@/assets/images/prjlearn/u6401.svg" alt="" />
        收藏夹
      </div>
      <!-- 在此处要实现数据的懒加载 -->
      <!--同时需要判断题目的长度-->
      <div
        class="content"
        v-infinite-scroll="loadCollect"
        :infinite-scroll-disabled="!hasMoreCollect || collectLoadBusy"
      >
        <div
          @click="goPage(collectItem.favoriteId, index)"
          class="collect-item"
          v-for="(collectItem, index) in collectList"
          :key="collectItem.examId"
        >
          <span v-html="Number(index + 1) + '. ' + collectItem.title"></span>
        </div>
        <div class="nomore" v-if="!hasMoreCollect">----------暂无其他----------</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCollectDataApi } from '@/apis/collect';
import { getExamRelatedApi } from '@/apis/case';
import { useRouter } from 'vue-router';
import type { BoughtInfoType } from '@/types/case';
import { useLearnStore } from '@/stores/learnintro';
// import { useProjectStore } from '@/stores/project';
import { PrjForm } from '@/types/project';
import TitleandInfo from '../components/TitleandInfo.vue';
import { ref, onMounted, onUnmounted } from 'vue';
import type { ElTable } from 'element-plus';
//表明当前是测试页面，不显示相关按钮
const isTest = ref(true);
// 使用统一的数据管理方法
const learnStore = useLearnStore();
// const prjStore = useProjectStore();
const route = useRoute();
const spuId = route.query.spuId as string;
//跳转到收藏集题目
const router = useRouter();
const goPage = (id: number, index: number) => {
  // console.log("examid", id)
  // router.push({ path: '/case/collect', query: { spuId, favoriteId: id } });
  const { href } = router.resolve({ path: '/mystudy/detail', query: { favoriteId: id } });
  window.open(href, '_blank');
};

// 获得测验内容
let examData = ref([]);
const getExamData = async () => {
  const params = {
    spuId: spuId,
    limit: pageExamSize.value,
    current: currentExamPage.value
  };
  const res = await getExamRelatedApi(params);
  //将其加入已有的list当中
  examData.value = examData.value.concat(res.data.records);
  // 如果它们相等，表示可能还有更多的数据可供加载，hasMore会被设置为true；否则，设置为false。以决定当用户滚动到页面或容器底部时是否继续加载更多数据。
  if (examData.value.length == res.data.total) {
    hasMoreExam.value = false;
  }
};

const currentExamPage = ref(0); // 表示当前在第几页
//一次取多少根据高度进行调整
const pageExamSize = ref(20); // 每页的数据条数
const hasMoreExam = ref(true); // 是否还有更多数据
//用于上锁，避免一次加载多条数据
const examLoadBusy = ref(false);

const loadExam = async () => {
  //用来判断是否已经加载过一次
  currentExamPage.value += 1;
  examLoadBusy.value = true;
  await getExamData();
  examLoadBusy.value = false;
};

//获取收藏夹内容
type collectlistType = {
  examId: number;
  title: string;
  favoriteId: number;
  hasAccess: boolean;
};
const collectList = ref<collectlistType[]>([]);
const currentCollectPage = ref(0); // 表示当前在第几页
//一次取多少根据高度进行调整
const pageCollectSize = ref(21); // 每页的数据条数
const hasMoreCollect = ref(true); // 是否还有更多数据
const getCollectData = async () => {
  const params = {
    limit: pageCollectSize.value,
    current: currentCollectPage.value
  };
  const res = await getCollectDataApi(params);
  //将其加入已有的list当中
  collectList.value = collectList.value.concat(res.data.records);
  // 如果它们相等，表示可能还有更多的数据可供加载，hasMore会被设置为true；否则，设置为false。以决定当用户滚动到页面或容器底部时是否继续加载更多数据。
  if (collectList.value.length == res.data.total) {
    hasMoreCollect.value = false;
  }
};
const collectLoadBusy = ref(false);

//load elementui的无限滚动组件在渲染时自己就会加载一遍，所以不用再调用一遍getcollectdata()
const loadCollect = async () => {
  currentCollectPage.value += 1;
  collectLoadBusy.value = true;
  await getCollectData();
  collectLoadBusy.value = false;
};

//测验跳转
const goexam = async (spuId: string) => {
  try {
    const { href } = router.resolve({
      path: '/exam',
      query: {
        spuId: spuId
      }
    });
    window.open(href, '_blank');
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
</script>

<style lang="less" scoped>
.test-method {
  width: 1100px;
  padding-left: 10px;
  display: flex;
  flex-direction: row;

  .main-content {
    width: 754px;
    height: 710px;
    border: 0.8px solid rgb(242, 242, 242);
    border-radius: 5px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
    // background-image: linear-gradient(to bottom, #f2f2f2, #ffffff);
    padding-right: 10px;
    padding-top: 20px;
    padding-bottom: 10px;
    padding-left: 10px;
    margin-bottom: 40px;

    .info {
      width: 100%;
      height: 50px;
      margin-top: 10px;
    }

    .action {
      color: var(--color-theme-project);
      cursor: pointer;

      &:hover {
        font-weight: 700;
      }
    }

    .tabletitle {
      display: flex;
      flex-direction: row;
      padding-left: 10px;
      padding-top: 10px;
      margin-top: 20px;
      height: 10px;
      background-color: #f2f2f2;
      color: var(--color-black);
      height: 38px;
      border-color: var(--color-second);
      font-weight: 600;
      // border-radius: 5px;

      .tabletitle1 {
        width: 580px;
        // background-color: aquamarine;
      }

      .tabletitle2 {
        width: 180px;
        // margin-left: 5px;
        // margin-right: 5px;
        // background-color: aqua
      }

      .tabletitle3 {
        width: 180px;
        // background-color: aquamarine;
      }

      .tabletitle4 {
        width: 150px;
        // background-color: aqua;
      }
    }

    .content {
      font-family: var(--text-family);
      color: var(--color-black);
      height: 480px;
      overflow-y: auto;

      .test-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        flex-direction: row;
      }
    }
  }

  .right-info {
    height: 710px;
    width: 300px;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 10px;
    margin-left: 20px;
    background-color: #ffffff;
    border: 0.8px solid rgb(242, 242, 242);
    border-radius: 5px;
    box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;

    .title {
      height: 20px;
      width: 100%;
      display: flex;
      align-items: center;
      font-size: var(--fontsize-middle-project);
      font-weight: 700;

      img {
        margin-right: 10px;
      }
    }

    .content {
      font-family: var(--text-family);
      color: var(--color-black);
      height: 600px;
      margin-top: 12px;
      width: 100%;
      margin-bottom: 10px;
      font-size: var(--fontsize-middle-project);
      color: #333333;
      overflow-y: auto;
      cursor: pointer;

      .collect-item {
        margin-top: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .nomore {
        font-family: var(--text-family);
        color: var(--color-black);
        margin-top: 10px;
        display: flex;
        justify-content: center;
      }

      .collect-item:hover {
        font-weight: 700;
        color: var(--color-theme-project);
      }
    }
  }

  .content::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 8px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }

  .content::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    background: var(--color-second);
    box-shadow: inset 0 0 1px rgb(89, 90, 90);
  }

  //滚动条底层颜色!
}
</style>
