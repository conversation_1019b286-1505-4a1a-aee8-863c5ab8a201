import { http } from '@/apis';
import axios from 'axios';
import { method } from 'lodash-es';

export function getKlgRecommend(klgCode: string, current: number, type: string) {
  return http.request<{
    recommend: {
      List: string[];
    };
  }>({
    url: `/klg/recommend`,
    method: 'get',
    params: {
      klgCode,
      current,
      type
    }
  });
}

// 获取知识点详情
export function getklgdetailApi(klgCode: string) {
  return http.request<{
    klgDetail: {
      klgTitle: string;
      referenceList: any[];
      klgCode: string;
      synonym: string;
      creatTime: string;
      provider: string;
      exerciseProgress: number;
      exerciseGoal: number;
      preKlgProgress: number;
      preKlgCount: number;
      status: number;
      areaList: Array<{
        areaCode: string;
        areaName: string;
      }>[];
      cnt: string;
      demonstrateList: any[];
    };
  }>({
    url: `/klg/detail/${klgCode}`
  });
}

// 获取前驱知识列表
export function getPreklglistApi(klgCode: string, current?: number, limit?: number) {
  //TODO: 此接口暂未定义
  return http.request({
    method: 'post',
    url: '/klg/preKlg/query',
    data: {
      current,
      limit,
      klgCode
    }
  });
}

// 提交反馈
export function saveFeedBackApi(data: any) {
  return http.request<{}>({
    method: 'post',
    url: '/knowledge-feedback/save',
    data
  });
}

//获取应用案例项目
export function getCaseListApi(klgCode: string) {
  return http.request<{
    list: [
      {
        spuId: string;
        goodsName: string;
        coverPic: string;
        prjForm: string;
        editorName: string;
        editorPic: string;
        studyInstructions: string;
        publishTime: string;
        hasPermission: number;
      }
    ];
  }>({
    method: 'get',
    url: '/project-goods/ListCaseByKlgCode',
    params: {
      klgCode
    }
  });
}

//klgCode获取知识溯源图数据
// export function getKsgMapApi(klgCode: String | String[], current: number, limit: number) {
//   return http.request({
//     method: 'post',
//     url: '/klg/graph',
//     data: {
//       klgCodes: [klgCode],
//       current,
//       limit
//     }
//   });
// }
export function getKsgMapApi(klgCode: String, current: number, limit: number) {
  return http.request({
    method: 'post',
    url: '/klg/knowledge/graph',
    data: {
      klgCodes: [klgCode],
      current,
      limit
    }
  });
}
export function getKsgMapMutileApi(klgCodes: String[], current: number, limit: number) {
  return http.request({
    method: 'post',
    url: '/klg/knowledge/graph',
    data: {
      klgCodes,
      current,
      limit
    }
  });
}
