<template>
  <h1>{{ info.title }}</h1>
</template>

<script setup lang="ts">
//只能在渲染时获取到相应的值，所以以{{titlee.title}}的模式渲染，在setup中获取不到相应的值，可能时生命周期还未传递过来的原因
// const props = defineProps({
//   title: String
// });
import { useProjectStore } from '@/stores/project';

const projectStore = useProjectStore();
const { info } = storeToRefs(projectStore);
</script>

<style lang="less" scoped>
h1 {
  width: 100%;
  font-size: 20px;
  font-weight: 700;
}
</style>
