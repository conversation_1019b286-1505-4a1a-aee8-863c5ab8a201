# 前端技术栈
Vue3 + Typescript + Vue-Router + Pinia + ElementPlus

# 项目规范
eslint + prettier + lint-staged + husky + commitlint

# commit 提交信息规范
我们简单采用一个原则：完成一件事情，就提交一次 commit。而不是等到你写完一整天的代码后，才在下班前只提交一次。 
格式：(): 示范： fix(DAO):用户查询缺少username属性 feat(Controller):用户查询接口开发
```
type(必须)
用于说明git commit的类别，只允许使用下面的标识。
feat：新功能（feature）。
fix/to：修复bug，可以是QA发现的BUG，也可以是研发自己发现的BUG。
fix：产生diff并自动修复此问题。适合于一次提交直接修复问题
to：只产生diff不自动修复此问题。适合于多次提交。最终修复问题提交时使用fix
docs：文档（documentation）相关。
style：格式（不影响代码运行的变动）。
refactor：重构（即不是新增功能，也不是修改bug的代码变动）。
perf：优化相关，比如提升性能、体验。
test：增加测试。
chore：构建过程或辅助工具的变动。
revert：回滚到上一个版本。
merge：代码合并。
sync：同步主线或分支的Bug。
chore: 更新依赖/修改脚手架配置等琐事
workflow: 工作流改进
ci: 持续集成相关
types: 类型定义文件更改
wip: 开发中

scope(可选)
1. scope用于说明 commit 影响的范围，比如数据层、控制层、视图层等等，视项目不同而不同。
2. 例如在Angular，可以是location，browser，compile，compile，rootScope， ngHref，ngClick，ngView等。如果你的修改影响了不止一个scope，你可以使用*代替。


subject(必须)
1. subject是commit目的的简短描述，不超过50个字符。
2. 建议使用中文（感觉中国人用中文描述问题能更清楚一些）。
3. 结尾不加句号或其他标点符号。
```

# 项目目录
components  存放公共组件
assets 静态资源
directs 封装的指令
hooks  封装的use函数
stores pinia创建的store
utils  工具函数
    constant.ts  放常量的地方
views  对应一个页面
    home 页面文件夹
        cpns    本页面的组件（非公共组件）
        index.vue


# 项目路由
/home  首页    对应原型  firstpage.html
/learnStatistics     学习统计数据详情页  对应原型 learning.html
/project(/id)    项目学习页    prjmap_v.html
    /project/map   知识地图页    prjmap_v.html
    /project/process   学习进程页    prjview.html
    /project/test   测评方案页    prjtest.html
/learning  视频学习（没有页脚）  对应原型 projectview_v_s.html
/classification   分类结果页                 对应原型 project.html
/shoppinglist  购物车       对应原型shoppinglist.html
    /shoppinglist/prj   材料    对应原型shoppinglist.html
    /shoppinglist/vip   会员    对应原型shoppinglist2.html
/message        消息        对应原型mymessage.html
/userspace     个人空间     对应原型userspace.html

/knowledge  知识地图(占个位置就行)              对应原型 /knowledge.html


# 公共组件
VideoCard
UserinfoCard
UnityMap

# 打版的时候可以这样提交
```
git add .
npx lint-staged   // eslint校验
git commit --no-verify -m 'v1.0.0'
```