import { defineStore } from 'pinia';
import { ref } from 'vue';
import { PrjType, PrjForm } from '@/types/project';
import { BuyStatus, GoodsType } from '@/types/goods';

export const useLearnStore = defineStore('learnintro', () => {
  // 根据LearnType ContentType 重新设定页面展示逻辑 通用变量
  const goodsType = ref<GoodsType>();
  const contentType = ref<PrjForm>(); // 1 为视频形式 2 为文稿形式
  const prjType = ref<PrjType>(); //1、知识讲解类 2、项目学习类 3、学习测试类 4、领域学习
  const buyStatus = ref<BuyStatus>(); //0、代表未购买 1、代表已购买
  // 介绍页数据操作函数
  const setData = (ct: PrjForm, bs: BuyStatus) => {
    contentType.value = ct;
    buyStatus.value = bs;
  };
  // 后续从其他地方传入
  // 在项目介绍页（案例，讲解，领域）需要用到uniqueCode 和 goodsId
  // 在会员商品介绍页（领域）仅仅需要用到 goodsId
  // goodsytype目前在介绍页都不涉及，可以先传入一下试试
  const spuId = ref('');
  // const goodsType = ref('0');
  const goodsId = ref();
  // 学习目录页
  const setintroData = (prj: string) => {
    spuId.value = prj;
    // goodsId.value = gi;
    // learnType.value = lt;
    // goodsType.value = gt;
  };
  return {
    spuId,
    goodsId,
    // 新变量
    contentType,
    goodsType,
    prjType,
    buyStatus,
    setData,
    setintroData
  };
});
