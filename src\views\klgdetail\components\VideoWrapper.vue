<template>
  <div class="main-wrapper">
    <div class="left-header" ref="header" v-if="VideoUrlUrl">
      <div class="title-row" style="display: flex; align-items: center; justify-content: space-between;">
        <div class="title">{{ prjInfo.title }}</div>
        <el-button class="ask-btn" type="info" size="small" plain @click="askDialogVisible = true" style="border: unset; margin-left: 10px;">
          <el-icon style="margin-right: 4px; border: unset;"><QuestionFilled /></el-icon>
          有看不懂的地方点我提问题
        </el-button>
      </div>
<!--      <div class="base-info">-->
<!--        <div class="creater">-->
<!--          <img :src="prjInfo.userCoverPic" class="avatar" />-->
<!--          <div class="name">{{ prjInfo.userName }}</div>-->
<!--        </div>-->
<!--        <div class="time">{{ prjInfo.createTime }}</div>-->
<!--        <el-popover-->
<!--          v-model:visible="descriptionVisible"-->
<!--          placement="bottom-start"-->
<!--          trigger="click"-->
<!--          width="200"-->
<!--          class="custom-popover"-->
<!--        >-->
<!--          &lt;!&ndash; 气泡卡片内容 &ndash;&gt;-->
<!--          <div class="popover-content">-->
<!--            <span v-html="prjTargetObj.description"></span>-->
<!--            <span v-html="prjTargetObj.purpose"></span>-->
<!--          </div>-->

<!--          &lt;!&ndash; 触发内容 &ndash;&gt;-->
<!--          <template #reference>-->
<!--            <div class="function-tag">项目介绍</div>-->
<!--          </template>-->
<!--        </el-popover>-->
<!--      </div>-->
    </div>
    <div class="body" v-if="VideoUrlUrl">
      <div class="left">
        <div class="left-video-wrapper">
          <!-- 左侧的video -->
          <div class="left-main">
            <template v-if="VideoUrlUrl">
              <XgPlayer
                :videoSrc="VideoUrlUrl"
                :canBeWiden="false"
                ref="player"
                style="width: 100%; height: 100%;"
                @timeupdate="handleTimeupdate"
              />
            </template>
          </div>
        </div>
      </div>
    </div>
    <AskQuestionDialog
      ref="askDialogRef"
      v-if="askDialogVisible"
      :prj-info="props.prjInfo"
      :transmit-spuId="props.transmitSpuId || ''"
      :transmit-chapterId="props.transmitChapterId || ''"
      :transmit-uniqueCode="props.transmitUniqueCode"
      :type="2"
      @close="askDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">

//留存"projectDetailData?.videoUrl"
import PrjStudyInfo from '@/components/PrjStudyInfo.vue';
import XgPlayer from '@/components/XgPlayer.vue';
import { defineExpose } from 'vue';
import { ArrowDown, QuestionFilled } from '@element-plus/icons-vue';
import type { PrjinfoItf, PrjTag as PrjTagType } from '@/types/learning';
import PrjTag from '@/views/learning/components/PrjTag.vue';
import anime from 'animejs/lib/anime.es.js';
import AskQuestionDialog from '../AskQuestionDialog.vue';
import { useRoute } from 'vue-router';
import { provide, ref, onMounted, watch, computed, inject } from 'vue';
import { getSeconds } from '@/utils/Video';
import type { VideoCaptionListObj } from '@/types/learning';

const learnedPct = ref();
const graspKlgPct = ref();
const targetKlgs = ref();

const props = defineProps(['prjInfo', 'targetKlgs', 'prjTargetObj', 'videoUrl', 'transmitSpuId', 'transmitChapterId', 'transmitUniqueCode']);

const klg = inject('klg') as Ref;
const saveType = inject('saveType') as Ref;
const prjTargetObj = ref();

onMounted(async () => {
  saveType.value = 0;
  learnedPct.value =
    klg.value.klgNumbers == 0 ? 0 : Math.floor((klg.value.learned / klg.value.klgNumbers) * 100);
  graspKlgPct.value =
    klg.value.klgNumbers == 0 ? 0 : Math.floor((klg.value.graspKlg / klg.value.klgNumbers) * 100);
});

const tags = ref<PrjTagType[]>([]);
const header = ref();
const float = ref();
const descriptionVisible = ref(false);
const tagsVisible = ref(false);

function handleClose(event: MouseEvent) {
  if (
    header.value.contains(event.target as HTMLElement) ||
    (event.target as HTMLElement).closest('.el-popover')
  ) {
    return;
  }
  descriptionVisible.value = false;
  tagsVisible.value = false;
}

onMounted(() => {
  document.addEventListener('click', handleClose);
  watch(
    () => descriptionVisible || tagsVisible,
    () => {
      if (descriptionVisible || tagsVisible) {
        anime({
          targets: header.value,
          backgroundColor: {
            value: '#F2F2F2',
            duration: 300,
            easing: 'linear'
          }
        });
      } else {
        anime({
          targets: header.value,
          backgroundColor: {
            value: '#FFFFFF',
            duration: 300,
            easing: 'linear'
          }
        });
      }
    }
  );
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClose);
});

const VideoUrlUrl = ref();

watch(
  () => props,
  (newvalue) => {
    if (newvalue.prjTargetObj) prjTargetObj.value = newvalue.prjTargetObj;
    if (newvalue.targetKlgs) targetKlgs.value = newvalue.targetKlgs;
    if (newvalue.prjInfo.prjTags) tags.value = newvalue.prjInfo.prjTags as Array<PrjTagType>;
    if (newvalue.videoUrl) {
      VideoUrlUrl.value = newvalue.videoUrl;
      console.log("url变化");
    }
  },
  { deep: true, immediate: true }
);

function handleExpandTag() {
  tagsVisible.value = !tagsVisible.value;
}

// 简化的视频相关数据
const projectDetailData = ref();
const player = ref<InstanceType<typeof XgPlayer> | null>(null);
provide('playerRef', player); // 关键：将 player ref 提供给子组件

// 初始化视频数据
onMounted(async () => {
  // 这里可以根据需要从props或其他地方获取视频URL
  // 暂时使用一个示例URL或从props中获取

  console.log('看看都有啥')
  console.log(props.prjInfo)
  console.log(props.prjTargetObj)
  console.log(props.targetKlgs)
  console.log('看看都有啥')
  if (props.prjInfo?.videoUrl) {
    projectDetailData.value = {
      videoUrl: props.videoUrl
    };
  }
});

const askDialogVisible = ref(false);
const route = useRoute();
const spuId = route.query.spuId as string;
const chapterId = route.query.chapterId as string;
const transmitUniqueCode = computed(() => props.prjInfo?.uniqueCode || '');

const askDialogRef = ref();
//const videoCaptionList = ref<VideoCaptionListObj[][]>([]); // 明确类型
function handleTimeupdate(currentTime: number) {
  console.log(currentTime)
  if (askDialogRef.value && typeof askDialogRef.value.setCurrentTime === 'function') {
    askDialogRef.value.setCurrentTime(currentTime);
  }
}

// 监听askDialogVisible，控制PIP
watch(
  () => askDialogVisible.value,
  (visible) => {
    if (player.value && player.value.requestPIP && player.value.exitPIP) {
      if (visible) {
        player.value.requestPIP();
      } else {
        player.value.exitPIP();
      }
    }
  }
);

defineExpose({});
</script>

<style scoped lang="less">
.main-wrapper {
  width: 100%;
  display: flex;
  height: 500px;
  flex-direction: column;
}

.left-header {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  //background-color: #f2f2f2;
  //margin-left: 10px;
  //width: 668px;
  .title {
    font-size: 18px;
    font-weight: 700;
    margin: 10px 0px 10px 0;
  }
  .title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .base-info {
    font-size: 12px;
    display: flex;
    align-items: center;
    color: var(--color-deep);
    .creater {
      display: flex;
      align-items: center;
      .avatar {
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-right: 5px;
      }
      .name {
      }
    }
    .time {
      margin: 0 10px;
    }
    .function-tag {
      margin: 0 10px;
      padding: 0 10px;
      border-radius: 10px;
      border: 1px solid var(--color-deep);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
      &:hover {
        background-color: var(--color-inactive-project);
        cursor: pointer;
      }
    }
  }
}

.body {
  display: flex;
  justify-content: center;
}

.left-video-wrapper {
  margin-left: 0;
  margin-right: 0;
  width: 900px;
  height: 520px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left-main {
  display: flex;
  flex-direction: column;
  width: 900px;
  margin: 0 auto;
}

.down-wrapper {
  margin: 0 auto;
  
  .prj-study-info {
  }
  
  .tags {
    align-items: center;
  }
}

.float {
  width: 100%;
  z-index: 500;
  top: 70px;
  background-color: #f2f2f2;
  padding: 0 10px 10px 10px;
  position: absolute;
  display: grid;
  grid-template-columns: 10fr 7fr;
  grid-auto-rows: auto;
  box-shadow: 0px 8px 5px 4px #f2f2f2;
  
  .float-left {
    grid-column: 1 / 2;
    word-wrap: break-word;
    overflow: hidden;
  }
  
  .float-right {
    grid-column: 2 / 3;
  }
}

.tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 15px;
  
  .prj-tag {
    width: 80px;
    height: 20px;
    margin: 5px 5px;
    border-radius: 10px;
    color: var(--color-black);
    background-color: var(--color-inactive-project);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-sizing: border-box;
    transition: border 0.3s ease;

    .tag-content {
      max-width: 70px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &:hover {
      border: 1px solid var(--color-deep);
    }
  }
  
  .more {
    width: 18px;
    height: 18px;
    margin: 5px 5px;
    border-radius: 10px;
    color: var(--color-black);
    background-color: var(--color-inactive-project);
    
    &:hover {
      border: 1px solid var(--color-deep);
    }
  }
}

.custom-popover {
  .el-popper {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #dcdfe6;
    padding: 10px;
  }

  .el-popper__arrow {
    border-color: transparent transparent #f4f4f9 transparent;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>