/**
 * 支付
 */
import { http } from '@/apis';
export enum PaymentChannel {
  Free = 0,
  Alipay = 1,
  Wechat = 2
}
interface PaymentParams {
  skuId: string;
  channel: PaymentChannel;
  orderNo?: string;
}

// 获取支付二维码
export function getPaymentcodeApi(data: PaymentParams) {
  return http.request({
    method: 'post',
    url: '/pay/create',
    data,
    loading: false
  });
}

// 支付免费商品
export function payFreeApi(orderNo: string) {
  return http.request({
    method: 'post',
    url: '/pay/free/purchase',
    data: {
      orderNo: orderNo
    },
    loading: false
  });
}

export interface QueryOrderResult {
  orderNo: string;
  subtotal: number;
  discount: number;
  actualPaidAmount: number;
  orderStatus: number;
  createTime: string;
}

// 轮询获取
export function getPollResultApi(orderNo: string) {
  return http.request({
    method: 'get',
    url: '/pay/query/orderNo',
    params: {
      orderNo
    },
    loading: false
  });
}


//查找是否有创建好的订单
export function getCreatedOrder(skuId:string) {
  return http.request({
    method: 'get',
    url: '/pay/query/order',
    params: {
      skuId
    },
  })
}

export function getProjectGoodsAmountApi(spuId: string) {
  return http.get(
    '/project-goods/getProjectGoodsAmount',
    {
      spuId
    },
    false
  );
}

export function getProjectRecommendApi(spuId: string) {
  return http.get(
    '/vip-goods/recommend',
    {
      spuId
    },
    false
  );
}
//个人空间推荐
export function getUserRecommendApi() {
  return http.get('/userSpace/vip/recommend', {}, false);
}
//会员商品获取skuList
export function getVipSkuListApi(spuId: string) {
  return http.get(
    '/vip-goods/introduce/order/query',
    {
      spuId
    },
    false
  );
}

export function getSkuListApi(spuId: string) {
  return http.get(
    '/vip-goods/getList',
    {
      spuId
    },
    false
  );
}
//取消订单
export function cancelPaymentApi(orderNo: string) {
  return http.request({
    method: 'post',
    url: '/pay/order/close',
    data: {
      orderNo
    }
  });
}
//删除订单
export function deletePaymentApi(orderNo: string) {
  return http.request({
    method: 'post',
    url: '/pay/order/remove',
    data: {
      orderNo
    }
  });
}
