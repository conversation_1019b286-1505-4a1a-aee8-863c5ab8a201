.paragraph-wrapper {
  margin-bottom: 10px;

  .select_to_ask_time {
    width: 70px;
    font-weight: 600;
    color: #333333;
    font-size: 12px;
    margin-bottom: 5px;
    user-select: none;

    &:hover {
      cursor: pointer;
      color: var(--color-theme-project);
    }
  }

  .text {
    &:deep(.highlight2) {
      background-color: yellow;
      cursor: pointer;
      font-weight: 700;
    }

    &:deep(.text-hover) {
      color: var(--color-theme-project);
      cursor: pointer;
    }

    &:deep(.text-hover:hover) {
      font-weight: 700;
    }
  }
}

.content-card {
  color: #333333;
  max-width: 100%;
  max-height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 0;

  // overflow-y: scroll;
  .tool {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 37px;
    padding-left: 10px;
    margin-bottom: 10px;
    .remind {
      font-size: 12px;
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }
    }
    .mode {
      margin-left: 10px;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      display: flex;
      align-items: center;

      .el-switch {
        margin-right: 10px;
      }
    }

    .input {
      margin-top: 5px;
      margin-right: 10px;
      .custom-el-input :deep(.el-input__inner)::placeholder {
        font-size: 12px;
      }
      .custom-el-input {
        height: 25px;
        width: 230px;
      }
    }

    .back-to-video {
      margin-right: 10px;

      .el-button {
        background: var(--color-theme-project);
        border-color: var(--color-theme-project);
        color: white;
        font-size: 12px;
        padding: 6px 12px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--color-theme-project-hover, #409eff);
          border-color: var(--color-theme-project-hover, #409eff);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .content-text-wrapper {
    // height: 420px;
    // height: 100%;
    width: 100%;
    overflow-y: auto;
    word-wrap: break-word;
    padding: 0 10px;
    margin-top: 2px;
    height: calc(100vh - 66px - 70px - 60px);
    position: relative;
    &.learning {
      min-height: calc(100vh - 66px - 70px - 100px);
      max-height: calc(100vh - 60px - 70px); // 里面的高度要比calc(100vh - 60px - 70px - 40px)小
    }
  }

  &:deep(.search-keys) {
    color: red;
    cursor: pointer;
    font-weight: 700;
  }
  &:deep(.highlight2) {
    // color: var(--color-theme-project);
    background-color: v-bind(matchingColor);
    cursor: pointer;
    font-weight: v-bind(matchingHeight);
  }
  &:deep(.activeElement) {
    background-color: #ff9632;
  }
}
.floating-content {
  padding: 6px 12px;
  background: var(--color-theme-project);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family:
    '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 14px;
  font-weight: 400;
  .floating-content-item {
    &:hover {
      // background-color: #C5D5EA;
      font-weight: 700;
      cursor: pointer;
    }
  }
}
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease;
  transform-origin: top; /* 设置动画起点为左侧 */
}

.scale-enter-from,
.scale-leave-to {
  transform: scaleY(0); /* 从左侧开始水平缩放 */
  opacity: 0;
}

//.active:deep(.text-hover) {
//color: red;
//font-weight: 700;
//cursor: pointer;
//}
