<template>
  <div ref="warpper" class="warpper">
    <div class="left">
      <div class="title">{{ areaInfo.goodsName }}</div>
      <div class="base-info">
        <div class="creater">
          <!-- todo -->
          <!-- <img :src="areaInfo.editorPic" class="avatar" /> -->
          <div class="name">{{ areaInfo.editorName }}</div>
        </div>
        <div class="time">{{ areaInfo.createTime }}</div>
        <el-popover
          v-model:visible="descriptionVisible"
          placement="bottom-start"
          trigger="click"
          width="200"
          class="custom-popover"
        >
          <!-- 气泡卡片内容 -->
          <div class="popover-content">
            <span v-html="areaInfo.goodsDescribe"></span>
            <!-- <span v-html="areaTargetObj.purpose"></span> -->
          </div>

          <!-- 触发内容 -->
          <template #reference>
            <div class="function-tag">领域介绍</div>
          </template>
        </el-popover>
        <!-- <div class="function-tag" @click="descriptionVisible = !descriptionVisible">项目介绍</div> -->
        <!-- <div class="function-tag">案例知识地图</div> -->
      </div>
    </div>
    <div class="right">
      <!-- <PrjStudyInfo
        ref="studyInfo"
        :knowledgeSum="klg.klgNumbers"
        :studyPercent="learnedPct"
        :acquaintePercent="graspKlgPct"
        :targetKlgs="prjType == PrjType.case ? null : targetKlgs"
        class="prj-study-info"
      ></PrjStudyInfo> -->

      <!-- <div v-if="tags.length <= 6" class="tags">
        <el-tooltip v-for="tag in tags" :key="tag.id" :content="tag.content" effect="customized">
          <PrjTag :label="tag.content" class="prj-tag"></PrjTag>
        </el-tooltip>
      </div> -->
      <!-- <div v-else class="tags">
        <el-tooltip
          v-for="tag in tags.slice(0, 6)"
          :key="tag.id"
          :content="tag.content"
          effect="customized"
        >
          <PrjTag :label="tag.content" class="prj-tag"></PrjTag>
        </el-tooltip>
        <div ref="more">
          <el-icon class="more" @click="handleExpandTag"><ArrowDown /></el-icon>
        </div>
      </div> -->
    </div>
    <!-- <Transition name="fade">
      <div v-if="descriptionVisible || tagsVisible" ref="float" class="float">
        
        <div v-if="tagsVisible" class="float-right">
          <div class="tags">
            <el-tooltip
              v-for="tag in tags.slice(6)"
              :key="tag.id"
              :content="tag.content"
              effect="customized"
            >
              <div class="prj-tag">
                <div class="tag-content">
                  {{ tag.content }}
                </div>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </Transition> -->
  </div>
</template>

<script setup lang="ts">
// import PrjTag from './PrjTag.vue';
import PrjStudyInfo from '@/components/PrjStudyInfo.vue';
import { getPrjMoreInfoApi } from '@/apis/learning';
import type { PrjinfoItf, PrjTag as PrjTagType } from '@/types/learning';
import anime from 'animejs/lib/anime.es.js';
import { PrjType } from '@/types/project';
import { AreaData } from '@/types/area';

const areaInfo = inject('areaInfo') as Ref<AreaData>;
const route = useRoute();
const spuId = route.query.spuId as string;
const targetKlgs = ref<any>([]);
// for (let i = 0; i < 10; ++i) {
//   targetKlgs.value.push({ id: i, klgTitle: '知识' + i + '12312312' });
// }
const klg = ref<{
  klgNumbers: number;
  learned: number;
  graspKlg: number;
}>({ klgNumbers: 0, learned: 0, graspKlg: 0 });
const areaTargetObj = ref<{
  description: string;
  purpose: string;
}>({ description: '', purpose: '' }); // 项目描述和项目目标
const learnedPct = ref();
const graspKlgPct = ref();
const descriptionVisible = ref(false);
const tagsVisible = ref(false);
const warpper = ref();
const more = ref();
const float = ref();
const studyInfo = ref();
const prjType = inject('prjType') as Ref;
// const tags = areaInfo.value.prjTags as Array<PrjTagType>;
// const remainingTags = computed(() => {
//   return tags.length > 6 ? tags.slice(6) : []; // 只返回第7个及之后的标签
// });

function handleClose(event: MouseEvent) {
  if (warpper.value.contains(event.target as HTMLElement)) {
    return;
  }
  descriptionVisible.value = false;
  tagsVisible.value = false;
  studyInfo.value.closeExpandTag();
}

function handleExpandTag() {
  tagsVisible.value = !tagsVisible.value;
  // anime({
  //   targets: more.value,
  //   rotate: {
  //     value: '+=0.5turn',
  //     duration: 100,
  //     easing: 'easeInOutQuad'
  //   }
  // });
}

onMounted(async () => {
  //console.log('22', areaInfo.value);
});

onMounted(() => {
  document.addEventListener('click', handleClose);
  watch(
    () => descriptionVisible.value || tagsVisible.value,
    () => {
      if (descriptionVisible.value || tagsVisible.value) {
        anime({
          targets: warpper.value,
          backgroundColor: {
            value: '#F2F2F2',
            duration: 300,
            easing: 'linear'
          }
        });
      } else {
        anime({
          targets: warpper.value,
          backgroundColor: {
            value: '#FFFFFF',
            duration: 300,
            easing: 'linear'
          }
        });
      }
    }
  );
});
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClose);
});
</script>

<style scoped lang="less">
.warpper {
  position: relative;
  flex-grow: 1;
  // max-width: 1400px;
  background-color: white;
  font-size: 12px;
  font-family:
    '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0';
  font-weight: 400;
  display: grid;
  grid-template-columns: 10fr 7fr;
  padding: 0 0 0 10px;
  //box-shadow: 0px 1px 5px 4px #f2f2f2;
  .left {
    width: calc(61.8vw - 10px);
    .title {
      font-size: 20px;
      font-weight: 700;
      margin: 10px 0px 10px 0;
    }
    .base-info {
      display: flex;
      align-items: center;
      color: var(--color-deep);
      .creater {
        display: flex;
        align-items: center;
        .avatar {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
        .name {
        }
      }
      .time {
        margin: 0 10px;
        //font-size: 12px;
      }
      .function-tag {
        margin: 0 10px;
        padding: 0 10px;
        border-radius: 10px;
        border: 1px solid var(--color-deep);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;

        &:hover {
          background-color: var(--color-inactive-project);
          cursor: pointer;
        }
      }
    }
  }
  .right {
    .prj-study-info {
      // margin: 5px 0px 0px 0;
    }
    .tags {
      align-items: center;
    }
  }
  .float {
    width: 100%;
    z-index: 500;
    top: 70px;
    background-color: #f2f2f2;
    padding: 0 10px 10px 10px;
    position: absolute;
    display: grid;
    grid-template-columns: 10fr 7fr;
    grid-auto-rows: auto;
    box-shadow: 0px 8px 5px 4px #f2f2f2;
    .float-left {
      grid-column: 1 / 2;
      word-wrap: break-word;
      overflow: hidden;
    }
    .float-right {
      grid-column: 2 / 3;
    }
  }
  .tags {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    margin-left: 15px;
    .prj-tag {
      width: 80px;
      height: 20px;
      margin: 5px 5px;
      border-radius: 10px;
      color: var(--color-black);
      background-color: var(--color-inactive-project);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-sizing: border-box;
      transition: border 0.3s ease;

      .tag-content {
        max-width: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:hover {
        border: 1px solid var(--color-deep);
      }
    }
    .more {
      width: 18px;
      height: 18px;
      margin: 5px 5px;
      border-radius: 10px;
      color: var(--color-black);
      background-color: var(--color-inactive-project);
      &:hover {
        border: 1px solid var(--color-deep);
      }
    }
  }
}
.custom-popover {
  .el-popper {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1); /* 修改阴影 */
    border: 1px solid #dcdfe6; /* 增加边框 */
    padding: 10px; /* 增加内边距 */
  }

  .el-popper__arrow {
    border-color: transparent transparent #f4f4f9 transparent; /* 修改箭头颜色 */
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
