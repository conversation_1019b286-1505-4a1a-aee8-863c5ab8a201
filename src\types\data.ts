import type { Klg } from './knowledge';

// 回答数据类型
export interface answerData {
  answerId: number; //回答自增id
  answerKlgs: Array<Klg>; //知识点名称列表
  answerExplanation: string; //解释
  createTime: string; //回答时间
  creatorName: string; //回答者名称
  modifiedTime: string; //修改时间
}
// 问题数据类型
export interface questionData {
  questionId: number; //问题自增id
  associatedWords: string; //关联文本内容
  keyword: string; //关键字内容
  questionType: string; //问题类型，是什么、为什么、怎么做
  questionNecessity: number; //问题必要性 1必须问题，2参考问题
  creatorId: string; //创建者id
  creatorName: string; //创建者名字
  createTime: string; //创建时间
  explanation: string; //解释说明
  answerList: Array<answerData>; //回答列表
}
// 测评题目数据类型
export interface documentData {
  examId: number; //测试题目id
  sectionId: number; //小节id
  spuId: string; //项目id
  order: number; //题目序号
  sectionTitle: string; //小节名称
  examType: number; //1.填空 2选择 3判断 4问答
  examTitle: string; //题目
  examChoices?: Array<string>; //选项
  examAnswer: string | boolean; //答案
  myAnswer: string; //我的答案，错题中会有
  examExplanation: string; //解释说明
  questionList: Array<questionData>; //问题列表
  hasAccess: boolean;
}
export interface collectData extends documentData {
  nextId: number;
}
// 获取测评时向后端传递的参数数据类型
export interface getDocumentListParams {
  current: number;
  limit: number;
  projectId: string;
  sectionId: number; //与接口文档写的都用冲突
}
