import { defineStore } from 'pinia';
import { type PrjInfo, PrjForm, PrjType } from '@/types/project';
import { BuyStatus, GoodsType } from '@/types/goods';

export const userPrjInfoStore = defineStore('prjInfo', {
  state: () => ({
    info: {
      title: '',
      coverPic: '',
      purpose: '',
      description: '',
      learnedTime: 0,
      masteredKlg: 0,
      learnedKlg: 0,
      prjForm: PrjForm.video,
      chapterList: [
        {
          chapterId: 0,
          chapterName: ''
        }
      ],
      quesList: [
        {
          questionId: 0,
          keyword: '',
          questionType: '',
          questionWeight: '',
          questionNecessity: '',
          learned: true
        }
      ],
      price: '0',
      klgCount: 0,
      studyTime: 0,
      questionCount: 0,
      studyInstructions: '',
      learnedQuestion: 0,
      startTime: '',
      expirationTime: '',
      hasPermission: 0
    }
  }),
  getters: {},
  actions: {
    setPrjInfo(info: PrjInfo) {
      // console.log('old', this.info);
      this.info = info;
      // console.log('new', this.info);
    },
    setPrice(price: string) {
      this.info.price = price;
    },
    setStudyTime(studyTime: number) {
      //判断this.info的类型
      if ('studyTime' in this.info) {
        this.info.studyTime = studyTime;
      }
    }
  }
});
