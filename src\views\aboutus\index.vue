<template>
  <div class="about-us">
    <div class="container">
      <div class="up">
        <div class="title">换个方式 &nbsp;爱上学习</div>
      </div>
      <div class="down">
        <div class="about">
          <div class="us">关于我们</div>
          <div class="subheading">无尽本源 (EndlessOrigin)</div>
          <div class="downabout">
            <div class="downaboutleft">
              <div class="logo">
                <!-- <h1 class="chinese">本源</h1>
                <h3 class="eng">EndlessOrigin</h3> -->
                <img src="@/assets/logo.png" alt="" style="width: 80px;">
              </div>
            </div>
            <div class="downaboutright">
              <div class="content">
                {{ detail }}
              </div>
            </div>
          </div>
        </div>
        <div class="contact">
          <div class="left-content">
            <div class="contitle">联系我们</div>
            <div class="contactdown">
              <div class="phone">
                <div class="phoneicon">
                  <el-icon style="font-size: 18px">
                    <Iphone />
                  </el-icon>
                </div>
                <div class="phonetext">电话：188XXXX0000</div>
              </div>
              <div class="email">
                <div class="emailicon">
                  <el-icon style="font-size: 18px">
                    <Message />
                  </el-icon>
                </div>
                <div class="emailtext">邮箱：<EMAIL></div>
              </div>
            </div>
          </div>
          <div class="right-content">
            <div class="contitle">微信客服</div>
            <div class="wxcontainer">
              <img src="@/assets/weixin.jpg" alt="" class="wximg">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
//按照文本原有格式输入即可
let detail = `北京无尽本源科技有限公司成立于2022年，是一家由半导体行业和互联网行业资深人士联合创建的在线职业教育公司，致力于打造一个全新的结合项目式学习、知识图谱、推荐系统和表现性评价的在线职业教育平台。该平台通过项目式学习实践案例引发强化学习动机，利用NLP知识图谱技术降低学习过程的难度，基于推荐系统实现学习过程的个性化，通过表现性评价体系实现学习效果的多维度评价，为企业内部培训和个人职业技能提升提供一个全新的高效学习平台。`;
</script>

<style lang="less" scoped>
.about-us {
  width: 100%;
  display: flex;
  // height: 500px;
  // 百分比可调蓝色占整个页面高度的比例
  background: linear-gradient(to bottom, var(--color-theme-project) 0%, var(--color-theme-project) 40%, #fafafa 40%, #fafafa);
  /* 水平居中 */
  justify-content: center;

  .container {
    display: flex;
    width: 1400px;
    flex-direction: column;
    align-items: center;

    .up {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      width: 1000px;


      .title {
        margin-top: 113px;
        margin-left: 60px;
        margin-bottom: 92px;
        width: 432px;
        height: 48px;
        font-size: 48px;
        font-weight: 700;
        color: rgba(255, 255, 255, 0.996);
        line-height: normal;
        font-feature-settings: 'kern';
        font-family: 'Alimama FangYuanTi VF SemiBold', 'Alimama FangYuanTi VF Regular', 'Alimama FangYuanTi VF', sans-serif;
      }
    }

    .down {
      display: flex;
      flex-direction: column;
      align-items: center;

      .about {
        display: flex;
        flex-direction: column;
        // align-items: center;
        width: 1000px;
        height: 320px;
        background-color: rgba(255, 255, 255, 0.996);
        border-radius: 5px;
        margin-top: 20px;
        margin-bottom: 20px;

        .us {
          width: 64px;
          height: 16px;
          margin-top: 31px;
          margin-left: 60px;
          font-size: 16px;
          font-weight: 700;
          color: #333333;
          line-height: normal;
          font-feature-settings: 'kern';
          font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif
        }

        .subheading {
          margin: 0 auto;
          margin-top: 43px;
          margin-bottom: 44px;
          font-size: 24px;
          font-weight: 700;
          color: #333333;
          line-height: normal;
          font-feature-settings: 'kern';
          font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
        }

        .downabout {
          width: 1000px;
          display: flex;
          justify-content: space-around;

          .content {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            line-height: normal;
            font-feature-settings: 'kern';
            line-height: 20px;
            width: 742px;
            height: 73px;
            margin-right: 42px;
            //用于控制文本格式
            white-space: pre-wrap;
            text-indent: 30px;
            font-family: '阿里巴巴普惠体 3.0 55  L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
          }

          .logo {
            color: var(--color-theme-project);
            font-feature-settings: 'kern';
            font-weight: 700;
            margin-right: 70px;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-left: 80px;
            margin-right: 72px;

            .chinese {
              font-size: 32px;
              font-family: 'Alimama FangYuanTi VF SemiBold', 'Alimama FangYuanTi VF Regular',
            'Alimama FangYuanTi VF', sans-serif;
            }

            .eng {
              font-size: var(--fontsize-small-project);
              font-weight: 400;
              color: var(--color-theme-project);
              font-family: 'Alimama FangYuanTi VF', sans-serif;
            }
          }
        }
      }

      .contact {
        display: flex;
        
        width: 1000px;
        height: 196px;
        background-color: rgba(255, 255, 255, 0.996);
        border-radius: 5px;
        margin-bottom: 20px;

        .left-content{
          width: 500px;
          .contitle {
            width: 64px;
            height: 16px;
            margin-top: 31px;
            margin-left: 60px;
            font-size: 16px;
            font-weight: 700;
            color: #333333;
            line-height: normal;
            font-feature-settings: 'kern';
            font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
          }

          .contactdown {
            display: flex;
            flex-direction: column;
            width: 500px;

            .phone {
              display: flex;
              flex-direction: row;
              margin-top: 43px;
              margin-left: 80px;

              .phoneicon {
                margin-right: 18px;
              }

              .phonetext {
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                line-height: normal;
                font-feature-settings: 'kern';
                font-family: '阿里巴巴普惠体 3.0 55  L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
              }
            }

            .email {
              display: flex;
              flex-direction: row;
              margin-top: 19px;
              margin-left: 80px;

              .emailicon {
                margin-right: 18px;
              }

              .emailtext {
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                line-height: normal;
                font-feature-settings: 'kern';
                font-family: '阿里巴巴普惠体 3.0 55  L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
              }
            }
          }
        }

        .right-content{
          
          .contitle {
            width: 64px;
            height: 16px;
            margin-top: 31px;
            margin-left: 60px;
            font-size: 16px;
            font-weight: 700;
            color: #333333;
            line-height: normal;
            font-feature-settings: 'kern';
            font-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
          }

          .wxcontainer{
            display: flex;
            width: 500px;
            
            
            .wximg{
              margin-top: 10px;
              margin-left: 80px;
              width: 130px;
            }
          }
          
        }
      }
    }
  }
}
</style>
