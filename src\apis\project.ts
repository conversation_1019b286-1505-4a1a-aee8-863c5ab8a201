import { http } from '@/apis';
//import type { PrjForm, PrjType } from '@/types';
//获取项目介绍详情接口参数
interface prjIntroduceParams {
  /**
   * 项目唯一标识
   */
  spuId: string;
  skuId?: string;
}
//获取项目介绍详情
export function getPrjIntroduceApi(data: prjIntroduceParams) {
  return http.post<{
    studyInstructions: string;
    klgNumbers: number;
    prjType: 2;
    latestChapterId: number;
    skuList: any[];
    chapterList: any[];
    learned: number;
    description: string;
    title: string;
    userName: string;
    goodsType: 0;
    prjForm: 1;
    buyStatus: 1;
    spuId: string;
    coverPic: string;
    userCoverPic: string;
    graspKlg: number;
    skuId: string;
  }>('/project-goods/getPrjIntroduce', data);
}


export function getPrjPart(spuId: string) {
  return http.request<{
    prjType: number;
    description: string;
    purpose: string;
    createTime: string;
    prjForm: number;
    title: string;
    userName: string;
    userCoverPic: string;
    areaTags: Array<{
      areaCode: string;
      title: string;
    }>;
    targetKlgList: Array<{
      klgCode: string;
      klgTitle: string;
      choose: boolean;
    }>;
    prjTags: string;
    klgCount: number;
    masteredKlgCount: number;
    fullyMasteredKlgCount: number;
  }>({
    method: 'get',
    url: 'project-goods/part/query',
    params: {
      spuId
    }
  });
}
//以前 getPartProApi

export function getPrjAll(spuId: string, chapter: number) {
  return http.request<{
    validIndex: number;
    prjId: number;
    uniqueCode: string;
    prjForm: number;
    prjType: number;
    chapterList: Array<{
      chapterId: number;
      chapterName: string;
      chapterNum?: number;
      chaCoverPic?: string;
      videoUrl?: string;
      videoCaptionList?: Array<Array<{
        oid: number;
        startTime: string;
        endTime: string;
        caption: string;
        beginning: number;
      }>>;
      wordContentType?: number;
      contentId?: number;
      wordContent?: string;
      preview?: string;
    }>;
  }>({
    method: 'get',
    url: '/project-goods/info',
    params: {
      spuId,
      chapter
    }
  });
}
//以前 getPrjMoreInfoApi
