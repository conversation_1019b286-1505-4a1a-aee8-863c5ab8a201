import { defineStore } from 'pinia';
import { startsWith, endsWith } from 'lodash-es';
import he from 'he';
//import { WorkerType } from '@/utils/constant';
import { ref } from 'vue';
// import { ProofCondItem, ProofListItem } from "@/utils/type"
export enum WorkerType {
  draft = 1,
  video = 2,
  exercise = 3,
  proof = 4
}
export const useWordStore = defineStore('wordStore', () => {
  // 用于存储后端拿到的初始数据, 每次计算高亮用这个
  const constContent = ref<any | any[]>();
  const constProofContent = ref<any | any[]>();
  // 下面是存储计算完的数据，用来渲染的内容
  const wordContent = ref();
  const videoCaptionList = ref<any[]>([]);
  const proofList = ref<any[]>([]);
  const exercise = ref({
    type: 0,
    stem: '',
    content: [],
    answer: '',
    explanation: ''
  });
  let regString = '';
  let regStringBuildFlag = true; // 用于标识是否需要建匹配串 default: true
  // 视频
  let videoStringList: Array<{ list: Array<string>; start: number }> = [];
  let originalVideoStringList: Array<{
    list: Array<string>;
    start: number;
  }> = [];
  // 习题
  let exerciseStringList: Array<{ list: Array<string>; start: number }> = [];
  let originalExerciseStringList: Array<{
    list: Array<string>;
    start: number;
  }> = [];
  // 文稿
  let draftStringList: Array<{ list: Array<string>; start: number }> = [];
  let originalDraftStringList: Array<{ list: Array<string>; start: number }> = [];
  // 论证块
  let proofRegString = '';
  let proofRegStringBuildFlag = true; // 用于标识是否需要建匹配串 default: true(由于论证块会和文稿一起用，所以需要单独建串)
  let proofStringList: Array<{ list: Array<string>; start: number }> = [];
  let originalProofStringList: Array<{
    list: Array<string>;
    start: number;
  }> = [];
  // other
  let unitList: Array<{
    tagName: string;
    children: any[];
    index: number;
    qids: number[];
    highlight: boolean;
    stringIndex: number;
  }> = [];
  let uncommonWordMap = new Map<string, string>();
  const getOriginalStringList = (type: number | string): string | string[] => {
    let originalStringList: any[] = [];
    switch (type) {
      case WorkerType.draft:
      case 'draft':
        originalStringList = originalDraftStringList;
        break;
      case WorkerType.video:
      case 'video':
        originalStringList = originalVideoStringList;
        break;
      case WorkerType.exercise:
      case 'exercise':
        originalStringList = originalExerciseStringList;
        break;
      case WorkerType.proof:
      case 'proof':
        originalStringList = originalProofStringList;
        break;
    }
    const list = originalStringList.map((item) => {
      return item.list.join('');
    });
    if (type === WorkerType.draft || type === 'draft') {
      return list.join('');
    } else {
      return list;
    }
  };
  const getStringList = (type: number | string): string | string[] => {
    let stringList: any[] = [];
    switch (type) {
      case WorkerType.draft:
      case 'draft':
        stringList = draftStringList;
        break;
      case WorkerType.video:
      case 'video':
        stringList = videoStringList;
        break;
      case WorkerType.exercise:
      case 'exercise':
        stringList = exerciseStringList;
        break;
      case WorkerType.proof:
      case 'proof':
        stringList = proofStringList;
        break;
    }
    let index = 0;
    const list = stringList.map((item) => {
      const stringList = [];
      let tempQid = null;
      let tempContent = null;
      let tempClassName = null;
      let unMatchTempText = '';
      const regex = /span[^>]*data-qid="([^"]+)"\s+class="([^"]+)"[^>]*>(.*?)<\/span>/;
      for (let i = 0; i < item.list.length; i++) {
        const match = item.list[i].match(regex);
        if (match) {
          const qid = match[1];
          const className = match[2];
          const content = match[3];
          if (unMatchTempText.length > 0) {
            stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
            unMatchTempText = '';
          }
          if (tempQid == null) {
            tempQid = qid;
            tempContent = content;
            tempClassName = className;
          } else if (tempQid == qid && tempClassName == className) {
            tempContent += content;
            tempClassName = className;
          } else {
            stringList.push(
              `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
            );
            tempQid = qid;
            tempContent = content;
            tempClassName = className;
          }
        } else {
          if (tempQid != null) {
            stringList.push(
              `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
            );
          }
          if (startsWith(item.list[i], '<') || endsWith(item.list[i], '>')) {
            if (unMatchTempText.length > 0) {
              stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
              unMatchTempText = '';
            }
            stringList.push(item.list[i]);
          } else {
            unMatchTempText += item.list[i];
          }
          tempQid = null;
          tempContent = null;
          tempClassName = null;
        }
      }
      if (tempQid != null) {
        stringList.push(
          `<span data-qid="${tempQid}" class="${tempClassName}" data-index="${index++}">${tempContent}</span>`
        );
      }
      if (unMatchTempText.length > 0) {
        stringList.push(`<span data-index="${index++}">${unMatchTempText}</span>`);
      }
      return stringList.join('');
    });
    if (type === WorkerType.draft || type === 'draft') {
      return list.join('');
    } else {
      return list;
    }
  };
  const buildLineWord = (node: Node): string => {
    let lineWordList: string[] = [];
    const dfs = (node: Node) => {
      let flag = false;
      // @ts-ignore
      if (node.classList) {
        // @ts-ignore
        if (node.classList.contains('unlineWordContent')) {
          flag = false;
        } else {
          flag = true;
        }
      } else {
        flag = true;
      }
      if (flag) {
        if (node.nodeType == Node.TEXT_NODE) {
          const textContent = node.textContent as string;
          if (textContent?.length == 0) {
            return;
          }
          for (const char of textContent) {
            if (char != '\n') {
              lineWordList.push(he.escape(char));
            }
          }
        } else if (node.nodeType == Node.ELEMENT_NODE) {
          if ((node as HTMLElement).tagName == 'IMG') {
            lineWordList.push((node as HTMLElement).outerHTML);
          } else {
            if ((node as HTMLElement).closest("[class^='inline-equation']")) {
              const element = (node as HTMLElement).closest("[class^='inline-equation']");
              const latexCode = element?.getAttribute('latexCode') as string;
              lineWordList.push(`<script type="math/tex">${latexCode}</script>`);
            } else if ((node as HTMLElement).closest("[class^='equation']")) {
              const element = (node as HTMLElement).closest("[class^='equation']");
              const latexCode = element?.getAttribute('latexCode') as string;
              lineWordList.push(`<script type="math/tex">${latexCode}</script>`);
            } else {
              for (const child of (node as HTMLElement).childNodes) {
                dfs(child);
              }
            }
          }
        }
      }
    };
    for (const childNode of node.childNodes) {
      dfs(childNode);
    }
    return lineWordList.join('');
  };
  // 从wordStore构建草稿内容 并 赋值给contentStore
  const buildDraftContent = (flag?: boolean) => {
    if (flag) {
      wordContent.value = getOriginalStringList(WorkerType.draft);
    } else {
      wordContent.value = getStringList(WorkerType.draft);
    }
  };
  // 从wordStore构建视频内容 并 赋值给contentStore
  const buildVideoContent = (flag?: boolean) => {
    let index = 0;
    let tempList = getConstContent();
    tempList?.forEach((videoCaption: any[]) => {
      videoCaption.forEach((item) => {
        if (flag) {
          item.caption = getOriginalStringList(WorkerType.video)[index++];
        } else {
          item.caption = getStringList(WorkerType.video)[index++];
        }
      });
    });
    videoCaptionList.value = tempList;
  };
  // 从wordStore构建习题内容 并 赋值给contentStore
  const buildExerciseContent = (flag?: boolean) => {
    let index = 0;
    let tempList = [];
    let length;
    if (flag) {
      length = originalExerciseStringList.length;
    } else {
      length = exerciseStringList.length;
    }
    for (let i = 0; i < length; i++) {
      let item = '';
      if (flag) {
        item = getOriginalStringList(WorkerType.exercise)[index++];
      } else {
        item = getStringList(WorkerType.exercise)[index++];
      }
      tempList.push(item);
    }
    exercise.value = transferList2Exercise(tempList);
  };
  // 从wordStore构建论证内容 并 赋值给contentStore
  const buildProofContent = (flag?: boolean) => {
    // true: 提问模式  false: 展示模式
    let index = 0;
    let tempReturnList = [];
    let list = [];
    let tempProofList: string | string[] = [];
    let length;
    if (flag) {
      length = originalProofStringList.length;
      tempProofList = getOriginalStringList(WorkerType.proof);
    } else {
      length = proofStringList.length;
      tempProofList = getStringList(WorkerType.proof);
    }

    for (let i = 0; i < length; i++) {
      // 获取当前项的 HTML 元素字符串
      const item = tempProofList[index++];

      const parser = new DOMParser();
      const doc = parser.parseFromString(item, 'text/html');
      const span = doc.querySelector('span'); // 获取最外层的 span 元素

      if (span && span.hasAttribute('condid') && span.getAttribute('condid')?.trim()) {
        const sort = span.getAttribute('sortid')?.trim();
        let newBlankCond: any = {
          cnt: item.toString()
        };
        if (sort) {
          newBlankCond.sort = parseInt(sort);
        }
        list.push(newBlankCond);
      } else {
        const newBlankBlock: any = {
          conclusion: item.toString(),
          klgProofCondList: list
        };
        tempReturnList.push(newBlankBlock);
        list = [];
      }
    }
    console.log('proofList', tempReturnList);
    proofList.value = tempReturnList;
  };
  // 把worker的list转化为exercise
  const transferList2Exercise = (list: any[]): any => {
    const tempExercise = ref<{
      type: number;
      stem: string;
      content: any[];
      answer: string;
      explanation: string;
    }>({
      type: 0,
      stem: '',
      content: [],
      answer: '',
      explanation: ''
    });
    const tempList = ref<any[]>([]);
    list.forEach((item: any) => {
      const parser = new DOMParser();
      const doc = parser.parseFromString(item, 'text/html');
      const span = doc.querySelector('span'); // 获取最外层的 span 元素
      if (span && span.hasAttribute('etype')) {
        const etype = span.getAttribute('etype');
        if (span.hasAttribute('type')) {
          const type = span.getAttribute('type');
          if (type) {
            tempExercise.value.type = parseInt(type);
          }
        }
        if (etype === 'content') {
          const op = {
            optionId: span.getAttribute('contentid'),
            text: item
          };
          tempList.value.push(op);
        } else {
          tempExercise.value[etype] = item;
        }
      }
    });
    tempExercise.value.content = tempList.value;
    return tempExercise.value;
  };
  // 设置初始内容
  const setConstContent = (data: any) => {
    constContent.value = data;
  };
  // 获得初始内容
  const getConstContent = () => {
    return constContent.value;
  };
  // 设置初始论证块内容
  const setConstProofContent = (data: any) => {
    constProofContent.value = data;
  };
  // 获得初始论证块内容
  const getConstProofContent = () => {
    return constProofContent.value;
  };
  // 获得文稿
  const getDraftContent = () => {
    return wordContent.value;
  };
  // 获得视频
  const getVideoContent = () => {
    return videoCaptionList.value;
  };
  // 获得习题
  const getExerciseContent = () => {
    return exercise.value;
  };
  // 获得论证块
  const getProofContent = () => {
    return proofList.value;
  };
  // 生成内容
  const buildContent = (type: number | string, flag?: boolean) => {
    switch (type) {
      case WorkerType.draft:
        if (flag) buildDraftContent(flag);
        else buildDraftContent();
        break;
      case WorkerType.video:
        if (flag) buildVideoContent(flag);
        else buildVideoContent();
        break;
      case WorkerType.exercise:
        if (flag) buildExerciseContent(flag);
        else buildExerciseContent();
        break;
      case WorkerType.proof:
        if (flag) buildProofContent(flag);
        else buildProofContent();
        break;
    }
  };

  // 处理设置store
  const setStore = (type: number | string, data: any) => {
    console.log('my data', data);
    unitList = data.unitList;
    uncommonWordMap = data.uncommonWordMap;
    switch (type) {
      case WorkerType.draft:
        regString = data.regString;
        draftStringList = data.allStringList;
        originalDraftStringList = data.originalAllStringList;
        break;
      case WorkerType.video:
        regString = data.regString;
        videoStringList = data.allStringList;
        originalVideoStringList = data.originalAllStringList;
        break;
      case WorkerType.exercise:
        regString = data.regString;
        exerciseStringList = data.allStringList;
        originalExerciseStringList = data.originalAllStringList;
        break;
      case WorkerType.proof:
        proofRegString = data.regString;
        proofStringList = data.allStringList;
        originalProofStringList = data.originalAllStringList;
        break;
    }
  };
  return {
    buildLineWord,
    buildContent,
    getDraftContent,
    getVideoContent,
    getExerciseContent,
    getProofContent,
    getConstContent,
    setConstContent,
    getConstProofContent,
    setConstProofContent,
    setStore
  };
});
