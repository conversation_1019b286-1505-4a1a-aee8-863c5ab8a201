<template>
  <el-card :body-style="{ padding: '0px' }">
<!--    <div class="vip" :class="isFree ? 'free' : ''">-->
<!--      <i>{{ isFree ? '免费' : 'VIP' }}</i>-->
<!--    </div>-->
    <div class="image-wrapper" @click="goIntroPage">
      <!-- <img v-image-lazy="coverPic" class="image" v-if="type == 'video'" /> -->
      <!-- TODO: 懒加载好像不太稳定？但我也没搜到这个字段的信息，不知道它对性能的优化程度，先改成笨蛋加载了 -->
      <img :src="coverPic" class="image" v-if="type == 'video'" />
      <img :src="coverPic" class="image" v-else-if="type == 'draft'" />
      <img :src="coverPic" class="image" v-else />
      <template v-if="props.prjForm == PrjForm.video">
        <el-icon :size="40" class="big-video-icon" color="rgb(170, 170, 170)">
          <VideoPlay />
        </el-icon>
      </template>
      <template v-else-if="props.prjForm == PrjForm.draft">
        <el-icon :size="40" class="big-video-icon" color="rgb(170, 170, 170)">
          <DocumentCopy />
        </el-icon>
      </template>
      <template v-else>
        <el-icon :size="40" class="big-video-icon" color="rgb(170, 170, 170)">
          <Share />
        </el-icon>
      </template>
      <div class="video-info">
        <div class="video-info-left">
          <el-icon>
            <VideoPlay />
          </el-icon>
          <span class="play-num">{{ prjStudyFrequence }}</span>
          <el-icon>
            <Operation />
          </el-icon>
          <span class="dm-num">{{ prjQuestionNumbers }}</span>
        </div>
        <div class="video-info-right">
          <template v-if="props.prjForm == PrjForm.video">
            <span class="video-time">{{ duration }}</span>
          </template>
          <template v-else-if="props.prjForm == PrjForm.draft">
            <span class="video-time">{{ countWord }}</span>
          </template>
        </div>
      </div>
      <div class="layer"></div>
    </div>
    <div class="intro">
      <h3 class="title"><span v-html="title"></span></h3>
      <p v-if="goodsType !== GoodsType.vip" class="abstract">
        <span v-html="description"></span>
      </p>
      <p v-else class="abstract-five">
        <span v-html="description"></span>
      </p>
      <!-- 学习时间信息 -->
      <div class="study-time-info">
        <div class="study-time-row">
          <span class="label">学习开始时间：</span>
          <span class="value">{{ startTime }}</span>
        </div>
        <div class="study-time-row">
          <span class="label">学习结束时间：</span>
          <span class="value">{{ expirationTime }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import defaultPng from '@/assets/images/home/<USER>';
import { PrjForm, PrjType } from '@/types/project';
import { BuyStatus, GoodsType } from '@/types/goods';
// import { de } from 'element-plus/es/locale';
import { DocumentCopy, Operation, Share, VideoPlay } from '@element-plus/icons-vue';

const router = useRouter();

const testdescription = "你好你好你好你好你好你好你好你好你好你好你好你好你好你好你好你好你好你好你好"

interface Props {
  /**
   * 项目封面
   */
  coverPic: string;
  /**
   * 项目表述
   */
  description: string;

  goodsType?: GoodsType;
  /**
   * 问题次数
   */
  prjQuestionNumbers?: number;
  /**
   * 学习次数
   */
  prjStudyFrequence?: number;
  /**
   * 学习时长
   */
  studyTime: number;
  //视频时长
  duration?: string;
  /**
   * 项目标题
   */
  title: string;

  //跳转介绍页需要用到的参数
  // goodsId: number;
  prjType: number;
  /**
   * 项目商品唯一标识
   */
  spuId: string;
  //视频形式还是文稿形式
  prjForm: PrjForm;
  userCoverPic?: string;
  userName?: string;
  //开始学习需要判断的参数
  /**
   * 1表示免费商品，0表示付费商品
   */
  isFree: number;
  buyStatus: number;
  latestChapterId?: number;
  [property: string]: any;
  countWord?: number;
  startTime: string;
  expirationTime: string;
}
// 几种形式，video，draft ，domain
const props = withDefaults(defineProps<Props>(), {
  // 用于设置默认值
  coverPic: defaultPng,
  userCoverPic: defaultPng
});
const goIntroPage = async () => {

  const { href } = router.resolve({
    path: '/goodIntroduce',
    query: {
      spuId: props.spuId
    }
  });
  window.open(href, '_blank');
  // try {
  //   if (props.goodsType == GoodsType.vip) {
  //     const { href } = router.resolve({
  //       path: '/case',
  //       query: {
  //         spuId: props.spuId
  //       }
  //     });
  //     window.open(href, '_blank');
  //   } else {
  //     if (props.prjType == PrjType.case) {
  //       const { href } = router.resolve({
  //         path: '/case',
  //         query: {
  //           spuId: props.spuId
  //         }
  //       });
  //       window.open(href, '_blank');
  //     } else if (props.prjType == PrjType.klgExplain) {
  //       const { href } = router.resolve({
  //         path: '/klgexplain',
  //         query: {
  //           spuId: props.spuId
  //         }
  //       });
  //       window.open(href, '_blank');
  //     } else {
  //       const { href } = router.resolve({
  //         path: '/exam',
  //         query: {
  //           spuId: props.spuId
  //         }
  //       });
  //       window.open(href, '_blank');
  //     }
  //   }
  // } catch (error) {
  //   console.error('Error setting intro data:', error);
  // }
};
// 免费的和已购买的跳转详情页
// 付费的跳转到介绍页
const goDetailPage = async () => {
  try {
    // istry 0代表不可看，1代表可看
    // isFree 0 付费
    if (props.buyStatus == BuyStatus.bought) {
      const query: {
        spuId: string;
        chapterId?: string;
      } = {
        spuId: props.spuId
      };
      if (props.latestChapterId) {
        query.chapterId = props.latestChapterId.toString();
      }
      const { href } = router.resolve({
        path: '/learning',
        query: query
      });
      window.open(href, '_blank');
    } else {
      // 跳转介绍页
      goIntroPage();
    }
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
</script>

<style scoped lang="less">
.w130 {
  width: 130px;
  height: 25px;
  font-size: 12px;
}

.el-card {
  width: 300px;
  max-height: 354px;

  border-width: 1px;
  border-style: solid;
  border-color: rgba(242, 242, 242, 1);
  border-radius: 5px;
  -moz-box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.***************);
  -webkit-box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.***************);
  box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.***************);

  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  font-family: var(--text-family);

  .vip {
    position: absolute;
    left: 5px;
    top: 5px;
    display: inline-block;
    width: 36px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    background-color: #ffd37a;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.996);
    z-index: 1;

    &.free {
      background-color: rgb(18, 170, 156);
    }
  }

  .image-wrapper {
    position: relative;
    width: 300px;
    height: 225px;
    overflow: hidden;
  }

  .layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #797979;
    z-index: 1;
    opacity: 0;
  }

  &:hover {
    .layer {
      opacity: 0.5;
    }

    .video-info,
    .big-video-icon {
      opacity: 1;
    }
    .video-info {
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
    }
  }

  .big-video-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: rgb(170, 170, 170);
    opacity: 0;
    z-index: 10;
  }

  .video-info {
    height: 30px;
    position: absolute;
    width: 100%;
    padding: 0 10px 4px;
    bottom: 0px;
    color: rgb(254, 254, 254);
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    font-size: var(--fontsize-small-project);
    // border-bottom-right-radius: 4px;
    // border-bottom-left-radius: 4px;
    z-index: 10;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);

    .video-info-left {
      display: flex;
      align-items: center;
    }

    .play-num {
      margin-right: 20px;
    }

    .el-icon {
      width: var(--fontsize-small-project);
      height: var(--fontsize-small-project);
      display: flex;
      align-items: center;
      margin-right: 5px;
    }
  }

  .intro {
    padding: 10px;
    color: var(--color-black);
  }

  .image {
    width: 100%;
    height: auto;
    display: block;
  }

  .title {
    font-size: var(--fontsize-middle-project);
    font-family: var(--title-family);
    font-weight: 600;
    height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 1px;
  }

  .abstract {
    font-size: var(--fontsize-small-project);
    font-family: var(--text-family);
    font-weight: 400;
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.5;
    min-height: 36px;
  }

  .abstract-five {
    font-size: var(--fontsize-small-project);
    font-family: var(--text-family);
    font-weight: 400;
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-height: 1.5;
    min-height: 54px;
  }

  .logo-info {
    display: flex;
    align-items: center;
    height: 20px;
    line-height: 20px;
    margin: 5px 0;

    .logo {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      overflow: hidden;
    }

    .wjby {
      font-size: var(--fontsize-small-project);
      font-weight: 400;
      color: #797979;
      margin-left: 10px;
    }
  }

  .btn-group {
    display: flex;
    justify-content: space-between;
  }
}
.study-time-info {
  //margin-top: 10px;
  .study-time-row {
    font-size: 13px;
    color: #666;
    margin-bottom: 2px;
    display: flex;
    .label {
      font-size: var(--fontsize-small-project);
      font-family: var(--text-family);
      font-weight: 400;
      margin-right: 4px;
    }
    .value {
      font-size: var(--fontsize-small-project);
      font-family: var(--text-family);
      font-weight: 400;
      color: #222;
    }
  }
}
</style>
