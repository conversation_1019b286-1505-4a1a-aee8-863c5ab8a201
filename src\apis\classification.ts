import { http } from '@/apis';

export function getPrjThemeListApi() {
  return http.request<{
    list: Array<{ areaCode: string; title: string }>;
  }>({
    url: '/tag/getBTags'
  });
}

export interface ParamsItf {
  themes: Array<string>;
  formType: Array<number>;
  price: Array<number>;
  order: number;
  prjType: Array<number>;
  current: number;
  limit: number;
  keyword: string;
}

export function getPrjListApi(data: ParamsItf) {
  return http.request<{
    current: number;
    limit: number;
    total: number;
    records: Array<{
      uniqueCode: string;
      title: string;
      prjForm: number;
      prjType: number;
      wordsCount?: number;
      coverPic: string;
      spuId: string;
      price: number;
      goodsType: number;
      description: string;
      prjStudyFrequence: number;
      prjQuestionNumbers: number;
      duration?: string;
      userName: string;
      userCoverPic: string;
      isTry: number;
      buyStatus: number;
      latestChapterId?: number;
      isFree?: number;
    }>;
  }>({
    url: '/project-goods/page',
    method: 'post',
    data
  });
}

