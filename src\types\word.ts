export interface RenderInfo {
  content: string;         // 内容文本
  isTag: boolean;          // 是否是HTML标签
  relatedQuestionCount: number;  // 关联问题数量阈值
  relatedSearchCount: number;    // 搜索关联阈值
  questionCountMap: {      // 问题ID到计数的映射
    [key: string | number]: number;
  };
  searchCount: number;     // 搜索计数
}
export enum QuestionAction {
  add = 0,
  remove = 1
}

export enum Mode {
  read = 0,
  ask = 1
}
