<template>
  <div class="markdown-outline hover-scrollbar">
    <div class="outline-header">
      <div class="titlefont title">大纲</div>
    </div>
    <div class="outline-content" v-if="treeData.length > 0">
      <el-tree
        :data="treeData"
        :props="treeProps"
        :default-expand-all="true"
        :expand-on-click-node="false"
        node-key="id"
        class="outline-tree"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="tree-node" :class="`level-${data.level}`">
            <span
              class="node-label textfont ellipsis"
              v-html="data.label"
            ></span>
          </div>
        </template>
      </el-tree>
    </div>
    <div class="outline-empty" v-else>
      <p>暂无目录</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineEmits } from 'vue';
import type { OutlineItem, TreeNode } from '@/utils/outlineUtils';
import {
  buildHierarchicalOutline,
  scrollToOutlineItem,
  convertToTreeData
} from '@/utils/outlineUtils';
interface Props {
  outline: OutlineItem[];
}

const props = defineProps<Props>();
const emit = defineEmits(['jump']);

// 将平铺的大纲转换为层级结构
const hierarchicalOutline = computed(() => {
  return buildHierarchicalOutline(props.outline);
});

// 转换为el-tree需要的数据格式
const treeData = computed(() => {
  return convertToTreeData(hierarchicalOutline.value);
});

// el-tree的props配置
const treeProps = {
  children: 'children',
  label: 'label'
};

// 处理节点点击
const handleNodeClick = (data: TreeNode) => {
  scrollToOutlineItem(data.id)
  emit('jump', data.id);
};
</script>

<style scoped lang="less">
.markdown-outline {
  /* 设置足够的高度以触发父容器滚动条 */
  display: flex;
  flex-direction: column;
  background: #fff;
  max-height: calc(100vh - 70px); /* 设置最小高度确保能触发滚动 */
  width: 100%;
}

.outline-header {
  padding: 5px;
  background: #fafafa;
  font-size: 14px;
  height: 20px;
  margin-bottom: 10px;

  .title {
    margin-left: 5px;
  }
}

.outline-content {
  /* 让父容器(outline-sidebar)处理滚动 */
  flex: 1;
  padding: 8px 0;
  /* 添加足够的内容高度以触发滚动 */

  :deep(.outline-tree) {
    .el-tree-node {
      margin-bottom: 10px;
      width: 90%;
      .el-tree-node__content {
        height: 20px;
        line-height: 20px;
        &:hover {
          background-color: none;
        }

        .el-tree-node__expand-icon {
          color: #666;
          font-size: 12px;
          padding: 1px;
        }
      }

      .tree-node {
        width: 100%;
        cursor: pointer;
        padding: 0;

        .node-label {
          font-size: 14px;
          color: #333;
          word-break: break-word;
        }
      }

      // 子节点样式
      .el-tree-node__children {
        margin-top: 2px;
        max-height: 20px;
        .el-tree-node .tree-node {
          .node-label {
            font-weight: 400;
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }
}

.outline-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  min-height: calc(100vh - 70px - 60px); /* 给空状态足够的高度以触发滚动 */
  padding: 20px 0;
}
</style>
